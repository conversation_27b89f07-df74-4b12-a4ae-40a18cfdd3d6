# 功能3-ENI创建改造-单元测试清单

## 测试范围
功能3 ENI创建改造包含以下模块：
- ENI创建改造（支持双栈ENI创建）
- 辅助IP管理增强（基于isIpv6参数）
- 修改紧急分配请求处理（支持双栈IP分配）

## 1. ENI创建改造测试

### 1.1 doCreateEni函数测试
- **TestDoCreateEni_IPv4Only**: 测试仅IPv4的ENI创建
  - 节点不包含`bci-enable-ipv6`标签
  - 验证只创建IPv4辅助IP
  - 验证`createArgs.IPv6PrivateIPSet`为空

- **TestDoCreateEni_IPv6Enabled**: 测试启用IPv6的ENI创建
  - 节点包含`bci-enable-ipv6=true`标签
  - 验证同时创建IPv4和IPv6辅助IP
  - 验证IPv4和IPv6的initPrivateIPCount相同

- **TestDoCreateEni_IPv6Disabled**: 测试IPv6明确禁用的ENI创建
  - 节点包含`bci-enable-ipv6=false`标签
  - 验证只创建IPv4辅助IP
  - 验证`createArgs.IPv6PrivateIPSet`为空

### 1.2 日志记录测试
- **TestCreateEniLogging**: 验证创建ENI时的日志输出
  - 检查是否记录了enableIPv6状态
  - 检查日志格式是否正确

## 2. 辅助IP管理增强测试

### 2.1 doBatchAddEniPrivateIPWithIPv6函数测试
- **TestBatchAddIPv6_IPv4Only**: 测试仅添加IPv4地址
  - `needAllocateIPv4Count > 0`，`needAllocateIPv6Count = 0`
  - 验证只调用IPv4的BatchAddPrivateIP
  - 验证`IsIpv6=false`参数

- **TestBatchAddIPv6_IPv6Only**: 测试仅添加IPv6地址
  - `needAllocateIPv4Count = 0`，`needAllocateIPv6Count > 0`
  - 验证只调用IPv6的BatchAddPrivateIP
  - 验证`IsIpv6=true`参数

- **TestBatchAddIPv6_DualStack**: 测试同时添加IPv4和IPv6地址
  - 两个参数都大于0
  - 验证分别调用IPv4和IPv6的BatchAddPrivateIP
  - 验证两次调用的参数正确性

- **TestBatchAddIPv6_NoAddition**: 测试不需要添加地址的情况
  - 两个参数都为0
  - 验证不调用任何BatchAddPrivateIP
  - 但仍需调用updateBciNodeCRDSpecWhenPrivateIPChanged

### 2.2 限流和重试测试
- **TestBatchAddIPv6_RatelimitRetry**: 测试IPv4添加时的限流重试
  - 模拟第一次调用被限流
  - 验证重试逻辑正确
  - 验证最终成功

- **TestBatchAddIPv6_IPv6RatelimitRetry**: 测试IPv6添加时的限流重试
  - 类似IPv4但针对IPv6
  - 验证IPv6重试参数正确

### 2.3 错误处理测试
- **TestBatchAddIPv6_IPv4Error**: 测试IPv4添加失败的错误处理
  - IPv4添加返回非限流错误
  - 验证立即返回错误，不执行IPv6添加

- **TestBatchAddIPv6_IPv6Error**: 测试IPv6添加失败的错误处理
  - IPv4成功但IPv6失败
  - 验证返回相应错误信息

## 3. IPv6辅助函数测试

### 3.1 computeENIAllocatedAndFreePrivateIPv6List函数测试
- **TestComputeIPv6AllocatedList_EmptyNode**: 测试空节点的IPv6计算
  - 节点上没有Pod
  - 验证返回空的已使用和空闲列表

- **TestComputeIPv6AllocatedList_WithPods**: 测试有Pod的IPv6计算
  - 节点上有使用IPv6的Pod
  - 验证正确计算已使用和空闲IPv6地址

- **TestComputeIPv6AllocatedList_MixedPods**: 测试混合Pod的IPv6计算
  - 一些Pod有IPv6，一些没有
  - 验证只统计有IPv6的Pod

### 3.2 computeENIAllocatedPrivateIPv6List函数测试
- **TestComputeENIIPv6_FromAPIResponse**: 测试从API响应获取IPv6列表
  - StatENI成功返回
  - 验证正确解析IPv6PrivateIPSet
  - 验证排除主IPv6地址

- **TestComputeENIIPv6_FromCRD**: 测试从CRD获取IPv6列表
  - StatENI失败，回退到CRD
  - 验证从bcinode.Spec.EniMultiIP.Pool获取
  - 验证PrivateIPv6Addresses解析正确

- **TestComputeENIIPv6_AccountNotFound**: 测试账户未找到的情况
  - CRD中不存在对应账户
  - 验证返回空列表而不是错误

### 3.3 computePodAllocatedPrivateIPv6List函数测试
- **TestComputePodIPv6_FromPodStatus**: 测试从Pod Status获取IPv6
  - Pod.Status.PodIPs包含IPv6地址
  - 验证正确识别和提取IPv6地址

- **TestComputePodIPv6_FromAnnotation**: 测试从Annotation获取IPv6
  - Pod Status中没有IPv6，但Annotation中有
  - 验证使用`bci_internal_PodIP_ipv6`注解

- **TestComputePodIPv6_NoIPv6**: 测试没有IPv6的Pod
  - Pod既没有Status IPv6也没有Annotation IPv6
  - 验证该Pod不计入IPv6统计

### 3.4 isIPv6Address函数测试
- **TestIsIPv6Address_ValidIPv6**: 测试有效的IPv6地址
  - `2001:db8::1`等标准IPv6格式
  - 验证返回true

- **TestIsIPv6Address_ValidIPv4**: 测试IPv4地址
  - `***********`等IPv4格式
  - 验证返回false

- **TestIsIPv6Address_InvalidFormat**: 测试无效格式
  - 空字符串、不完整地址等
  - 验证正确处理边界情况

## 4. 紧急分配请求处理测试

### 4.1 dealEmergencyAllocateIPRequest函数测试
- **TestEmergencyRequest_IPv4Only**: 测试仅IPv4的紧急分配
  - 节点未启用IPv6
  - 验证只分配IPv4地址
  - 验证调用参数正确

- **TestEmergencyRequest_DualStack**: 测试双栈紧急分配
  - 节点启用IPv6
  - IPv4和IPv6都不足
  - 验证同时分配两种地址

- **TestEmergencyRequest_IPv6Sufficient**: 测试IPv6充足的情况
  - IPv4不足但IPv6充足
  - 验证只分配IPv4，不分配IPv6

- **TestEmergencyRequest_BothSufficient**: 测试都充足的情况
  - IPv4和IPv6都充足
  - 验证不进行任何分配

### 4.2 分配数量计算测试
- **TestEmergencyAllocation_MaxLimit**: 测试最大限制计算
  - 需要分配的数量超过最大可分配数量
  - 验证正确限制分配数量

- **TestEmergencyAllocation_CalculateCorrectly**: 测试正确计算分配数量
  - `needCount = waitPods - freeIPs`
  - 验证IPv4和IPv6分别计算正确

## 5. 批量删除改造测试

### 5.1 doBatchDeletePrivateIP函数测试
- **TestBatchDelete_IPv4Only**: 测试删除纯IPv4地址列表
  - 删除列表只包含IPv4地址
  - 验证正确调用BatchDeletePrivateIP

- **TestBatchDelete_IPv6Only**: 测试删除纯IPv6地址列表
  - 删除列表只包含IPv6地址
  - 验证系统自动识别IPv6

- **TestBatchDelete_MixedAddresses**: 测试删除混合地址列表
  - 删除列表包含IPv4和IPv6地址
  - 验证系统自动识别和处理

- **TestBatchDelete_EmptyList**: 测试空删除列表
  - 传入空的删除列表
  - 验证直接返回不执行删除

### 5.2 限流和错误处理测试
- **TestBatchDelete_RatelimitRetry**: 测试删除时的限流重试
  - 模拟删除被限流
  - 验证重试逻辑正确

- **TestBatchDelete_PermanentError**: 测试永久错误处理
  - 删除返回非限流错误
  - 验证正确返回错误信息

## 6. 边界条件和错误处理测试

### 6.1 边界条件测试
- **TestBoundary_ZeroInitCount**: 测试初始IP数量为0
- **TestBoundary_MaxInitCount**: 测试最大初始IP数量
- **TestBoundary_InvalidNodeLabel**: 测试无效的节点标签值

### 6.2 错误处理测试
- **TestError_GetNodeEniInfoFailed**: 测试获取节点ENI信息失败
- **TestError_UpdateCRDFailed**: 测试更新CRD失败
- **TestError_EniClientError**: 测试ENI客户端错误

## 7. 集成测试

### 7.1 完整流程测试
- **TestIntegration_CompleteIPv6Workflow**: 测试完整的IPv6工作流
  - 从ENI创建到IP分配再到删除
  - 验证整个流程的一致性

- **TestIntegration_MixedWorkload**: 测试混合工作负载
  - 同时有仅IPv4和双栈Pod
  - 验证资源管理的正确性

## 测试覆盖率要求
- 代码覆盖率: > 85%
- 分支覆盖率: > 80%
- 函数覆盖率: 100%

## Mock对象
需要Mock以下对象：
- `eni.Client`: ENI客户端接口
- `corev1_listers.PodLister`: Pod列表接口
- `corev1_listers.NodeLister`: Node列表接口
- `bcinode_listers.BciNodeLister`: BciNode列表接口 