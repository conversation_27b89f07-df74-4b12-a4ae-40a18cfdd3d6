# 功能4.3-紧急分配请求处理优化-端到端测试清单

## 1. 测试覆盖目标
- 验证紧急分配请求处理在真实环境中的功能正确性
- 测试双栈IP分配优化的端到端流程
- 验证资源充足性检查在实际场景中的准确性
- 测试容量限制检查的有效性
- 验证错误处理和恢复机制

## 2. 测试环境准备

### 2.1 集群环境要求
- Kubernetes 1.20+
- 支持IPv6的网络环境
- 配置双栈网络的BCI集群
- 启用bci-resource-controller的IPv6功能

### 2.2 节点配置要求
```yaml
# 支持IPv6的节点标签
metadata:
  labels:
    bci-ipv6-enabled: "true"
    
# 不支持IPv6的节点标签
metadata:
  labels:
    bci-ipv6-enabled: "false"
```

### 2.3 测试资源准备
- 测试用的VPC和子网（支持IPv6）
- 测试用的安全组配置
- 测试用的ENI配置
- 测试用的Pod模板

## 3. 端到端测试场景

### 3.1 基本功能测试

#### 3.1.1 双栈IP紧急分配测试
```yaml
# 测试场景：Pod请求双栈IP时的紧急分配
apiVersion: v1
kind: Pod
metadata:
  name: test-dual-stack-pod
  annotations:
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  containers:
  - name: test-container
    image: nginx:alpine
```

**测试步骤：**
1. 清空ENI的IP buffer
2. 创建需要双栈IP的Pod
3. 验证紧急分配触发
4. 检查IPv4和IPv6地址的分配结果
5. 验证Pod网络连通性

**期望结果：**
- Pod成功获得IPv4和IPv6地址
- 网络连通性正常
- 分配日志记录完整

#### 3.1.2 IPv4单栈紧急分配测试
```yaml
# 测试场景：Pod仅请求IPv4时的紧急分配
apiVersion: v1
kind: Pod
metadata:
  name: test-ipv4-only-pod
  annotations:
    bci.baidu.com/bci-enable-ipv6: "false"
spec:
  containers:
  - name: test-container
    image: nginx:alpine
```

**测试步骤：**
1. 清空ENI的IPv4 buffer
2. 创建仅需IPv4的Pod
3. 验证紧急分配触发
4. 检查仅IPv4地址的分配结果
5. 验证Pod网络连通性

**期望结果：**
- Pod成功获得IPv4地址
- 不分配IPv6地址
- 网络连通性正常

### 3.2 资源充足性检查测试

#### 3.2.1 IPv4资源充足测试
**测试场景：** IPv4资源充足时的紧急分配行为

**测试步骤：**
1. 确保ENI有足够的IPv4空闲地址
2. 创建需要IPv4的Pod
3. 验证不触发紧急分配
4. 检查Pod使用现有空闲地址

**期望结果：**
- 不触发紧急分配
- Pod使用现有空闲IPv4地址
- 日志显示资源充足跳过分配

#### 3.2.2 IPv6资源充足测试
**测试场景：** IPv6资源充足时的紧急分配行为

**测试步骤：**
1. 确保ENI有足够的IPv6空闲地址
2. 创建需要IPv6的Pod
3. 验证不触发紧急分配
4. 检查Pod使用现有空闲地址

**期望结果：**
- 不触发紧急分配
- Pod使用现有空闲IPv6地址
- 日志显示资源充足跳过分配

#### 3.2.3 混合资源状态测试
**测试场景：** IPv4充足但IPv6不足时的紧急分配

**测试步骤：**
1. 确保ENI有足够的IPv4但IPv6不足
2. 创建需要双栈IP的Pod
3. 验证仅触发IPv6紧急分配
4. 检查分配结果

**期望结果：**
- 仅分配IPv6地址
- IPv4使用现有空闲地址
- 分配日志准确记录

### 3.3 容量限制检查测试

#### 3.3.1 IPv4容量限制测试
**测试场景：** IPv4容量达到上限时的处理

**测试步骤：**
1. 将ENI的IPv4地址分配到最大容量
2. 创建需要IPv4的Pod
3. 验证紧急分配失败
4. 检查错误处理和Pod状态

**期望结果：**
- 紧急分配失败
- Pod保持Pending状态
- 错误信息正确记录到Pod annotation

#### 3.3.2 IPv6容量限制测试
**测试场景：** IPv6容量达到上限时的处理

**测试步骤：**
1. 将ENI的IPv6地址分配到最大容量
2. 创建需要IPv6的Pod
3. 验证紧急分配失败
4. 检查错误处理和Pod状态

**期望结果：**
- 紧急分配失败
- Pod保持Pending状态
- 错误信息正确记录到Pod annotation

### 3.4 边界情况测试

#### 3.4.1 节点不支持IPv6测试
**测试场景：** 在不支持IPv6的节点上请求IPv6地址

**测试步骤：**
1. 确保节点标签为`bci-ipv6-enabled: "false"`
2. 创建需要IPv6的Pod并调度到该节点
3. 验证错误处理
4. 检查Pod状态和错误信息

**期望结果：**
- Pod分配失败
- 错误信息明确指出节点不支持IPv6
- 错误记录到Pod annotation

#### 3.4.2 并发紧急分配测试
**测试场景：** 多个Pod同时触发紧急分配

**测试步骤：**
1. 清空ENI的IP buffer
2. 同时创建多个需要IP的Pod
3. 验证并发分配处理
4. 检查所有Pod的分配结果

**期望结果：**
- 所有Pod成功分配地址
- 不发生地址冲突
- 分配过程日志完整

### 3.5 错误处理和恢复测试

#### 3.5.1 API调用失败测试
**测试场景：** 底层API调用失败时的处理

**测试步骤：**
1. 模拟网络或认证问题
2. 触发紧急分配
3. 验证错误处理和重试机制
4. 检查错误恢复

**期望结果：**
- 错误正确处理
- 重试机制生效
- 失败时Pod状态正确更新

#### 3.5.2 限流处理测试
**测试场景：** API限流时的处理

**测试步骤：**
1. 触发API限流
2. 验证限流错误处理
3. 检查重试机制
4. 验证最终分配成功

**期望结果：**
- 限流错误正确识别
- 重试机制生效
- 最终分配成功

## 4. 性能测试

### 4.1 分配性能测试
**测试场景：** 大量Pod同时请求IP时的性能表现

**测试步骤：**
1. 创建100个Pod同时请求IP
2. 监控分配耗时
3. 检查系统资源使用
4. 验证分配成功率

**期望结果：**
- 分配延迟在可接受范围内
- 系统资源使用合理
- 分配成功率100%

### 4.2 资源检查性能测试
**测试场景：** 资源充足性检查的性能表现

**测试步骤：**
1. 在大量已分配IP的环境中测试
2. 监控资源检查耗时
3. 验证检查准确性
4. 检查内存使用

**期望结果：**
- 检查耗时在可接受范围内
- 检查结果准确
- 内存使用稳定

## 5. 稳定性测试

### 5.1 长时间运行测试
**测试场景：** 长时间运行的稳定性

**测试步骤：**
1. 持续运行24小时
2. 定期创建和删除Pod
3. 监控内存和CPU使用
4. 检查错误日志

**期望结果：**
- 系统稳定运行
- 无内存泄漏
- 无异常错误

### 5.2 异常恢复测试
**测试场景：** 异常情况下的恢复能力

**测试步骤：**
1. 模拟网络中断
2. 模拟controller重启
3. 验证状态恢复
4. 检查数据一致性

**期望结果：**
- 快速恢复正常
- 数据状态一致
- 无数据丢失

## 6. 监控和日志验证

### 6.1 监控指标验证
**验证指标：**
- `dynamic_ip_fail_counter` - IP分配失败计数器
- `dynamic_ipv6_fail_counter` - IPv6分配失败计数器
- ENI IP使用率监控
- 分配延迟监控

**验证要点：**
- 指标准确性
- 告警规则有效性
- 数据完整性

### 6.2 日志验证
**验证日志：**
- 紧急分配触发日志
- 资源检查日志
- 分配结果日志
- 错误处理日志

**验证要点：**
- 日志格式正确
- 信息完整性
- 错误级别准确

## 7. 回归测试

### 7.1 向后兼容性测试
**测试场景：** 确保原有IPv4功能不受影响

**测试步骤：**
1. 在IPv4-only环境中测试
2. 验证原有功能正常
3. 检查性能无退化
4. 确认配置兼容性

**期望结果：**
- 原有功能正常
- 性能无退化
- 配置向后兼容

### 7.2 配置变更测试
**测试场景：** 配置变更时的适应性

**测试步骤：**
1. 动态修改IPv6配置
2. 验证配置生效
3. 检查运行时适应性
4. 确认无服务中断

**期望结果：**
- 配置正确生效
- 运行时适应良好
- 无服务中断

## 8. 测试执行策略

### 8.1 测试环境管理
```bash
# 创建测试环境
kubectl create namespace bci-test

# 部署测试资源
kubectl apply -f test-resources.yaml

# 清理测试环境
kubectl delete namespace bci-test
```

### 8.2 测试数据收集
```bash
# 收集日志
kubectl logs -n bci-system bci-resource-controller-* > controller.log

# 收集监控数据
kubectl get --raw /metrics > metrics.txt

# 收集Pod状态
kubectl get pods -o wide > pod-status.txt
```

### 8.3 测试报告生成
- 测试结果汇总
- 性能数据分析
- 错误情况统计
- 改进建议

## 9. 测试验收标准

### 9.1 功能验收标准
- ✅ 所有基本功能测试通过
- ✅ 边界情况正确处理
- ✅ 错误处理机制有效
- ✅ 资源检查准确性100%

### 9.2 性能验收标准
- ✅ 分配延迟 < 2s
- ✅ 资源检查耗时 < 100ms
- ✅ 内存使用稳定
- ✅ CPU使用率 < 50%

### 9.3 稳定性验收标准
- ✅ 24小时稳定运行
- ✅ 异常恢复时间 < 30s
- ✅ 无内存泄漏
- ✅ 无数据丢失

## 10. 风险控制

### 10.1 测试风险
- 测试环境与生产环境差异
- 测试数据量限制
- 并发测试复杂性

### 10.2 风险缓解
- 使用生产级别的测试环境
- 逐步增加测试规模
- 完善的回滚机制

### 10.3 应急预案
- 测试失败时的快速回滚
- 生产环境的影响评估
- 问题定位和修复流程 