# 功能4.2-辅助IP管理增强-端到端测试清单

## 测试概述
本测试清单涵盖了功能4.2 辅助IP管理增强的端到端测试场景，验证IPv6双栈IP批量管理功能在真实环境中的表现。

## 测试环境准备

### 1. 测试环境要求
- Kubernetes集群（v1.20+）
- 百度云VPC环境支持IPv6
- 部署bci-resource-controller组件
- 配置百度云认证信息
- 节点支持IPv6网络

### 2. 测试数据准备
```yaml
# IPv6双栈节点配置
apiVersion: v1
kind: Node
metadata:
  name: test-node-dual-stack
  labels:
    cluster-id: "test-cluster"
    kubernetes.io/node-resource-type: "bcc.g4.c2m4"
    bci-ipv6-enabled: "true"  # 启用IPv6
    node-enable-eni-private-ip-gc: "true"  # 启用IP GC
  annotations:
    node.beta.kubernetes.io/instance-type: "bcc.g4.c2m4"
    node-eni-private-ip-buffer: "3"  # 设置buffer数量
status:
  conditions:
  - type: Ready
    status: "True"
```

```yaml
# IPv4单栈节点配置
apiVersion: v1
kind: Node
metadata:
  name: test-node-ipv4-only
  labels:
    cluster-id: "test-cluster"
    kubernetes.io/node-resource-type: "bcc.g4.c2m4"
    # 不包含bci-ipv6-enabled标签
    node-enable-eni-private-ip-gc: "true"
  annotations:
    node.beta.kubernetes.io/instance-type: "bcc.g4.c2m4"
    node-eni-private-ip-buffer: "3"
status:
  conditions:
  - type: Ready
    status: "True"
```

```yaml
# 测试配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: eni-config
  namespace: kube-system
data:
  config.yaml: |
    defaultENIBufferPrivateIPCount: 3
    defaultENIPrivateIPCount: 10
    enableAllNodePrivateIPGC: true
    dynamicChangePrivateIPBatchsize: 5
    eniChangeRatelimitLoopRetryCount: 3
```

## 测试场景

### 场景1：IPv4单栈节点的IP管理（向后兼容测试）

#### 1.1 测试目标
验证在IPv4单栈节点上，辅助IP管理功能与原有逻辑保持一致。

#### 1.2 测试步骤
1. 创建IPv4单栈节点
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Node
metadata:
  name: test-node-ipv4-only
  labels:
    cluster-id: "test-cluster"
    kubernetes.io/node-resource-type: "bcc.g4.c2m4"
    node-enable-eni-private-ip-gc: "true"
  annotations:
    node.beta.kubernetes.io/instance-type: "bcc.g4.c2m4"
    node-eni-private-ip-buffer: "3"
status:
  conditions:
  - type: Ready
    status: "True"
EOF
```

2. 创建多个Pod触发IP分配
```bash
for i in {1..5}; do
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-pod-ipv4-$i
  namespace: default
  annotations:
    cross-vpc-eni.cce.io/subnetID: "subnet-123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123"
spec:
  nodeSelector:
    kubernetes.io/hostname: test-node-ipv4-only
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
EOF
done
```

3. 观察ENI和IP分配情况
```bash
# 检查ENI状态
kubectl get bcinode test-node-ipv4-only -o yaml

# 检查controller日志
kubectl logs -n kube-system -l app=bci-resource-controller --tail=50 | grep -E "(BatchAddPrivateIP|IPv4)"
```

#### 1.3 预期结果
- Pod成功创建并获得IPv4地址
- ENI上分配了正确数量的IPv4辅助IP
- 没有IPv6相关的操作
- 日志中显示IPv4分配信息

#### 1.4 验证命令
```bash
# 检查BciNode CRD中的IP分配
kubectl get bcinode test-node-ipv4-only -o jsonpath='{.spec.eniMultiIP}' | jq '.'

# 检查Pod IP分配
kubectl get pod test-pod-ipv4-1 -o jsonpath='{.status.podIP}'

# 检查ENI上的IP数量
kubectl get bcinode test-node-ipv4-only -o jsonpath='{.spec.eniMultiIP.pool}' | jq '.[] | length'
```

### 场景2：IPv6双栈节点的IP管理

#### 2.1 测试目标
验证在IPv6双栈节点上，系统能够正确分配和管理IPv4和IPv6辅助IP。

#### 2.2 测试步骤
1. 创建IPv6双栈节点
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Node
metadata:
  name: test-node-dual-stack
  labels:
    cluster-id: "test-cluster"
    kubernetes.io/node-resource-type: "bcc.g4.c2m4"
    bci-ipv6-enabled: "true"
    node-enable-eni-private-ip-gc: "true"
  annotations:
    node.beta.kubernetes.io/instance-type: "bcc.g4.c2m4"
    node-eni-private-ip-buffer: "4"
status:
  conditions:
  - type: Ready
    status: "True"
EOF
```

2. 创建需要IPv6的Pod
```bash
for i in {1..4}; do
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-pod-dual-stack-$i
  namespace: default
  annotations:
    cross-vpc-eni.cce.io/subnetID: "subnet-123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123"
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  nodeSelector:
    kubernetes.io/hostname: test-node-dual-stack
    bci-ipv6-enabled: "true"
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
EOF
done
```

3. 观察IPv4和IPv6 IP分配
```bash
# 检查ENI状态
kubectl get bcinode test-node-dual-stack -o yaml

# 检查controller日志
kubectl logs -n kube-system -l app=bci-resource-controller --tail=100 | grep -E "(BatchAddPrivateIP|IPv4|IPv6)"
```

#### 2.3 预期结果
- Pod成功创建并获得IPv4和IPv6地址
- ENI上分配了IPv4和IPv6辅助IP
- 日志中显示IPv4和IPv6分配信息
- BciNode CRD中包含IPv6地址信息

#### 2.4 验证命令
```bash
# 检查BciNode CRD中的IPv6地址
kubectl get bcinode test-node-dual-stack -o jsonpath='{.spec.eniMultiIP.pool}' | jq '.'

# 检查Pod的IPv6地址
kubectl get pod test-pod-dual-stack-1 -o jsonpath='{.metadata.annotations.bci_internal_PodIPv6}'

# 检查ENI上的IPv4和IPv6 IP数量
kubectl get bcinode test-node-dual-stack -o yaml | grep -A 10 -B 10 "privateIPv6Addresses"
```

### 场景3：紧急IP分配测试

#### 3.1 测试目标
验证在IP不足时，紧急分配机制能正确处理IPv4和IPv6需求。

#### 3.2 测试步骤
1. 创建有限IP缓冲区的节点
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Node
metadata:
  name: test-node-limited-buffer
  labels:
    cluster-id: "test-cluster"
    kubernetes.io/node-resource-type: "bcc.g4.c2m4"
    bci-ipv6-enabled: "true"
    node-enable-eni-private-ip-gc: "true"
  annotations:
    node.beta.kubernetes.io/instance-type: "bcc.g4.c2m4"
    node-eni-private-ip-buffer: "2"  # 设置小的buffer
status:
  conditions:
  - type: Ready
    status: "True"
EOF
```

2. 快速创建多个Pod触发紧急分配
```bash
for i in {1..8}; do
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-pod-emergency-$i
  namespace: default
  annotations:
    cross-vpc-eni.cce.io/subnetID: "subnet-123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123"
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  nodeSelector:
    kubernetes.io/hostname: test-node-limited-buffer
    bci-ipv6-enabled: "true"
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
EOF
done
```

3. 观察紧急分配过程
```bash
# 实时监控controller日志
kubectl logs -n kube-system -l app=bci-resource-controller -f | grep -E "(dealEmergencyAllocateIPRequest|needAllocateIPv4Count|needAllocateIPv6Count)"
```

#### 3.3 预期结果
- 系统检测到IP不足并触发紧急分配
- 紧急分配同时处理IPv4和IPv6需求
- 所有Pod最终获得所需的IP地址
- 日志显示紧急分配的详细过程

#### 3.4 验证命令
```bash
# 检查所有Pod的IP分配状态
kubectl get pods -o wide | grep test-pod-emergency

# 检查紧急分配事件
kubectl get events --sort-by=.metadata.creationTimestamp | grep -i emergency

# 检查最终的IP分配情况
kubectl get bcinode test-node-limited-buffer -o jsonpath='{.spec.eniMultiIP.pool}' | jq '.'
```

### 场景4：IP缓冲区管理测试

#### 4.1 测试目标
验证IP缓冲区自动扩容和GC功能对IPv4和IPv6的支持。

#### 4.2 测试步骤
1. 创建启用GC的节点
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Node
metadata:
  name: test-node-gc-enabled
  labels:
    cluster-id: "test-cluster"
    kubernetes.io/node-resource-type: "bcc.g4.c2m4"
    bci-ipv6-enabled: "true"
    node-enable-eni-private-ip-gc: "true"
  annotations:
    node.beta.kubernetes.io/instance-type: "bcc.g4.c2m4"
    node-eni-private-ip-buffer: "3"
status:
  conditions:
  - type: Ready
    status: "True"
EOF
```

2. 创建Pod观察自动扩容
```bash
for i in {1..3}; do
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-pod-buffer-$i
  namespace: default
  annotations:
    cross-vpc-eni.cce.io/subnetID: "subnet-123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123"
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  nodeSelector:
    kubernetes.io/hostname: test-node-gc-enabled
    bci-ipv6-enabled: "true"
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
EOF
done
```

3. 等待系统稳定后删除Pod观察GC
```bash
# 等待系统稳定
sleep 30

# 删除Pod
kubectl delete pod test-pod-buffer-1 test-pod-buffer-2

# 观察GC过程
kubectl logs -n kube-system -l app=bci-resource-controller -f | grep -E "(syncEniBuffer|needDeleteIPCount|needAddIPCount)"
```

#### 4.3 预期结果
- 系统自动为IPv4和IPv6分配缓冲区IP
- Pod删除后，多余的IP被自动回收
- 缓冲区维持在配置的水平
- 日志显示IPv4和IPv6的扩容和GC过程

#### 4.4 验证命令
```bash
# 检查缓冲区状态
kubectl get bcinode test-node-gc-enabled -o yaml | grep -A 5 -B 5 "currentBufferIPCount"

# 检查IP使用情况
kubectl get bcinode test-node-gc-enabled -o jsonpath='{.spec.eniMultiIP.pool}' | jq '.'

# 检查GC事件
kubectl get events --sort-by=.metadata.creationTimestamp | grep -i "buffer\|gc"
```

### 场景5：批量操作压力测试

#### 5.1 测试目标
验证大规模IPv4和IPv6 IP批量分配和删除的性能。

#### 5.2 测试步骤
1. 创建高配置节点
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Node
metadata:
  name: test-node-high-capacity
  labels:
    cluster-id: "test-cluster"
    kubernetes.io/node-resource-type: "bcc.g4.c8m16"  # 高配置
    bci-ipv6-enabled: "true"
    node-enable-eni-private-ip-gc: "true"
  annotations:
    node.beta.kubernetes.io/instance-type: "bcc.g4.c8m16"
    node-eni-private-ip-buffer: "10"
status:
  conditions:
  - type: Ready
    status: "True"
EOF
```

2. 快速创建大量Pod
```bash
for i in {1..30}; do
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-pod-pressure-$i
  namespace: default
  annotations:
    cross-vpc-eni.cce.io/subnetID: "subnet-123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123"
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  nodeSelector:
    kubernetes.io/hostname: test-node-high-capacity
    bci-ipv6-enabled: "true"
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
EOF
done
```

3. 监控性能指标
```bash
# 监控controller资源使用
kubectl top pod -n kube-system -l app=bci-resource-controller

# 监控批量操作性能
kubectl logs -n kube-system -l app=bci-resource-controller --tail=200 | grep -E "(BatchAddPrivateIP|batch size|count.*result)"
```

#### 5.3 预期结果
- 所有Pod能在合理时间内完成IP分配
- 批量操作按配置的大小正确分割
- Controller资源使用合理
- IPv4和IPv6 IP都能正确分配

#### 5.4 验证命令
```bash
# 检查所有Pod状态
kubectl get pods -o wide | grep test-pod-pressure | wc -l

# 检查IP分配数量
kubectl get bcinode test-node-high-capacity -o jsonpath='{.spec.eniMultiIP.pool}' | jq '. | length'

# 检查性能指标
kubectl get events --sort-by=.metadata.creationTimestamp | grep -E "(time|duration)"
```

### 场景6：限流和重试机制测试

#### 6.1 测试目标
验证在遇到限流时，IPv4和IPv6操作的重试机制正常工作。

#### 6.2 测试步骤
1. 创建测试节点
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Node
metadata:
  name: test-node-ratelimit
  labels:
    cluster-id: "test-cluster"
    kubernetes.io/node-resource-type: "bcc.g4.c2m4"
    bci-ipv6-enabled: "true"
    node-enable-eni-private-ip-gc: "true"
  annotations:
    node.beta.kubernetes.io/instance-type: "bcc.g4.c2m4"
    node-eni-private-ip-buffer: "2"
status:
  conditions:
  - type: Ready
    status: "True"
EOF
```

2. 短时间内创建大量Pod触发限流
```bash
for i in {1..15}; do
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-pod-ratelimit-$i
  namespace: default
  annotations:
    cross-vpc-eni.cce.io/subnetID: "subnet-123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123"
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  nodeSelector:
    kubernetes.io/hostname: test-node-ratelimit
    bci-ipv6-enabled: "true"
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
EOF
done
```

3. 观察限流和重试过程
```bash
# 监控限流和重试日志
kubectl logs -n kube-system -l app=bci-resource-controller -f | grep -E "(ratelimited|try again|retry)"
```

#### 6.3 预期结果
- 系统检测到限流并触发重试机制
- IPv4和IPv6操作都能正确重试
- 最终所有Pod都能获得IP地址
- 重试次数在合理范围内

#### 6.4 验证命令
```bash
# 检查重试事件
kubectl get events --sort-by=.metadata.creationTimestamp | grep -i retry

# 检查最终Pod状态
kubectl get pods -o wide | grep test-pod-ratelimit | grep -v Pending

# 检查限流指标
kubectl get --raw /metrics | grep bci_eni | grep fail
```

### 场景7：混合IP删除测试

#### 7.1 测试目标
验证IPv4和IPv6混合删除功能的正确性。

#### 7.2 测试步骤
1. 创建已有IP的节点
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Node
metadata:
  name: test-node-mixed-delete
  labels:
    cluster-id: "test-cluster"
    kubernetes.io/node-resource-type: "bcc.g4.c2m4"
    bci-ipv6-enabled: "true"
    node-enable-eni-private-ip-gc: "true"
  annotations:
    node.beta.kubernetes.io/instance-type: "bcc.g4.c2m4"
    node-eni-private-ip-buffer: "2"
status:
  conditions:
  - type: Ready
    status: "True"
EOF
```

2. 创建Pod分配IP
```bash
for i in {1..6}; do
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-pod-mixed-$i
  namespace: default
  annotations:
    cross-vpc-eni.cce.io/subnetID: "subnet-123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123"
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  nodeSelector:
    kubernetes.io/hostname: test-node-mixed-delete
    bci-ipv6-enabled: "true"
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
EOF
done
```

3. 等待稳定后删除部分Pod
```bash
# 等待系统稳定
sleep 30

# 删除一些Pod
kubectl delete pod test-pod-mixed-1 test-pod-mixed-2 test-pod-mixed-3

# 观察混合删除过程
kubectl logs -n kube-system -l app=bci-resource-controller -f | grep -E "(BatchDeletePrivateIP|deleteIPList|mixed)"
```

#### 7.3 预期结果
- 系统能正确识别需要删除的IPv4和IPv6地址
- 混合删除操作成功执行
- 删除的IP从ENI上正确移除
- 缓冲区保持在合理水平

#### 7.4 验证命令
```bash
# 检查IP删除情况
kubectl get bcinode test-node-mixed-delete -o jsonpath='{.spec.eniMultiIP.pool}' | jq '.'

# 检查删除事件
kubectl get events --sort-by=.metadata.creationTimestamp | grep -i delete

# 检查剩余Pod状态
kubectl get pods -o wide | grep test-pod-mixed
```

## 测试数据收集

### 1. 性能指标
- IPv4 IP分配时间：平均分配时间
- IPv6 IP分配时间：平均分配时间
- 批量操作性能：每批处理时间
- 限流重试次数：平均重试次数

### 2. 功能指标
- IPv4单栈成功率：应为100%
- IPv6双栈成功率：应为100%
- 混合删除成功率：应为100%
- 紧急分配成功率：应为100%

### 3. 监控命令
```bash
# 收集controller metrics
kubectl get --raw /metrics | grep bci_eni

# 收集IP分配统计
kubectl get bcinode -o yaml | grep -E "(privateIPAddresses|privateIPv6Addresses)" | wc -l

# 收集性能数据
kubectl logs -n kube-system -l app=bci-resource-controller | grep -E "(time|duration|BatchAddPrivateIP.*result)"
```

## 故障排除

### 1. 常见问题
- IPv6地址分配失败：检查VPC IPv6配置
- 批量操作超时：检查网络延迟和配置
- 限流过于频繁：调整QPS配置
- 混合删除失败：检查IP状态和权限

### 2. 调试命令
```bash
# 检查ENI状态
kubectl get bcinode -o yaml | grep -A 20 -B 5 "privateIPv6Addresses"

# 检查controller配置
kubectl get configmap -n kube-system eni-config -o yaml

# 检查限流配置
kubectl logs -n kube-system -l app=bci-resource-controller | grep -E "(ratelimit|qps)"

# 检查权限
kubectl auth can-i update eni --as=system:serviceaccount:kube-system:bci-resource-controller
```

## 清理测试环境

### 1. 删除测试Pod
```bash
kubectl delete pod -l app=test-pod --all-namespaces
```

### 2. 删除测试节点
```bash
kubectl delete node test-node-dual-stack test-node-ipv4-only test-node-limited-buffer test-node-gc-enabled test-node-high-capacity test-node-ratelimit test-node-mixed-delete
```

### 3. 清理ENI资源
```bash
# 通过controller自动清理，或手动清理VPC中的ENI资源
```

## 测试报告模板

### 1. 测试总结
- 测试场景数量：7个
- 通过场景数量：X个
- 失败场景数量：Y个
- 总体测试时间：Z小时

### 2. 性能指标
- 平均IPv4分配时间：X秒
- 平均IPv6分配时间：Y秒
- 批量操作性能：Z个/秒
- 限流重试成功率：W%

### 3. 功能验证
- IPv4单栈功能：✓/✗
- IPv6双栈功能：✓/✗
- 紧急分配功能：✓/✗
- 缓冲区管理功能：✓/✗
- 混合删除功能：✓/✗

### 4. 问题记录
- 问题描述
- 复现步骤
- 解决方案
- 影响范围

### 5. 建议
- 性能优化建议
- 配置调整建议
- 监控改进建议

## 验收标准

1. **功能完整性**：所有IPv4和IPv6批量管理功能正常工作
2. **性能要求**：批量操作时间<5秒/批，重试成功率>95%
3. **稳定性**：连续运行8小时无异常
4. **兼容性**：不影响现有IPv4功能
5. **扩展性**：支持大规模IP分配和删除（100+个IP）
6. **可靠性**：限流和重试机制工作正常
7. **监控性**：完善的日志记录和指标监控 