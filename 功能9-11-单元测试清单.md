# 功能9-11 单元测试清单

根据代码review结果，识别以下核心业务逻辑需要单元测试覆盖：

## 功能9：IPv6分配失败处理 (bci-cni-driver)

### 核心测试场景 (P0)

#### 1. AllocateIPWithRetry 重试机制测试
- **测试目标**: 验证重试机制正确性
- **测试场景**:
  - 第1次失败，第2次成功 - 应该返回成功
  - 连续3次失败 - 应该返回错误
  - 临时错误重试 - 应该等待指数退避时间
  - 不可恢复错误 - 应该直接返回，不重试

#### 2. isIPv6PoolExhausted 错误分类测试
- **测试目标**: 验证IPv6地址池耗尽错误识别
- **测试场景**:
  - 包含"IPv6 used out"的错误 - 应该返回true
  - 包含"IPv6 pool exhausted"的错误 - 应该返回true
  - 包含"no available IPv6"的错误 - 应该返回true
  - 其他错误类型 - 应该返回false
  - nil错误 - 应该返回false

#### 3. isTransientError 临时错误识别测试
- **测试目标**: 验证临时错误类型识别
- **测试场景**:
  - timeout错误 - 应该返回true
  - connection refused错误 - 应该返回true
  - rate limit错误 - 应该返回true
  - 其他错误类型 - 应该返回false

#### 4. requestEmergencyIPv6 紧急申请测试
- **测试目标**: 验证紧急IPv6申请逻辑
- **测试场景**:
  - 管理员Pod申请 - 应该使用ADMINUSERNAME
  - 普通用户Pod申请 - 应该使用podNameSpace
  - 无效ENI ID - 应该返回错误
  - 申请失败 - 应该返回错误

#### 5. enhanceIPv6AllocationWithFallback 双栈分配测试
- **测试目标**: 验证双栈IP分配和回退逻辑
- **测试场景**:
  - IPv4成功，IPv6成功 - 应该返回双栈结果
  - IPv4成功，IPv6失败 - 应该释放IPv4并返回错误
  - IPv4失败 - 应该直接返回错误
  - 不需要IPv6 - 应该只返回IPv4结果

#### 6. validateDualStackRequirement 双栈验证测试
- **测试目标**: 验证双栈要求验证逻辑
- **测试场景**:
  - Pod有IPv6注解，节点支持IPv6 - 应该通过验证
  - Pod有IPv6注解，节点不支持IPv6 - 应该返回错误
  - Pod无IPv6注解 - 应该通过验证
  - 无法获取Pod信息 - 应该返回错误

#### 7. releaseIPs 批量IP释放测试
- **测试目标**: 验证IPv4/IPv6混合释放逻辑
- **测试场景**:
  - 空IP列表 - 应该直接返回
  - 纯IPv4列表 - 应该正确分类
  - 纯IPv6列表 - 应该正确分类
  - 混合IP列表 - 应该正确分类IPv4和IPv6

### 边界条件测试 (P1)

#### 8. 异常输入处理测试
- **测试目标**: 验证异常输入处理
- **测试场景**:
  - 空podName - 应该返回错误
  - 空podNameSpace - 应该返回错误
  - 空podID - 应该返回错误
  - 无效IP地址格式 - 应该正确处理

#### 9. 并发安全测试
- **测试目标**: 验证并发访问安全性
- **测试场景**:
  - 并发调用AllocateIPWithRetry - 应该不产生竞争条件
  - 并发调用requestEmergencyIPv6 - 应该正确处理

## 功能10：ENI创建失败回滚 (bci-resource-controller)

### 核心测试场景 (P0)

#### 1. createEniWithRollback 三步回滚测试
- **测试目标**: 验证ENI创建三步回滚机制
- **测试场景**:
  - ENI创建失败 - 应该直接返回错误，无需回滚
  - ENI创建成功，挂载失败 - 应该删除已创建的ENI
  - ENI创建成功，挂载成功，IP分配失败 - 应该detach并删除ENI
  - 全部成功 - 应该返回成功

#### 2. attachEniToNode ENI挂载测试
- **测试目标**: 验证ENI挂载到节点逻辑
- **测试场景**:
  - 有效ENI ID和节点 - 应该成功挂载
  - 无效ENI ID - 应该返回错误
  - ENI已挂载 - 应该处理重复挂载

#### 3. deleteEni ENI删除测试
- **测试目标**: 验证ENI删除逻辑
- **测试场景**:
  - 有效ENI ID - 应该成功删除
  - 无效ENI ID - 应该返回错误
  - ENI不存在 - 应该正确处理

#### 4. allocatePrivateIPs 私有IP分配测试
- **测试目标**: 验证IPv4/IPv6私有IP分配
- **测试场景**:
  - 只分配IPv4 - 应该调用allocateIPv4PrivateIPs
  - 只分配IPv6 - 应该调用allocateIPv6PrivateIPs
  - 同时分配IPv4和IPv6 - 应该调用两个函数

#### 5. gcEniPrivateIP 垃圾回收测试
- **测试目标**: 验证ENI私有IP垃圾回收逻辑
- **测试场景**:
  - IPv4使用率高 - 应该回收部分未使用的IPv4
  - IPv6使用率高 - 应该回收部分未使用的IPv6
  - 混合使用 - 应该分别计算IPv4和IPv6回收数量
  - 无需回收 - 应该不进行回收操作

### 边界条件测试 (P1)

#### 6. 异常情况处理测试
- **测试目标**: 验证异常情况处理
- **测试场景**:
  - 回滚过程中删除ENI失败 - 应该记录错误但不阻断
  - 网络超时 - 应该正确处理
  - 权限不足 - 应该返回相应错误

#### 7. 资源泄漏防护测试
- **测试目标**: 验证资源泄漏防护
- **测试场景**:
  - 部分操作成功，部分失败 - 应该清理所有已创建资源
  - 异常退出 - 应该不留下孤立资源

## 功能11：CRD更新失败处理 (bci-cni-driver)

### 核心测试场景 (P0)

#### 1. updateBciNodeWithRetry 重试机制测试
- **测试目标**: 验证BciNode更新重试机制
- **测试场景**:
  - 第1次成功 - 应该直接返回成功
  - 第1次冲突，第2次成功 - 应该重试成功
  - 连续5次冲突 - 应该返回错误
  - 非冲突错误 - 应该直接返回，不重试

#### 2. updateBciNodeStatusWithRetry 状态更新重试测试
- **测试目标**: 验证BciNode状态更新重试机制
- **测试场景**:
  - 状态更新成功 - 应该返回成功
  - 状态更新冲突 - 应该重试
  - 状态更新失败 - 应该返回错误

#### 3. 冲突检测和处理测试
- **测试目标**: 验证资源版本冲突检测
- **测试场景**:
  - IsConflict返回true - 应该重试
  - IsConflict返回false - 应该直接返回错误
  - 获取最新资源失败 - 应该返回错误

#### 4. 指数退避策略测试
- **测试目标**: 验证重试间隔计算
- **测试场景**:
  - 第1次重试 - 应该等待100ms
  - 第2次重试 - 应该等待200ms
  - 第3次重试 - 应该等待300ms

#### 5. applyBciNodeUpdate 应用更新测试
- **测试目标**: 验证更新函数应用逻辑
- **测试场景**:
  - 更新函数成功 - 应该应用更新
  - 更新函数失败 - 应该返回错误
  - 找不到BciNode - 应该返回错误

### 边界条件测试 (P1)

#### 6. 并发更新测试
- **测试目标**: 验证并发更新处理
- **测试场景**:
  - 多个进程同时更新 - 应该正确处理冲突
  - 高频更新 - 应该避免过度重试

#### 7. 清理功能测试
- **测试目标**: 验证BciNode状态清理
- **测试场景**:
  - 删除非活跃Pod的IP分配 - 应该正确清理
  - 保留活跃Pod的IP分配 - 应该不被清理
  - 空状态 - 应该不进行更新

### 性能测试 (P2)

#### 8. 大规模数据处理测试
- **测试目标**: 验证大规模数据处理能力
- **测试场景**:
  - 大量IP分配记录 - 应该正确处理
  - 频繁状态更新 - 应该保持性能

## 测试工具函数

### Mock对象需求
- MockEniClient: 模拟ENI API调用
- MockBciNodeClient: 模拟BciNode CRD操作
- MockIPAMClient: 模拟IPAM操作
- MockLogger: 验证日志输出

### 测试数据准备
- 创建测试用的Pod对象
- 创建测试用的Node对象
- 创建测试用的BciNode对象
- 准备各种错误场景的模拟数据

### 测试环境配置
- 设置测试用的Kubernetes集群
- 配置测试用的网络参数
- 准备测试用的ENI和IP资源 