# 功能4.1-ENI改造-端到端测试清单

## 测试概述
本测试清单涵盖了功能4.1 ENI改造的端到端测试场景，验证IPv6双栈ENI创建功能在真实环境中的表现。

## 测试环境准备

### 1. 测试环境要求
- Kubernetes集群（v1.20+）
- 百度云VPC环境支持IPv6
- 部署bci-resource-controller组件
- 配置百度云认证信息

### 2. 测试数据准备
```yaml
# 测试节点配置
apiVersion: v1
kind: Node
metadata:
  name: test-node-ipv6
  labels:
    cluster-id: "test-cluster"
    kubernetes.io/node-resource-type: "bcc.g4.c2m4"
    bci-ipv6-enabled: "true"  # 启用IPv6
  annotations:
    node.beta.kubernetes.io/instance-type: "bcc.g4.c2m4"
status:
  conditions:
  - type: Ready
    status: "True"
```

```yaml
# 测试配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: eni-config
  namespace: kube-system
data:
  config.yaml: |
    defaultENIBufferPrivateIPCount: 2
    defaultENIPrivateIPCount: 10
    enableAllNodePrivateIPGC: true
    dynamicChangePrivateIPBatchsize: 5
```

## 测试场景

### 场景1：IPv4单栈ENI创建（向后兼容测试）

#### 1.1 测试目标
验证在没有IPv6标签的节点上，ENI创建行为与原有逻辑一致。

#### 1.2 测试步骤
1. 创建不包含IPv6标签的测试节点
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Node
metadata:
  name: test-node-ipv4
  labels:
    cluster-id: "test-cluster"
    kubernetes.io/node-resource-type: "bcc.g4.c2m4"
    # 不包含bci-ipv6-enabled标签
  annotations:
    node.beta.kubernetes.io/instance-type: "bcc.g4.c2m4"
status:
  conditions:
  - type: Ready
    status: "True"
EOF
```

2. 创建Pod触发ENI创建
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-pod-ipv4
  namespace: default
  annotations:
    cross-vpc-eni.cce.io/subnetID: "subnet-123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123"
spec:
  nodeSelector:
    kubernetes.io/hostname: test-node-ipv4
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
EOF
```

3. 等待Pod调度和ENI创建完成
```bash
kubectl wait --for=condition=Ready pod/test-pod-ipv4 --timeout=300s
```

#### 1.3 预期结果
- Pod成功创建并Running
- ENI创建成功（仅包含IPv4地址）
- 节点annotation中包含ENI信息
- 不包含IPv6相关配置

#### 1.4 验证命令
```bash
# 检查Pod状态
kubectl get pod test-pod-ipv4 -o wide

# 检查节点ENI信息
kubectl get node test-node-ipv4 -o jsonpath='{.metadata.annotations}' | jq '.'

# 检查BciNode CRD
kubectl get bcinode test-node-ipv4 -o yaml

# 检查controller日志
kubectl logs -n kube-system -l app=bci-resource-controller --tail=100 | grep "test-node-ipv4"
```

### 场景2：IPv6双栈ENI创建

#### 2.1 测试目标
验证在启用IPv6的节点上，ENI创建包含IPv4和IPv6地址。

#### 2.2 测试步骤
1. 创建启用IPv6的测试节点
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Node
metadata:
  name: test-node-ipv6
  labels:
    cluster-id: "test-cluster"
    kubernetes.io/node-resource-type: "bcc.g4.c2m4"
    bci-ipv6-enabled: "true"  # 启用IPv6
  annotations:
    node.beta.kubernetes.io/instance-type: "bcc.g4.c2m4"
status:
  conditions:
  - type: Ready
    status: "True"
EOF
```

2. 创建需要IPv6的Pod
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-pod-ipv6
  namespace: default
  annotations:
    cross-vpc-eni.cce.io/subnetID: "subnet-123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123"
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  nodeSelector:
    kubernetes.io/hostname: test-node-ipv6
    bci-ipv6-enabled: "true"
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
EOF
```

3. 等待Pod调度和ENI创建完成
```bash
kubectl wait --for=condition=Ready pod/test-pod-ipv6 --timeout=300s
```

#### 2.3 预期结果
- Pod成功创建并Running
- ENI创建成功（包含IPv4和IPv6地址）
- 节点annotation中包含ENI信息
- BciNode CRD中包含IPv6地址信息

#### 2.4 验证命令
```bash
# 检查Pod状态
kubectl get pod test-pod-ipv6 -o wide

# 检查节点ENI信息
kubectl get node test-node-ipv6 -o jsonpath='{.metadata.annotations}' | jq '.'

# 检查BciNode CRD IPv6信息
kubectl get bcinode test-node-ipv6 -o yaml | grep -A 20 "privateIPv6Addresses"

# 检查controller日志（应包含IPv6相关信息）
kubectl logs -n kube-system -l app=bci-resource-controller --tail=100 | grep -E "(enableIPv6|initPrivateIPv6Count)"
```

### 场景3：IPv6标签值异常测试

#### 3.1 测试目标
验证IPv6标签值为非true时的处理逻辑。

#### 3.2 测试步骤
1. 创建IPv6标签为false的节点
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Node
metadata:
  name: test-node-ipv6-false
  labels:
    cluster-id: "test-cluster"
    kubernetes.io/node-resource-type: "bcc.g4.c2m4"
    bci-ipv6-enabled: "false"  # 明确设置为false
  annotations:
    node.beta.kubernetes.io/instance-type: "bcc.g4.c2m4"
status:
  conditions:
  - type: Ready
    status: "True"
EOF
```

2. 创建Pod
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-pod-ipv6-false
  namespace: default
  annotations:
    cross-vpc-eni.cce.io/subnetID: "subnet-123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123"
spec:
  nodeSelector:
    kubernetes.io/hostname: test-node-ipv6-false
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
EOF
```

#### 3.3 预期结果
- Pod成功创建并Running
- ENI创建成功（仅包含IPv4地址）
- 不包含IPv6相关配置

### 场景4：多Pod并发ENI创建测试

#### 4.1 测试目标
验证多个Pod同时请求ENI创建时的并发处理能力。

#### 4.2 测试步骤
1. 创建多个Pod同时触发ENI创建
```bash
for i in {1..5}; do
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-pod-concurrent-$i
  namespace: default
  annotations:
    cross-vpc-eni.cce.io/subnetID: "subnet-123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123"
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  nodeSelector:
    kubernetes.io/hostname: test-node-ipv6
    bci-ipv6-enabled: "true"
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
EOF
done
```

2. 等待所有Pod创建完成
```bash
kubectl wait --for=condition=Ready pod -l app=test-concurrent --timeout=300s
```

#### 4.3 预期结果
- 所有Pod成功创建并Running
- ENI创建包含足够的IPv4和IPv6地址
- 没有竞态条件导致的错误

### 场景5：ENI创建失败场景测试

#### 5.1 测试目标
验证ENI创建失败时的错误处理和恢复机制。

#### 5.2 测试步骤
1. 创建使用无效子网ID的Pod
```bash
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-pod-invalid-subnet
  namespace: default
  annotations:
    cross-vpc-eni.cce.io/subnetID: "subnet-invalid"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123"
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  nodeSelector:
    kubernetes.io/hostname: test-node-ipv6
    bci-ipv6-enabled: "true"
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
EOF
```

2. 观察Pod状态和错误处理
```bash
kubectl describe pod test-pod-invalid-subnet
```

#### 5.3 预期结果
- Pod创建失败，显示合适的错误信息
- controller日志包含详细的错误信息
- 不影响其他Pod的ENI创建

### 场景6：性能压力测试

#### 6.1 测试目标
验证大规模ENI创建的性能表现。

#### 6.2 测试步骤
1. 创建大量Pod触发ENI创建
```bash
for i in {1..50}; do
kubectl apply -f - <<EOF
apiVersion: v1
kind: Pod
metadata:
  name: test-pod-performance-$i
  namespace: default
  annotations:
    cross-vpc-eni.cce.io/subnetID: "subnet-123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123"
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  nodeSelector:
    kubernetes.io/hostname: test-node-ipv6
    bci-ipv6-enabled: "true"
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
EOF
done
```

2. 监控性能指标
```bash
# 监控controller资源使用
kubectl top pod -n kube-system -l app=bci-resource-controller

# 监控ENI创建时间
kubectl get events --sort-by=.metadata.creationTimestamp | grep ENI
```

#### 6.3 预期结果
- 所有Pod能够在合理时间内完成创建
- Controller资源使用在合理范围内
- 没有内存泄漏或性能退化

## 测试数据收集

### 1. 性能指标
- ENI创建时间：从Pod创建到ENI Ready的时间
- 资源使用：Controller CPU和内存使用情况
- 并发能力：同时处理的ENI创建请求数量

### 2. 监控命令
```bash
# 收集controller metrics
kubectl get --raw /metrics | grep bci_eni

# 收集事件信息
kubectl get events --all-namespaces --sort-by=.metadata.creationTimestamp

# 收集资源使用信息
kubectl top pod -n kube-system -l app=bci-resource-controller
```

### 3. 日志分析
```bash
# 分析ENI创建日志
kubectl logs -n kube-system -l app=bci-resource-controller | grep -E "(doCreateEni|IPv6|enableIPv6)"

# 分析错误日志
kubectl logs -n kube-system -l app=bci-resource-controller | grep -E "(ERROR|Failed|Error)"
```

## 故障排除

### 1. 常见问题
- Pod调度失败：检查节点标签和NodeSelector
- ENI创建超时：检查网络配置和权限
- IPv6地址分配失败：检查VPC IPv6配置

### 2. 调试命令
```bash
# 检查controller状态
kubectl get deployment -n kube-system bci-resource-controller

# 检查配置
kubectl get configmap -n kube-system eni-config -o yaml

# 检查权限
kubectl auth can-i create eni --as=system:serviceaccount:kube-system:bci-resource-controller
```

## 清理测试环境

### 1. 删除测试Pod
```bash
kubectl delete pod -l app=test-pod --all-namespaces
```

### 2. 删除测试节点
```bash
kubectl delete node test-node-ipv4 test-node-ipv6 test-node-ipv6-false
```

### 3. 清理ENI资源
```bash
# 通过controller自动清理，或手动清理VPC中的ENI资源
```

## 测试报告模板

### 1. 测试总结
- 测试场景数量：X个
- 通过场景数量：Y个
- 失败场景数量：Z个
- 总体测试时间：T小时

### 2. 性能指标
- 平均ENI创建时间：X秒
- 并发处理能力：Y个/秒
- 资源使用峰值：CPU X%，内存Y MB

### 3. 问题记录
- 问题描述
- 复现步骤
- 解决方案
- 影响范围

### 4. 建议
- 优化建议
- 配置调整
- 后续改进方向

## 验收标准

1. **功能完整性**：所有IPv6相关功能正常工作
2. **性能要求**：ENI创建时间<30秒，并发处理能力>10个/秒
3. **稳定性**：连续运行24小时无异常
4. **兼容性**：不影响现有IPv4功能
5. **错误处理**：异常情况下有合适的错误信息和恢复机制 