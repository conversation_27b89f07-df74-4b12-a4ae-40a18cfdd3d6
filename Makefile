# init project path
HOMEDIR := $(shell pwd)
OUTDIR  := $(HOMEDIR)/output

GOPKGS  := $$(go list ./...| grep -vE "vendor")

# 设置编译时所需的GO环境
export GOENV = $(HOMEDIR)/go.env

# 执行编译，可使用命令 make 或 make all 执行， 顺序执行 prepare -> compile -> package 几个阶段
all: prepare compile package

# prepare阶段
prepare:
#    bcloud local -U # 下载非 Go 依赖，依赖之前的 BCLOUD 文件
	git version     # 低于 2.17.1 可能不能正常工作
	go env          # 打印出 go 环境信息，可用于排查问题
	go mod download || go mod download -x # 下载 Go 依赖

# compile 阶段，执行编译命令，可单独执行命令: make compile
compile: build

build: prepare
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o $(HOMEDIR)/bci-resource-controller cmd/main.go

# make test, test your code
test: prepare test-case
test-case:
	go test -race -timeout 30s -gcflags="-N -l" -v -cover $(GOPKGS)

# package阶段，阶段，对编译产出进行打包，输出到 output 目录， 可单独执行命令: make package
package: package-bin
package-bin:
	mkdir -p $(OUTDIR)
	mv bci-resource-controller $(OUTDIR)/
	cp Dockerfile $(OUTDIR)/

# clean 阶段，清除过程中的输出， 可单独执行命令: make clean
clean:
	rm -rf $(OUTDIR)
	rm -rf $(HOMEDIR)/bci-resource-controller

# avoid filename conflict and speed up build 
.PHONY: all prepare compile test package clean build