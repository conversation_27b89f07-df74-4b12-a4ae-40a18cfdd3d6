# 功能3-ENI创建改造-端到端测试清单

## 测试目标
验证ENI创建改造功能在真实Kubernetes环境中的端到端工作流程，确保IPv6双栈ENI创建、IP管理和Pod调度的完整性。

## 前置条件
- 百度云VPC环境支持IPv6
- Kubernetes集群版本 >= 1.18
- bci-resource-controller和bci-cni-driver已部署
- 测试节点已配置IPv6支持

## 测试场景1：IPv4单栈ENI创建和管理

### 测试目标
验证传统IPv4单栈模式下的ENI创建和IP管理功能保持正常。

### 测试步骤
1. **准备测试节点**
   - 创建测试节点，不添加`bci-enable-ipv6`标签
   - 验证节点状态为Ready

2. **部署IPv4单栈Pod**
   ```yaml
   apiVersion: v1
   kind: Pod
   metadata:
     name: ipv4-only-pod
     annotations:
       bci.baidu.com/subnet-id: "sbn-xxxxxxxxxxxx"
       bci.baidu.com/security-group-ids: "g-xxxxxxxxxxxx"
   spec:
     containers:
     - name: test
       image: busybox
       command: ["sleep", "3600"]
   ```

3. **验证ENI创建**
   - 检查ENI是否成功创建
   - 验证ENI只包含IPv4地址
   - 确认ENI成功attach到节点

4. **验证IP分配**
   - 确认Pod获得IPv4地址
   - 验证Pod网络连通性
   - 检查BciNode CRD状态更新

5. **验证IP回收**
   - 删除Pod
   - 确认IPv4地址回到空闲池
   - 验证BciNode CRD状态更新

### 预期结果
- ENI创建成功，只包含IPv4地址
- Pod正常获得IPv4地址并运行
- IP回收机制正常工作

## 测试场景2：IPv6双栈ENI创建和管理

### 测试目标
验证启用IPv6后的双栈ENI创建和管理功能。

### 测试步骤
1. **准备IPv6节点**
   - 为测试节点添加`bci-enable-ipv6=true`标签
   - 验证节点支持IPv6

2. **部署双栈Pod**
   ```yaml
   apiVersion: v1
   kind: Pod
   metadata:
     name: dual-stack-pod
     annotations:
       bci.baidu.com/subnet-id: "sbn-xxxxxxxxxxxx"
       bci.baidu.com/security-group-ids: "g-xxxxxxxxxxxx"
       bci.baidu.com/bci-enable-ipv6: "true"
   spec:
     containers:
     - name: test
       image: busybox
       command: ["sleep", "3600"]
   ```

3. **验证双栈ENI创建**
   - 检查ENI同时创建IPv4和IPv6地址
   - 验证ENI成功attach到节点
   - 确认BciNode CRD包含IPv6信息

4. **验证双栈IP分配**
   - 确认Pod同时获得IPv4和IPv6地址
   - 测试IPv4和IPv6网络连通性
   - 验证Pod状态显示双栈地址

5. **验证双栈IP回收**
   - 删除Pod
   - 确认IPv4和IPv6地址都回到空闲池
   - 验证BciNode CRD状态正确更新

### 预期结果
- ENI创建包含IPv4和IPv6地址
- Pod获得双栈IP配置
- 双栈IP回收正确工作

## 测试场景3：混合工作负载管理

### 测试目标
验证同一节点上IPv4单栈和IPv6双栈Pod的混合部署。

### 测试步骤
1. **准备混合节点**
   - 节点标签：`bci-enable-ipv6=true`
   - 确保节点支持混合工作负载

2. **部署混合Pod**
   - 部署2个IPv4单栈Pod
   - 部署2个IPv6双栈Pod
   - 验证所有Pod调度到同一节点

3. **验证ENI和IP管理**
   - 检查ENI配置包含足够的IPv4和IPv6地址
   - 验证IP分配策略正确
   - 确认每个Pod获得正确的IP配置

4. **验证资源隔离**
   - IPv4单栈Pod只有IPv4地址
   - 双栈Pod有IPv4和IPv6地址
   - 网络连通性测试

5. **验证动态扩缩容**
   - 添加更多Pod触发IP紧急分配
   - 删除Pod验证IP回收
   - 检查资源管理的动态性

### 预期结果
- 混合工作负载正确调度和运行
- IP资源正确分配和隔离
- 动态扩缩容功能正常

## 测试场景4：紧急IP分配机制

### 测试目标
验证在IP资源不足时的紧急分配机制。

### 测试步骤
1. **准备资源紧张环境**
   - 节点启用IPv6：`bci-enable-ipv6=true`
   - 消耗大部分可用IP地址

2. **触发紧急分配**
   - 快速部署多个双栈Pod
   - 验证触发紧急IP分配机制

3. **验证分配行为**
   - 检查IPv4和IPv6分配日志
   - 验证`doBatchAddEniPrivateIPWithIPv6`调用
   - 确认分配数量计算正确

4. **验证分配结果**
   - 所有Pod最终获得IP地址
   - 双栈Pod获得完整的IPv4+IPv6配置
   - BciNode CRD状态正确

### 预期结果
- 紧急分配机制正确触发
- IPv4和IPv6地址同时分配
- 所有Pod最终运行成功

## 测试场景5：故障处理和回滚

### 测试目标
验证IPv6相关故障的处理和回滚机制。

### 测试步骤
1. **模拟IPv6分配失败**
   - 使用网络策略阻断IPv6分配
   - 部署双栈Pod

2. **验证故障处理**
   - 检查IPv6分配失败日志
   - 验证Pod创建失败
   - 确认不影响IPv4功能

3. **验证系统恢复**
   - 恢复IPv6网络连通性
   - 重新部署Pod
   - 验证双栈功能恢复

4. **验证资源清理**
   - 检查失败Pod的资源清理
   - 验证IP地址没有泄露
   - 确认BciNode CRD状态一致

### 预期结果
- 故障正确检测和处理
- 系统能够从故障中恢复
- 资源清理完整

## 测试场景6：大规模并发测试

### 测试目标
验证系统在大规模并发场景下的稳定性。

### 测试步骤
1. **准备大规模环境**
   - 准备多个IPv6节点
   - 配置充足的IP资源

2. **并发Pod创建**
   - 同时创建100个双栈Pod
   - 分布在多个节点上
   - 监控系统资源使用

3. **验证系统稳定性**
   - 检查所有Pod最终运行
   - 验证IP分配无冲突
   - 监控ENI创建和attach性能

4. **验证资源管理**
   - 检查IP资源利用率
   - 验证BciNode CRD更新性能
   - 确认系统无资源泄露

### 预期结果
- 系统在大规模场景下稳定运行
- 所有Pod正确获得IP配置
- 资源管理效率良好

## 性能基准测试

### 测试指标
- ENI创建时间：双栈ENI vs 单栈ENI
- IP分配时间：IPv4 vs IPv6 vs 双栈
- Pod启动时间：对比启用IPv6前后
- 资源利用率：CPU和内存使用

### 性能目标
- 双栈ENI创建时间 < 单栈ENI + 50%
- IPv6 IP分配时间 < IPv4 + 30%
- Pod启动时间增加 < 20%
- 资源使用增加 < 15%

## 兼容性测试

### 测试范围
- Kubernetes版本兼容性（1.18-1.25）
- 不同Pod网络配置的兼容性
- 与现有IPv4工作负载的兼容性
- 升级和回滚场景测试

### 验证要点
- 现有IPv4功能完全不受影响
- 新旧版本组件的兼容性
- 配置变更的向后兼容性

## 监控和告警测试

### 测试内容
- IPv6相关指标收集
- 双栈IP分配失败告警
- ENI创建失败监控
- 资源使用情况监控

### 验证要点
- 监控指标正确采集
- 告警及时准确触发
- 日志记录完整清晰

## 测试工具和脚本

### 自动化测试脚本
```bash
#!/bin/bash
# e2e-ipv6-eni-test.sh

# 测试场景1：IPv4单栈
test_ipv4_only() {
    echo "Testing IPv4-only ENI creation..."
    # 测试逻辑
}

# 测试场景2：IPv6双栈  
test_dual_stack() {
    echo "Testing dual-stack ENI creation..."
    # 测试逻辑
}

# 执行所有测试
run_all_tests() {
    test_ipv4_only
    test_dual_stack
    # 其他测试场景
}
```

### 验证工具
- kubectl命令验证
- 网络连通性测试工具
- IP地址分配检查脚本
- BciNode CRD状态检查工具

## 测试环境要求

### 基础设施
- 百度云VPC环境
- 支持IPv6的子网和安全组
- Kubernetes集群（3个master，5个worker节点）

### 软件版本
- Kubernetes: 1.20+
- bci-resource-controller: 最新版本
- bci-cni-driver: 包含IPv6支持的版本

### 网络配置
- IPv6-enabled VPC
- 双栈子网配置
- 合适的安全组规则

## 测试执行和验收标准

### 执行计划
- 自动化测试：每日执行
- 手工测试：每周执行
- 性能测试：每月执行
- 兼容性测试：版本发布前执行

### 验收标准
- 所有功能测试用例通过率 >= 98%
- 性能指标满足基准要求
- 兼容性测试全部通过
- 监控和告警功能正常

### 缺陷分类
- 阻塞性：影响基本功能的缺陷
- 严重：影响性能或稳定性的缺陷  
- 一般：用户体验相关的缺陷
- 轻微：非关键路径的缺陷 