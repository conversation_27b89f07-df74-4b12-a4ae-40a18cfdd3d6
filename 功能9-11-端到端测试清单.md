# 功能9-11 端到端测试清单

根据代码review结果，识别以下完整用户操作流程需要端到端测试覆盖：

## 功能9：IPv6分配失败处理 E2E测试

### 关键业务场景 (P0)

#### 1. 双栈Pod创建完整链路测试
- **测试目标**: 验证IPv6分配失败时的完整处理流程
- **测试步骤**:
  1. 创建启用IPv6的节点
  2. 创建双栈Pod（带`bci.baidu.com/bci-enable-ipv6: true`注解）
  3. 模拟IPv6地址池耗尽
  4. 观察Pod创建失败和重试行为
  5. 验证紧急IPv6申请被触发
  6. 验证Pod最终创建成功或失败
- **预期结果**: Pod创建失败时应该有明确的错误信息，紧急申请成功后Pod应该能正常创建

#### 2. IPv6地址池耗尽恢复测试
- **测试目标**: 验证IPv6地址池耗尽后的恢复能力
- **测试步骤**:
  1. 创建多个双栈Pod直到IPv6地址池耗尽
  2. 观察新Pod创建失败
  3. 删除部分Pod释放IPv6地址
  4. 验证新Pod能够正常创建
- **预期结果**: 地址池恢复后新Pod应该能正常获取IPv6地址

#### 3. 节点IPv6支持切换测试
- **测试目标**: 验证节点IPv6支持状态变化时的处理
- **测试步骤**:
  1. 创建不支持IPv6的节点
  2. 尝试创建双栈Pod
  3. 验证Pod调度失败
  4. 为节点添加IPv6支持标签
  5. 验证Pod能够正常调度和创建
- **预期结果**: 只有支持IPv6的节点才能运行双栈Pod

#### 4. 临时错误重试测试
- **测试目标**: 验证临时错误的自动重试机制
- **测试步骤**:
  1. 创建双栈Pod
  2. 模拟网络超时错误
  3. 观察自动重试行为
  4. 验证重试间隔符合指数退避
  5. 验证最终成功创建
- **预期结果**: 临时错误应该触发重试，最终成功创建Pod

### 异常场景测试 (P1)

#### 5. 紧急IPv6申请失败场景
- **测试目标**: 验证紧急IPv6申请失败时的处理
- **测试步骤**:
  1. 创建双栈Pod
  2. 模拟IPv6地址池耗尽
  3. 模拟紧急申请API失败
  4. 验证Pod创建最终失败
  5. 验证错误信息准确性
- **预期结果**: 紧急申请失败时Pod应该创建失败，并有明确错误信息

#### 6. 混合工作负载测试
- **测试目标**: 验证单栈和双栈Pod混合场景
- **测试步骤**:
  1. 创建支持IPv6的节点
  2. 创建单栈Pod（无IPv6注解）
  3. 创建双栈Pod（有IPv6注解）
  4. 验证两种Pod都能正常运行
  5. 验证IP分配不冲突
- **预期结果**: 单栈和双栈Pod应该能够共存

## 功能10：ENI创建失败回滚 E2E测试

### 关键业务场景 (P0)

#### 1. ENI创建完整链路测试
- **测试目标**: 验证ENI创建、挂载、IP分配的完整流程
- **测试步骤**:
  1. 创建需要ENI的Pod
  2. 观察ENI创建过程
  3. 验证ENI成功挂载到节点
  4. 验证IPv4和IPv6 IP分配成功
  5. 验证Pod能正常使用网络
- **预期结果**: ENI创建成功，Pod网络正常

#### 2. ENI挂载失败回滚测试
- **测试目标**: 验证ENI挂载失败时的回滚机制
- **测试步骤**:
  1. 创建Pod触发ENI创建
  2. 模拟ENI挂载到节点失败
  3. 验证系统自动删除已创建的ENI
  4. 验证Pod创建失败
  5. 验证资源清理完整性
- **预期结果**: ENI挂载失败时应该自动删除ENI，不产生资源泄漏

#### 3. IP分配失败回滚测试
- **测试目标**: 验证IP分配失败时的回滚机制
- **测试步骤**:
  1. 创建Pod触发ENI创建
  2. ENI创建和挂载成功
  3. 模拟IP分配失败
  4. 验证系统自动detach和删除ENI
  5. 验证Pod创建失败
- **预期结果**: IP分配失败时应该清理ENI资源

#### 4. 多ENI并发创建测试
- **测试目标**: 验证多个ENI并发创建的稳定性
- **测试步骤**:
  1. 同时创建多个需要ENI的Pod
  2. 观察ENI创建的并发处理
  3. 验证每个Pod都能获得独立的ENI
  4. 验证没有资源冲突
- **预期结果**: 所有ENI应该能并发创建成功

### 异常场景测试 (P1)

#### 5. 节点资源不足测试
- **测试目标**: 验证节点资源不足时的处理
- **测试步骤**:
  1. 创建资源接近上限的节点
  2. 尝试创建超出限制的Pod
  3. 验证ENI创建失败
  4. 验证错误信息准确性
- **预期结果**: 应该优雅处理资源不足，不创建多余ENI

#### 6. 网络分区恢复测试
- **测试目标**: 验证网络分区恢复后的处理
- **测试步骤**:
  1. 创建Pod过程中发生网络分区
  2. 部分ENI操作失败
  3. 网络恢复后验证清理机制
  4. 验证重新创建能成功
- **预期结果**: 网络恢复后应该正确清理残留资源

## 功能11：CRD更新失败处理 E2E测试

### 关键业务场景 (P0)

#### 1. BciNode CRD更新完整链路测试
- **测试目标**: 验证BciNode CRD更新的完整流程
- **测试步骤**:
  1. 创建Pod触发IP分配
  2. 观察BciNode CRD状态更新
  3. 验证IP分配信息正确记录
  4. 删除Pod验证状态清理
  5. 验证CRD状态一致性
- **预期结果**: CRD状态应该准确反映IP分配情况

#### 2. CRD更新冲突处理测试
- **测试目标**: 验证多个进程同时更新CRD时的冲突处理
- **测试步骤**:
  1. 同时创建多个Pod
  2. 观察BciNode CRD的并发更新
  3. 验证冲突检测和重试机制
  4. 验证最终状态一致性
- **预期结果**: 所有更新应该最终成功，状态一致

#### 3. 节点重启后CRD状态恢复测试
- **测试目标**: 验证节点重启后CRD状态的恢复
- **测试步骤**:
  1. 创建Pod并分配IP
  2. 模拟节点重启
  3. 观察CRD状态恢复过程
  4. 验证IP分配信息准确性
  5. 验证清理机制正常工作
- **预期结果**: 节点重启后CRD状态应该正确恢复

#### 4. 大规模IP分配状态管理测试
- **测试目标**: 验证大规模IP分配的CRD管理能力
- **测试步骤**:
  1. 创建大量Pod进行IP分配
  2. 观察CRD状态更新性能
  3. 验证状态清理效率
  4. 验证系统稳定性
- **预期结果**: 大规模场景下CRD更新应该稳定

### 异常场景测试 (P1)

#### 5. API Server不可用测试
- **测试目标**: 验证API Server不可用时的处理
- **测试步骤**:
  1. 创建Pod过程中API Server变为不可用
  2. 观察CRD更新失败的处理
  3. API Server恢复后验证重试机制
  4. 验证数据一致性
- **预期结果**: API Server恢复后应该正确重试更新

#### 6. 频繁状态变更测试
- **测试目标**: 验证频繁状态变更时的处理
- **测试步骤**:
  1. 快速创建和删除Pod
  2. 观察CRD状态的频繁变更
  3. 验证更新队列管理
  4. 验证资源使用合理性
- **预期结果**: 频繁变更时系统应该保持稳定

## 综合集成测试

### 端到端完整流程测试 (P0)

#### 1. 双栈Pod完整生命周期测试
- **测试目标**: 验证双栈Pod从创建到删除的完整生命周期
- **测试步骤**:
  1. 创建支持IPv6的节点
  2. 创建双栈Pod
  3. 验证IPv6分配失败处理
  4. 验证ENI创建回滚
  5. 验证CRD状态更新
  6. 验证Pod网络连通性
  7. 删除Pod验证资源清理
- **预期结果**: 完整生命周期应该正常工作

#### 2. 多节点混合场景测试
- **测试目标**: 验证多节点环境下的混合场景
- **测试步骤**:
  1. 创建支持IPv6和不支持IPv6的节点
  2. 创建单栈和双栈Pod
  3. 验证调度策略正确性
  4. 验证跨节点网络连通性
  5. 验证资源隔离性
- **预期结果**: 不同类型节点和Pod应该能正确协作

#### 3. 故障恢复完整测试
- **测试目标**: 验证各种故障情况下的恢复能力
- **测试步骤**:
  1. 模拟IPv6地址池耗尽
  2. 模拟ENI创建失败
  3. 模拟CRD更新冲突
  4. 验证系统自动恢复
  5. 验证服务连续性
- **预期结果**: 系统应该能从各种故障中恢复

## 测试环境和工具

### 测试环境要求
- Kubernetes集群（支持IPv6）
- 百度云ENI服务
- 测试用的VPC和子网
- 支持IPv6的节点

### 测试工具
- Pod创建和管理脚本
- 网络连通性测试工具
- 资源状态监控工具
- 日志分析工具

### 测试数据
- 各种类型的Pod定义
- 节点配置模板
- 网络配置参数
- 故障模拟脚本

### 监控指标
- Pod创建成功率
- IP分配成功率
- ENI创建成功率
- CRD更新成功率
- 故障恢复时间
- 资源利用率 