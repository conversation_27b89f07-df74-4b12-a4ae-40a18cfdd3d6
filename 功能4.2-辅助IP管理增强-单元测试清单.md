# 功能4.2-辅助IP管理增强-单元测试清单

## 测试概述
本测试清单涵盖了功能4.2 辅助IP管理增强的所有新增功能，包括IPv6双栈IP的批量添加和删除管理。

## 测试架构

### 核心测试对象
1. `doBatchAddEniPrivateIPWithIPv6` - 双栈IP批量添加函数
2. `doBatchDeletePrivateIP` - 混合IP批量删除函数（已增强）
3. `dealEmergencyAllocateIPRequest` - 紧急分配请求处理（已增强）
4. `syncEniBuffer` - ENI缓冲区同步（已增强）

## 1. doBatchAddEniPrivateIPWithIPv6函数测试

### 1.1 测试IPv4单独分配
```go
func TestDoBatchAddEniPrivateIPWithIPv6_IPv4Only(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
    }
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                // 不包含IPv6标签
            },
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟ENI客户端
    mockEniClient := &mockEniClient{
        batchAddResults: []batchAddResult{
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"********", "********"}}, err: nil},
        },
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试
    err := worker.doBatchAddEniPrivateIPWithIPv6(2, 0)
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, 1, len(mockEniClient.batchAddCalls))
    
    // 验证调用参数
    call := mockEniClient.batchAddCalls[0]
    assert.Equal(t, "eni-12345", call.EniID)
    assert.Equal(t, false, call.IsIpv6) // IPv4调用
    assert.Equal(t, 2, len(call.PrivateIPAddresses))
}
```

### 1.2 测试IPv6单独分配
```go
func TestDoBatchAddEniPrivateIPWithIPv6_IPv6Only(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
    }
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.BCIEnableIPv6Key: "true",
            },
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟ENI客户端
    mockEniClient := &mockEniClient{
        batchAddResults: []batchAddResult{
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"2001:db8::1", "2001:db8::2"}}, err: nil},
        },
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试
    err := worker.doBatchAddEniPrivateIPWithIPv6(0, 2)
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, 1, len(mockEniClient.batchAddCalls))
    
    // 验证调用参数
    call := mockEniClient.batchAddCalls[0]
    assert.Equal(t, "eni-12345", call.EniID)
    assert.Equal(t, true, call.IsIpv6) // IPv6调用
    assert.Equal(t, 2, len(call.PrivateIPAddresses))
}
```

### 1.3 测试IPv4和IPv6双栈分配
```go
func TestDoBatchAddEniPrivateIPWithIPv6_DualStack(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
    }
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.BCIEnableIPv6Key: "true",
            },
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟ENI客户端
    mockEniClient := &mockEniClient{
        batchAddResults: []batchAddResult{
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"********", "********"}}, err: nil}, // IPv4
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"2001:db8::1", "2001:db8::2"}}, err: nil}, // IPv6
        },
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试
    err := worker.doBatchAddEniPrivateIPWithIPv6(2, 2)
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, 2, len(mockEniClient.batchAddCalls))
    
    // 验证IPv4调用
    ipv4Call := mockEniClient.batchAddCalls[0]
    assert.Equal(t, "eni-12345", ipv4Call.EniID)
    assert.Equal(t, false, ipv4Call.IsIpv6)
    assert.Equal(t, 2, len(ipv4Call.PrivateIPAddresses))
    
    // 验证IPv6调用
    ipv6Call := mockEniClient.batchAddCalls[1]
    assert.Equal(t, "eni-12345", ipv6Call.EniID)
    assert.Equal(t, true, ipv6Call.IsIpv6)
    assert.Equal(t, 2, len(ipv6Call.PrivateIPAddresses))
}
```

### 1.4 测试限流重试机制
```go
func TestDoBatchAddEniPrivateIPWithIPv6_RateLimitRetry(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
    }
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.BCIEnableIPv6Key: "true",
            },
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟ENI客户端 - 第一次限流，第二次成功
    mockEniClient := &mockEniClient{
        batchAddResults: []batchAddResult{
            {result: nil, err: &rateLimitError{}}, // 第一次调用限流
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"********"}}, err: nil}, // 重试成功
        },
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试
    err := worker.doBatchAddEniPrivateIPWithIPv6(1, 0)
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, 2, len(mockEniClient.batchAddCalls)) // 调用了两次
}
```

### 1.5 测试批量分割功能
```go
func TestDoBatchAddEniPrivateIPWithIPv6_BatchSplit(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
    }
    
    // 设置小的批量大小
    worker.Controller.networkOption.DynamicChangePrivateIPBatchsize = 2
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.BCIEnableIPv6Key: "true",
            },
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟ENI客户端
    mockEniClient := &mockEniClient{
        batchAddResults: []batchAddResult{
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"********", "********"}}, err: nil},
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"********"}}, err: nil},
        },
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试 - 3个IPv4 IP，应该分两批
    err := worker.doBatchAddEniPrivateIPWithIPv6(3, 0)
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, 2, len(mockEniClient.batchAddCalls)) // 分两批调用
    
    // 验证第一批
    assert.Equal(t, 2, len(mockEniClient.batchAddCalls[0].PrivateIPAddresses))
    // 验证第二批
    assert.Equal(t, 1, len(mockEniClient.batchAddCalls[1].PrivateIPAddresses))
}
```

## 2. doBatchDeletePrivateIP函数测试

### 2.1 测试IPv4和IPv6混合删除
```go
func TestDoBatchDeletePrivateIP_MixedDeletion(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
    }
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟ENI客户端
    mockEniClient := &mockEniClient{
        batchDeleteResults: []error{nil}, // 删除成功
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试 - 删除混合IPv4和IPv6地址
    deleteIPList := []string{"********", "********", "2001:db8::1", "2001:db8::2"}
    err := worker.doBatchDeletePrivateIP(deleteIPList)
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, 1, len(mockEniClient.batchDeleteCalls))
    
    // 验证调用参数
    call := mockEniClient.batchDeleteCalls[0]
    assert.Equal(t, "eni-12345", call.EniID)
    assert.Equal(t, deleteIPList, call.PrivateIPAddresses)
}
```

### 2.2 测试空列表处理
```go
func TestDoBatchDeletePrivateIP_EmptyList(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
    }
    
    // 执行测试 - 空列表
    err := worker.doBatchDeletePrivateIP([]string{})
    
    // 验证结果
    assert.NoError(t, err)
    // 不应该有任何API调用
}
```

### 2.3 测试删除失败处理
```go
func TestDoBatchDeletePrivateIP_DeleteFailure(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
    }
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟ENI客户端 - 删除失败
    mockEniClient := &mockEniClient{
        batchDeleteResults: []error{fmt.Errorf("delete failed")},
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试
    deleteIPList := []string{"********", "2001:db8::1"}
    err := worker.doBatchDeletePrivateIP(deleteIPList)
    
    // 验证结果
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "failed to delete private IPs")
}
```

## 3. dealEmergencyAllocateIPRequest函数测试

### 3.1 测试IPv4节点的紧急分配
```go
func TestDealEmergencyAllocateIPRequest_IPv4Node(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
        PrivateIPMaxCount: 10,
        LastDealEmergencyAllocateIPTime: time.Now().Add(-5 * time.Second),
    }
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                // 不包含IPv6标签
            },
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟等待的Pod
    pods := []*corev1.Pod{
        createTestPod("pod-1", "default", map[string]string{
            "cross-vpc-eni.cce.io/subnetID": "subnet-123",
            "cross-vpc-eni.cce.io/securityGroupIDs": "sg-123",
        }),
        createTestPod("pod-2", "default", map[string]string{
            "cross-vpc-eni.cce.io/subnetID": "subnet-123",
            "cross-vpc-eni.cce.io/securityGroupIDs": "sg-123",
        }),
    }
    
    // 模拟ENI客户端
    mockEniClient := &mockEniClient{
        statENIResults: []statENIResult{
            {
                response: &eni.StatENIResponse{
                    PrivateIPSet: []*eni.PrivateIP{
                        {Primary: true, PrivateIPAddress: "********00"},
                        {Primary: false, PrivateIPAddress: "********"},
                    },
                },
                err: nil,
            },
        },
        batchAddResults: []batchAddResult{
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"********"}}, err: nil},
        },
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试
    err := worker.dealEmergencyAllocateIPRequest()
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, 1, len(mockEniClient.batchAddCalls))
    
    // 验证调用参数（应该只分配IPv4）
    call := mockEniClient.batchAddCalls[0]
    assert.Equal(t, false, call.IsIpv6)
    assert.Equal(t, 1, len(call.PrivateIPAddresses))
}
```

### 3.2 测试IPv6节点的紧急分配
```go
func TestDealEmergencyAllocateIPRequest_IPv6Node(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
        PrivateIPMaxCount: 10,
        LastDealEmergencyAllocateIPTime: time.Now().Add(-5 * time.Second),
    }
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.BCIEnableIPv6Key: "true", // 启用IPv6
            },
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟等待的Pod
    pods := []*corev1.Pod{
        createTestPod("pod-1", "default", map[string]string{
            "cross-vpc-eni.cce.io/subnetID": "subnet-123",
            "cross-vpc-eni.cce.io/securityGroupIDs": "sg-123",
        }),
        createTestPod("pod-2", "default", map[string]string{
            "cross-vpc-eni.cce.io/subnetID": "subnet-123",
            "cross-vpc-eni.cce.io/securityGroupIDs": "sg-123",
        }),
    }
    
    // 模拟ENI客户端
    mockEniClient := &mockEniClient{
        statENIResults: []statENIResult{
            {
                response: &eni.StatENIResponse{
                    PrivateIPSet: []*eni.PrivateIP{
                        {Primary: true, PrivateIPAddress: "********00"},
                        {Primary: false, PrivateIPAddress: "********"},
                    },
                },
                err: nil,
            },
        },
        batchAddResults: []batchAddResult{
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"********"}}, err: nil}, // IPv4
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"2001:db8::1"}}, err: nil}, // IPv6
        },
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试
    err := worker.dealEmergencyAllocateIPRequest()
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, 2, len(mockEniClient.batchAddCalls))
    
    // 验证IPv4调用
    ipv4Call := mockEniClient.batchAddCalls[0]
    assert.Equal(t, false, ipv4Call.IsIpv6)
    
    // 验证IPv6调用
    ipv6Call := mockEniClient.batchAddCalls[1]
    assert.Equal(t, true, ipv6Call.IsIpv6)
}
```

## 4. syncEniBuffer函数测试

### 4.1 测试IPv4节点的缓冲区同步
```go
func TestSyncEniBuffer_IPv4Node(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
        PrivateIPMaxCount: 10,
        PrivateIPBufferCount: 3,
    }
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                // 不包含IPv6标签
            },
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟ENI客户端
    mockEniClient := &mockEniClient{
        statENIResults: []statENIResult{
            {
                response: &eni.StatENIResponse{
                    PrivateIPSet: []*eni.PrivateIP{
                        {Primary: true, PrivateIPAddress: "********00"},
                        {Primary: false, PrivateIPAddress: "********"},
                    },
                },
                err: nil,
            },
        },
        batchAddResults: []batchAddResult{
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"********", "********"}}, err: nil},
        },
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试
    err := worker.syncEniBuffer()
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, 1, len(mockEniClient.batchAddCalls))
    
    // 验证调用参数（应该只分配IPv4）
    call := mockEniClient.batchAddCalls[0]
    assert.Equal(t, false, call.IsIpv6)
    assert.Equal(t, 2, len(call.PrivateIPAddresses))
}
```

### 4.2 测试IPv6节点的缓冲区同步
```go
func TestSyncEniBuffer_IPv6Node(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
        PrivateIPMaxCount: 10,
        PrivateIPBufferCount: 4,
    }
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.BCIEnableIPv6Key: "true", // 启用IPv6
            },
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟ENI客户端
    mockEniClient := &mockEniClient{
        statENIResults: []statENIResult{
            {
                response: &eni.StatENIResponse{
                    PrivateIPSet: []*eni.PrivateIP{
                        {Primary: true, PrivateIPAddress: "********00"},
                        {Primary: false, PrivateIPAddress: "********"},
                    },
                },
                err: nil,
            },
        },
        batchAddResults: []batchAddResult{
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"********", "********"}}, err: nil}, // IPv4
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"2001:db8::1", "2001:db8::2"}}, err: nil}, // IPv6
        },
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试
    err := worker.syncEniBuffer()
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, 2, len(mockEniClient.batchAddCalls))
    
    // 验证IPv4调用
    ipv4Call := mockEniClient.batchAddCalls[0]
    assert.Equal(t, false, ipv4Call.IsIpv6)
    assert.Equal(t, 2, len(ipv4Call.PrivateIPAddresses))
    
    // 验证IPv6调用
    ipv6Call := mockEniClient.batchAddCalls[1]
    assert.Equal(t, true, ipv6Call.IsIpv6)
    assert.Equal(t, 2, len(ipv6Call.PrivateIPAddresses))
}
```

## 5. 错误处理测试

### 5.1 测试IPv4分配失败但IPv6成功
```go
func TestDoBatchAddEniPrivateIPWithIPv6_IPv4FailIPv6Success(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
    }
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.BCIEnableIPv6Key: "true",
            },
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟ENI客户端
    mockEniClient := &mockEniClient{
        batchAddResults: []batchAddResult{
            {result: nil, err: fmt.Errorf("IPv4 allocation failed")}, // IPv4失败
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"2001:db8::1"}}, err: nil}, // IPv6成功
        },
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试
    err := worker.doBatchAddEniPrivateIPWithIPv6(1, 1)
    
    // 验证结果
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "failed to add IPv4 private IPs")
    assert.Equal(t, 1, len(mockEniClient.batchAddCalls)) // 只调用了IPv4，IPv6没有执行
}
```

### 5.2 测试IPv6分配失败但IPv4成功
```go
func TestDoBatchAddEniPrivateIPWithIPv6_IPv4SuccessIPv6Fail(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
    }
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.BCIEnableIPv6Key: "true",
            },
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟ENI客户端
    mockEniClient := &mockEniClient{
        batchAddResults: []batchAddResult{
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"********"}}, err: nil}, // IPv4成功
            {result: nil, err: fmt.Errorf("IPv6 allocation failed")}, // IPv6失败
        },
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试
    err := worker.doBatchAddEniPrivateIPWithIPv6(1, 1)
    
    // 验证结果
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "failed to add IPv6 private IPs")
    assert.Equal(t, 2, len(mockEniClient.batchAddCalls)) // IPv4和IPv6都调用了
}
```

## 6. 性能测试

### 6.1 测试大量IP分配性能
```go
func BenchmarkDoBatchAddEniPrivateIPWithIPv6_LargeScale(b *testing.B) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
    }
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.BCIEnableIPv6Key: "true",
            },
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟ENI客户端
    mockEniClient := &mockEniClient{
        batchAddResults: []batchAddResult{
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: make([]string, 50)}, err: nil}, // IPv4
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: make([]string, 50)}, err: nil}, // IPv6
        },
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行基准测试
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        worker.doBatchAddEniPrivateIPWithIPv6(50, 50)
    }
}
```

## 7. 日志测试

### 7.1 测试日志输出
```go
func TestDoBatchAddEniPrivateIPWithIPv6_LoggingIPv6(t *testing.T) {
    // 准备测试数据
    worker := &eniPrivateIPWorker{
        NodeName:  "test-node",
        EniID:     "eni-12345",
        AccountID: "test-account",
        Controller: createMockController(),
    }
    
    // 捕获日志输出
    var logOutput bytes.Buffer
    klog.SetOutput(&logOutput)
    
    // 模拟node信息
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.BCIEnableIPv6Key: "true",
            },
            Annotations: map[string]string{
                "bci-node-eni-info-eni-12345": `{"eniID":"eni-12345","accountID":"test-account","subnetID":"subnet-123","securityGroupIDs":["sg-123"]}`,
            },
        },
    }
    
    // 模拟ENI客户端
    mockEniClient := &mockEniClient{
        batchAddResults: []batchAddResult{
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"********"}}, err: nil}, // IPv4
            {result: &eni.BatchAddPrivateIPResult{PrivateIPAddresses: []string{"2001:db8::1"}}, err: nil}, // IPv6
        },
    }
    worker.Controller.eniClient = mockEniClient
    
    // 执行测试
    err := worker.doBatchAddEniPrivateIPWithIPv6(1, 1)
    
    // 验证结果
    assert.NoError(t, err)
    
    // 验证日志包含IPv4和IPv6信息
    logStr := logOutput.String()
    assert.Contains(t, logStr, "BatchAddPrivateIP IPv4")
    assert.Contains(t, logStr, "BatchAddPrivateIP IPv6")
}
```

## 8. Mock对象定义

```go
// mockEniClient 用于单元测试的ENI客户端mock
type mockEniClient struct {
    batchAddResults    []batchAddResult
    batchDeleteResults []error
    statENIResults     []statENIResult
    
    batchAddCalls    []batchAddCall
    batchDeleteCalls []batchDeleteCall
    statENICalls     []statENICall
}

type batchAddResult struct {
    result *eni.BatchAddPrivateIPResult
    err    error
}

type batchAddCall struct {
    EniID               string
    PrivateIPAddresses  []string
    IsIpv6              bool
}

type batchDeleteCall struct {
    EniID               string
    PrivateIPAddresses  []string
}

type statENIResult struct {
    response *eni.StatENIResponse
    err      error
}

type statENICall struct {
    EniID string
}

func (m *mockEniClient) BatchAddPrivateIP(ctx context.Context, args *eni.BatchPrivateIPArgs, signOpt *bce.SignOption) (*eni.BatchAddPrivateIPResult, error) {
    call := batchAddCall{
        EniID:              args.EniID,
        PrivateIPAddresses: args.PrivateIPAddresses,
        IsIpv6:             args.IsIpv6,
    }
    m.batchAddCalls = append(m.batchAddCalls, call)
    
    if len(m.batchAddResults) > 0 {
        result := m.batchAddResults[0]
        m.batchAddResults = m.batchAddResults[1:]
        return result.result, result.err
    }
    return nil, nil
}

func (m *mockEniClient) BatchDeletePrivateIP(ctx context.Context, args *eni.BatchDeletePrivateIPArgs, signOpt *bce.SignOption) error {
    call := batchDeleteCall{
        EniID:              args.EniID,
        PrivateIPAddresses: args.PrivateIPAddresses,
    }
    m.batchDeleteCalls = append(m.batchDeleteCalls, call)
    
    if len(m.batchDeleteResults) > 0 {
        result := m.batchDeleteResults[0]
        m.batchDeleteResults = m.batchDeleteResults[1:]
        return result
    }
    return nil
}

func (m *mockEniClient) StatENI(ctx context.Context, eniID string, signOpt *bce.SignOption) (*eni.StatENIResponse, error) {
    call := statENICall{EniID: eniID}
    m.statENICalls = append(m.statENICalls, call)
    
    if len(m.statENIResults) > 0 {
        result := m.statENIResults[0]
        m.statENIResults = m.statENIResults[1:]
        return result.response, result.err
    }
    return nil, nil
}

// 其他辅助函数
func createMockController() *Controller {
    return &Controller{
        networkOption: &options.NetworkOptions{
            DynamicChangePrivateIPBatchsize: 10,
            EniChangeRatelimitLoopRetryCount: 3,
        },
    }
}

func createTestPod(name, namespace string, annotations map[string]string) *corev1.Pod {
    return &corev1.Pod{
        ObjectMeta: metav1.ObjectMeta{
            Name:        name,
            Namespace:   namespace,
            Annotations: annotations,
        },
    }
}

type rateLimitError struct{}

func (e *rateLimitError) Error() string {
    return "rate limited"
}

func isENIRatelimited(err error) bool {
    _, ok := err.(*rateLimitError)
    return ok
}
```

## 测试运行说明

1. 运行全部测试：
```bash
go test -v ./pkg/controller/eni/... -run TestDoBatchAddEniPrivateIPWithIPv6
```

2. 运行性能测试：
```bash
go test -bench=. ./pkg/controller/eni/... -run=^$
```

3. 运行覆盖率测试：
```bash
go test -coverprofile=coverage.out ./pkg/controller/eni/...
go tool cover -html=coverage.out
```

## 预期测试结果

所有测试应该通过，并且：
- 代码覆盖率应该达到85%以上
- 双栈IP分配和删除功能正常工作
- 错误处理机制完善
- 性能在合理范围内（每次批量操作<100ms）
- 日志输出包含必要的调试信息

## 注意事项

1. 测试需要mock外部依赖（ENI客户端、Kubernetes API等）
2. 测试数据应该覆盖IPv4单栈、IPv6单栈、IPv4+IPv6双栈场景
3. 确保限流重试机制正常工作
4. 验证批量分割逻辑的正确性
5. 测试错误传播和日志记录 