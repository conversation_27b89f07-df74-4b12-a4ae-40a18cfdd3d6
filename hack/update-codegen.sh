# Copyright 2022 Baidu, Inc.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#!/bin/bash

set -o errexit
set -o nounset
set -o pipefail

CNI_PKG="icode.baidu.com/baidu/bci2/bci-cni-driver"

# TODO(chenyaqi): make codegen containerized

# Generate clientset and apis code with K8s codegen tool
client-gen \
  --clientset-name versioned \
  --input-base "${CNI_PKG}/apis" \
  --input "networking/v1" \
  --output-package "${CNI_PKG}/pkg/generated/clientset" \
  --go-header-file hack/boilerplate.go.txt \

# Generate listers with K8s codegen tools.
lister-gen \
  --input-dirs "${CNI_PKG}/apis/networking/v1" \
  --output-package "${CNI_PKG}/pkg/generated/listers" \
  --go-header-file hack/boilerplate.go.txt \

# Generate informers with K8s codegen tools.
informer-gen \
  --input-dirs "${CNI_PKG}/apis/networking/v1" \
  --versioned-clientset-package "${CNI_PKG}/pkg/generated/clientset/versioned" \
  --listers-package "${CNI_PKG}/pkg/generated/listers" \
  --output-package "${CNI_PKG}/pkg/generated/informers" \
  --go-header-file hack/boilerplate.go.txt \
