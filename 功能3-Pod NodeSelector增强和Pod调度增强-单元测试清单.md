# 功能3 - Pod NodeSelector增强和Pod调度增强 - 单元测试清单

## 测试范围
本测试清单针对IPv6双栈网络支持中的Pod NodeSelector增强和Pod调度增强功能进行单元测试验证。

## 测试环境准备
- 准备测试用的K8s集群环境
- 部署bci-resource-controller组件
- 准备测试用的Pod对象和Node对象

## 1. 常量定义测试

### 1.1 测试用例：验证IPv6相关常量定义
**测试目的**: 确保IPv6相关常量定义正确
**测试文件**: `pkg/entity/instance_group_test.go`
**测试方法**: 
```go
func TestIPv6Constants(t *testing.T) {
    assert.Equal(t, "bci-ipv6-enabled", entity.BCIEnableIPv6Key)
    assert.Equal(t, "bci.baidu.com/bci-enable-ipv6", entity.BciEnableIPv6AnnotationKey)
}
```
**预期结果**: 所有IPv6相关常量值正确

## 2. Pod NodeSelector增强测试

### 2.1 测试用例：checkIPv6Annotation函数测试
**测试目的**: 验证IPv6 annotation检查功能
**测试文件**: `pkg/webhook/pod/mutating/inject_node_selector_test.go`

#### 2.1.1 Pod包含IPv6 annotation且值为true
**测试方法**:
```go
func TestCheckIPv6Annotation_True(t *testing.T) {
    pod := &corev1.Pod{
        ObjectMeta: metav1.ObjectMeta{
            Annotations: map[string]string{
                entity.BciEnableIPv6AnnotationKey: "true",
            },
        },
    }
    result := checkIPv6Annotation(pod)
    assert.True(t, result)
}
```
**预期结果**: 返回true

#### 2.1.2 Pod包含IPv6 annotation但值为false
**测试方法**:
```go
func TestCheckIPv6Annotation_False(t *testing.T) {
    pod := &corev1.Pod{
        ObjectMeta: metav1.ObjectMeta{
            Annotations: map[string]string{
                entity.BciEnableIPv6AnnotationKey: "false",
            },
        },
    }
    result := checkIPv6Annotation(pod)
    assert.False(t, result)
}
```
**预期结果**: 返回false

#### 2.1.3 Pod不包含IPv6 annotation
**测试方法**:
```go
func TestCheckIPv6Annotation_Missing(t *testing.T) {
    pod := &corev1.Pod{
        ObjectMeta: metav1.ObjectMeta{
            Annotations: map[string]string{},
        },
    }
    result := checkIPv6Annotation(pod)
    assert.False(t, result)
}
```
**预期结果**: 返回false

#### 2.1.4 Pod annotation为nil
**测试方法**:
```go
func TestCheckIPv6Annotation_NilAnnotations(t *testing.T) {
    pod := &corev1.Pod{
        ObjectMeta: metav1.ObjectMeta{
            Annotations: nil,
        },
    }
    result := checkIPv6Annotation(pod)
    assert.False(t, result)
}
```
**预期结果**: 返回false

### 2.2 测试用例：injectNodeSelector函数IPv6增强测试
**测试目的**: 验证IPv6 NodeSelector注入功能
**测试文件**: `pkg/webhook/pod/mutating/inject_node_selector_test.go`

#### 2.2.1 Pod需要IPv6支持时注入NodeSelector
**测试方法**:
```go
func TestInjectNodeSelector_IPv6Enabled(t *testing.T) {
    pod := BuildPodWithContainers("default", "test-pod", 
        map[string]string{
            entity.BciAccountKey: "test-account",
            entity.BciEnableIPv6AnnotationKey: "true",
            bciZoneKey: "zoneA",
        }, 
        []corev1.Container{
            {
                Name: "test-container",
                Resources: corev1.ResourceRequirements{
                    Requests: corev1.ResourceList{
                        corev1.ResourceCPU:    resource.MustParse("1"),
                        corev1.ResourceMemory: resource.MustParse("1Gi"),
                    },
                },
            },
        })
    
    // 执行注入
    change, failedInfo := injectNodeSelector(pod, mockGetInstanceGroupConfig, mockGetAccountWhiteList, mockIsImageCachePodUseUserNode)
    
    // 验证结果
    assert.True(t, change)
    assert.Nil(t, failedInfo)
    assert.Equal(t, "true", pod.Spec.NodeSelector[entity.BCIEnableIPv6Key])
    assert.Equal(t, "test-account", pod.Spec.NodeSelector[entity.TenantLockKey])
}
```
**预期结果**: 
- Pod.Spec.NodeSelector[entity.BCIEnableIPv6Key] = "true"
- Pod.Spec.NodeSelector[entity.TenantLockKey] = "test-account"

#### 2.2.2 Pod不需要IPv6支持时不注入IPv6 NodeSelector
**测试方法**:
```go
func TestInjectNodeSelector_IPv6Disabled(t *testing.T) {
    pod := BuildPodWithContainers("default", "test-pod", 
        map[string]string{
            entity.BciAccountKey: "test-account",
            bciZoneKey: "zoneA",
        }, 
        []corev1.Container{
            {
                Name: "test-container",
                Resources: corev1.ResourceRequirements{
                    Requests: corev1.ResourceList{
                        corev1.ResourceCPU:    resource.MustParse("1"),
                        corev1.ResourceMemory: resource.MustParse("1Gi"),
                    },
                },
            },
        })
    
    // 执行注入
    change, failedInfo := injectNodeSelector(pod, mockGetInstanceGroupConfig, mockGetAccountWhiteList, mockIsImageCachePodUseUserNode)
    
    // 验证结果
    assert.True(t, change)
    assert.Nil(t, failedInfo)
    assert.Equal(t, "", pod.Spec.NodeSelector[entity.BCIEnableIPv6Key])
    assert.Equal(t, "test-account", pod.Spec.NodeSelector[entity.TenantLockKey])
}
```
**预期结果**: 
- Pod.Spec.NodeSelector不包含IPv6键值对
- Pod.Spec.NodeSelector[entity.TenantLockKey] = "test-account"

## 3. Pod调度增强测试

### 3.1 测试用例：doLockNode函数IPv6标签增强测试
**测试目的**: 验证IPv6节点标签添加功能
**测试文件**: `pkg/controller/node/tenant_lock_controller_test.go`

#### 3.1.1 Pod需要IPv6支持时为节点添加IPv6标签
**测试方法**:
```go
func TestDoLockNode_IPv6Enabled(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.InstanceGroupKey: "test-ig",
            },
        },
    }
    
    pod := &corev1.Pod{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-pod",
            Namespace: "default",
        },
        Spec: corev1.PodSpec{
            NodeSelector: map[string]string{
                entity.TenantLockKey: "test-tenant",
                entity.BCIEnableIPv6Key: "true",
            },
        },
    }
    
    // 创建mock client
    mockClient := fake.NewClientBuilder().WithObjects(node).Build()
    controller := &Controller{
        client: mockClient,
    }
    
    // 执行锁定节点
    err := controller.doLockNode("test-tenant", node, pod)
    
    // 验证结果
    assert.NoError(t, err)
    
    // 获取更新后的节点
    updatedNode := &corev1.Node{}
    err = mockClient.Get(context.TODO(), client.ObjectKey{Name: "test-node"}, updatedNode)
    assert.NoError(t, err)
    assert.Equal(t, "test-tenant", updatedNode.Labels[entity.TenantLockKey])
    assert.Equal(t, "true", updatedNode.Labels[entity.BCIEnableIPv6Key])
}
```
**预期结果**: 
- 节点labels包含tenant-lock = "test-tenant"
- 节点labels包含bci-ipv6-enabled = "true"

#### 3.1.2 Pod不需要IPv6支持时不添加IPv6标签
**测试方法**:
```go
func TestDoLockNode_IPv6Disabled(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.InstanceGroupKey: "test-ig",
            },
        },
    }
    
    pod := &corev1.Pod{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-pod",
            Namespace: "default",
        },
        Spec: corev1.PodSpec{
            NodeSelector: map[string]string{
                entity.TenantLockKey: "test-tenant",
            },
        },
    }
    
    // 创建mock client
    mockClient := fake.NewClientBuilder().WithObjects(node).Build()
    controller := &Controller{
        client: mockClient,
    }
    
    // 执行锁定节点
    err := controller.doLockNode("test-tenant", node, pod)
    
    // 验证结果
    assert.NoError(t, err)
    
    // 获取更新后的节点
    updatedNode := &corev1.Node{}
    err = mockClient.Get(context.TODO(), client.ObjectKey{Name: "test-node"}, updatedNode)
    assert.NoError(t, err)
    assert.Equal(t, "test-tenant", updatedNode.Labels[entity.TenantLockKey])
    assert.Equal(t, "", updatedNode.Labels[entity.BCIEnableIPv6Key])
}
```
**预期结果**: 
- 节点labels包含tenant-lock = "test-tenant"
- 节点labels不包含bci-ipv6-enabled键值对

#### 3.1.3 Pod NodeSelector为nil时不添加IPv6标签
**测试方法**:
```go
func TestDoLockNode_NilNodeSelector(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.InstanceGroupKey: "test-ig",
            },
        },
    }
    
    pod := &corev1.Pod{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-pod",
            Namespace: "default",
        },
        Spec: corev1.PodSpec{
            NodeSelector: nil,
        },
    }
    
    // 创建mock client
    mockClient := fake.NewClientBuilder().WithObjects(node).Build()
    controller := &Controller{
        client: mockClient,
    }
    
    // 执行锁定节点
    err := controller.doLockNode("test-tenant", node, pod)
    
    // 验证结果
    assert.NoError(t, err)
    
    // 获取更新后的节点
    updatedNode := &corev1.Node{}
    err = mockClient.Get(context.TODO(), client.ObjectKey{Name: "test-node"}, updatedNode)
    assert.NoError(t, err)
    assert.Equal(t, "test-tenant", updatedNode.Labels[entity.TenantLockKey])
    assert.Equal(t, "", updatedNode.Labels[entity.BCIEnableIPv6Key])
}
```
**预期结果**: 
- 节点labels包含tenant-lock = "test-tenant"
- 节点labels不包含bci-ipv6-enabled键值对

## 4. 集成测试

### 4.1 测试用例：完整流程测试
**测试目的**: 验证从Pod创建到节点锁定的完整IPv6流程
**测试文件**: `pkg/controller/node/tenant_lock_controller_integration_test.go`

#### 4.1.1 双栈Pod完整调度流程
**测试方法**:
```go
func TestIPv6FullFlow(t *testing.T) {
    // 1. 创建带IPv6 annotation的Pod
    pod := &corev1.Pod{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-pod",
            Namespace: "default",
            Annotations: map[string]string{
                entity.BciAccountKey: "test-account",
                entity.BciEnableIPv6AnnotationKey: "true",
                bciZoneKey: "zoneA",
            },
        },
        Spec: corev1.PodSpec{
            Containers: []corev1.Container{
                {
                    Name: "test-container",
                    Resources: corev1.ResourceRequirements{
                        Requests: corev1.ResourceList{
                            corev1.ResourceCPU:    resource.MustParse("1"),
                            corev1.ResourceMemory: resource.MustParse("1Gi"),
                        },
                    },
                },
            },
        },
    }
    
    // 2. 执行NodeSelector注入
    change, failedInfo := injectNodeSelector(pod, mockGetInstanceGroupConfig, mockGetAccountWhiteList, mockIsImageCachePodUseUserNode)
    assert.True(t, change)
    assert.Nil(t, failedInfo)
    
    // 3. 验证NodeSelector包含IPv6要求
    assert.Equal(t, "true", pod.Spec.NodeSelector[entity.BCIEnableIPv6Key])
    assert.Equal(t, "test-account", pod.Spec.NodeSelector[entity.TenantLockKey])
    
    // 4. 创建候选节点
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                entity.InstanceGroupKey: "test-ig",
            },
        },
    }
    
    // 5. 执行节点锁定
    mockClient := fake.NewClientBuilder().WithObjects(node).Build()
    controller := &Controller{
        client: mockClient,
    }
    
    err := controller.doLockNode("test-account", node, pod)
    assert.NoError(t, err)
    
    // 6. 验证节点获得IPv6标签
    updatedNode := &corev1.Node{}
    err = mockClient.Get(context.TODO(), client.ObjectKey{Name: "test-node"}, updatedNode)
    assert.NoError(t, err)
    assert.Equal(t, "test-account", updatedNode.Labels[entity.TenantLockKey])
    assert.Equal(t, "true", updatedNode.Labels[entity.BCIEnableIPv6Key])
}
```
**预期结果**: 
- Pod获得正确的NodeSelector
- 节点获得正确的标签
- 整个流程无错误

## 5. 边界条件测试

### 5.1 测试用例：异常情况处理
**测试目的**: 验证各种异常情况的处理

#### 5.1.1 节点Patch失败处理
**测试方法**: 使用mock client模拟patch失败情况
**预期结果**: 函数返回相应错误，重试机制正常工作

#### 5.1.2 大量并发Pod处理
**测试方法**: 并发创建多个需要IPv6支持的Pod
**预期结果**: 各Pod的处理不互相影响

## 6. 性能测试

### 6.1 测试用例：性能基准测试
**测试目的**: 验证IPv6增强不显著影响性能

#### 6.1.1 NodeSelector注入性能测试
**测试方法**: 使用go benchmark测试injectNodeSelector函数性能
**预期结果**: 性能损耗在可接受范围内

#### 6.1.2 节点锁定性能测试
**测试方法**: 使用go benchmark测试doLockNode函数性能
**预期结果**: 性能损耗在可接受范围内

## 7. 验收标准

### 7.1 功能验收标准
1. 所有单元测试用例通过率100%
2. 代码覆盖率达到90%以上
3. 无内存泄漏或goroutine泄漏
4. 日志输出格式正确，便于问题定位

### 7.2 性能验收标准
1. IPv6增强后的处理时间不超过原来的110%
2. 内存使用增长不超过5%
3. 并发处理能力不下降

### 7.3 兼容性验收标准
1. 不影响现有IPv4 Pod的调度
2. 向后兼容，不破坏现有功能
3. 优雅处理旧版本Pod对象

## 8. 测试执行指南

### 8.1 测试环境搭建
```bash
# 1. 设置测试环境变量
export TEST_ENV=unit

# 2. 运行单元测试
cd bci-resource-controller
go test -v ./pkg/webhook/pod/mutating/...
go test -v ./pkg/controller/node/...
go test -v ./pkg/entity/...

# 3. 运行覆盖率测试
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

### 8.2 测试数据准备
- 准备各种类型的Pod配置文件
- 准备不同状态的Node对象
- 准备mock数据和测试用例

### 8.3 测试结果验证
- 检查所有测试用例通过
- 验证代码覆盖率达标
- 检查性能基准测试结果

## 9. 问题记录和解决

### 9.1 常见问题
1. **问题**: 测试环境中Pod NodeSelector未正确注入
   **解决**: 检查mock函数配置，确保测试数据正确

2. **问题**: 节点标签更新失败
   **解决**: 检查RBAC权限配置，确保测试环境权限正确

### 9.2 调试技巧
1. 使用详细日志模式运行测试
2. 检查kubernetes事件日志
3. 使用单步调试工具

## 10. 测试报告模板

### 10.1 测试结果统计
- 测试用例总数: XX
- 通过用例数: XX
- 失败用例数: XX
- 代码覆盖率: XX%

### 10.2 性能测试结果
- 平均响应时间: XX ms
- 内存使用: XX MB
- CPU使用率: XX%

### 10.3 问题总结
- 发现问题数: XX
- 已解决问题数: XX
- 待解决问题数: XX 