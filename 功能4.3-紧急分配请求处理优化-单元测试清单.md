# 功能4.3-紧急分配请求处理优化-单元测试清单

## 1. 测试覆盖目标
- 验证紧急分配请求处理中的双栈IP分配优化
- 测试IPv4/IPv6资源充足性检查机制
- 验证IPv6容量限制检查功能
- 测试边界情况处理（节点不支持IPv6但Pod请求IPv6）
- 验证错误处理一致性

## 2. 测试文件定位
- **主要测试文件**: `pkg/controller/eni/eni_private_ip_controller_test.go`
- **相关测试文件**: `pkg/controller/eni/util_test.go`

## 3. 测试用例详细清单

### 3.1 dealEmergencyAllocateIPRequest函数测试

#### 3.1.1 资源充足性检查测试
```go
func TestDealEmergencyAllocateIPRequest_ResourceSufficiency(t *testing.T) {
    // 测试场景1：IPv4和IPv6资源都充足
    // 测试场景2：IPv4充足但IPv6不足
    // 测试场景3：IPv4不足但IPv6充足
    // 测试场景4：IPv4和IPv6都不足
    // 测试场景5：仅IPv4 Pod时的资源检查
}
```

#### 3.1.2 边界情况处理测试
```go
func TestDealEmergencyAllocateIPRequest_BoundaryConditions(t *testing.T) {
    // 测试场景1：节点不支持IPv6但Pod请求IPv6
    // 测试场景2：节点支持IPv6但Pod不请求IPv6
    // 测试场景3：空等待Pod列表
    // 测试场景4：容量限制达到上限
}
```

#### 3.1.3 容量限制检查测试
```go
func TestDealEmergencyAllocateIPRequest_CapacityLimits(t *testing.T) {
    // 测试场景1：IPv4容量已满
    // 测试场景2：IPv6容量已满
    // 测试场景3：IPv4和IPv6容量都已满
    // 测试场景4：容量检查计算准确性
}
```

#### 3.1.4 分配数量计算测试
```go
func TestDealEmergencyAllocateIPRequest_AllocationCalculation(t *testing.T) {
    // 测试场景1：需要分配数量计算准确性
    // 测试场景2：最大可分配数量限制
    // 测试场景3：负数分配数量处理
    // 测试场景4：零分配数量处理
}
```

#### 3.1.5 错误处理测试
```go
func TestDealEmergencyAllocateIPRequest_ErrorHandling(t *testing.T) {
    // 测试场景1：doBatchAddEniPrivateIPWithIPv6调用失败
    // 测试场景2：限流错误处理
    // 测试场景3：认证失败错误处理
    // 测试场景4：Pod annotation patch失败处理
}
```

### 3.2 资源检查逻辑测试

#### 3.2.1 IPv4资源检查测试
```go
func TestIPv4ResourceSufficiency(t *testing.T) {
    // 测试场景1：IPv4资源充足
    // 测试场景2：IPv4资源不足
    // 测试场景3：IPv4资源刚好满足
    // 测试场景4：IPv4资源计算边界情况
}
```

#### 3.2.2 IPv6资源检查测试
```go
func TestIPv6ResourceSufficiency(t *testing.T) {
    // 测试场景1：IPv6资源充足
    // 测试场景2：IPv6资源不足
    // 测试场景3：IPv6资源刚好满足
    // 测试场景4：没有IPv6需求时的处理
}
```

### 3.3 节点IPv6支持检查测试

#### 3.3.1 节点标签检查测试
```go
func TestNodeIPv6Support(t *testing.T) {
    // 测试场景1：节点支持IPv6（标签为true）
    // 测试场景2：节点不支持IPv6（标签为false）
    // 测试场景3：节点缺少IPv6标签
    // 测试场景4：节点标签值异常
}
```

#### 3.3.2 Pod IPv6请求检查测试
```go
func TestPodIPv6Request(t *testing.T) {
    // 测试场景1：Pod请求IPv6（annotation为true）
    // 测试场景2：Pod不请求IPv6（annotation为false）
    // 测试场景3：Pod缺少IPv6 annotation
    // 测试场景4：Pod annotation值异常
}
```

### 3.4 分配优化测试

#### 3.4.1 精确分配测试
```go
func TestPreciseAllocation(t *testing.T) {
    // 测试场景1：仅分配实际需要的IPv4地址
    // 测试场景2：仅分配实际需要的IPv6地址
    // 测试场景3：混合IPv4/IPv6精确分配
    // 测试场景4：避免过度分配
}
```

#### 3.4.2 容量限制遵守测试
```go
func TestCapacityLimitCompliance(t *testing.T) {
    // 测试场景1：IPv4容量限制遵守
    // 测试场景2：IPv6容量限制遵守
    // 测试场景3：双栈容量限制遵守
    // 测试场景4：动态容量调整
}
```

### 3.5 错误处理一致性测试

#### 3.5.1 错误类型区分测试
```go
func TestErrorTypeDistinction(t *testing.T) {
    // 测试场景1：IPv4分配失败错误处理
    // 测试场景2：IPv6分配失败错误处理
    // 测试场景3：双栈分配失败错误处理
    // 测试场景4：容量限制错误处理
}
```

#### 3.5.2 错误传播测试
```go
func TestErrorPropagation(t *testing.T) {
    // 测试场景1：下游错误正确传播
    // 测试场景2：错误信息完整性
    // 测试场景3：错误日志记录
    // 测试场景4：错误监控指标
}
```

## 4. Mock和Helper函数

### 4.1 Mock函数清单
```go
// Mock getNodeEniInfo函数
func mockGetNodeEniInfo(supportIPv6 bool) (*entity.EniInfo, *corev1.Node, error)

// Mock computeENIAllocatedAndFreePrivateIPList函数
func mockComputeENIAllocatedAndFreePrivateIPList(allocated, free []string) error

// Mock computeENIAllocatedAndFreePrivateIPv6List函数
func mockComputeENIAllocatedAndFreePrivateIPv6List(allocated, free []string) error

// Mock doBatchAddEniPrivateIPWithIPv6函数
func mockDoBatchAddEniPrivateIPWithIPv6(ipv4Count, ipv6Count int) error

// Mock patchPodAnnotationWhenAllocateIPFailed函数
func mockPatchPodAnnotationWhenAllocateIPFailed(namespace, name string, err error) error
```

### 4.2 Helper函数清单
```go
// 创建测试用的Pod列表
func createTestPodList(ipv4Count, ipv6Count int) []*corev1.Pod

// 创建测试用的Node
func createTestNode(supportsIPv6 bool) *corev1.Node

// 创建测试用的ENI信息
func createTestEniInfo() *entity.EniInfo

// 创建测试用的eniPrivateIPWorker
func createTestEniPrivateIPWorker(maxIPv4, maxIPv6 int) *eniPrivateIPWorker
```

## 5. 测试数据准备

### 5.1 测试Node配置
```go
// 支持IPv6的Node
nodeWithIPv6 := &corev1.Node{
    ObjectMeta: metav1.ObjectMeta{
        Name: "test-node-ipv6",
        Labels: map[string]string{
            entity.BCIEnableIPv6Key: "true",
        },
    },
}

// 不支持IPv6的Node
nodeWithoutIPv6 := &corev1.Node{
    ObjectMeta: metav1.ObjectMeta{
        Name: "test-node-ipv4",
        Labels: map[string]string{
            entity.BCIEnableIPv6Key: "false",
        },
    },
}
```

### 5.2 测试Pod配置
```go
// 请求IPv6的Pod
podWithIPv6 := &corev1.Pod{
    ObjectMeta: metav1.ObjectMeta{
        Name: "test-pod-ipv6",
        Namespace: "default",
        Annotations: map[string]string{
            entity.BciEnableIPv6AnnotationKey: "true",
        },
    },
}

// 仅使用IPv4的Pod
podIPv4Only := &corev1.Pod{
    ObjectMeta: metav1.ObjectMeta{
        Name: "test-pod-ipv4",
        Namespace: "default",
        Annotations: map[string]string{
            entity.BciEnableIPv6AnnotationKey: "false",
        },
    },
}
```

## 6. 测试执行策略

### 6.1 单元测试运行
```bash
# 运行所有相关测试
go test -v ./pkg/controller/eni/... -run TestDealEmergencyAllocateIPRequest

# 运行特定测试组
go test -v ./pkg/controller/eni/... -run TestDealEmergencyAllocateIPRequest_ResourceSufficiency

# 运行测试并生成覆盖率报告
go test -v -cover ./pkg/controller/eni/...
```

### 6.2 性能测试
```bash
# 运行性能基准测试
go test -bench=BenchmarkDealEmergencyAllocateIPRequest -benchmem ./pkg/controller/eni/...
```

## 7. 测试验证要点

### 7.1 功能验证
- ✅ 双栈IP分配优化正确性
- ✅ 资源充足性检查准确性
- ✅ 容量限制检查有效性
- ✅ 边界情况处理完整性
- ✅ 错误处理一致性

### 7.2 性能验证
- ✅ 资源检查性能优化
- ✅ 分配计算效率
- ✅ 错误处理开销
- ✅ 内存使用优化

### 7.3 稳定性验证
- ✅ 高并发场景稳定性
- ✅ 异常情况恢复能力
- ✅ 资源泄漏防护
- ✅ 长时间运行稳定性

## 8. 测试覆盖率目标
- **行覆盖率**: ≥95%
- **分支覆盖率**: ≥90%
- **函数覆盖率**: 100%
- **关键路径覆盖率**: 100%

## 9. 回归测试
- 确保原有IPv4功能不受影响
- 验证向后兼容性
- 测试配置变更适应性
- 验证监控和日志功能

## 10. 测试环境要求
- Go 1.19+
- Kubernetes测试框架
- Mock测试框架
- 覆盖率分析工具
- 性能分析工具 