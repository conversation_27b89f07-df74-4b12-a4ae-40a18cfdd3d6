package main

import (
	"flag"
	"math/rand"
	"os"
	"time"

	// Import all Kubernetes client auth plugins (e.g. Azure, GCP, OIDC, etc.)
	// to ensure that exec-entrypoint and run can make use of them.
	"github.com/prometheus/client_golang/prometheus"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	_ "k8s.io/client-go/plugin/pkg/client/auth"
	"k8s.io/klog/v2"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/webhook"

	bci_node_scheme "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/generated/clientset/versioned/scheme"
	ops_task_v1 "icode.baidu.com/baidu/bci2/bci-ops-agent/api/bciops/v1"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/healthz"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
)

var (
	scheme   = runtime.NewScheme()
	setupLog = ctrl.Log.WithName("setup")
)

var (
	isLeader = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Subsystem: "bci",
			Name:      "is_leader",
			Help:      "Is leader controller.",
		},
		[]string{},
	)
)

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))

	utilruntime.Must(bci_node_scheme.AddToScheme(scheme))

	utilruntime.Must(ops_task_v1.AddToScheme(scheme))

	metrics.Registry.MustRegister(isLeader)
}

func main() {
	rand.Seed(time.Now().UnixNano())
	serverRunOptions := options.NewServerRunOptions()

	opts := zap.Options{
		Development: true,
	}

	serverRunOptions.AddFlags(flag.CommandLine)
	opts.BindFlags(flag.CommandLine)
	klog.InitFlags(nil)
	flag.Parse()

	ctx := ctrl.SetupSignalHandler()

	ctrl.SetLogger(zap.New(zap.UseFlagOptions(&opts)))

	leaderElectionID := "bci-controller"

	if serverRunOptions.WebHookServer {
		leaderElectionID = "bci-webhook"
	}

	mgr, err := ctrl.NewManager(ctrl.GetConfigOrDie(), ctrl.Options{
		Scheme:                  scheme,
		MetricsBindAddress:      serverRunOptions.MetricsAddr,
		Port:                    serverRunOptions.Port,
		CertDir:                 serverRunOptions.CertDir,
		HealthProbeBindAddress:  serverRunOptions.ProbeAddr,
		LeaderElection:          serverRunOptions.EnableLeaderElection,
		LeaderElectionNamespace: serverRunOptions.LeaderElectionNamespace,
		LeaderElectionID:        leaderElectionID,
	})
	if err != nil {
		setupLog.Error(err, "unable to start manager")
		os.Exit(1)
	}

	klog.Infof("option.CoolStateHoldTime  %+v ", serverRunOptions.NodeOptions.CoolStateHoldTime)
	klog.Infof("option.PodExitStateHoldTime  %+v ", serverRunOptions.NodeOptions.PodExitStateHoldTime)

	if serverRunOptions.WebHookServer {
		// 注册webhook handler
		setupLog.Info("setup webhook")
		if err := webhook.SetupWithManager(serverRunOptions, mgr); err != nil {
			setupLog.Error(err, "unable to setup webhook")
			os.Exit(1)
		}
	}

	if !serverRunOptions.WebHookServer {
		setupLog.Info("setup controller")
		if err := controller.SetupWithManager(serverRunOptions, mgr); err != nil {
			setupLog.Error(err, "unable to init controllers")
			os.Exit(1)
		}

		go func() {
			isLeader.WithLabelValues().Set(0)
			if serverRunOptions.EnableLeaderElection {
				// 当选主成功后，调用自定义的Start函数
				<-mgr.Elected()
				// leader 打点
				isLeader.WithLabelValues().Set(1)
			}
			setupLog.Info("setup controller")
			controller.Start(ctx)
		}()
	}

	if err := mgr.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up health check")
		os.Exit(1)
	}
	if err := mgr.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up ready check")
		os.Exit(1)
	}

	setupLog.Info("starting manager")
	if err := mgr.Start(ctx); err != nil {
		// 当丢失leader后，捕获err，进程退出
		setupLog.Error(err, "problem running manager")
		os.Exit(1)
	}
}
