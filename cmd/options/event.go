package options

import "flag"

type EventOptions struct {
	Enabled                  bool
	Region                   string
	IamEndpoint              string
	StsEndpoint              string
	BcmPushEndpoint          string
	BciServiceName           string
	BciServiceRoleName       string
	BciServicePasswd         string
	BciEndpoint              string
	NsOfLastEventTimestamp   string
	NameOfLastEventTimestamp string
	Workers                  int
	Pushers                  int
}

func NewEventOptions() *EventOptions {
	return &EventOptions{
		Enabled:                  true,
		Region:                   "bj",
		IamEndpoint:              "http://iam.bj.internal-qasandbox.baidu-int.com/v3",
		StsEndpoint:              "sts.bj.internal-qasandbox.baidu-int.com:8586/v1",
		BcmPushEndpoint:          "http://10.169.25.203:8869",
		BciServiceName:           "bci",
		BciServiceRoleName:       "BceServiceRole_bci",
		BciServicePasswd:         "Bol9kmMkhqr0xHmMjxrvwcQueHL7nHSk",
		BciEndpoint:              "10.221.50.25:8784",
		NsOfLastEventTimestamp:   "kube-system",
		NameOfLastEventTimestamp: "last-event-timestamp",
		Workers:                  1,
		Pushers:                  1,
	}
}

func (e *EventOptions) AddFlags(fs *flag.FlagSet) {
	// global info
	fs.BoolVar(&e.Enabled, "event-push-enable", e.Enabled, "enable event push controller, default value is true")
	fs.StringVar(&e.Region, "event-push-region", e.Region, "bcm event push region")
	fs.StringVar(&e.IamEndpoint, "event-sync-bcm-iamendpoint", e.IamEndpoint, "iam endpoint used to get AKSK for requesting bcm")
	fs.StringVar(&e.StsEndpoint, "describe-pod-stsendpoint", e.StsEndpoint, "sts endpoint to access bci api logic")
	fs.StringVar(&e.BcmPushEndpoint, "event-sync-bcm-endpoint", e.BcmPushEndpoint, "endpoint to push event to bcm")

	fs.StringVar(&e.BciServiceRoleName, "event-bci-iam-rolename", e.BciServiceRoleName, "bci service roleName, default value is BceServiceRole_bci")
	fs.StringVar(&e.BciServiceName, "event-bci-iam-username", e.BciServiceName, "eip controller, default value is bci")
	fs.StringVar(&e.BciServicePasswd, "event-sync-bcm-bciservicepasswd", e.BciServicePasswd,
		"bci service passwd used to authenticate when requesting bcm")
	fs.StringVar(&e.BciEndpoint, "event-bci-endpoint", e.BciEndpoint, "bci endpoint")
	fs.StringVar(&e.NsOfLastEventTimestamp, "event-push-last-time-configmap-ns", e.NsOfLastEventTimestamp,
		"ns of configmap storing last event time, default value is kube-system")
	fs.StringVar(&e.NameOfLastEventTimestamp, "event-push-last-time-configmap-name", e.NameOfLastEventTimestamp,
		"name of configmap storing last event time, default value is last-event-timestamp")
	fs.IntVar(&e.Workers, "event-worker-count", e.Workers, "event worker count, default value is 1")
	fs.IntVar(&e.Pushers, "event-pusher-count", e.Pushers, "event pusher count, default value is 1")
}
