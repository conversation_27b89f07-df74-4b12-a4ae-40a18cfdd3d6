package options

import (
	"flag"
	"time"
)

type InstanceGroupOptions struct {
	InstanceGroupControllerWorkers      int           // worker 并发处理数
	DefaultBufferCount                  int           // 节点组默认buffer 数量
	DefaultSyncInstanceGroupInterval    time.Duration // 定时更新节点组cm配置间隔
	PlaceholderImage                    string
	TimedCaImage                        string // 定时扩容 ca image地址
	HiClientTokenURL                    string // 节点组自动扩缩容 hi client url
	TimedInstanceGroupTaskMaxCostMinute int    // 节点组自动扩容task运行时间超过此值发送hi报警
}

func NewInstanceGroupOptions() *InstanceGroupOptions {
	return &InstanceGroupOptions{
		InstanceGroupControllerWorkers:      3,
		DefaultBufferCount:                  10,
		DefaultSyncInstanceGroupInterval:    30 * time.Second,
		PlaceholderImage:                    "registry.baidubce.com/cce-public/pause:3.1",
		TimedCaImage:                        "registry.baidubce.com/cce-plugin-dev/cluster-autoscaler:miao-latest-ca",
		HiClientTokenURL:                    "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d9fcfe7922f8d56685a36c93a21039a9b",
		TimedInstanceGroupTaskMaxCostMinute: 10,
	}
}

func (o *InstanceGroupOptions) AddFlags(fs *flag.FlagSet) {
	fs.IntVar(&o.InstanceGroupControllerWorkers, "instancegroup-controller-workers", o.InstanceGroupControllerWorkers, "The instancegroup controller workers count")
	fs.IntVar(&o.DefaultBufferCount, "default-buffer-count", o.DefaultBufferCount, "The InstanceGroup default buffer count ")
	fs.DurationVar(&o.DefaultSyncInstanceGroupInterval, "default-sync-instancegroup-interval", o.DefaultSyncInstanceGroupInterval, "default sync instancegroup interval ")
	fs.StringVar(&o.PlaceholderImage, "placeholder-image", o.PlaceholderImage, "The placeholder image url")
	fs.StringVar(&o.TimedCaImage, "timed-ca-image", o.TimedCaImage, "The timed ca image")
	fs.StringVar(&o.HiClientTokenURL, "hi-client-token-url", o.HiClientTokenURL, "hi client token url")
	fs.IntVar(&o.TimedInstanceGroupTaskMaxCostMinute, "timed-ig-task-max-cost-minute",
		o.TimedInstanceGroupTaskMaxCostMinute, "timed ig task max cost minute,default 10")
}
