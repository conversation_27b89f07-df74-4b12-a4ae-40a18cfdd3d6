package options

import (
	"flag"
	"time"
)

type NetworkOptions struct {
	EnableEIP bool
	Region    string

	ResourceAccountID  string
	IamRoleName        string
	IamUserName        string // bci
	IamConsolePassword string
	EniHexKey          string

	EniWorkerCount int

	DefaultENIPrivateIPCount       int
	DefaultENIBufferPrivateIPCount int
	EniAttachQueryTimeout          time.Duration
	EniDetachQueryMaxCount         int
	EniAttachTaskInterval          time.Duration

	EIPWorkerCount                     int
	EIPMaxRetryTimes                   int
	EIPRetryInterval                   time.Duration
	ChangeEniQPS                       int           // region维度修改eni qps (create|attach|detach|delete)
	ChangeEniPrivateIPQPS              int           // region维度修改eni辅助ip qps (attach|detach)
	ReadEniQPS                         int           // region维度eni 读取qps
	EniChangeRatelimitLoopRetryCount   int           // eni 接口被限速以后，在for循环中重试次数
	ENIPrivateIPConfigFilePath         string        // eni 辅助ip配置文件路径
	BidBccEventPageSize                int           // list bcc event 页数大小
	EvictBidNodePodWaitSecond          int           // 竞价实例node，list 到event 驱逐pod 等待时间
	SyncBidNodeEventWorkerCount        int           // 删除竞价node或潮汐node worker数量
	SyncBidNodeTaskInterval            time.Duration // 定期sync竞价或潮汐node时间
	EnableAllNodePrivateIPGC           bool          // 所有node 开启eni 辅助ip动态gc功能
	DynamicChangePrivateIPBatchsize    int           // 动态创建、删除的eni辅助ip的批量size
	TidalDebugPort                     int           // 潮汐调试端口
	StatisticsPendingTidalTaskInterval time.Duration
}

func NewNetworkOptions() *NetworkOptions {
	return &NetworkOptions{

		EnableEIP:                      true,
		Region:                         "",
		ResourceAccountID:              "",
		IamRoleName:                    "",
		IamUserName:                    "",
		IamConsolePassword:             "",
		EniHexKey:                      "",
		EniWorkerCount:                 6,
		DefaultENIPrivateIPCount:       16,
		DefaultENIBufferPrivateIPCount: 1,
		EniAttachQueryTimeout:          2 * time.Minute,
		EniDetachQueryMaxCount:         60,
		EniAttachTaskInterval:          3 * time.Minute,

		EIPWorkerCount:                     6,
		EIPMaxRetryTimes:                   60,
		EIPRetryInterval:                   3 * time.Second,
		ChangeEniQPS:                       10,
		ChangeEniPrivateIPQPS:              30,
		ReadEniQPS:                         50,
		EniChangeRatelimitLoopRetryCount:   3,
		ENIPrivateIPConfigFilePath:         "",
		BidBccEventPageSize:                100,
		EvictBidNodePodWaitSecond:          60,
		SyncBidNodeEventWorkerCount:        500,
		SyncBidNodeTaskInterval:            10 * time.Second,
		EnableAllNodePrivateIPGC:           false,
		DynamicChangePrivateIPBatchsize:    10,
		TidalDebugPort:                     10000,
		StatisticsPendingTidalTaskInterval: 1 * time.Hour,
	}
}

func (o *NetworkOptions) AddFlags(fs *flag.FlagSet) {
	// global info
	fs.StringVar(&o.Region, "bci-region", o.Region, "eip controller data of xxx region, default value is bj")

	fs.StringVar(&o.ResourceAccountID, "bci-resource-accoutid", o.ResourceAccountID, "eip controller, default value is xxx")
	fs.StringVar(&o.IamRoleName, "bci-iam-rolename", o.IamRoleName, "eip controller, default value is xxx")
	fs.StringVar(&o.IamUserName, "bci-iam-username", o.IamUserName, "eip controller, default value is xxx")
	fs.StringVar(&o.IamConsolePassword, "bci-iam-console-paaword", o.IamConsolePassword, "eip controller, default value is xxx")
	fs.StringVar(&o.EniHexKey, "bci-eni-hexkey", o.EniHexKey, "eip controller, default value is xxx")

	fs.IntVar(&o.EniWorkerCount, "eni-worker-count", o.EniWorkerCount, "eni worker count default value is 6")
	fs.IntVar(&o.DefaultENIPrivateIPCount, "default-eni-private-ip-count", o.DefaultENIPrivateIPCount, "the eni private ip count ,default 16")
	fs.DurationVar(&o.EniAttachQueryTimeout, "eni-attach-query-timeout", o.EniAttachQueryTimeout, "the eni attach query timeout time ,default 2 min")
	fs.IntVar(&o.EniDetachQueryMaxCount, "eni-detach-query-max-count", o.EniDetachQueryMaxCount, "the eni detach query max retry count ,default 60 1min")
	fs.DurationVar(&o.EniAttachTaskInterval, "eni-attach-task-interval", o.EniAttachTaskInterval, "the all node eni attach task interval,defaule 3 min")
	fs.IntVar(&o.ChangeEniQPS, "change-eni-qps", o.ChangeEniQPS, "call iaas change eni client qps,default 10 qps.")
	fs.IntVar(&o.ChangeEniPrivateIPQPS, "change-eni-private-ip-qps", o.ChangeEniPrivateIPQPS,
		"call iaas eni attach+detach private ip qps,default 30 qps.")
	fs.IntVar(&o.ReadEniQPS, "read-eni-qps", o.ReadEniQPS, "call iaas read eni client qps,default 100 qps.")
	fs.IntVar(&o.EniChangeRatelimitLoopRetryCount, "eni-change-ratelimit-loop-retry-count", o.EniChangeRatelimitLoopRetryCount,
		"eni call iaas ratelimit retry loop ")
	fs.StringVar(&o.ENIPrivateIPConfigFilePath, "eni-private-ip-config-file-path", o.ENIPrivateIPConfigFilePath, "the eni private ip config file path.")
	fs.IntVar(&o.BidBccEventPageSize, "bid-bcc-event-page-size", o.BidBccEventPageSize, "list bid node bcc event page size,default 100.")
	fs.IntVar(&o.EvictBidNodePodWaitSecond, "evict-bid-node-pod-wait-second", o.EvictBidNodePodWaitSecond, "evict bid node pod wait second,default 60s.")
	fs.IntVar(&o.SyncBidNodeEventWorkerCount, "sync-bid-node-event-worker-count", o.SyncBidNodeEventWorkerCount,
		"sync bid bcc node event worker count, default 500.")
	fs.DurationVar(&o.SyncBidNodeTaskInterval, "sync-bid-node-task-interval", o.SyncBidNodeTaskInterval, "sync bid node task interval,default 10s.")
	fs.IntVar(&o.DefaultENIBufferPrivateIPCount, "default-eni-buffer-private-ip-count", o.DefaultENIBufferPrivateIPCount,
		"the eni buffer private ip count ,default 1")
	fs.BoolVar(&o.EnableAllNodePrivateIPGC, "enable-all-node-privateip-gc", o.EnableAllNodePrivateIPGC, "Enable all node privateIP gc,default false.")
	fs.IntVar(&o.DynamicChangePrivateIPBatchsize, "dynamic-change-private-ip-batchsize", o.DynamicChangePrivateIPBatchsize,
		"Dynamic change private ip batchsize, default 10")
	fs.IntVar(&o.TidalDebugPort, "tidal-debug-port", o.TidalDebugPort, "Tidal debug port, default 10000")
	fs.DurationVar(&o.StatisticsPendingTidalTaskInterval, "statistics-pending-tidal-task-interval", o.StatisticsPendingTidalTaskInterval,
		"Statistics pending tidal task interval, default 1h")

	// eip
	fs.BoolVar(&o.EnableEIP, "eip-enable", o.EnableEIP, "enable eip controller, default value is false")
	fs.IntVar(&o.EIPWorkerCount, "eip-worker-count", o.EIPWorkerCount, "eip worker count, default value is 6")
	fs.IntVar(&o.EIPMaxRetryTimes, "eip-max-retry-times", o.EIPMaxRetryTimes, "eip max retry times, default value is 6")
	fs.DurationVar(&o.EIPRetryInterval, "eip-retry-interval", o.EIPRetryInterval, "eip retry interval, default value is 3s")
}
