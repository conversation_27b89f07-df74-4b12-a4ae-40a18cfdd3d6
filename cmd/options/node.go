package options

import (
	"flag"
	"time"
)

type NodeOptions struct {
	NodeControllerWorkers                 int           // 打node标签worker 并发数
	NodeGCControllerWorkers               int           // gc worker count
	PodExitStateHoldTime                  time.Duration // podExit 状态最长保持时间
	CoolStateHoldTime                     time.Duration // cool 状态最长保持时间
	SyncNodeStateTaskInterval             time.Duration // node 状态机刷新定时任务间隔
	MonitorPendingPodsInterval            time.Duration // 监控pendingpods 定时任务时间间隔
	DeleteNodeTaskInterval                time.Duration // 删除节点组node 定时任务间隔
	EnableAutoChangeInstanceGroupReplicas bool          // 是否程序自动修改节点组最大节点数
	EnableDeleteNotReadyNodes             bool          // 是否程序自动删除notReady的node
	AutoChangeInstanceGroupMaxReplicas    int           // 修改节点组最大副本数
	NodeGcImage                           string        // node gc image
	DockerSockPath                        string        // docker Unix server 监听地址
	ContainerdSockPath                    string        // containerd Unix server 监听地址
	EnableAutoDeleteCustomizeFilterNode   bool
	EnableAutoDeleteExceedBufferNode      bool
	EnableAutoDeleteGCFailedNode          bool
	NotReadyStateHoldTime                 time.Duration // node 处于notReady状态时多久可删除
	EvictNotReadyNodePodTime              time.Duration // 驱逐notReady node上bci pod时间
	NodeConditionFaultTypes               string        // node condition 故障列表,以逗号隔开
}

func NewNodeOptions() *NodeOptions {
	return &NodeOptions{
		NodeControllerWorkers:                 3,
		NodeGCControllerWorkers:               10,
		PodExitStateHoldTime:                  5 * time.Minute,
		CoolStateHoldTime:                     5 * time.Minute,
		SyncNodeStateTaskInterval:             10 * time.Second,
		MonitorPendingPodsInterval:            30 * time.Second,
		DeleteNodeTaskInterval:                30 * time.Second,
		EnableAutoChangeInstanceGroupReplicas: false,
		EnableDeleteNotReadyNodes:             false,
		AutoChangeInstanceGroupMaxReplicas:    2000,
		// TODO 镜像待打正式的
		NodeGcImage:                         "registry.baidubce.com/cce-plugin-dev/bci-node-gc:v1",
		DockerSockPath:                      "/var/run/docker.sock",
		ContainerdSockPath:                  "/run/containerd/containerd.sock",
		EnableAutoDeleteCustomizeFilterNode: true,
		EnableAutoDeleteExceedBufferNode:    true,
		EnableAutoDeleteGCFailedNode:        false,
		NotReadyStateHoldTime:               8 * time.Minute,
		EvictNotReadyNodePodTime:            5 * time.Minute,
		NodeConditionFaultTypes:             "ReadonlyFilesystem",
	}
}

// gcimage dockerfile
/*
FROM alpine:edge

ADD ./docker /usr/bin/docker
ADD ./crictl /usr/bin/crictl
*/
func (n *NodeOptions) AddFlags(fs *flag.FlagSet) {
	fs.IntVar(&n.NodeControllerWorkers, "node-controller-workers", n.NodeControllerWorkers, "The node controller worker num")
	fs.IntVar(&n.NodeGCControllerWorkers, "node-gc-controller-workers", n.NodeGCControllerWorkers, "The gc node controller worker num")
	fs.DurationVar(&n.PodExitStateHoldTime, "pod-exit-state-hold-time", n.PodExitStateHoldTime, "The node state (podExit) hold time. Defaults 5m")
	fs.DurationVar(&n.CoolStateHoldTime, "cool-state-hold-time", n.CoolStateHoldTime, "The node state (cool) hold time,Defaults 5m")
	fs.DurationVar(&n.NotReadyStateHoldTime, "not-ready-state-hold-time",
		n.NotReadyStateHoldTime, "The node state (notReady) hold time,Defaults 8m")
	fs.DurationVar(&n.EvictNotReadyNodePodTime, "evict-not-ready-node-pod-time", n.EvictNotReadyNodePodTime, "The notReady node evict bci pod time,Defaults 5m")
	fs.DurationVar(&n.SyncNodeStateTaskInterval, "sync-node-state-task-interval", n.SyncNodeStateTaskInterval, "sync node state task interval,Defaults 10s")
	fs.DurationVar(&n.MonitorPendingPodsInterval, "monitor-pending-pods-interval", n.MonitorPendingPodsInterval, "monitor pending pods interval ,Defaults 30s")
	fs.BoolVar(&n.EnableAutoChangeInstanceGroupReplicas, "enable-auto-change-instance-group-replicas", n.EnableAutoChangeInstanceGroupReplicas, "enable auto change instance group replicas")
	fs.BoolVar(&n.EnableDeleteNotReadyNodes, "enable-delete-notready-nodes", n.EnableDeleteNotReadyNodes, "enable delete notready nodes,Default false")
	fs.IntVar(&n.AutoChangeInstanceGroupMaxReplicas, "auto-change-instance-group-max-replicas", n.AutoChangeInstanceGroupMaxReplicas, "The max instance group replicas when enable ca")
	fs.StringVar(&n.NodeGcImage, "node-gc-image", n.NodeGcImage, "The node gc job image ")
	fs.StringVar(&n.DockerSockPath, "docker-sock-path", n.DockerSockPath, "The docker unix socket path")
	fs.StringVar(&n.ContainerdSockPath, "containerd-sock-path", n.ContainerdSockPath, "The containerd unix socket path")
	fs.DurationVar(&n.DeleteNodeTaskInterval, "delete-node-task-interval", n.DeleteNodeTaskInterval, "The delete instanceGroup node task interval")
	fs.BoolVar(&n.EnableAutoDeleteCustomizeFilterNode, "enable-delete-customize-filter-node",
		n.EnableAutoDeleteCustomizeFilterNode, "enable delete customize filter node,default true")
	fs.BoolVar(&n.EnableAutoDeleteExceedBufferNode, "enable-delete-exceed-buffer-node",
		n.EnableAutoDeleteExceedBufferNode, "enable delete Exceed buffer node , default true")
	fs.BoolVar(&n.EnableAutoDeleteGCFailedNode, "enable-delete-gc-failed-node",
		n.EnableAutoDeleteGCFailedNode, "enable delete gc failed node , default false")
	fs.StringVar(&n.NodeConditionFaultTypes, "node-condition-fault-types", n.NodeConditionFaultTypes,
		"The node condition fault, split with , .")
}
