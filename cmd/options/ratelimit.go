package options

import (
	"flag"
	"time"
)

type RateLimiterOptions struct {
	BaseDelay  time.Duration
	MaxDelay   time.Duration
	Qps        int
	BucketSize int
}

func NewRateLimiterOptions() *RateLimiterOptions {
	return &RateLimiterOptions{
		BaseDelay:  time.Millisecond * 5,
		MaxDelay:   time.Second * 1000,
		Qps:        10,
		BucketSize: 100,
	}
}

func (o *RateLimiterOptions) AddFlags(fs *flag.FlagSet) {
	flag.DurationVar(&o.<PERSON>ela<PERSON>, "rate-limiter-base-delay", o.<PERSON><PERSON>, "The base delay for rate limiter. Defaults 5ms")
	flag.DurationVar(&o.<PERSON>, "rate-limiter-max-delay", o.<PERSON>, "The max delay for rate limiter. Defaults 1000s")
	flag.IntVar(&o.Qps, "rate-limiter-qps", o.Qps, "The qps for rate limier. Defaults 10")
	flag.IntVar(&o.<PERSON>, "rate-limiter-bucket-size", o<PERSON>, "The bucket size for rate limier. Defaults 100")
}
