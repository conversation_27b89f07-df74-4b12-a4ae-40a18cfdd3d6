package options

import (
	"flag"
)

type MonitorSyncCceOptions struct {
	CceAccessKeyIDs     string
	CceSecretAccessKeys string
	CceEndpoints        string
	ClusterIDs          string
}

type BcmOptions struct {
	IamEndpoint                   string
	BcmPushEndpoint               string
	BcmRegisterEndpoint           string
	BcmAggrTaskEndpoint           string
	BciServiceID                  string
	BciServicePasswd              string
	BcmAggrTaskDataSources        string
	BcmRequestTimeoutSec          int
	BcmPushContainerCountPerBatch int
	BcmPushPodCountPerBatch       int
	BcmPushThreadNum              int
	Debug                         bool
}

type PromOptions struct {
	Host          string
	URI           string
	InstanceID    string
	AuthToken     string
	ReqTimeoutSec int
	QueryStepSec  int
}

type MonitorSyncOptions struct {
	Enabled                    bool
	Region                     string
	NsOfLastSyncTimestamp      string
	ResNameOfLastSyncTimestamp string
	SyncIntervalSec            int64
	CceOptions                 MonitorSyncCceOptions
	BcmOptions                 BcmOptions
	PromOptions                PromOptions
}

func NewMonitorSyncOptions() *MonitorSyncOptions {
	return &MonitorSyncOptions{
		Enabled:                    false,
		Region:                     "bj",
		NsOfLastSyncTimestamp:      "kube-system",
		ResNameOfLastSyncTimestamp: "last-sync-timestamp",
		SyncIntervalSec:            5,
		CceOptions:                 MonitorSyncCceOptions{},
		BcmOptions: BcmOptions{
			BcmRequestTimeoutSec: 30,
			Debug:                true,
			// MaxMetricsCountPerPush limits the max metrics count per push request since
			// the max body length for each push request is 32768 bytes (limited by bcm push api).
			BcmPushContainerCountPerBatch: 150,
			// MaxMetricsCountPerPush limits the max aggregate data count per push request (limited by
			// bcm aggregate task api).
			BcmPushPodCountPerBatch: 10,
			BcmPushThreadNum:        10,
		},
		PromOptions: PromOptions{
			ReqTimeoutSec: 60,
			QueryStepSec:  30,
		},
	}
}

func (o *MonitorSyncOptions) AddFlags(fs *flag.FlagSet) {
	// global info
	fs.BoolVar(&o.Enabled, "monitor-sync-enabled", o.Enabled, "enable monitor sync controller, default value is false")
	fs.StringVar(&o.Region, "monitor-sync-region", o.Region, "sync monitor data of xxx region, default value is bj")
	fs.StringVar(&o.NsOfLastSyncTimestamp, "monitor-sync-lastsynctime-configmap-ns", o.NsOfLastSyncTimestamp,
		"ns of configmap storing lastsynctime, default value is kube-system")
	fs.StringVar(&o.ResNameOfLastSyncTimestamp, "monitor-sync-lastsynctime-configmap-name", o.ResNameOfLastSyncTimestamp,
		"name of configmap storing lastsynctime, default value is last-sync-timestamp")
	fs.Int64Var(&o.SyncIntervalSec, "monitor-sync-intervalsec", o.SyncIntervalSec, "interval of taking monitor sync, default value is 5s")

	// cce info
	fs.StringVar(&o.CceOptions.ClusterIDs, "monitor-sync-cce-clusterids", o.CceOptions.ClusterIDs, "cluster ids of cce, split by comma")
	fs.StringVar(&o.CceOptions.CceEndpoints, "monitor-sync-cce-endpoints", o.CceOptions.CceEndpoints,
		"endpoints of cce, please keep same order with it's cluster id in monitor-sync-cce-clusterids, split by comma")
	fs.StringVar(&o.CceOptions.CceAccessKeyIDs, "monitor-sync-cce-accesskeyids", o.CceOptions.CceAccessKeyIDs,
		"access key ids of cce, please keep same order with it's cluster id in monitor-sync-cce-clusterids, split by comma")
	fs.StringVar(&o.CceOptions.CceSecretAccessKeys, "monitor-sync-cce-accesskeys", o.CceOptions.CceSecretAccessKeys,
		"access keys of cce, please keep same order with it's cluster id in monitor-sync-cce-clusterids, split by comma")

	// bcm info
	fs.StringVar(&o.BcmOptions.IamEndpoint, "monitor-sync-bcm-iamendpoint", o.BcmOptions.IamEndpoint, "iam endpoint used to get AKSK for requesting bcm")
	fs.StringVar(&o.BcmOptions.BcmRegisterEndpoint, "monitor-sync-bcm-registerendpoint", o.BcmOptions.BcmRegisterEndpoint,
		"bcm endpoint used to register monitor object")
	fs.StringVar(&o.BcmOptions.BcmPushEndpoint, "monitor-sync-bcm-pushendpoint", o.BcmOptions.BcmPushEndpoint,
		"bcm endpoint used to push container monitor data")
	fs.StringVar(&o.BcmOptions.BcmAggrTaskEndpoint, "monitor-sync-bcm-arrgtaskendpoint", o.BcmOptions.BcmAggrTaskEndpoint,
		"bcm endpoint used to push pod monitor data")
	fs.StringVar(&o.BcmOptions.BcmAggrTaskDataSources, "monitor-sync-bcm-arrgtasksources", o.BcmOptions.BcmAggrTaskDataSources,
		"source of pushing pod monitor data, split by comma")
	fs.StringVar(&o.BcmOptions.BciServiceID, "monitor-sync-bcm-bciserviceid", o.BcmOptions.BciServiceID,
		"bci service id used to authenticate when requesting bcm")
	fs.StringVar(&o.BcmOptions.BciServicePasswd, "monitor-sync-bcm-bciservicepasswd", o.BcmOptions.BciServicePasswd,
		"bci service passwd used to authenticate when requesting bcm")
	fs.IntVar(&o.BcmOptions.BcmRequestTimeoutSec, "monitor-sync-bcm-requesttimeoutsec", o.BcmOptions.BcmRequestTimeoutSec,
		"timeout of requesting bcm, default value is 30s")
	fs.IntVar(&o.BcmOptions.BcmPushContainerCountPerBatch, "monitor-sync-bcm-pushcontainercountperbatch", o.BcmOptions.BcmPushContainerCountPerBatch,
		"count of pushing container to bcm in one batch, default value is 150")
	fs.IntVar(&o.BcmOptions.BcmPushPodCountPerBatch, "monitor-sync-bcm-pushpodcountperbatch", o.BcmOptions.BcmPushPodCountPerBatch,
		"count of pushing pod to bcm in one batch, default value is 10")
	fs.IntVar(&o.BcmOptions.BcmPushThreadNum, "monitor-sync-bcm-pushthreadnum", o.BcmOptions.BcmPushThreadNum,
		"num of thread concurrently pushing metrics to bcm, default value is 10")
	fs.BoolVar(&o.BcmOptions.Debug, "monitor-sync-bcm-debug", o.BcmOptions.Debug,
		"debug of requesting bcm, default value is true")

	// prom info
	fs.StringVar(&o.PromOptions.Host, "monitor-sync-prom-host", o.PromOptions.Host, "host of cprom")
	fs.StringVar(&o.PromOptions.URI, "monitor-sync-prom-uri", o.PromOptions.URI, "uri of cprom, default value is prometheus/api/v1/query_range")
	fs.StringVar(&o.PromOptions.InstanceID, "monitor-sync-prom-instanceid", o.PromOptions.InstanceID, "instance id of cprom")
	fs.StringVar(&o.PromOptions.AuthToken, "monitor-sync-prom-authtoken", o.PromOptions.AuthToken, "auth token of bci used to request cprom")
	fs.IntVar(&o.PromOptions.ReqTimeoutSec, "monitor-sync-prom-requesttimeoutsec", o.PromOptions.ReqTimeoutSec,
		"timeout of requesting cprom, default value is 60s")
	fs.IntVar(&o.PromOptions.QueryStepSec, "monitor-sync-prom-querystepsec", o.PromOptions.QueryStepSec,
		"step of cprom range query, default value is 30s")
}
