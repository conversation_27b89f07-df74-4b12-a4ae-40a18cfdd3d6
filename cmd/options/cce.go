package options

import "flag"

type CceOptions struct {
	CceAccessKeyID     string
	CceSecretAccessKey string
	CceEndpoint        string
	ClusterID          string
}

func NewCceOptions() *CceOptions {
	return &CceOptions{
		CceAccessKeyID:     "",
		CceSecretAccessKey: "",
		CceEndpoint:        "cce.bj.baidubce.com",
		ClusterID:          "",
	}
}

func (o *CceOptions) AddFlags(fs *flag.FlagSet) {
	fs.StringVar(&o.CceAccessKeyID, "cce-access-key-id", o.CceAccessKeyID, "The cce access key id ")
	fs.StringVar(&o.CceSecretAccessKey, "cce-secret-access-key", o.<PERSON><PERSON>ccess<PERSON>ey, "The cce secret access key ")
	fs.StringVar(&o.CceEndpoint, "cce-endpoint", o.CceEndpoint, "The cce endpoint ")
	fs.StringVar(&o.<PERSON>, "cce-cluster-id", o<PERSON><PERSON>, "The cce cluster id")
}
