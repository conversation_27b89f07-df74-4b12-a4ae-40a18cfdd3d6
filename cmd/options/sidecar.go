package options

import "flag"

type SidecarOptions struct {
	SidecarControllerWorkers           int
	EnableDeleteSidecarFailedPod       bool
	SidecarFailedForcePatchDelaySecond int
	SidecarContainerFailedPodSavePath  string
}

func NewSidecarOptions() *SidecarOptions {

	return &SidecarOptions{
		SidecarControllerWorkers:           3,
		EnableDeleteSidecarFailedPod:       true,
		SidecarFailedForcePatchDelaySecond: 60,
		SidecarContainerFailedPodSavePath:  "./runtime/sidecarfailed",
	}
}

func (o *SidecarOptions) AddFlags(fs *flag.FlagSet) {
	fs.IntVar(&o.SidecarControllerWorkers, "sidecar-controller-workers",
		o.<PERSON>carController<PERSON>orkers, "The sidecar controller worker num,default 3")
	fs.BoolVar(&o.EnableDeleteSidecarFailedPod, "enable-delete-sidecar-failed-pod",
		o.EnableDeleteSidecarFailedPod, "Enable auto delete sidecar failed pods, default true")
	fs.IntVar(&o.SidecarFailedForcePatchDelaySecond, "sidecar-failed-force-patch-delay-second",
		o.SidecarFailedForcePatchDelaySecond, "The sidecar failed force patch delay second, default 60")
	fs.StringVar(&o.SidecarContainerFailedPodSavePath, "sidecar-container-failed-pod-save-path",
		o.SidecarContainerFailedPodSavePath, "The sidecar container failed pod save path, default ./runtime/sidecarfailed")
}
