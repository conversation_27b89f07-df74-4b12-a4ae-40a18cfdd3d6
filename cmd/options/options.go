package options

import (
	"flag"
)

type ServerRunOptions struct {
	MetricsAddr             string
	EnableLeaderElection    bool
	LeaderElectionNamespace string
	ProbeAddr               string
	Port                    int
	CertDir                 string
	APIGoAppKey             string
	APIGoSecretKey          string

	WebHookServer        bool
	BciIamAccessKey      string
	BciIamSecretKey      string
	RatelimitOptions     *RateLimiterOptions
	NodeOptions          *NodeOptions
	CceOptions           *CceOptions
	InstanceGroupOptions *InstanceGroupOptions
	MonitorSyncOptions   *MonitorSyncOptions
	NetworkOptions       *NetworkOptions
	SidecarOptions       *SidecarOptions
	EventOptions         *EventOptions
}

func NewServerRunOptions() *ServerRunOptions {
	s := ServerRunOptions{
		MetricsAddr:             ":8080",
		EnableLeaderElection:    true,
		LeaderElectionNamespace: "kube-system",
		ProbeAddr:               ":8081",
		Port:                    9443,
		CertDir:                 "",
		WebHookServer:           true,
		APIGoAppKey:             "apiGoAk-683d1e06ffff478aba5d8a132c4c9d49-online",
		APIGoSecretKey:          "apiGoSk-ee15560388024120bf2333b34315cd90-online",
		BciIamAccessKey:         "ALTAKDjCAabTwyhDGIyn1bEwos",
		BciIamSecretKey:         "f03b1b9467ad4cf394dc03fa2d5d0786",
		RatelimitOptions:        NewRateLimiterOptions(),
		NodeOptions:             NewNodeOptions(),
		CceOptions:              NewCceOptions(),
		InstanceGroupOptions:    NewInstanceGroupOptions(),
		MonitorSyncOptions:      NewMonitorSyncOptions(),
		NetworkOptions:          NewNetworkOptions(),
		SidecarOptions:          NewSidecarOptions(),
		EventOptions:            NewEventOptions(),
	}
	return &s
}

func (s *ServerRunOptions) AddFlags(fs *flag.FlagSet) {
	flag.IntVar(&s.Port, "port", s.Port, "pod-admission-webhook listen port.")
	flag.StringVar(&s.CertDir, "cert-dir", s.CertDir, "CertDir is the directory that contains the server key and certificate. "+
		"if not set, webhook server would look up the server key and certificate in "+
		"{TempDir}/k8s-webhook-server/serving-certs. The server key and certificate "+
		"must be named tls.key and tls.crt, respectively.")

	flag.StringVar(&s.MetricsAddr, "metrics-bind-address", s.MetricsAddr, "The address the metric endpoint binds to.")
	flag.StringVar(&s.ProbeAddr, "health-probe-bind-address", s.ProbeAddr, "The address the probe endpoint binds to.")
	flag.BoolVar(&s.EnableLeaderElection, "enable-leader-election", s.EnableLeaderElection,
		"Enable leader election for controller manager. "+
			"Enabling this will ensure there is only one active controller manager.")

	flag.StringVar(&s.LeaderElectionNamespace, "leader-election-namespace", s.LeaderElectionNamespace,
		"This determines the namespace in which the leader election configmap will be created, it will use in-cluster namespace if empty.")
	flag.StringVar(&s.APIGoAppKey, "api-go-appkey", s.APIGoAppKey, "The API key for APIGo")
	flag.StringVar(&s.APIGoSecretKey, "api-go-secretkey", s.APIGoSecretKey, "The Secret Key for APIGo")

	flag.BoolVar(&s.WebHookServer, "webhook-server", s.WebHookServer, "webhook server ,default true")
	flag.StringVar(&s.BciIamAccessKey, "bci-iam-console-accesskey", s.BciIamAccessKey, "bci iam accesskey, default value is xxx")
	flag.StringVar(&s.BciIamSecretKey, "bci-iam-console-secretkey", s.BciIamSecretKey, "bci iam secretykey, default value is xxx")
	s.RatelimitOptions.AddFlags(fs)
	s.NodeOptions.AddFlags(fs)
	s.CceOptions.AddFlags(fs)
	s.InstanceGroupOptions.AddFlags(fs)
	s.MonitorSyncOptions.AddFlags(fs)
	s.NetworkOptions.AddFlags(fs)
	s.SidecarOptions.AddFlags(fs)
	s.EventOptions.AddFlags(fs)
}
