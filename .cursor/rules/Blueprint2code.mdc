---
description:
globs:
alwaysApply: false
---

# Go项目开发规范与实现规则（Clean Architecture & Best Practice）

*在代码实现的过程中，有以下7条宏观规则，请一定牢记*
1. 代码必须严格匹配现有项目的包结构和架构分层，流程完整参考对应的参考文档流程图。
2. 输出的代码为【可以直接加入到现有代码库并编译通过的完整实现】，而不是零散的代码块或片段。每一步输出为完整的文件内容或明确的代码补丁。
3. 需要新增/修改/注册依赖、配置等内容时，请一并输出到对应文件，并注明修改点。
4. 严格遵循当前规则下的所有代码规范（包括依赖注入、错误处理、输入校验、并发安全等），如遇不确定的参数或结构，请在输出结尾列出并等待我确认。
5. 实现前请先输出代码框架和接口定义，待我确认后再逐步细化具体功能实现，单元测试内容请先以md文件清单形式输出，待我确认后再生成测试代码。
6. 输出时请勿留下“请在这里补充”或“TODO”等未完成提示，所有内容均需完整实现。
7. 在实现过程中不需要补充单测。但是一定要留下两个 markdown 格式文档，分别用来记录你认为需要在单测中补充的测试样例，以及你认为在端到端测试时需要进行测试的样例。
8. 尽量复用现有代码实现，如有新增字段或方法需要找我确认。

* 其他代码实现的规范如下 *
## 1. 架构与模块化

- 所有代码必须严格遵循设计文档，架构调整需详细说明并经确认。
- 强制采用 **整洁架构（Clean Architecture）**：分为 domain（实体/业务规则）、usecase（用例逻辑）、interface（接口/控制器）、infrastructure（第三方/持久化）四层。
- 每层职责清晰、相互解耦，依赖只可自上而下（依赖倒置）。
- 禁止全局状态，所有依赖必须通过构造函数注入。

## 2. 函数与错误处理

- 函数简短、单一职责。
- 所有错误必须显式检查和处理，禁止无视或直接panic。
- 错误需使用 `fmt.Errorf("context: %w", err)` 包装，增强可追溯性。
- 输入参数必须验证和净化，拒绝非法或风险数据，避免出现空指针问题。

## 3. 依赖与资源管理

- 禁止全局变量注入依赖，全部通过构造函数/接口注入。
- 资源（文件、连接等）使用 `defer` 关闭，避免泄漏。
- 外部调用（如HTTP、DB、RPC）必须实现超时、重试（指数退避）、异常处理。

## 4. 并发与协程安全

- 禁止不安全地操作共享状态，需使用通道、互斥锁等同步原语保护。
- 协程需通过 context 传递取消信号，避免泄漏或死锁。
- 并发场景下，严格遵循数据竞态检查（go run -race）。

## 5. 安全与鲁棒性

- 任何外部输入必须验证和净化。
- 敏感操作要边界隔离，权限校验显式。
- 外部接口需实现分布式限流（如Redis），防止服务滥用。
- 所有关键路径需记录操作日志、异常和耗时。

## 6. 性能与监控

- 关键路径（DB、RPC、计算）需 runtime metrics/trace 监控。
- 严格按需优化，禁止过早优化，优化前需 benchmark。
- 优先减少不必要内存分配。

## 7. 工具与依赖管理

- 优先标准库，第三方库需稳定且精简。
- 采用 Go Modules，锁定依赖版本。
- CI流程必须集成：代码检查（lint/vet）、单元测试、依赖安全扫描。

## 8. 单测与文档

- 实现前仅输出待测内容清单（md文件），经确认后才生成单测代码。
- 单测覆盖接口、业务、异常、性能等关键路径。
- 所有模块需有详细README文档，说明接口、依赖和用法。