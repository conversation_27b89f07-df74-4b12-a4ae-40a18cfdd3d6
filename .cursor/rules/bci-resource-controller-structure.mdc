---
description:
globs:
alwaysApply: false
---

# bci-resource-controller 组件结构

**bci-resource-controller** 是百度智能云（BCI）的容器资源控制器，作为Kubernetes集群中的核心组件，负责管理ENI（弹性网络接口）生命周期、维护IP地址池水位、控制Pod调度、以及节点资源的智能管理与回收。该项目基于Controller Runtime框架构建，为大规模容器集群提供企业级的资源管理解决方案。

## 目录结构

- [cmd](mdc:bci-resource-controller/cmd) - 程序入口

- [pkg](mdc:bci-resource-controller/pkg) - 主要功能包
 - [client](mdc:bci-resource-controller/pkg/client) - client定义
 - [controller](mdc:bci-resource-controller/pkg/controller) - 各控制器功能实现
  - [eni](mdc:bci-resource-controller/pkg/controller/eni) - ENI 弹性网卡控制器
   - [eni-attach-controller](mdc:bci-resource-controller/pkg/controller/eni/eni_attach_controller.go) - ENI绑定和解绑
   - [eni-controller](mdc:bci-resource-controller/pkg/controller/eni/eni_controller.go) - ENI生命周期管理(创建、删除)
   - [eni-gc-controller](mdc:bci-resource-controller/pkg/controller/eni/eni_gc_controller.go) - ENI回收
   - [eni-leakage-gc-controller](mdc:bci-resource-controller/pkg/controller/eni/eni_leakage_gc_controller.go) - 竞价实例node ENI回收
   - [eni-private-ip-controller](mdc:bci-resource-controller/pkg/controller/eni/eni_private_ip_controller.go) - 动态分配和回收辅助IP地址、实现水位线驱动的IP预分配策略
  - [event](mdc:bci-resource-controller/pkg/controller/event) - Kubernetes事件管理、用户操作审计跟踪
  - [instancegroup](mdc:bci-resource-controller/pkg/controller/instancegroup) - 节点组的动态扩缩容管理、支持竞价实例的智能调度、潮汐节点的生命周期管理、定时扩容策略执行
  - [network](mdc:bci-resource-controller/pkg/controller/network) - EIP（弹性公网IP）管理、网络策略配置、跨VPC网络连接管理
  - [monitorsync](mdc:bci-resource-controller/pkg/controller/monitorsync) - 多集群监控数据同步、Prometheus指标收集与转发、BCM（百度云监控）数据推送、监控对象的动态注册与注销
  - [node](mdc:bci-resource-controller/pkg/controller/node) - 节点控制器
  - [nodegc](mdc:bci-resource-controller/pkg/controller/nodegc) - 节点垃圾回收控制器
  - [ops](mdc:bci-resource-controller/pkg/controller/ops) - 运维控制器
  - [sidecar](mdc:bci-resource-controller/pkg/controller/sidecar) - sidecar控制器
 - [entity](mdc:bci-resource-controller/pkg/entity) - 各实例对象辅助工具函数
 - [webhook](mdc:bci-resource-controller/pkg/controller/webhook) - pod/node mutating/validating webhook

## 核心组件

项目包含九个主要控制器组件，采用事件驱动的分布式架构：

### 1. ENI Controller (弹性网卡控制器)
- **作用域**: 集群级别
- **主要职责**: 
  - 管理ENI设备的完整生命周期（创建、绑定、删除）
  - 动态分配和回收辅助IP地址
  - 处理多租户网络隔离
  - 实现水位线驱动的IP预分配策略

### 2. Node Controller (节点控制器)
- **作用域**: 集群级别
- **主要职责**:
  - 节点资源状态管理和监控
  - Pod调度前的节点锁定机制
  - 租户级别的节点资源隔离
  - 节点状态机管理

### 3. Instance Group Controller (实例组控制器)
- **作用域**: 集群级别
- **主要职责**:
  - 节点组的动态扩缩容管理
  - 支持竞价实例的智能调度
  - 潮汐节点的生命周期管理
  - 定时扩容策略执行

### 4. Monitor Sync Controller (监控同步控制器)
- **作用域**: 跨集群
- **主要职责**:
  - 多集群监控数据同步
  - Prometheus指标收集与转发
  - BCM（百度云监控）数据推送
  - 监控对象的动态注册与注销

### 5. Node GC Controller (节点垃圾回收控制器)
- **作用域**: 集群级别
- **主要职责**:
  - 超配ENI资源的智能回收
  - 基于策略的资源清理
  - 异常节点的自动清理

### 6. Network Controller (网络控制器)
- **作用域**: 集群级别
- **主要职责**:
  - EIP（弹性公网IP）管理
  - 网络策略配置
  - 跨VPC网络连接管理

### 7. Event Controller (事件控制器)
- **作用域**: 集群级别
- **主要职责**:
  - Kubernetes事件管理
  - 用户操作审计跟踪

### 8. Ops Controller (运维控制器)
- **作用域**: 集群级别
- **主要职责**:
  - Token管理和认证
  - 运维任务调度

### 9. Sidecar Controller (边车控制器)
- **作用域**: 集群级别
- **主要职责**:
  - Sidecar容器管理
  - 服务网格集成

