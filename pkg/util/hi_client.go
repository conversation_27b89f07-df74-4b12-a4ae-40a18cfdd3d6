package util

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	ttl "github.com/imkira/go-ttlmap"
	"k8s.io/klog/v2"
)

// Alerter - 定义发送报警方法
type Alerter interface {
	SendMarkDownMessage(ctx context.Context, msg string) error
}

// hi - 百度 Hi 实现 alert.Interface
type hi struct {
	tokenURL string
	ttlMap   *ttl.Map
}

// NewHiClient - 初始化 HiClient
func NewHiClient(tokenURL string) Alerter {
	// 声明ttl map,主要对发送msg 去重
	options := &ttl.Options{
		// 1024个队列长度
		InitialCapacity: 1024,
		OnWillExpire: func(key string, item ttl.Item) {
			klog.V(4).Infof("hiClient ttlMap key %s value %+v Expired", key, item.Value())
		},
		OnWillEvict: func(key string, item ttl.Item) {
			fmt.Printf("evicted: [%s=%v]\n", key, item.Value())
			klog.V(4).Infof("hiClient ttlMap key %s value %+v Evictd", key, item.Value())
		},
	}
	ttlMap := ttl.New(options)

	return &hi{
		tokenURL: tokenURL,
		ttlMap:   ttlMap,
	}
}

// HiMessage - Hi 消息
type HiMessage struct {
	HMessage HMessage `json:"message"`
}

// HMessage - Hi 消息
type HMessage struct {
	BodyList []Body `json:"body"`
}

// Body - 消息类型
type Body struct {
	Type    string   `json:"type"`
	Content string   `json:"content"`
	ATUser  []string `json:"atuserids"`
	ATAll   bool     `json:"atall"`
}

func (h *hi) SendMarkDownMessage(ctx context.Context, msg string) error {
	option := &ttl.SetOptions{
		KeyExist: ttl.KeyExistNotYet,
	}

	// ttl 过期时间为3分钟
	err := h.ttlMap.Set(msg, ttl.NewItem("1", ttl.WithTTL(3*time.Minute)), option)
	if err != nil && err == ttl.ErrExist {
		klog.V(4).Infof("hiClient SendMarkDownMessage msg %+v Exist ignore ", msg)
		return nil
	}
	// 为msg 拼接时间
	msg += "time: " + time.Now().Format("2006-01-02 15:04") + "\n"

	// 构建消息
	bl := []Body{
		{
			Type:    "MD",
			Content: msg,
		},
	}

	hm := HiMessage{
		HMessage: HMessage{
			BodyList: bl,
		},
	}

	return h.Send(ctx, hm)
}

// func (h *hi) SendMessage(ctx context.Context, msg string) error {
// 	// 构建消息
// 	bl := []Body{
// 		{
// 			Type:    "TEXT",
// 			Content: msg,
// 		},
// 	}

// 	hm := HiMessage{
// 		HMessage: HMessage{
// 			BodyList: bl,
// 		},
// 	}

// 	return h.Send(ctx, hm)
// }

// func (h *hi) SendMessageAtUsers(ctx context.Context, msg string, atUserList []string) error {
// 	bl := []Body{
// 		{
// 			Type:    "TEXT",
// 			Content: msg,
// 		},
// 		{
// 			Type:   "AT",
// 			ATUser: atUserList,
// 			ATAll:  false,
// 		},
// 	}
// 	hm := HiMessage{
// 		HMessage: HMessage{
// 			BodyList: bl,
// 		},
// 	}

// 	return h.Send(ctx, hm)
// }

func (h *hi) Send(ctx context.Context, hm HiMessage) error {
	data, err := json.Marshal(hm)
	if err != nil {
		klog.Errorf("json Marshal failed: %s", err)
		return err
	}

	// 发送消息
	var jsonStr = []byte(fmt.Sprintf("%s", data))
	req, err := http.NewRequest("POST", h.tokenURL, bytes.NewBuffer(jsonStr))
	if err != nil {
		klog.Errorf("http post failed: %s", err)
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		klog.Errorf("http post failed: %s", err)
		return err
	}
	defer resp.Body.Close()

	// 接收响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		klog.Errorf("read  resp body failed: %s", err)
		return err
	}

	klog.Infof("Response.Body: %s", string(body))
	return nil
}
