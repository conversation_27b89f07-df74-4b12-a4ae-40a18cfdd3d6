package util

import (
	"context"
	"fmt"
	"testing"
	"time"

	ttl "github.com/imkira/go-ttlmap"
)

func TestHiClient(t *testing.T) {
	client := NewHiClient("http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d8850854d6fc851d861ee0cab7523e822")

	_ = client.SendMarkDownMessage(context.TODO(), "**region:bd**\nmessage:测试消息")
}

func TestTTLMap(t *testing.T) {
	options := &ttl.Options{
		InitialCapacity: 1024,
		OnWillExpire: func(key string, item ttl.Item) {
			fmt.Printf("expired: [%s=%v]\n", key, item.Value())
		},
		OnWillEvict: func(key string, item ttl.Item) {
			fmt.Printf("evicted: [%s=%v]\n", key, item.Value())
		},
	}
	ttlMap := ttl.New(options)

	err := ttlMap.Set("key", ttl.NewItem("value", ttl.WithTTL(3*time.Second)),
		&ttl.SetOptions{KeyExist: ttl.KeyExistNotYet})
	fmt.Println(err)
	time.Sleep(2 * time.Second)
	err = ttlMap.Set("key", ttl.NewItem("value", ttl.WithTTL(3*time.Second)),
		&ttl.SetOptions{KeyExist: ttl.KeyExistNotYet})
	fmt.Println(err == ttl.ErrExist)
	if err == nil {
		t.Errorf("err should not null")
	}
}
