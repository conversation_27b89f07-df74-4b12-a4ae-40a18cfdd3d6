package util

import (
	"fmt"
	"testing"
)

func TestSliceSubtract(t *testing.T) {
	a := []string{"1", "3", "5", "6", "7"}
	b := []string{"1", "2", "3", "2", "2", "2", "3", "3", "3"}

	result := SliceSubtract(a, b)
	fmt.Println(result)

	// var c []string = nil
	c := []string{"1", "3", "5", "6", "7"}
	var d []string = []string{}

	result = SliceSubtract(c, d)
	fmt.Println(result)

	e := []string{}
	f := []string{"1", "3", "5", "6", "7"}
	result = SliceSubtract(e, f)
	fmt.Println(result)

}
