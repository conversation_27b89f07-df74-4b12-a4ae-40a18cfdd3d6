package util

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

func PatchNodeAnnotation(k8sClient client.Client, node *corev1.Node, patchValues []entity.PatchValue) error {
	if len(patchValues) == 0 {
		return nil
	}
	clientNode := &corev1.Node{}
	key := client.ObjectKey{
		Name: node.Name,
	}

	// 从client获取最新node，因为有可能在一个函数中多次patch，存在覆盖场景。
	err := k8sClient.Get(context.Background(), key, clientNode)
	if err != nil {
		klog.Errorf("patchUtil k8sClient get node %s err %+v ", node.Name, err)
		return err
	}
	copyNode := clientNode.DeepCopy()
	nodeAnnotations := copyNode.ObjectMeta.Annotations
	if nodeAnnotations == nil {
		nodeAnnotations = make(map[string]string)
	}

	newAnnotations := make(map[string]interface{})

	for k, v := range nodeAnnotations {
		newAnnotations[k] = v
	}

	// label 是否改变
	labelChange := false

	for _, patch := range patchValues {
		val := nodeAnnotations[patch.Key]
		if patch.Type == entity.PatchTypeDelete && val != "" {
			// 删除某个label
			newAnnotations[patch.Key] = nil
			labelChange = true
			continue
		}
		// 更新某个label
		if patch.Type == entity.PatchTypeUpdate && val != patch.Value {
			newAnnotations[patch.Key] = patch.Value
			labelChange = true
		}
	}

	if !labelChange {
		klog.V(5).Infof("patchUtil patch node %s patch value %+v not change ", node.Name, patchValues)
		return nil
	}

	metaData := map[string]map[string]interface{}{
		"annotations": newAnnotations,
	}
	patchData, _ := json.Marshal(&map[string]interface{}{
		"metadata": &metaData,
	})

	for i := 0; i < 3; i++ {
		err = k8sClient.Patch(context.TODO(), copyNode, client.RawPatch(types.StrategicMergePatchType, []byte(patchData)))
		klog.V(3).Infof("patchUtil patch node %s patch value %+v index %+v , err %+v ", node.Name, patchValues, i, err)

		if err == nil {
			return nil
		}
	}

	return err
}

func PatchPodAnnotation(k8sClient client.Client, pod *corev1.Pod, patchValues []entity.PatchValue) error {
	if len(patchValues) == 0 {
		return nil
	}

	// 从k8s client中获取最新的
	clientPod := &corev1.Pod{}
	key := client.ObjectKey{
		Name:      pod.Name,
		Namespace: pod.Namespace,
	}
	err := k8sClient.Get(context.Background(), key, clientPod)
	if err != nil {
		klog.Errorf("patchUtil k8sClient get pod %+v err %+v ", key, err)
		return err
	}
	copyPod := clientPod.DeepCopy()
	podAnnotations := copyPod.ObjectMeta.Annotations
	if podAnnotations == nil {
		podAnnotations = make(map[string]string)
	}

	newAnnotations := make(map[string]interface{})

	for k, v := range podAnnotations {
		newAnnotations[k] = v
	}

	// label 是否改变
	labelChange := false

	for _, patch := range patchValues {
		val := podAnnotations[patch.Key]
		if patch.Type == entity.PatchTypeDelete && val != "" {
			// 删除某个label
			newAnnotations[patch.Key] = nil
			labelChange = true
			continue
		}
		// 更新某个label
		if patch.Type == entity.PatchTypeUpdate && val != patch.Value {
			newAnnotations[patch.Key] = patch.Value
			labelChange = true
		}
	}

	if !labelChange {
		klog.V(5).Infof("patchUtil patch pod %s patch value %+v not change ", pod.Name, patchValues)
		return nil
	}

	metaData := map[string]map[string]interface{}{
		"annotations": newAnnotations,
	}
	patchData, _ := json.Marshal(&map[string]interface{}{
		"metadata": &metaData,
	})

	for i := 0; i < 3; i++ {
		err = k8sClient.Patch(context.TODO(), copyPod, client.RawPatch(types.StrategicMergePatchType, []byte(patchData)))
		klog.V(3).Infof("patchUtil patch pod %s patch value %+v index %+v , err %+v ", pod.Name, patchValues, i, err)

		if err == nil {
			return nil
		}
	}

	return err
}
