package util

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/http"
	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	ccev2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	ccev2Types "github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
)

var (
	clusterID     = "cce-rvjnxgdy"
	igID          = "cce-ig-f7ig6xak"
	instanceID    = "i-KzSV6UEc"
	cceInstanceID = "cce-rvjnxgdy-mnlj3ybs"
	cceOptions    = &options.CceOptions{
		CceAccessKeyID:     "e0c68be8495540f280b3e9ef03ec25d2",
		CceSecretAccessKey: "b599d5f4f30e41bcb0e1482c9de65bd6",
		CceEndpoint:        "cce.bj.baidubce.com",
		ClusterID:          clusterID,
	}
)

func TestChangeInstanceGroupMaxReplicas(t *testing.T) {
	client, err := NewCceClient(cceOptions)
	if err != nil {
		t.Errorf("init cce client err %+v ", err)
		return
	}

	detailResp, err := client.GetInstanceGroupDetail(igID)
	if err != nil {
		fmt.Println(err)
	}

	if detailResp != nil {
		bytes, _ := json.Marshal(detailResp)
		fmt.Println(string(bytes))
	}

	// 此处不关注失败信息
	// resp, err := client.ChangeInstanceGroupAutoscalerMaxReplicas(igID, 5)
	// if err != nil {
	// 	fmt.Println(err)
	// }
	// fmt.Println(resp.RequestID)
}

// func TestChangeInstanceGroupReplicas(t *testing.T) {
// 	client, err := NewCceClient(cceOptions)
// 	if err != nil {
// 		t.Errorf("init cce client err %+v ", err)
// 		return
// 	}

// 	resp, err := client.ChangeInstanceGroupReplicas(clusterID, igID, 3)
// 	if err != nil {
// 		fmt.Println(err)
// 	}
// 	fmt.Println(resp.RequestID)
// }

func TestRemoveInstanceGroupNodes(t *testing.T) {

	// client, err := NewCceClient(cceOptions)
	// if err != nil {
	// 	t.Errorf("init cce client err %+v ", err)
	// 	return
	// }

	// resp, err := client.RemoveInstanceGroupNodes(igID, []string{"cce-ig-f7ig6xak-iraa5p30"})
	// if err != nil {
	// 	fmt.Println(err)
	// }
	// fmt.Println(resp.RequestID)
}

func TestGetInstanceGroupList(t *testing.T) {
	client, err := NewCceClient(cceOptions)
	if err != nil {
		t.Errorf("init cce client err %+v ", err)
		return
	}
	resp, err := client.GetInstanceGroupList()
	if err != nil {
		fmt.Println(err)
	}
	bytes, _ := json.Marshal(resp)
	fmt.Println(string(bytes))
}

func TestListInstanceGroupNodes(t *testing.T) {
	client, err := NewCceClient(cceOptions)
	if err != nil {
		t.Errorf("init cce client err %+v ", err)
		return
	}
	cceClient := client.(*cceClient)

	resp, err := cceClient.GetInstanceGroupNodes(igID)

	if err != nil {
		fmt.Println(err)
		return
	}
	bytes, _ := json.Marshal(resp.Page.List)
	fmt.Println(string(bytes))
}

func Test_cceClient_GetInstance(t *testing.T) {
	client, err := NewCceClient(cceOptions)
	if err != nil {
		t.Errorf("init cce client err %+v ", err)
		return
	}
	cceClient := client.(*cceClient)
	resp, err := cceClient.GetInstance(instanceID)
	if err != nil {
		fmt.Println(err)
		return
	}
	bytes, _ := json.Marshal(resp.Instance)
	fmt.Println(string(bytes))
}

func Test_cceClient_GetInstanceEvent(t *testing.T) {
	client, err := ccev2.NewClient(cceOptions.CceAccessKeyID, cceOptions.CceSecretAccessKey, cceOptions.CceEndpoint)

	type Steps struct {
		StepName     string      `json:"stepName"`
		StepStatus   string      `json:"stepStatus"`
		Ready        bool        `json:"ready"`
		StartTime    time.Time   `json:"startTime"`
		FinishedTime time.Time   `json:"finishedTime"`
		CostSeconds  int         `json:"costSeconds"`
		RetryCount   int         `json:"retryCount"`
		ErrInfo      interface{} `json:"errInfo"`
	}

	type GetInstanceEventResult struct {
		RequestID string  `json:"requestID"`
		Status    string  `json:"status"`
		Steps     []Steps `json:"steps"`
	}
	result := &GetInstanceEventResult{}

	err = bce.NewRequestBuilder(client).
		WithMethod(http.GET).
		WithURL("/v2/event/instance/" + instanceID).
		WithResult(result).
		Do()

	fmt.Println(err)
	bytes, _ := json.Marshal(result)
	fmt.Println(string(bytes))
}

func Test_cceClient_CreateInstances(t *testing.T) {
	client, err := NewCceClient(cceOptions)
	if err != nil {
		t.Errorf("init cce client err %+v ", err)
		return
	}
	cceClient := client.(*cceClient)
	// 用instanceGroup的模板创建一个实例
	instanceGroupDetail, err := cceClient.GetInstanceGroupDetail(igID)
	if err != nil {
		fmt.Println(err)
		return
	}
	if instanceGroupDetail.InstanceGroup == nil || instanceGroupDetail.InstanceGroup.Spec == nil {
		fmt.Println(err)
		return
	}
	fmt.Println("GetInstanceGroupDetail " + igID + " success")
	instanceSpec := instanceGroupDetail.InstanceGroup.Spec.InstanceTemplate.InstanceSpec
	instanceSpecBytes, _ := json.Marshal(instanceSpec)
	fmt.Println(string(instanceSpecBytes))

	// 携带竞价相关信息
	instanceSpec.Bid = true
	instanceSpec.BidOption = ccev2Types.BidOption{
		BidMode:       ccev2Types.BidModeMarketPrice,
		BidTimeout:    3,
		BidReleaseEIP: false,
		BidReleaseCDS: false,
	}
	// only postpaid instance supports bid
	instanceSpec.InstanceChargingType = bccapi.PaymentTimingPostPaid
	// 指定label相关信息用于被调度
	nodeLabel := make(map[string]string)
	nodeLabel[entity.NodeLabelInstanceTemplateKey] = instanceID
	nodeLabel[entity.NodeLabelChargingTypeKey] = string(bccapi.PaymentTimingBidding)

	if instanceSpec.BidOption.BidMode == ccev2Types.BidModeCustomPrice {
		nodeLabel[entity.NodeLabelBidModeKey] = entity.NodeLabelBidModePriceValue
		nodeLabel[entity.NodeLabelBidPriceKey] = instanceSpec.BidOption.BidPrice
	} else {
		nodeLabel[entity.NodeLabelBidModeKey] = entity.NodeLabelBidModeMarketValue
	}
	// 清除节点组相关信息, 否则节点将会创建至节点组中
	instanceSpec.InstanceGroupID = ""
	instanceSpec.InstanceGroupName = ""
	instanceSpec.Labels = nodeLabel
	resp, err := cceClient.CreateInstances([]*ccev2.InstanceSet{{
		Count:        1,
		InstanceSpec: instanceSpec,
	}})
	if err != nil {
		fmt.Println(err)
		return
	}
	if resp.CCEInstanceIDs == nil {
		fmt.Println("CreateInstances success, but resp.CCEInstanceIDs is nil")
		return
	}
	bytes, _ := json.Marshal(resp)
	fmt.Println(string(bytes))
}

func Test_cceClient_DeleteInstances(t *testing.T) {
	client, err := NewCceClient(cceOptions)
	if err != nil {
		t.Errorf("init cce client err %+v ", err)
		return
	}
	cceClient := client.(*cceClient)
	resp, err := cceClient.DeleteInstances([]string{cceInstanceID})
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(resp.RequestID)
}
