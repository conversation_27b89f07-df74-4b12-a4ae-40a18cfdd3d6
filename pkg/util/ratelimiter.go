package util

import (
	"golang.org/x/time/rate"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"k8s.io/client-go/util/workqueue"
)

func DefaultControllerRateLimiter(serverOptions *options.ServerRunOptions) workqueue.RateLimiter {
	return workqueue.NewMaxOfRateLimiter(
		workqueue.NewItemExponentialFailureRateLimiter(serverOptions.RatelimitOptions.BaseDelay, serverOptions.RatelimitOptions.MaxDelay),
		&workqueue.BucketRateLimiter{Limiter: rate.NewLimiter(rate.Limit(serverOptions.RatelimitOptions.Qps), serverOptions.RatelimitOptions.BucketSize)},
	)
}
