package util

import (
	"strconv"
	"time"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/client"
)

var LocalLocation *time.Location = nil

func init() {
	cstSh, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		panic(err)
	}
	LocalLocation = cstSh
}

// 时间封装结构体
type TidalTime struct {
	// 当前时间
	CurrentYear   int
	CurrentMonth  int
	CurrentDay    int
	CurrentHour   int
	CurrentMinute int
	// 夜间潮汐配置
	TidalNightStartTime string
	TidalNightEndTime   string
	TidalNightGCTime    string
	// 节假日列表
	Holidays *client.HolidayList
	// 调休日列表
	TimeoffDay *client.HolidayList
}

type CloudBridgeTidalParam struct {
	TidalStartSubmitTime string `json:"tidalStartSubmitTime"`
	TidalEndSubmitTime   string `json:"tidalEndSubmitTime"`
	TidalStartGCTime     string `json:"tidalStartGCTime"`
}

func (t *TidalTime) String() string {
	str := strconv.Itoa(t.CurrentYear) + "-" + strconv.Itoa(t.CurrentMonth) + "-" +
		strconv.Itoa(t.CurrentDay) + " " + strconv.Itoa(t.CurrentHour) + ":" + strconv.Itoa(t.CurrentMinute)
	return str
}

// 是否是周末
func isWeekend(year, month, day int) bool {

	t := time.Date(year, time.Month(month), day, 0, 0, 0, 0, LocalLocation)
	weekday := t.Weekday()
	if weekday == time.Sunday || weekday == time.Saturday {
		return true
	}
	return false
}

// InTidalRunTime 判断当前时间是否在潮汐时刻表的时段内
func InTidalRunTime(tidalTime TidalTime) (result bool) {

	// defer func() {
	// 	klog.V(4).Infof("InTidalRunTime currentTime %s result %+v", tidalTime.String(), result)
	// }()

	// 1、先判断是否在夜间潮汐
	if inTidalNightTime(tidalTime, false) {
		return true
	}
	// 2、判断是否在节假日潮汐
	if tidalTime.Holidays.Contains(tidalTime.CurrentYear,
		tidalTime.CurrentMonth, tidalTime.CurrentDay) {
		return true
	}
	// 3、判断是否在周末
	if !isWeekend(tidalTime.CurrentYear, tidalTime.CurrentMonth, tidalTime.CurrentDay) {
		return false
	}
	// 4、说明在周末，判断周末是否调休
	if tidalTime.TimeoffDay.Contains(tidalTime.CurrentYear, tidalTime.CurrentMonth, tidalTime.CurrentDay) {
		return false
	}
	return true
}

// 是否需要打印潮汐pod 即将释放event
func ShouldReportTidalReleasedEvent(tidalTime TidalTime) bool {
	// 1、在节假日
	if tidalTime.Holidays.Contains(tidalTime.CurrentYear,
		tidalTime.CurrentMonth, tidalTime.CurrentDay) {
		return false
	}

	// 2、在周末 & 非调休
	if isWeekend(tidalTime.CurrentYear, tidalTime.CurrentMonth, tidalTime.CurrentDay) &&
		!tidalTime.TimeoffDay.Contains(tidalTime.CurrentYear, tidalTime.CurrentMonth, tidalTime.CurrentDay) {
		return false
	}
	// 3、在TidalNightEndTime & TidalNightGCTime 之间
	if inTidalNightTime(tidalTime, true) {
		return true
	}
	return false
}

// inTidalNightTime 判断是否在潮汐夜间
func inTidalNightTime(currentTime TidalTime, recordEvent bool) bool {
	startTidalTime := currentTime.TidalNightStartTime
	if recordEvent {
		startTidalTime = currentTime.TidalNightEndTime
	}
	startHour, startMinute, err := ParseTidalTime(startTidalTime)
	if err != nil {
		return false
	}
	endHour, endMinute, err := ParseTidalTime(currentTime.TidalNightGCTime)
	if err != nil {
		return false
	}
	currentDayMinute := currentTime.CurrentHour*60 + currentTime.CurrentMinute
	startDayMinute := startHour*60 + startMinute

	endDayMinute := endHour*60 + endMinute

	// 说明跨天了
	if startHour > endHour {
		return currentDayMinute >= startDayMinute || currentDayMinute <= endDayMinute
	}

	return currentDayMinute >= startDayMinute && currentDayMinute <= endDayMinute
}

func BuildCurrentTidalTime(param CloudBridgeTidalParam, holidays, timeoffDay *client.HolidayList) TidalTime {
	currentTime := time.Now().In(LocalLocation)
	result := TidalTime{
		CurrentYear:         currentTime.Year(),
		CurrentMonth:        int(currentTime.Month()),
		CurrentDay:          currentTime.Day(),
		CurrentHour:         currentTime.Hour(),
		CurrentMinute:       currentTime.Minute(),
		TidalNightStartTime: param.TidalStartSubmitTime,
		TidalNightEndTime:   param.TidalEndSubmitTime,
		TidalNightGCTime:    param.TidalStartGCTime,
		Holidays:            holidays,
		TimeoffDay:          timeoffDay,
	}
	return result
}
