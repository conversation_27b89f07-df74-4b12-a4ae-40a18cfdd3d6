package util

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/client"
)

// TestInTidalDate 测试在指定的时间是否在浮动时间内，包括节假日、工作日、周末等情况。
// 参数：*testing.T - 单元测试对象，用于记录错误信息。
// 返回值：无
func TestInTidalDate(t *testing.T) {
	client := client.NewAPIGoCliect("apiGoAk-683d1e06ffff478aba5d8a132c4c9d49-online", "apiGoSk-ee15560388024120bf2333b34315cd90-online")
	holiday, timeoffday, _ := client.BuildHolidayAndTimeOffDayMap()
	fmt.Println(holiday)
	fmt.Println(timeoffday)
	// 周末调休
	tidalTime := TidalTime{
		CurrentYear:         2025,
		CurrentMonth:        7,
		CurrentDay:          9, // 改为周一
		CurrentHour:         8,
		CurrentMinute:       3,
		TidalNightStartTime: "23:30",
		TidalNightEndTime:   "6:30",
		TidalNightGCTime:    "6:40",
		Holidays:            holiday,
		TimeoffDay:          timeoffday,
	}

	result := InTidalRunTime(tidalTime)
	fmt.Println(result)
	if result {
		t.Error("result false")
		return
	}
	// 工作日非夜间
	tidalTime.CurrentDay = 1
	tidalTime.CurrentHour = 12

	result = InTidalRunTime(tidalTime)
	fmt.Println(result) // false

	// 工作日夜间
	tidalTime.CurrentDay = 2
	tidalTime.CurrentHour = 0
	result = InTidalRunTime(tidalTime)
	fmt.Println(result) // true

	// 周末
	tidalTime.CurrentDay = 18
	tidalTime.CurrentHour = 22
	result = InTidalRunTime(tidalTime)
	fmt.Println(result) // true

	// 节假日
	tidalTime.CurrentMonth = 10
	tidalTime.CurrentDay = 3
	tidalTime.CurrentHour = 22
	result = InTidalRunTime(tidalTime)
	fmt.Println(result) // true

	// 当前时间
	tidalTime.CurrentMonth = 6
	tidalTime.CurrentDay = 14
	tidalTime.CurrentHour = 16
	tidalTime.CurrentMinute = 17
	tidalTime.TidalNightStartTime = "10:00"
	tidalTime.TidalNightEndTime = "16:30"
	tidalTime.TidalNightGCTime = "16:40"
	result = InTidalRunTime(tidalTime)
	fmt.Println(result) // true

}

func TestShouldReportTidalReleasedEvent(t *testing.T) {
	client := client.NewAPIGoCliect("apiGoAk-683d1e06ffff478aba5d8a132c4c9d49-online", "apiGoSk-ee15560388024120bf2333b34315cd90-online")
	holiday, timeoffday, _ := client.BuildHolidayAndTimeOffDayMap()
	fmt.Println(holiday)
	fmt.Println(timeoffday)
	// 节假日
	tidalTime := TidalTime{
		CurrentYear:         2023,
		CurrentMonth:        6,
		CurrentDay:          24,
		CurrentHour:         6,
		CurrentMinute:       35,
		TidalNightStartTime: "23:30",
		TidalNightEndTime:   "6:30",
		TidalNightGCTime:    "6:40",
		Holidays:            holiday,
		TimeoffDay:          timeoffday,
	}

	fmt.Println(tidalTime)
	result := ShouldReportTidalReleasedEvent(tidalTime)
	fmt.Println(result) // false
	// 周末
	tidalTime.CurrentDay = 10
	result = ShouldReportTidalReleasedEvent(tidalTime)
	fmt.Println(result) // false
	tidalTime.CurrentDay = 11
	result = ShouldReportTidalReleasedEvent(tidalTime)
	fmt.Println(result) // false

	// 工作日
	tidalTime.CurrentDay = 12
	result = ShouldReportTidalReleasedEvent(tidalTime)
	fmt.Println(result) // true

	// 工作日
	tidalTime.CurrentDay = 12
	tidalTime.CurrentHour = 13
	result = ShouldReportTidalReleasedEvent(tidalTime)
	fmt.Println(result) // false
}
