package util

import (
	ccev2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"github.com/prometheus/client_golang/prometheus"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
)

var (
	// 调用cce接口打点统计
	callCceCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "call_cce_total",
			Help:      "The Tenant Lock Node Total Count .",
		},
		[]string{"result", "method"},
	)
)

func init() {
	metrics.Registry.MustRegister(callCceCounter)
}

type CceClient interface {
	ChangeInstanceGroupAutoscalerConfig(enableAutoscaler bool, instanceGroupID string, maxReplicas int) (
		*ccev2.UpdateInstanceGroupClusterAutoscalerSpecResponse, error)

	GetInstanceGroupDetail(instanceGroupID string) (*ccev2.GetInstanceGroupResponse, error)

	RemoveInstanceGroupNodes(instanceGroupID string, removeInstances []string) (*ccev2.CreateTaskResp, error)

	GetInstanceGroupList() (*ccev2.ListInstanceGroupResponse, error)

	GetInstanceGroupNodes(instanceGroupID string) (*ccev2.ListInstancesByInstanceGroupIDResponse, error)

	GetKubeconfig(clusterID string) (*ccev2.GetKubeConfigResponse, error)

	GetInstance(instanceID string) (resp *ccev2.GetInstanceResponse, err error)

	CreateInstances(instanceSet []*ccev2.InstanceSet) (resp *ccev2.CreateInstancesResponse, err error)

	DeleteInstances(removeCCEInstanceIds []string) (resp *ccev2.DeleteInstancesResponse, err error)

	InstanceGroupTasks(instanceGroupID string, pageNo, pageSize int) (resp *ccev2.ListTaskResp, err error)
}

// cce api 文档参考:  https://github.com/baidubce/bce-sdk-go/blob/v0.9.135/doc/CCEv2.md

// NewCceClient 创建cce client
func NewCceClient(cceOptions *options.CceOptions) (CceClient, error) {
	client, err := ccev2.NewClient(cceOptions.CceAccessKeyID, cceOptions.CceSecretAccessKey, cceOptions.CceEndpoint)
	if err != nil {
		return nil, err
	}
	// 配置连接超时时间为30秒
	client.Config.ConnectionTimeoutInMillis = 30 * 1000
	return &cceClient{client: client, clusterID: cceOptions.ClusterID}, nil
}

// CceClient 封装cce 相关操作
type cceClient struct {
	client    *ccev2.Client
	clusterID string
}

// 监控打点
func (c *cceClient) metricCallResult(method string, err error) {
	result := "fail"
	if err == nil {
		result = "success"
	}
	callCceCounter.WithLabelValues(result, method).Inc()
}

// ChangeInstanceGroupAutoscalerConfig 修改节点组最大副本数
func (c *cceClient) ChangeInstanceGroupAutoscalerConfig(enableAutoscaler bool, instanceGroupID string, maxReplicas int) (
	resp *ccev2.UpdateInstanceGroupClusterAutoscalerSpecResponse, err error) {
	args := &ccev2.UpdateInstanceGroupClusterAutoscalerSpecArgs{
		ClusterID:       c.clusterID,
		InstanceGroupID: instanceGroupID,
		Request: &ccev2.ClusterAutoscalerSpec{
			Enabled:              enableAutoscaler,
			MinReplicas:          0, // 最小副本数写死为0
			MaxReplicas:          maxReplicas,
			ScalingGroupPriority: 1,
		},
	}

	defer func() { c.metricCallResult("ChangeInstanceGroupAutoscalerConfig", err) }()

	resp, err = c.client.UpdateInstanceGroupClusterAutoscalerSpec(args)
	return
}

// GetInstanceGroupDetail 获取节点组详情
func (c *cceClient) GetInstanceGroupDetail(instanceGroupID string) (resp *ccev2.GetInstanceGroupResponse, err error) {
	args := &ccev2.GetInstanceGroupArgs{
		ClusterID:       c.clusterID,
		InstanceGroupID: instanceGroupID,
	}

	defer func() { c.metricCallResult("GetInstanceGroupDetail", err) }()

	resp, err = c.client.GetInstanceGroup(args)
	return
}

// ChangeInstanceGroupReplicas 修改节点组副本数
// func (c *CceClient) ChangeInstanceGroupReplicas(clusterID, instanceGroupID string, expectReplicas int) (*ccev2.UpdateInstanceGroupReplicasResponse, error) {
// 	args := &ccev2.UpdateInstanceGroupReplicasArgs{
// 		ClusterID:       clusterID,
// 		InstanceGroupID: instanceGroupID,
// 		Request: &ccev2.UpdateInstanceGroupReplicasRequest{
// 			Replicas:       expectReplicas,
// 			DeleteInstance: false, // 不删除实例
// 		},
// 	}
// 	return c.client.UpdateInstanceGroupReplicas(args)
// }

// RemoveInstanceGroupNodes 指定节点删除
func (c *cceClient) RemoveInstanceGroupNodes(instanceGroupID string, removeInstances []string) (resp *ccev2.CreateTaskResp, err error) {
	args := &ccev2.CreateScaleDownInstanceGroupTaskArgs{
		ClusterID:            c.clusterID,
		InstanceGroupID:      instanceGroupID,
		InstancesToBeRemoved: removeInstances,
	}

	defer func() { c.metricCallResult("RemoveInstanceGroupNodes", err) }()

	resp, err = c.client.CreateScaleDownInstanceGroupTask(args)
	return
}

// GetInstanceGroupList 获取节点组列表
func (c *cceClient) GetInstanceGroupList() (resp *ccev2.ListInstanceGroupResponse, err error) {
	req := &ccev2.ListInstanceGroupsArgs{
		ClusterID:  c.clusterID,
		ListOption: &ccev2.InstanceGroupListOption{},
	}
	defer func() { c.metricCallResult("GetInstanceGroupList", err) }()
	resp, err = c.client.ListInstanceGroups(req)
	return
}

// GetInstanceGroupNodes 获取节点组下node列表
func (c *cceClient) GetInstanceGroupNodes(instanceGroupID string) (resp *ccev2.ListInstancesByInstanceGroupIDResponse, err error) {
	req := &ccev2.ListInstanceByInstanceGroupIDArgs{
		ClusterID:       c.clusterID,
		InstanceGroupID: instanceGroupID,
		// TODO : 暂时写死，后续cce 提供接口，按照nodeName 去删除节点组节点
		PageSize: 1000,
	}
	defer func() { c.metricCallResult("GetInstanceGroupNodes", err) }()
	resp, err = c.client.ListInstancesByInstanceGroupID(req)
	return
}

func (c *cceClient) GetKubeconfig(clusterID string) (*ccev2.GetKubeConfigResponse, error) {
	req := &ccev2.GetKubeConfigArgs{
		ClusterID:      clusterID,
		KubeConfigType: ccev2.KubeConfigTypeInternal,
	}
	return c.client.GetKubeConfig(req)
}

func (c *cceClient) GetInstance(instanceID string) (resp *ccev2.GetInstanceResponse, err error) {
	req := &ccev2.GetInstanceArgs{
		ClusterID:  c.clusterID,
		InstanceID: instanceID,
	}
	defer func() { c.metricCallResult("GetInstance", err) }()
	resp, err = c.client.GetInstance(req)
	return
}

// CreateInstances 创建节点
func (c *cceClient) CreateInstances(instanceSet []*ccev2.InstanceSet) (resp *ccev2.CreateInstancesResponse, err error) {
	req := &ccev2.CreateInstancesArgs{
		ClusterID: c.clusterID,
		Instances: instanceSet,
	}
	defer func() { c.metricCallResult("CreateInstances", err) }()
	resp, err = c.client.CreateInstances(req)
	return
}

// DeleteInstances 指定节点删除
func (c *cceClient) DeleteInstances(removeCCEInstanceIds []string) (resp *ccev2.DeleteInstancesResponse, err error) {
	// 说明: 由于节点组会维护其内节点数达到buffer值, 若调用该接口删除节点组中的节点, 若删除后节点数量<buffer可能会触发自动扩容, 故节点组内节点缩容不建议使用该接口
	req := &ccev2.DeleteInstancesArgs{
		ClusterID: c.clusterID,
		DeleteInstancesRequest: &ccev2.DeleteInstancesRequest{
			InstanceIDs: removeCCEInstanceIds,
		},
	}
	defer func() { c.metricCallResult("DeleteInstances", err) }()
	resp, err = c.client.DeleteInstances(req)
	return
}

// InstanceGroupTasks 获取节点组task列表
func (c *cceClient) InstanceGroupTasks(instanceGroupID string, pageNo, pageSize int) (resp *ccev2.ListTaskResp, err error) {

	req := &ccev2.ListTasksArgs{
		TaskType: types.TaskTypeInstanceGroupReplicas,
		TargetID: instanceGroupID,
		PageNo:   pageNo,
		PageSize: pageSize,
	}
	defer func() { c.metricCallResult("InstanceGroupTasks", err) }()

	resp, err = c.client.ListTasks(req)
	return
}
