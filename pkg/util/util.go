package util

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	"k8s.io/klog/v2"
)

func GetInstanceGroupCm(cmLister corev1_listers.ConfigMapLister) (map[string]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
	result := make(map[string]entity.InstanceGroupCm)
	allIgList := make([]entity.InstanceGroupCm, 0)

	igConfigMap, err := cmLister.ConfigMaps(entity.InstanceGroupCmNamespace).Get(entity.InstanceGroupCmName)

	if err != nil {
		klog.Errorf("get instanceGroupConfig cm err %+v ", err)
		return result, allIgList, err
	}
	if len(igConfigMap.Data) > 0 {
		if data, ok := igConfigMap.Data[entity.InstanceGropuCmDataKey]; ok {
			_ = json.Unmarshal([]byte(data), &allIgList)
		}
	}
	for _, ig := range allIgList {
		result[ig.InstanceGroupId] = ig
	}
	return result, allIgList, nil
}

// ParseTidalTime 解析潮汐时间 eg: 6：30
func ParseTidalTime(tidalTime string) (int, int, error) {
	err := fmt.Errorf("ParseTidalTime %s err ", tidalTime)
	splits := strings.Split(tidalTime, ":")
	if len(splits) != 2 {
		klog.Error(err)
		return 0, 0, err
	}

	hour, err := strconv.Atoi(splits[0])
	if err != nil {
		klog.Error(err)
		return 0, 0, err
	}
	minute, err := strconv.Atoi(splits[1])
	if err != nil {
		klog.Error(err)
		return 0, 0, err
	}
	return hour, minute, nil
}

// IsInTidalNodeRunTime 是否在潮汐node 运行时间段内
func IsInTidalNodeRunTime(startRunTime string, endRunTime string) bool {
	startHour, startMinute, err := ParseTidalTime(startRunTime)
	if err != nil {
		return false
	}
	endHour, endMinute, err := ParseTidalTime(endRunTime)
	if err != nil {
		return false
	}

	now := time.Now()

	currentDayMinute := now.Hour()*60 + now.Minute()
	startDayMinute := startHour*60 + startMinute

	endDayMinute := endHour*60 + endMinute

	// 说明跨天了
	if startHour > endHour {
		return currentDayMinute >= startDayMinute || currentDayMinute <= endDayMinute
	}

	return currentDayMinute >= startDayMinute && currentDayMinute <= endDayMinute
}
