/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package eni

import (
	"bytes"
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
)

type BatchPrivateIPArgs struct {
	EniID                 string   `json:"-"`                               // URL路径参数，不在JSON中传递
	PrivateIPAddresses    []string `json:"privateIpAddresses"`              // 指定IP地址列表（IPv4或IPv6）
	PrivateIPAddressCount int      `json:"privateIpAddressCount,omitempty"` // 自动分配数量
	IsIpv6                bool     `json:"isIpv6"`                          // 区分IPv4/IPv6
}

type BatchAddPrivateIPResult struct {
	PrivateIPAddresses []string `json:"privateIpAddresses"`
}

// BatchAddPrivateIP - batch add private ips
//
// PARAMS:
//   - args: the arguments to batch add private ips, property PrivateIpAddresses or PrivateIpAddressCount is required;
//     when PrivateIpAddressCount is set, private ips will be auto allocated,
//     and if you want assign private ips, please just set PrivateIpAddresses;
//
// RETURNS:
//   - *BatchAddPrivateIPResult: the private ips
//   - error: nil if success otherwise the specific error
func (c *Client) BatchAddPrivateIP(ctx context.Context, args *BatchPrivateIPArgs, signOpt *bce.SignOption) (*BatchAddPrivateIPResult, error) {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	bodyContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	path := "v1/eni/" + args.EniID + "/privateIp/batchAdd"
	req, err := bce.NewRequest("POST", c.GetURL(path, nil), bytes.NewBuffer(bodyContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return nil, err

	}
	bodyContent, err = resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	response := &BatchAddPrivateIPResult{}
	err = json.Unmarshal(bodyContent, response)
	if err != nil {
		return nil, err
	}

	return response, nil
}
