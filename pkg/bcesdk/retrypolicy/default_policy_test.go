// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/03/21 11:30:00, by <EMAIL>, 从 core.go 中移出来
*/
/*
DESCRIPTION
ce-sdk-go defaultRetryPolicy 单测
*/

package retrypolicy

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	bceerror "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/error"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/util"
)

func TestGetMaxErrorRetry(t *testing.T) {
	expected := 3

	retryPolicy := &DefaultRetryPolicy{
		MaxErrorRetry: expected,
	}

	if retryPolicy.MaxErrorRetry != expected {
		t.Error(util.FormatTest("GetMaxErrorRetry", strconv.Itoa(retryPolicy.MaxErrorRetry), strconv.Itoa(expected)))
	}
}

func TestGetMaxDelay(t *testing.T) {
	expected := 20

	retryPolicy := &DefaultRetryPolicy{
		MaxDelay: expected,
	}

	if retryPolicy.MaxDelay != expected {
		t.Error(util.FormatTest("GetMaxDelay", strconv.Itoa(retryPolicy.MaxDelay), strconv.Itoa(expected)))
	}
}

func TestGetDelayBeforeNextRetry(t *testing.T) {
	maxErrorRetry := 3
	maxDelay := 20

	retryPolicy := DefaultRetryPolicy{
		MaxErrorRetry: maxErrorRetry,
		MaxDelay:      maxDelay,
	}

	delay := retryPolicy.GetDelayBeforeNextRetry(context.Background(), errors.New("Unknown Error"), 5)
	if delay != -1 {
		t.Error(util.FormatTest("GetDelayBeforeNextRetry", delay.String(), strconv.Itoa(-1)))
	}

	delay = retryPolicy.GetDelayBeforeNextRetry(context.Background(), &bceerror.Error{StatusCode: http.StatusInternalServerError}, 1)
	expected := (1 << 1) * 300 * time.Millisecond
	if delay != expected {
		t.Error(util.FormatTest("GetDelayBeforeNextRetry 1<<1", delay.String(), expected.String()))
	}

	delay = retryPolicy.GetDelayBeforeNextRetry(context.Background(), &bceerror.Error{StatusCode: http.StatusServiceUnavailable}, 2)
	expected = (1 << 2) * 300 * time.Millisecond
	if delay != expected {
		t.Error(util.FormatTest("GetDelayBeforeNextRetry 1<<2", delay.String(), expected.String()))
	}

	maxDelay = 1
	retryPolicy = DefaultRetryPolicy{
		MaxErrorRetry: maxErrorRetry,
		MaxDelay:      maxDelay,
	}

	delay = retryPolicy.GetDelayBeforeNextRetry(context.Background(), &bceerror.Error{StatusCode: http.StatusServiceUnavailable}, 2)
	expected = retryPolicy.getMaxDelay()

	if delay != expected {
		t.Error(util.FormatTest("GetDelayBeforeNextRetry", delay.String(), expected.String()))
	}
}

func TestShouldRetry(t *testing.T) {
	maxErrorRetry := 3
	maxDelay := 20
	retryPolicy := DefaultRetryPolicy{
		MaxErrorRetry: maxErrorRetry,
		MaxDelay:      maxDelay,
	}

	expected := false
	unknownError := errors.New("Unknown Error")
	shouldRetry := retryPolicy.shouldRetry(context.Background(), unknownError, maxErrorRetry+1)

	if shouldRetry != expected {
		t.Error(util.FormatTest("shouldRetry", strconv.FormatBool(shouldRetry), strconv.FormatBool(expected)))
	}

	expected = true
	shouldRetry = retryPolicy.shouldRetry(context.Background(), unknownError, maxErrorRetry-1)

	if shouldRetry != expected {
		t.Error(util.FormatTest("shouldRetry", strconv.FormatBool(shouldRetry), strconv.FormatBool(expected)))
	}

	expected = false
	bceError := &bceerror.Error{}
	shouldRetry = retryPolicy.shouldRetry(context.Background(), bceError, maxErrorRetry-1)

	if shouldRetry != expected {
		t.Error(util.FormatTest("shouldRetry", strconv.FormatBool(shouldRetry), strconv.FormatBool(expected)))
	}

	expected = true
	bceError.StatusCode = http.StatusInternalServerError
	shouldRetry = retryPolicy.shouldRetry(context.Background(), bceError, maxErrorRetry-1)

	if shouldRetry != expected {
		t.Error(util.FormatTest("shouldRetry", strconv.FormatBool(shouldRetry), strconv.FormatBool(expected)))
	}

	bceError.StatusCode = http.StatusServiceUnavailable
	shouldRetry = retryPolicy.shouldRetry(context.Background(), bceError, maxErrorRetry-1)

	if shouldRetry != expected {
		t.Error(util.FormatTest("shouldRetry", strconv.FormatBool(shouldRetry), strconv.FormatBool(expected)))
	}
}
