package entity

import (
	"fmt"
	"reflect"
	"testing"
	"time"
)

func TestEqual(t *testing.T) {

	add := PatchTypeUpdate

	res := reflect.DeepEqual(add, "update")
	fmt.Println(res)

	m1 := make(map[string]string)
	m1["add"] = "add"

	m2 := make(map[string]interface{})
	m2["add"] = "add"
	res1 := reflect.DeepEqual(m1, m2)
	fmt.Println(res1)

	val := m1["xx"]

	fmt.Println(val == "xx")
	a := PatchType("xx")
	fmt.Println(a)

	s := NodeState("")
	fmt.Println(s == NodeStateEmpty)
}

func TestTime(t *testing.T) {
	now := time.Now()
	u := now.Unix()

	fmt.Println(now)
	fmt.Println(u)

	covert := time.Unix(u, 0)

	fmt.Println(covert)

	time.Sleep(3 * time.Second)
	d, _ := time.ParseDuration("2s")
	res := time.Now().After(covert.Add(d))
	fmt.Println(res)
}

func TestBuildPatchValue(t *testing.T) {
	updatePatch := BuildUpdatePatchValue("updateKey", "updateValue")
	deletePatch := BuildDeletePatchValue("deleteKey")
	fmt.Println(updatePatch)
	fmt.Println(deletePatch)
}
