package entity

import (
	"encoding/json"
	"fmt"
	"testing"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
)

var (
	bciBidMarketPod = &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns1", "bciBidPod")),
			Name:      "bciBidPod",
			Namespace: "ns1",
			Annotations: map[string]string{
				NodeLabelInstanceTemplateKey: "ig1",
				BciBidModeKey:                NodeLabelBidModeMarketValue,
				BciBidTimeOutKey:             "60",
				BciBidReleaseEIPKey:          "False",
				BciBidReleaseCDSKey:          "False",
			},
		},
		Spec: corev1.PodSpec{
			NodeSelector: map[string]string{
				NodeLabelInstanceTemplateKey: "ig1",
				TenantLockKey:                "user1",
				NodeLabelBidModeKey:          NodeLabelBidModeMarketValue,
			},
			NodeName: "bidNode1",
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
		},
	}
)

func TestPodInfo(t *testing.T) {
	fmt.Println(IsBciBidPod(bciBidMarketPod))
	fmt.Println(IsBciInstanceGroupPodByNodeSelector(bciPod))
	fmt.Println(IsDaemonsetRunningPod(daemonsetPod))

	NewPendingPod(bciBidMarketPod)
	bytes, _ := json.Marshal(NewPendingPod(bciBidMarketPod))
	fmt.Println(string(bytes))

	// 竞价相关信息
	fmt.Println(PodBidMode(bciBidMarketPod))
	fmt.Println(PodBidMode(&corev1.Pod{}))
	fmt.Println(PodBidPrice(bciBidMarketPod))
	fmt.Println(PodBidPrice(&corev1.Pod{}))
	fmt.Println(BidPodPriceStatisticKey(bciBidMarketPod))
}
