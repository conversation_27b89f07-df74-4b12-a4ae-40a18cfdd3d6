package entity

import (
	"time"

	"github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	v1 "k8s.io/api/core/v1"
)

const (
	// BciChargingTypeKey 付费模式
	BciChargingTypeKey = "bci_internal_chargingType"

	// BciBidModeKey 竞价模式
	BciBidModeKey = "bci_internal_bidModel"
	// BciBidPriceKey 用户的出价
	BciBidPriceKey = "bci_internal_bidPrice"
	// BciBidTimeOutKey 竞价超时时间
	BciBidTimeOutKey = "bci_internal_bidTimeout"
	// BciBidReleaseEIPKey 竞价实例被动释放时, 是否联动释放实例EIP
	BciBidReleaseEIPKey = "bci_internal_bidReleaseEIP"
	// BciBidReleaseCDSKey 竞价实例被动释放时, 是否联动释放实例CDS
	BciBidReleaseCDSKey = "bci_internal_bidReleaseCDS"
	//bci3.0 多租户共享节点
	Bci3PodNodeSelectorLock = "shared"
)

const (
	NotBciPod   = -1
	BciPodByBid = iota
	BciPodByNodeSelector
	BciPodByNodeAffinity
)

type PendingPod struct {
	Namespace   string
	Name        string
	PendingTime time.Duration
}

func NewPendingPod(pod *v1.Pod) PendingPod {
	return PendingPod{
		Namespace:   pod.Namespace,
		Name:        pod.Name,
		PendingTime: time.Now().Sub(pod.CreationTimestamp.Time)}
}

// 获取bci pod类型，根据bci pod不同的实现方式，将bci pod划分成3类
func GetBciPodType(pod *v1.Pod) int {
	if IsBciBidPod(pod) {
		return BciPodByBid
	} else if IsBciInstanceGroupPodByNodeSelector(pod) {
		return BciPodByNodeSelector
	} else if IsBciInstanceGroupPodByNodeAffinity(pod) {
		return BciPodByNodeAffinity
	}
	return NotBciPod
}

// IsBciBidPod 是否是bci竞价实例管理的用户pod
func IsBciBidPod(pod *v1.Pod) bool {
	if pod == nil {
		return false
	}

	nodeSelector := pod.Spec.NodeSelector
	if nodeSelector == nil {
		return false
	}

	instanceTemplateID := nodeSelector[NodeLabelInstanceTemplateKey]
	tenantID := nodeSelector[TenantLockKey]

	return instanceTemplateID != "" && tenantID != ""
}

// IsBciInstanceGroupPodByNodeSelector 是否是bci节点组管理的用户pod
func IsBciInstanceGroupPodByNodeSelector(pod *v1.Pod) bool {
	if pod == nil {
		return false
	}

	nodeSelector := pod.Spec.NodeSelector
	if nodeSelector == nil {
		return false
	}
	tenantID := nodeSelector[TenantLockKey]
	if tenantID == "" {
		return false
	}
	// 如果nodeSelector中存在InstanceGroupKey，即为bci pod
	instanceGroup := nodeSelector[InstanceGroupKey]
	if instanceGroup == "" {
		return false
	}

	return true
}

// IsBciInstanceGroupPodByNodeAffinity 是否是bci节点组管理的用户pod
func IsBciInstanceGroupPodByNodeAffinity(pod *v1.Pod) bool {
	if pod == nil {
		return false
	}

	nodeSelector := pod.Spec.NodeSelector
	if nodeSelector == nil {
		return false
	}
	tenantID := nodeSelector[TenantLockKey]
	if tenantID == "" {
		return false
	}
	// 从nodeAffinity中找到InstanceGroupKey，也是bci pod
	affinity := pod.Spec.Affinity
	if affinity == nil || affinity.NodeAffinity == nil || affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution == nil {
		return false
	}
	terms := affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms
	if terms == nil || len(terms) == 0 {
		return false
	}
	for _, t := range terms {
		if t.MatchExpressions == nil || len(t.MatchExpressions) == 0 {
			continue
		}
		for _, matchExp := range t.MatchExpressions {
			if matchExp.Key == InstanceGroupKey {
				return true
			}
		}
	}
	return false
}

func GetInstanceGroupIDFromNodeAffinity(pod *v1.Pod) []string {
	return GetInstanceGroupIDByNodeAffinity(pod.Spec.Affinity)
}

func GetInstanceGroupIDByNodeAffinity(affinity *v1.Affinity) []string {
	instanceGroupIDs := make([]string, 0)
	// 从nodeAffinity中找到InstanceGroupKey
	if affinity == nil || affinity.NodeAffinity == nil || affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution == nil {
		return instanceGroupIDs
	}
	terms := affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms
	if terms == nil || len(terms) == 0 {
		return instanceGroupIDs
	}
	for _, t := range terms {
		if t.MatchExpressions == nil || len(t.MatchExpressions) == 0 {
			continue
		}
		for _, matchExp := range t.MatchExpressions {
			if matchExp.Key == InstanceGroupKey {
				instanceGroupIDs = append(instanceGroupIDs, matchExp.Values...)
				break
			}
		}
	}
	return instanceGroupIDs
}

// PodBidMode 竞价类型的pod的竞价模式
func PodBidMode(pod *v1.Pod) string {
	podAnnotation := pod.Annotations
	if podAnnotation == nil || len(podAnnotation) == 0 {
		return ""
	}
	return podAnnotation[BciBidModeKey]
}

// PodBidPrice 竞价类型的pod的用户出价
func PodBidPrice(pod *v1.Pod) string {
	podAnnotation := pod.Annotations
	if podAnnotation == nil || len(podAnnotation) == 0 {
		return ""
	}
	return podAnnotation[BciBidPriceKey]
}

// BidPodPriceStatisticKey 竞价类型的pod其价格统计信息的Key
func BidPodPriceStatisticKey(pod *v1.Pod) string {
	if PodBidMode(pod) == string(types.BidModeMarketPrice) {
		return NodeLabelBidModeMarketValue
	}
	return PodBidPrice(pod)
}

type ZoneMapDetail struct {
	ID           int    `json:"id"`
	ZoneID       string `json:"zoneId"`
	AccountID    string `json:"accountId"`
	LogicalZone  string `json:"logicalZone"`
	PhysicalZone string `json:"physicalZone"`
	SubnetUUID   string `json:"subnetUuid"`
	Type         string `json:"type"`
}

type SubnetVo struct {
	Name        string `json:"name"`
	SubnetID    string `json:"subnetId"`
	Az          string `json:"az"`
	Cidr        string `json:"cidr"`
	VpcID       string `json:"vpcId"`
	VpcShortID  string `json:"vpcShortId"`
	SubnetUUID  string `json:"subnetUuid"`
	AccountID   string `json:"accountId"`
	SubnetType  int    `json:"subnetType"`
	Description string `json:"description"`
	ShortID     string `json:"shortId"`
}

type ZoneSubnetInfo struct {
	ZoneInfo   ZoneMapDetail `json:"zoneMapDetail"`
	SubnetInfo SubnetVo      `json:"subnetVo"`
}
