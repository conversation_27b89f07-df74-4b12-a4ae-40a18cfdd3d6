package entity

import "testing"

func TestNodeState(t *testing.T) {
	s := NodeState("")
	if s != NodeStateEmpty {
		t.<PERSON><PERSON>("node state is empty")
	}
	s.String()
	gcState := NodeGcStateInit
	gcState.String()

	result := NodeStatePodExit.CanRunPlaceholderPod()
	if result {
		t.<PERSON>rror("NodeStatePodExit CanRunPlaceholderPod get true")
		return
	}
	result = NodeStateRunPod.CanRunPlaceholderPod()
	if result {
		t.<PERSON>rror("NodeStateRunPod CanRunPlaceholderPod get true")
		return
	}
	result = NodeStateNotReady.CanRunPlaceholderPod()
	if result {
		t.Error("NodeStateNotReady CanRunPlaceholderPod get true")
		return
	}
	
	result = NodeStateInit.CanRunPlaceholderPod()
	if !result {
		t.Error("NodeStateInit CanRunPlaceholderPod get true")
		return
	}
}
