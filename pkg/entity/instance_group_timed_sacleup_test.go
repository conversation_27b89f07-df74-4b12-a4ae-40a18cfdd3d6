package entity

import (
	"fmt"
	"testing"
	"time"
)

func TestScaleUpTime(t *testing.T) {
	u, err := ParseIgAutoScaleUpTime("2023-01-01 23:04:05")
	fmt.Println(err)
	fmt.Println(u)
}

func TestTimedScaleUpParams(t *testing.T) {
	igMap := map[string]InstanceGroupCm{
		"tidal": {
			InstanceGroupId: "tidal",
		},
	}
	cm := &InstanceGroupTimedScaleUpCm{}
	cm.MaxScaleupNodeCount = -1
	err := cm.CheckParams(igMap)
	if err == nil {
		t.Errorf("expect err is not empty")
		return
	}

	cm.MaxScaleupNodeCount = 50
	err = cm.CheckParams(igMap)
	if err != nil {
		t.Errorf("expect err is empty")
		return
	}

	params := &TimedScaleUpParams{
		// InstanceGroupID: "test",
	}
	err = params.CheckParams()
	if err == nil {
		t.Errorf("expect err is not empty")
		return
	}
	params.InstanceGroupID = "tidal"
	err = params.CheckParams()
	if err == nil {
		t.Errorf("expect err is not empty")
		return
	}
	params.ExpectNodeCount = -1
	err = params.CheckParams()
	if err == nil {
		t.Errorf("expect err is not empty")
		return
	}
	params.AutoScaleUpStartTime = "2023-05-05 23:00:00"
	err = params.CheckParams()
	if err == nil {
		t.Errorf("expect err is not empty")
		return
	}

	params.AutoScaleUpEndTime = "2023-05-06 6:30:00"
	err = params.CheckParams()
	if err == nil {
		t.Errorf("expect err is not empty")
		return
	}

	params.ExpectScaleUpSuccessTime = "2023-05-05 23:30:00"
	params.ExpectNodeCount = 300
	err = params.CheckParams()
	if err != nil {
		t.Errorf("expect err is not empty")
		return
	}

	params.ExpectAutoDeleteNodeTime = "xx"
	err = params.CheckParams()
	if err == nil {
		t.Errorf("expect err is not empty")
		return
	}

	cm.InstanceGroups = []*TimedScaleUpParams{params}

	err = cm.CheckParams(igMap)
	if err == nil {
		t.Errorf("expect err is not empty")
		return
	}
	params.ExpectAutoDeleteNodeTime = "2023-05-06 7:30:00"
	err = cm.CheckParams(igMap)
	if err != nil {
		t.Errorf("expect err is empty")
		return
	}

	// 配置重复的场景
	cm.InstanceGroups = append(cm.InstanceGroups, params)
	err = cm.CheckParams(igMap)
	if err == nil {
		t.Errorf("expect err is not empty")
		return
	}

	cm.InstanceGroups = []*TimedScaleUpParams{params}

	params.InstanceGroupID = "xx"
	err = cm.CheckParams(igMap)
	if err == nil {
		t.Errorf("expect err is not empty")
		return
	}

}

func TestIgScaleUpTime(t *testing.T) {
	subOneHourDuration, _ := time.ParseDuration("-1h")
	subTwoHourDuration, _ := time.ParseDuration("-2h")
	addOneHourDuration, _ := time.ParseDuration("1h")

	autoScaleUpStartTime := time.Now().Add(subTwoHourDuration)
	expectScaleUpSuccessTime := time.Now().Add(subOneHourDuration)
	autoScaleUpEndTime := time.Now().Add(addOneHourDuration)

	params := TimedScaleUpParams{
		InstanceGroupID:          "tidal",
		AutoScaleUpStartTime:     autoScaleUpStartTime.Format("2006-01-02 15:04:05"),
		AutoScaleUpEndTime:       autoScaleUpEndTime.Format("2006-01-02 15:04:05"),
		ExpectScaleUpSuccessTime: expectScaleUpSuccessTime.Format("2006-01-02 15:04:05"),
		ExpectNodeCount:          100,
	}
	res := params.InTimedIgScaleUpTime()
	if !res {
		t.Errorf("expect in TimedIgScaleUpTime")
		return
	}
	res = params.AfterExpectAutoDeleteNodeTime()
	if res {
		t.Errorf("expect not in AfterExpectAutoDeleteNodeTime")
		return
	}
	params.ExpectAutoDeleteNodeTime = autoScaleUpStartTime.Format("2006-01-02 15:04:05")
	res = params.AfterExpectAutoDeleteNodeTime()
	if !res {
		t.Errorf("expect in AfterExpectAutoDeleteNodeTime")
		return
	}

	res = params.AfterExpectScaleUpSuccessTime()
	if !res {
		t.Errorf("expect in AfterExpectScaleUpSuccessTime")
		return
	}
}
