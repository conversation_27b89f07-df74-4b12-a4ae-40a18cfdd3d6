package entity

import (
	"fmt"
	"time"

	"k8s.io/klog/v2"
)

const (
	TimedSacleUPCmName      = "instance-group-timed-scaleup-config"
	TimedSacleUPCmNamespace = "kube-system"
)

type InstanceGroupTimedScaleUpCm struct {
	// 扩容梯度值
	MaxScaleupNodeCount int                   `json:"maxScaleupNodeCount"`
	InstanceGroups      []*TimedScaleUpParams `json:"instanceGroups"`
}

func (cm *InstanceGroupTimedScaleUpCm) CheckParams(igMap map[string]InstanceGroupCm) error {
	if cm.MaxScaleupNodeCount <= 0 {
		return fmt.Errorf("MaxScaleupNodeCount error")
	}
	configIgMap := make(map[string]struct{})
	for _, params := range cm.InstanceGroups {
		if _, ok := igMap[params.InstanceGroupID]; !ok {
			return fmt.Errorf("instanceGroupID %s not exist in cce ", params.InstanceGroupID)
		}
		// 校验配置是否重复
		if _, ok := configIgMap[params.InstanceGroupID]; ok {
			return fmt.Errorf("instanceGroupID %s repeat in config ", params.InstanceGroupID)
		}
		configIgMap[params.InstanceGroupID] = struct{}{}
		if err := params.CheckParams(); err != nil {
			return err
		}
	}
	return nil
}

func (cm *InstanceGroupTimedScaleUpCm) ParseAutoScaleUpInstanceGroup(igMap map[string]InstanceGroupCm) (
	inAutoScaleIgMap, outAutoScaleIgMap map[string]*TimedScaleUpParams) {
	inAutoScaleIgMap = make(map[string]*TimedScaleUpParams)
	outAutoScaleIgMap = make(map[string]*TimedScaleUpParams)
	for _, params := range cm.InstanceGroups {
		if !params.InTimedIgScaleUpTime() || params.ExpectNodeCount <= 0 {
			outAutoScaleIgMap[params.InstanceGroupID] = params
			continue
		}
		if _, ok := igMap[params.InstanceGroupID]; ok {
			klog.V(4).Infof("timedSacleUPController instanceGroupID %s in auto sacle up time ", params.InstanceGroupID)
			inAutoScaleIgMap[params.InstanceGroupID] = params
		}
	}
	return
}

type TimedScaleUpParams struct {
	InstanceGroupID string `json:"instanceGroupId"`
	// 自动扩容开始时间
	AutoScaleUpStartTime string `json:"autoScaleUpStartTime"`
	// 自动扩容结束时间
	AutoScaleUpEndTime string `json:"autoScaleUpEndTime"`
	// 期望扩容成功到ExpectNodeCount时间
	ExpectScaleUpSuccessTime string `json:"expectScaleUpSuccessTime"`
	// 期望扩容到的node 副本数
	ExpectNodeCount int `json:"expectNodeCount"`
	// 到达时间后自动设置节点组buffer 为0，不设置标示不自动缩容
	ExpectAutoDeleteNodeTime string `json:"expectAutoDeleteNodeTime"`
}

// 检查参数是否正确
func (p *TimedScaleUpParams) CheckParams() error {
	if p.InstanceGroupID == "" {
		return fmt.Errorf("InstanceGroupId is empty")
	}
	if p.AutoScaleUpStartTime == "" || p.AutoScaleUpEndTime == "" ||
		p.ExpectScaleUpSuccessTime == "" {
		return fmt.Errorf("InstanceGroupId %s AutoScaleUp time is empty", p.InstanceGroupID)
	}
	if p.ExpectNodeCount < 0 {
		return fmt.Errorf("InstanceGroupId %s ExpectNodeCount is less 0", p.InstanceGroupID)
	}

	// 校验时间
	autoScaleUpStartUnixTime, err := ParseIgAutoScaleUpTime(p.AutoScaleUpStartTime)
	if err != nil {
		return fmt.Errorf("InstanceGroupId %s AutoScaleUpStartTime format is err", p.InstanceGroupID)
	}

	autoScaleUpEndUnixTime, err := ParseIgAutoScaleUpTime(p.AutoScaleUpEndTime)
	if err != nil {
		return fmt.Errorf("InstanceGroupId %s AutoScaleUpEndTime format is err", p.InstanceGroupID)
	}

	expectScaleUpSuccessUnixTime, err := ParseIgAutoScaleUpTime(p.ExpectScaleUpSuccessTime)
	if err != nil {
		return fmt.Errorf("InstanceGroupId %s ExpectScaleUpSuccessTime format is err", p.InstanceGroupID)
	}

	if p.ExpectAutoDeleteNodeTime == "" {
		if (autoScaleUpStartUnixTime < expectScaleUpSuccessUnixTime) &&
			(expectScaleUpSuccessUnixTime < autoScaleUpEndUnixTime) {
			return nil
		}
		return fmt.Errorf("InstanceGroupId %s auto scale up time check failed .", p.InstanceGroupID)
	}

	expectAutoDeleteNodeUnixTime, err := ParseIgAutoScaleUpTime(p.ExpectAutoDeleteNodeTime)
	if err != nil {
		return fmt.Errorf("InstanceGroupId %s ExpectAutoDeleteNodeTime format is err", p.InstanceGroupID)
	}

	if (autoScaleUpStartUnixTime < expectScaleUpSuccessUnixTime) &&
		(expectScaleUpSuccessUnixTime < autoScaleUpEndUnixTime) &&
		(autoScaleUpEndUnixTime < expectAutoDeleteNodeUnixTime) {
		return nil
	}
	return fmt.Errorf("InstanceGroupId %s auto scale up time check failed .", p.InstanceGroupID)
}

func ParseIgAutoScaleUpTime(scaleUpTime string) (int64, error) {
	loc, _ := time.LoadLocation("Local")

	parseTime, err := time.ParseInLocation("2006-01-02 15:04:05", scaleUpTime, loc)
	if err != nil {
		return -1, err
	}

	unixTime := parseTime.Unix()
	return unixTime, nil
}

func (p *TimedScaleUpParams) InTimedIgScaleUpTime() bool {
	startUnix, err := ParseIgAutoScaleUpTime(p.AutoScaleUpStartTime)
	if err != nil {
		return false
	}
	endUnix, err := ParseIgAutoScaleUpTime(p.AutoScaleUpEndTime)
	if err != nil {
		return false
	}
	nowUnix := time.Now().Unix()

	return startUnix < nowUnix && endUnix > nowUnix
}

func (p *TimedScaleUpParams) AfterExpectAutoDeleteNodeTime() bool {
	if p.ExpectAutoDeleteNodeTime == "" {
		return false
	}
	expectAutoDeleteNodeUnixTime, err := ParseIgAutoScaleUpTime(p.ExpectAutoDeleteNodeTime)
	if err != nil {
		return false
	}
	nowUnixTime := time.Now().Unix()
	return nowUnixTime > expectAutoDeleteNodeUnixTime
}

func (p *TimedScaleUpParams) AfterExpectScaleUpSuccessTime() bool {
	if !p.InTimedIgScaleUpTime() {
		return false
	}

	expectScaleUpSuccessUnixTime, err := ParseIgAutoScaleUpTime(p.ExpectScaleUpSuccessTime)
	if err != nil {
		return false
	}

	return time.Now().Unix() > expectScaleUpSuccessUnixTime
}
