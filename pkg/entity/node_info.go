package entity

import (
	"strings"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	v1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"
)

type podType string

const (
	podTypeBci       podType = "bci"
	podTypeSystem    podType = "system"
	podTypeDaemonset podType = "daemonset"
)

const (
	// NodeLabelChargingTypeKey 指定节点的付费模式, value对应 bccapi.PaymentTimingType
	NodeLabelChargingTypeKey = "charging-type"

	// NodeLabelInstanceTemplateKey 指定竞价实例节点的模板, value对应一个instanceGroupId
	NodeLabelInstanceTemplateKey = "instance-template-id"
	// NodeLabelBidModeKey 竞价模式, market跟随市场出价, price用户自定义出价
	NodeLabelBidModeKey         = "bid-mode"
	NodeLabelBidModeMarketValue = "market"
	NodeLabelBidModePriceValue  = "price"
	// NodeLabelBidPriceKey 用户的出价, value表示价格
	NodeLabelBidPriceKey = "bid-custom-price"

	// NodeLabelCCEInstanceIDKey 标记节点对应的CCEInstanceId
	NodeLabelCCEInstanceIDKey = "cce-instance-id"

	// NodeLabelCCEClusterID 由CCE创建的节点会自动添加cluster-id和cluster-role两个label
	NodeLabelCCEClusterID = "cluster-id"

	// NodeLabelCGPUPlugin 需要有该label,cGPU插件才会生效 disable/enable:GPU独占和显存共享模式
	NodeLabelCGPUPlugin = "cce.baidubce.com/gpu-share-device-plugin"
	// NodeLabelCGPUPriority 指定是否使用cGPU的优先级
	NodeLabelCGPUPriority = "cce.baidubce.com/baidu-cgpu-priority"
	// NodeLabelImageAccelerate 使用CCE自定义镜像加速
	NodeLabelImageAccelerate = "cce.baidubce.com/cce.cluster.enable-image-accelerate"

	NodeLabelBiddingNodeType        = "kubernetes.io/bidding-node-type"
	NodeLabelBiddingNodeTypeBidding = "bidding"
	NodeLabelBiddingNodeTypeTidal   = "tidal"
)

func (t podType) String() string {
	return string(t)
}

type podWarpper struct {
	Pod  *v1.Pod
	Type podType
}

// NodeInfo 封装node -> pod 信息
type NodeInfo struct {
	Name string
	Node *v1.Node

	State NodeState // 单测使用

	// 节点上的全部pod, 非BCI用户pod在instanceGroup的实例中被用来获取placeHolderPod, 在竞价实例中无用
	pods map[string]podWarpper

	podInfo func(pod *v1.Pod) (podKey string, podType podType)
}

// NewNodeInfo 创建node缓存
func NewNodeInfo(node *v1.Node) *NodeInfo {
	n := &NodeInfo{
		Name: node.Name,
		Node: node,
		pods: make(map[string]podWarpper),
	}

	n.podInfo = func(pod *v1.Pod) (podKey string, podType podType) {
		podKey = pod.Namespace + "/" + pod.Name
		// 是否是daemonset的pod
		if IsDaemonsetRunningPod(pod) {
			podType = podTypeDaemonset
			return
		}

		// 是否是bci用户pod
		if ret := GetBciPodType(pod); ret != NotBciPod {
			podType = podTypeBci
			return
		}
		// 是系统pod
		podType = podTypeSystem
		return
	}
	return n
}

// AddOrUpdatePod 添加或更新pod
func (n *NodeInfo) AddOrUpdatePod(pod *v1.Pod) {
	podKey, podType := n.podInfo(pod)
	n.pods[podKey] = podWarpper{
		Pod:  pod,
		Type: podType,
	}
}

// DeletePod 删除pod
func (n *NodeInfo) DeletePod(pod *v1.Pod) {
	podKey, _ := n.podInfo(pod)
	delete(n.pods, podKey)
}

// UpdateNode 更新node
func (n *NodeInfo) UpdateNode(node *v1.Node) {
	n.Node = node
}

// SyncNodePods 定期刷新node上pod状态，有可能丢失事件，导致pod 状态不对
func (n *NodeInfo) SyncNodePods(currentPods []*v1.Pod) {
	newPodMap := make(map[string]podWarpper)
	for _, pod := range currentPods {
		podKey, podType := n.podInfo(pod)
		newPodMap[podKey] = podWarpper{
			Pod:  pod,
			Type: podType,
		}
	}
	if len(newPodMap) != len(n.pods) {
		klog.Warningf("node %s SyncNodePods current pod len %+v is not equal cache pod len %+v maybe loss informer pod event ",
			n.Name, len(newPodMap), len(n.pods))
	}
	n.pods = newPodMap
}

// TODO 当bci pod 成功退出后，能否进行gc操作，failed 状态的pod是否是空节点

// EmptyNode 是否是空节点，空节点条件: pending 或 running状态的pod
func (n *NodeInfo) EmptyNode() bool {
	for _, pod := range n.pods {
		if pod.Type == podTypeBci && (pod.Pod.Status.Phase == v1.PodRunning || pod.Pod.Status.Phase == v1.PodPending) {
			return false
		}
	}
	return true
}

// CurrentNodeState 获取node当前状态机
func (n *NodeInfo) CurrentNodeState() NodeState {
	nodeLabels := n.Node.Labels
	if len(nodeLabels) == 0 {
		return NodeStateEmpty
	}
	nodeStatus := nodeLabels[NodeStateLabelKey]
	return NodeState(nodeStatus)
}

// NodeNotReady node 是否处于notReady 状态
func (n *NodeInfo) NodeNotReady() bool {
	nodeConditions := n.Node.Status.Conditions
	for _, condition := range nodeConditions {
		if condition.Type != v1.NodeReady {
			continue
		}
		if condition.Status == v1.ConditionFalse || condition.Status == v1.ConditionUnknown {
			return true
		}
	}
	return false
}

// InstanceGroup 获取节点组名称
func (n *NodeInfo) InstanceGroup() string {
	labels := n.Node.Labels
	if len(labels) == 0 {
		return ""
	}
	return labels[InstanceGroupKey]
}

// NodeLocked node 是否被用户绑定
func (n *NodeInfo) NodeLocked() bool {
	return n.NodeLockedAccount() != ""
}

// NodeLockedAccount 获取用户占用信息
func (n *NodeInfo) NodeLockedAccount() string {
	labels := n.Node.Labels
	if len(labels) == 0 {
		return ""
	}
	return labels[TenantLockKey]
}

// GetPlaceholderPod 获取node上运行的placeholder pod
func (n *NodeInfo) GetPlaceholderPod() []*v1.Pod {

	result := make([]*v1.Pod, 0)
	for _, pod := range n.pods {
		if pod.Type != podTypeSystem {
			continue
		}
		// placeholder 由deployment 创建，在kube-system ns下
		if IsReplicaSetRunningOrFailedPod(pod.Pod) && strings.Contains(pod.Pod.Name, PlaceholderSuffixStr) &&
			pod.Pod.Namespace == InstanceGroupCmNamespace {
			result = append(result, pod.Pod)
		}
	}

	return result
}

func (n *NodeInfo) GetBiddingNodeType() string {
	return n.Node.Labels[NodeLabelBiddingNodeType]
}

// GetBciPendingOrRunningPods 获取bci pending或running的pod
func (n *NodeInfo) GetBciPendingOrRunningPods() []*v1.Pod {
	result := make([]*v1.Pod, 0)

	for _, pod := range n.pods {
		if pod.Type != podTypeBci {
			continue
		}
		if pod.Pod.Status.Phase == v1.PodRunning || pod.Pod.Status.Phase == v1.PodPending {
			result = append(result, pod.Pod)
		}
	}
	return result
}

// IsDaemonsetRunningPod 是否是daemonset创建的pod
func IsDaemonsetRunningPod(pod *v1.Pod) bool {
	ownerReferences := pod.OwnerReferences
	if len(ownerReferences) > 0 {
		for _, owner := range ownerReferences {
			if owner.Kind == "DaemonSet" && pod.Status.Phase == v1.PodRunning {
				return true
			}
		}
	}

	return false
}

func IsDaemonSetPod(pod *v1.Pod) bool {
	ownerReferences := pod.OwnerReferences
	if len(ownerReferences) > 0 {
		for _, owner := range ownerReferences {
			if owner.Kind == "DaemonSet" {
				return true
			}
		}
	}
	return false
}

// IsReplicaSetRunningOrFailedPod 是否是ReplicaSet 管理的pod
func IsReplicaSetRunningOrFailedPod(pod *v1.Pod) bool {
	ownerReferences := pod.OwnerReferences
	if len(ownerReferences) > 0 {
		for _, owner := range ownerReferences {
			if owner.Kind == "ReplicaSet" && (pod.Status.Phase == v1.PodRunning ||
				pod.Status.Phase == v1.PodFailed) {
				// 当placeholder pod phase 为 failed时也删除掉
				return true
			}
		}
	}

	return false
}

// NodeIsBidding 节点是否是竞价实例的节点
func (n *NodeInfo) NodeIsBidding() bool {
	return n.NodeChargingType() == string(bccapi.PaymentTimingBidding)
}

// NodeChargingType 获取节点的付费模式, 为空表示后付费(Postpaid)
func (n *NodeInfo) NodeChargingType() string {
	labels := n.Node.Labels
	if len(labels) == 0 {
		return ""
	}
	return labels[NodeLabelChargingTypeKey]
}

// InstanceSpecTemplateID 获取竞价实例规格模板(InstanceGroupId)名称
func (n *NodeInfo) InstanceSpecTemplateID() string {
	labels := n.Node.Labels
	if len(labels) == 0 {
		return ""
	}
	return labels[NodeLabelInstanceTemplateKey]
}

// BidMode 获取竞价实例的竞价模式
func (n *NodeInfo) BidMode() string {
	labels := n.Node.Labels
	if len(labels) == 0 {
		return ""
	}
	return labels[NodeLabelBidModeKey]
}

// BidPrice 获取竞价实例的用户出价
func (n *NodeInfo) BidPrice() string {
	labels := n.Node.Labels
	if len(labels) == 0 {
		return ""
	}
	return labels[NodeLabelBidPriceKey]
}

// CceInstanceID 获取节点对应的CCEInstanceId, 为空表示未填写
func (n *NodeInfo) CceInstanceID() string {
	labels := n.Node.Labels
	if len(labels) == 0 {
		return ""
	}
	return labels[NodeLabelCCEInstanceIDKey]
}

// InstanceID 解析节点所在虚机的InstanceId
func (n *NodeInfo) InstanceID() string {
	providerID := n.Node.Spec.ProviderID
	if len(providerID) == 0 {
		return ""
	}
	index := strings.Index(providerID, "://")
	return providerID[index+3:]
}

// K8sNodeIsBid 判断k8sNode是否是竞价实例创建的
func K8sNodeIsBid(node *v1.Node) bool {
	nodeIsBid := false
	labels := node.Labels
	if len(labels) != 0 && labels[NodeLabelChargingTypeKey] == string(bccapi.PaymentTimingBidding) {
		nodeIsBid = true
	}
	return nodeIsBid
}

// K8sNodeIsInitComplete 判断k8sNode是否已初始化完成(是否存在CCE集群ID标签)
func K8sNodeIsInitComplete(node *v1.Node) bool {
	nodeIsInitComplete := false
	labels := node.Labels
	if len(labels) != 0 && labels[NodeLabelCCEClusterID] != "" {
		nodeIsInitComplete = true
	}
	return nodeIsInitComplete
}

// BidPriceStatistics 竞价实例价格维度统计信息
type BidPriceStatistics struct {
	// price : count
	CountInfo map[string]int
	// price : podList
	PodInfo map[string][]*v1.Pod
}

// BidInstanceTemplateStatistics 竞价实例规格模板维度统计信息
type BidInstanceTemplateStatistics struct {
	// instanceTemplateID : price : count
	Info map[string]*BidPriceStatistics
}

// BuildBidNodeSelectLabels 根据pod的nodeSelector构造竞价实例label的筛选条件
func BuildBidNodeSelectLabels(nodeSelector map[string]string) map[string]string {
	bidNodeSelectLabels := make(map[string]string)
	bidNodeSelectLabels[NodeLabelInstanceTemplateKey] = nodeSelector[NodeLabelInstanceTemplateKey]
	bidNodeSelectLabels[NodeLabelBidModeKey] = nodeSelector[NodeLabelBidModeKey]
	if nodeSelector[NodeLabelBidModeKey] == NodeLabelBidModePriceValue {
		bidNodeSelectLabels[NodeLabelBidPriceKey] = nodeSelector[NodeLabelBidPriceKey]
	}
	return bidNodeSelectLabels
}

// BuildBidInstanceLockKey 根据pod的nodeSelector构造竞价实例tenantLock的KEY
func BuildBidInstanceLockKey(nodeSelector map[string]string) string {
	prefix := nodeSelector[NodeLabelInstanceTemplateKey] + "_" + nodeSelector[NodeLabelBidModeKey]
	if nodeSelector[NodeLabelBidModeKey] == NodeLabelBidModePriceValue {
		return prefix + "_" + nodeSelector[NodeLabelBidPriceKey]
	}
	return prefix
}
