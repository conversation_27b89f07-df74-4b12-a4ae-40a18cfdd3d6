package entity

import (
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"reflect"
	"sort"
	"time"
)

// WaitEniPodInfo 等待eni pod信息
type WaitEniPodInfo struct {
	AccountID           string
	PodNamespace        string
	PodName             string
	SubnetID            string
	SecurityGroupIDList []string
	SecurityGroupIDStr  string
	EnableIPv6          bool
}

type EniInfo struct {
	AccountID             string   `json:"accountID"`
	SubnetID              string   `json:"subnetID"`
	SecurityGroupIDs      []string `json:"securityGroupIDs"`
	VpcCIDR               string   `json:"vpcCIDR"`
	EniID                 string   `json:"eniID"`
	AttachTime            int64    `json:"attachTime"`
	AttachInterfaceCalled bool     `json:"attachInterfaceCalled"`
	AttachSuccess         bool     `json:"attachSuccess"`
	CurrentBuffer         int      `json:"currentBuffer"`
	EnableIPv6            bool     `json:"enableIPv6"`
}

func (e *EniInfo) HasSameNetworkConfiguration(otherEni *EniInfo) bool {

	sort.Strings(e.SecurityGroupIDs)
	sort.Strings(otherEni.SecurityGroupIDs)

	if e.AccountID == otherEni.AccountID &&
		e.SubnetID == otherEni.SubnetID &&
		reflect.DeepEqual(e.SecurityGroupIDs, otherEni.SecurityGroupIDs) &&
		e.EniID != otherEni.EniID {
		return true
	}
	return false
}

type DisableEniPodScheduleType string

const (
	// EniAttachFailed controller attach eni 失败
	EniAttachFailed DisableEniPodScheduleType = "kubernetes.io/eni-attach-failed"

	// PodEniInitFailed node agent init pod eni 失败
	PodEniInitFailed DisableEniPodScheduleType = "kubernetes.io/pod-eni-init-failed"

	// EniDeleteFailed controller delete eni 失败
	EniDeleteFailed DisableEniPodScheduleType = "kubernetes.io/eni-delete-failed"
)

// CreateNameForENI 构建eniName
func CreateNameForENI(clusterID, providerID, nodeName string) string {
	hash := sha1.Sum([]byte(time.Now().String()))
	suffix := hex.EncodeToString(hash[:])

	return fmt.Sprintf("bci-%s/%s/%s/%s", clusterID, providerID, nodeName, suffix[:6])
}

// ENIPrivateIPConfig eni 辅助ip信息
type ENIPrivateIPConfig struct {
	// node 机型信息，取值: 8c16G 、64c128G 等；
	NodeResourceType string `json:"nodeResourceType"`
	// eni 最大可创建辅助ip数
	EniMaxPrivateIPCount int `json:"eniMaxPrivateIPCount"`
	// eni 最大可创建辅助ipv6数
	EniMaxPrivateIPv6Count int `json:"eniMaxPrivateIPv6Count"`
	// node上可attach的最大eni数
	EniMaxCountOnNode int `json:"eniMaxCountOnNode"`
	// 单租户node上attach最大eni数
	UserMaxEniCountOnNode int `json:"userMaxEniCountOnNode"`
}

// 竞价+潮汐eni 结构体，保存到cm中
type BidNodeEniInfo struct {
	AccountID        string   `json:"accountID"`
	EniID            string   `json:"eniID"`
	ProviderID       string   `json:"providerID"`
	NodeName         string   `json:"nodeName"`
	SubnetID         string   `json:"subnetID"`
	SecurityGroupIDs []string `json:"securityGroupIDs"`
}

func BuildBidNodeEniInfo(eniInfo *EniInfo, providerID string, nodeName string) BidNodeEniInfo {

	result := BidNodeEniInfo{
		AccountID:        eniInfo.AccountID,
		EniID:            eniInfo.EniID,
		ProviderID:       providerID,
		NodeName:         nodeName,
		SubnetID:         eniInfo.SubnetID,
		SecurityGroupIDs: eniInfo.SecurityGroupIDs,
	}

	return result
}
