package entity

import (
	"fmt"
	"reflect"
	"testing"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
)

func TestResourceMatchPod(t *testing.T) {
	ig := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID1",
		InstanceGroupName: "ig-2c4G-cpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				CPU:    "1",
				Memory: "1Gi",
			},
		},
	}

	pod1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns1", "pod1")),
			Name:      "pod1",
			Namespace: "ns1",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig.ResourceMatchPod(pod1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod := pod1.DeepCopy()

	copyPod.Annotations = map[string]string{}
	copyPod.Annotations[podCPUSpecification] = "1"
	copyPod.Annotations[podMEMSpecification] = "1.0Gi"

	if !ig.ResourceMatchPod(copyPod, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	copyPod1 := pod1.DeepCopy()

	copyPod1.Annotations = map[string]string{}
	// cpu 配置错误
	copyPod1.Annotations[podCPUSpecification] = "1.x"
	copyPod1.Annotations[podMEMSpecification] = "1.0Gi"

	if ig.ResourceMatchPod(copyPod1, make(map[string]bool)) {
		t.Errorf("copyPod1 should not match ig")
	}

	copyPod2 := pod1.DeepCopy()

	copyPod2.Annotations = map[string]string{}
	// mem 配置错误
	copyPod2.Annotations[podCPUSpecification] = "1"
	copyPod2.Annotations[podMEMSpecification] = "1.xGi"

	if ig.ResourceMatchPod(copyPod2, make(map[string]bool)) {
		t.Errorf("copyPod2 should not match ig")
	}

	// 没有找到对应节点组
	copyPod3 := pod1.DeepCopy()

	copyPod3.Annotations = map[string]string{}
	// mem 配置错误
	copyPod3.Annotations[podCPUSpecification] = "1"
	copyPod3.Annotations[podMEMSpecification] = "2.3Gi"

	if ig.ResourceMatchPod(copyPod3, make(map[string]bool)) {
		t.Errorf("copyPod3 should not match ig")
	}

	// GPU的测试
	ig2 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID2",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				A1024G: "1",
				CPU:    "1",
				Memory: "1Gi",
			},
		},
	}

	pod2_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns2", "pod2")),
			Name:      "pod2",
			Namespace: "ns2",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							A1024GCGPU:            resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig2.ResourceMatchPod(pod2_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod2_1 := pod2_1.DeepCopy()

	copyPod2_1.Annotations = map[string]string{}
	copyPod2_1.Annotations[podCPUSpecification] = "1"
	copyPod2_1.Annotations[podMEMSpecification] = "1.0Gi"
	copyPod2_1.Annotations[podGPUTypeSpecification] = "baidu.com/a10_24g_cgpu"
	copyPod2_1.Annotations[podGPUCountSpecification] = "1"

	if !ig2.ResourceMatchPod(copyPod2_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	copyPod2_2 := pod2_1.DeepCopy()

	copyPod2_2.Annotations = map[string]string{}
	// gpu 配置错误
	copyPod2_2.Annotations[podCPUSpecification] = "1"
	copyPod2_2.Annotations[podMEMSpecification] = "1.0Gi"
	copyPod2_2.Annotations[podGPUTypeSpecification] = "baidu.com/a10_24g_cgpu"
	copyPod2_2.Annotations[podGPUCountSpecification] = "1.x"

	if ig2.ResourceMatchPod(copyPod2_2, make(map[string]bool)) {
		t.Errorf("copyPod1 should not match ig")
	}

	// GPU的测试
	ig3 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID3",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				A10040G: "1",
				CPU:     "1",
				Memory:  "1Gi",
			},
		},
	}

	pod3_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							A10040GCGPU:           resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig3.ResourceMatchPod(pod3_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod3_1 := pod3_1.DeepCopy()

	copyPod3_1.Annotations = map[string]string{}
	copyPod3_1.Annotations[podCPUSpecification] = "1"
	copyPod3_1.Annotations[podMEMSpecification] = "1.0Gi"
	copyPod3_1.Annotations[podGPUTypeSpecification] = "baidu.com/a100_40g_cgpu"
	copyPod3_1.Annotations[podGPUCountSpecification] = "1"

	if !ig3.ResourceMatchPod(copyPod3_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig4 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				A10080G: "1",
				CPU:     "1",
				Memory:  "1Gi",
			},
		},
	}

	pod4_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							A10080GCGPU:           resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig4.ResourceMatchPod(pod4_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod4_1 := pod4_1.DeepCopy()

	copyPod4_1.Annotations = map[string]string{}
	copyPod4_1.Annotations[podCPUSpecification] = "1"
	copyPod4_1.Annotations[podMEMSpecification] = "1.0Gi"
	copyPod4_1.Annotations[podGPUTypeSpecification] = "baidu.com/a100_80g_cgpu"
	copyPod4_1.Annotations[podGPUCountSpecification] = "1"

	if !ig4.ResourceMatchPod(copyPod4_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig5 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				A3024G: "1",
				CPU:    "1",
				Memory: "1Gi",
			},
		},
	}

	pod5_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							A3024GCGPU:            resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig5.ResourceMatchPod(pod5_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod5_1 := pod5_1.DeepCopy()

	copyPod5_1.Annotations = map[string]string{}
	copyPod5_1.Annotations[podCPUSpecification] = "1"
	copyPod5_1.Annotations[podMEMSpecification] = "1.0Gi"
	copyPod5_1.Annotations[podGPUTypeSpecification] = "baidu.com/a30_24g_cgpu"
	copyPod5_1.Annotations[podGPUCountSpecification] = "1"

	if !ig5.ResourceMatchPod(copyPod5_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig6 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				RTX3070: "1",
				CPU:     "1",
				Memory:  "1Gi",
			},
		},
	}

	pod6_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							RTX3070CGPU:           resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig6.ResourceMatchPod(pod6_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod6_1 := pod6_1.DeepCopy()

	copyPod6_1.Annotations = map[string]string{}
	copyPod6_1.Annotations[podCPUSpecification] = "1"
	copyPod6_1.Annotations[podMEMSpecification] = "1.0Gi"
	copyPod6_1.Annotations[podGPUTypeSpecification] = "baidu.com/rtx_3070_cgpu"
	copyPod6_1.Annotations[podGPUCountSpecification] = "1"

	if !ig6.ResourceMatchPod(copyPod6_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig7 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				RTX3080: "1",
				CPU:     "1",
				Memory:  "1Gi",
			},
		},
	}

	pod7_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							RTX3080CGPU:           resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig7.ResourceMatchPod(pod7_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod7_1 := pod7_1.DeepCopy()

	copyPod7_1.Annotations = map[string]string{}
	copyPod7_1.Annotations[podCPUSpecification] = "1"
	copyPod7_1.Annotations[podMEMSpecification] = "1.0Gi"
	copyPod7_1.Annotations[podGPUTypeSpecification] = "baidu.com/rtx_3080_cgpu"
	copyPod7_1.Annotations[podGPUCountSpecification] = "1"

	if !ig7.ResourceMatchPod(copyPod7_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig8 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				RTX3090: "1",
				CPU:     "1",
				Memory:  "1Gi",
			},
		},
	}

	pod8_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							RTX3090CGPU:           resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig8.ResourceMatchPod(pod8_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod8_1 := pod8_1.DeepCopy()

	copyPod8_1.Annotations = map[string]string{}
	copyPod8_1.Annotations[podCPUSpecification] = "1"
	copyPod8_1.Annotations[podMEMSpecification] = "1.0Gi"
	copyPod8_1.Annotations[podGPUTypeSpecification] = "baidu.com/rtx_3090_cgpu"
	copyPod8_1.Annotations[podGPUCountSpecification] = "1"

	if !ig8.ResourceMatchPod(copyPod8_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig9 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				T416G:  "1",
				CPU:    "1",
				Memory: "1Gi",
			},
		},
	}

	pod9_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							T416GCGPU:             resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig9.ResourceMatchPod(pod9_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}
	copyPod9_1 := pod9_1.DeepCopy()

	copyPod9_1.Annotations = map[string]string{}
	copyPod9_1.Annotations[podCPUSpecification] = "1"
	copyPod9_1.Annotations[podMEMSpecification] = "1.0Gi"
	copyPod9_1.Annotations[podGPUTypeSpecification] = "baidu.com/t4_16g_cgpu"
	copyPod9_1.Annotations[podGPUCountSpecification] = "1"

	if !ig9.ResourceMatchPod(copyPod9_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig10 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				V10032G: "1",
				CPU:     "1",
				Memory:  "1Gi",
			},
		},
	}

	pod10_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							V10032GCGPU:           resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig10.ResourceMatchPod(pod10_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod10_1 := pod10_1.DeepCopy()

	copyPod10_1.Annotations = map[string]string{}
	copyPod10_1.Annotations[podCPUSpecification] = "1"
	copyPod10_1.Annotations[podMEMSpecification] = "1.0Gi"
	copyPod10_1.Annotations[podGPUTypeSpecification] = "baidu.com/v100_32g_cgpu"
	copyPod10_1.Annotations[podGPUCountSpecification] = "1"

	if !ig10.ResourceMatchPod(copyPod10_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig11 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				R200:   "1",
				CPU:    "1",
				Memory: "1Gi",
			},
		},
	}

	pod11_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							R200CGPU:              resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig11.ResourceMatchPod(pod11_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod11_1 := pod11_1.DeepCopy()

	copyPod11_1.Annotations = map[string]string{}
	copyPod11_1.Annotations[podCPUSpecification] = "1"
	copyPod11_1.Annotations[podMEMSpecification] = "1.0Gi"
	copyPod11_1.Annotations[podGPUTypeSpecification] = "baidu.com/R200_cgpu"
	copyPod11_1.Annotations[podGPUCountSpecification] = "1"

	if !ig11.ResourceMatchPod(copyPod11_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig12 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				R200:   "1",
				CPU:    "1",
				Memory: "1Gi",
			},
		},
	}

	pod12_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							A10040GCGPU:           resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig12.ResourceMatchPod(pod12_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod12_1 := pod12_1.DeepCopy()

	copyPod12_1.Annotations = map[string]string{}
	copyPod12_1.Annotations[podCPUSpecification] = "1"
	copyPod12_1.Annotations[podMEMSpecification] = "1.0Gi"
	copyPod12_1.Annotations[podGPUTypeSpecification] = "baidu.com/rtx_3090_cgpu"
	copyPod12_1.Annotations[podGPUCountSpecification] = "1"

	if ig12.ResourceMatchPod(copyPod12_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig13 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID3",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				A10040G: "1",
				CPU:     "1",
				Memory:  "1Gi",
			},
		},
	}

	pod13_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							A10040GCGPU:           resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig13.ResourceMatchPod(pod13_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod13_1 := pod13_1.DeepCopy()

	copyPod13_1.Annotations = map[string]string{}
	copyPod13_1.Annotations[podGPUTypeSpecification] = "baidu.com/a100_40g_cgpu"
	copyPod13_1.Annotations[podGPUCountSpecification] = "1"

	if !ig13.ResourceMatchPod(copyPod13_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig14 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				A10080G: "1",
				CPU:     "1",
				Memory:  "1Gi",
			},
		},
	}

	pod14_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							A10080GCGPU:           resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig14.ResourceMatchPod(pod14_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod14_1 := pod14_1.DeepCopy()

	copyPod14_1.Annotations = map[string]string{}
	copyPod14_1.Annotations[podGPUTypeSpecification] = "baidu.com/a100_80g_cgpu"
	copyPod14_1.Annotations[podGPUCountSpecification] = "1"

	if !ig14.ResourceMatchPod(copyPod14_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig15 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				A3024G: "1",
				CPU:    "1",
				Memory: "1Gi",
			},
		},
	}

	pod15_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							A3024GCGPU:            resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig15.ResourceMatchPod(pod15_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod15_1 := pod15_1.DeepCopy()

	copyPod15_1.Annotations = map[string]string{}
	copyPod15_1.Annotations[podCPUSpecification] = "1"
	copyPod15_1.Annotations[podMEMSpecification] = "1.0Gi"
	copyPod15_1.Annotations[podGPUTypeSpecification] = "baidu.com/a30_24g_cgpu"
	copyPod15_1.Annotations[podGPUCountSpecification] = "1"

	if !ig15.ResourceMatchPod(copyPod15_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig16 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				RTX3070: "1",
				CPU:     "1",
				Memory:  "1Gi",
			},
		},
	}

	pod16_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							RTX3070CGPU:           resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig16.ResourceMatchPod(pod16_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod16_1 := pod16_1.DeepCopy()

	copyPod16_1.Annotations = map[string]string{}
	copyPod16_1.Annotations[podGPUTypeSpecification] = "baidu.com/rtx_3070_cgpu"
	copyPod16_1.Annotations[podGPUCountSpecification] = "1"

	if !ig16.ResourceMatchPod(copyPod16_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig17 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				RTX3080: "1",
				CPU:     "1",
				Memory:  "1Gi",
			},
		},
	}

	pod17_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							RTX3080CGPU:           resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig17.ResourceMatchPod(pod17_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod17_1 := pod17_1.DeepCopy()

	copyPod17_1.Annotations = map[string]string{}
	copyPod17_1.Annotations[podGPUTypeSpecification] = "baidu.com/rtx_3080_cgpu"
	copyPod17_1.Annotations[podGPUCountSpecification] = "1"

	if !ig17.ResourceMatchPod(copyPod17_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig18 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				RTX3090: "1",
				CPU:     "1",
				Memory:  "1Gi",
			},
		},
	}

	pod18_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							RTX3090CGPU:           resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig18.ResourceMatchPod(pod18_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod18_1 := pod18_1.DeepCopy()

	copyPod18_1.Annotations = map[string]string{}
	copyPod18_1.Annotations[podGPUTypeSpecification] = "baidu.com/rtx_3090_cgpu"
	copyPod18_1.Annotations[podGPUCountSpecification] = "1"

	if !ig18.ResourceMatchPod(copyPod8_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig19 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				T416G:  "1",
				CPU:    "1",
				Memory: "1Gi",
			},
		},
	}

	pod19_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							T416GCGPU:             resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig19.ResourceMatchPod(pod19_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}
	copyPod19_1 := pod19_1.DeepCopy()

	copyPod19_1.Annotations = map[string]string{}
	copyPod19_1.Annotations[podGPUTypeSpecification] = "baidu.com/t4_16g_cgpu"
	copyPod19_1.Annotations[podGPUCountSpecification] = "1"

	if !ig19.ResourceMatchPod(copyPod19_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig20 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				V10032G: "1",
				CPU:     "1",
				Memory:  "1Gi",
			},
		},
	}

	pod20_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							V10032GCGPU:           resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig20.ResourceMatchPod(pod20_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod20_1 := pod20_1.DeepCopy()

	copyPod20_1.Annotations = map[string]string{}
	copyPod20_1.Annotations[podGPUTypeSpecification] = "baidu.com/v100_32g_cgpu"
	copyPod20_1.Annotations[podGPUCountSpecification] = "1"

	if !ig20.ResourceMatchPod(copyPod20_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	// GPU的测试
	ig21 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		MatchResources: []InstanceGroupResource{
			{
				R200:   "1",
				CPU:    "1",
				Memory: "1Gi",
			},
		},
	}

	pod21_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							R200CGPU:              resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if !ig21.ResourceMatchPod(pod21_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod21_1 := pod21_1.DeepCopy()

	copyPod21_1.Annotations = map[string]string{}
	copyPod21_1.Annotations[podGPUTypeSpecification] = "baidu.com/R200_cgpu"
	copyPod21_1.Annotations[podGPUCountSpecification] = "1"

	if !ig21.ResourceMatchPod(copyPod21_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}
	// 竞价的测试
	ig22 := InstanceGroupCm{
		InstanceGroupId:   "instanceGroupID4",
		InstanceGroupName: "ig-2c4G-gpu",
		Buffer:            10,
		ChargingType:      "bidding",
		MatchResources: []InstanceGroupResource{
			{
				R200:   "1",
				CPU:    "1",
				Memory: "1Gi",
			},
		},
	}

	pod22_1 := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns3", "pod3")),
			Name:      "pod3",
			Namespace: "ns3",
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "c1",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							R200CGPU:              resource.MustParse("1"),
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1.0Gi"),
						},
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
		},
	}

	if ig22.ResourceMatchPod(pod22_1, make(map[string]bool)) {
		t.Errorf("pod should match ig")
	}

	copyPod22_1 := pod22_1.DeepCopy()

	copyPod22_1.Annotations = map[string]string{}
	copyPod22_1.Annotations[podGPUTypeSpecification] = "baidu.com/R200_cgpu"
	copyPod22_1.Annotations[podGPUCountSpecification] = "1"
	copyPod22_1.Annotations[bciModelKey] = "bid"

	if !ig22.ResourceMatchPod(copyPod22_1, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}

	copyPod22_2 := pod22_1.DeepCopy()

	copyPod22_2.Annotations = map[string]string{}
	copyPod22_2.Annotations[podGPUTypeSpecification] = "baidu.com/R200_cgpu"
	copyPod22_2.Annotations[podGPUCountSpecification] = "1"

	if ig22.ResourceMatchPod(copyPod22_2, make(map[string]bool)) {
		t.Errorf("copyPod should match ig")
	}
}

func TestGetIgTag(t *testing.T) {
	ig := &InstanceGroupResource{
		CPU:     "1",
		Memory:  "1Gi",
		RTX3090: "1",
	}

	s := reflect.TypeOf(ig).Elem()

	for i := 0; i < s.NumField(); i++ {
		fmt.Println(s.Field(i).Name)
		fmt.Println(s.Field(i).Tag.Get("json"))
	}
}

func TestPodGPUCountByGPUResource(t *testing.T) {
	ig := InstanceGroupResource{
		CPU:     "1",
		Memory:  "1Gi",
		RTX3090: "1",
		RTX3080: "1",
	}

	igCm := &InstanceGroupCm{
		MatchResources: []InstanceGroupResource{ig},
	}

	res := igCm.PodGPUCountByGPUResource("baidu.com/rtx_3080_cgpu", ig)
	fmt.Println(res)
}
