package entity

import (
	"fmt"
	"reflect"
	"testing"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

func TestResourceAddAndSub(t *testing.T) {
	resourceList := map[v1.ResourceName]resource.Quantity{
		v1.ResourceCPU:                      *resource.NewScaledQuantity(4, -3),
		v1.ResourceMemory:                   *resource.NewQuantity(2000, resource.BinarySI),
		"scalar.test/" + "scalar1":          *resource.NewQuantity(1, resource.DecimalSI),
		v1.ResourceHugePagesPrefix + "test": *resource.NewQuantity(2, resource.BinarySI),
		"pods":                              *resource.NewScaledQuantity(4, -3),
	}

	resourceList1 := map[v1.ResourceName]resource.Quantity{
		v1.ResourceCPU:    *resource.NewScaledQuantity(4, -3),
		v1.ResourceMemory: *resource.NewQuantity(2000, resource.BinarySI),
	}

	r1 := NewResource(resourceList)
	r1Copy := NewResource(resourceList)

	r2 := NewResource(resourceList1)

	r1.Add(r2)
	r1.Sub(r2)

	res := reflect.DeepEqual(r1, r1Copy)
	if !res {
		t.Errorf("r1 !=r1Copy")
	}

	r2.Add(r1)
	r2.Sub(r1)
}

func TestResourceGreatAny(t *testing.T) {
	resourceList := map[v1.ResourceName]resource.Quantity{
		v1.ResourceCPU:                      *resource.NewScaledQuantity(4, -3),
		v1.ResourceMemory:                   *resource.NewQuantity(2000, resource.BinarySI),
		"scalar.test/" + "scalar1":          *resource.NewQuantity(1, resource.DecimalSI),
		v1.ResourceHugePagesPrefix + "test": *resource.NewQuantity(2, resource.BinarySI),
	}

	resourceList1 := map[v1.ResourceName]resource.Quantity{
		v1.ResourceCPU:    *resource.NewScaledQuantity(4, -3),
		v1.ResourceMemory: *resource.NewQuantity(3000, resource.BinarySI),
	}

	resourceList2 := map[v1.ResourceName]resource.Quantity{
		v1.ResourceCPU:                      *resource.NewScaledQuantity(4, -3),
		v1.ResourceMemory:                   *resource.NewQuantity(2000, resource.BinarySI),
		"scalar.test/" + "scalar1":          *resource.NewQuantity(2, resource.DecimalSI),
		v1.ResourceHugePagesPrefix + "test": *resource.NewQuantity(3, resource.BinarySI),
	}

	r1 := NewResource(resourceList)

	r2 := NewResource(resourceList1)

	r3 := NewResource(resourceList2)

	res := r1.GreatAny(r2)

	if !res {
		t.Errorf("r1.GreatAny r2 should true")
		return
	}
	fmt.Println(res)

	res = r2.GreatAny(r1)
	if !res {
		t.Errorf("r2.GreatAny r1 should true")
		return
	}
	fmt.Println(res)

	res = r3.GreatAny(r1)
	if !res {
		t.Errorf("r3.GreatAny r1 should true")
		return
	}
	fmt.Println(res)
}
