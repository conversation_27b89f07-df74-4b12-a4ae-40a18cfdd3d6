package entity

import (
	v1 "k8s.io/api/core/v1"
)

type Resource struct {
	MilliCPU float64
	Memory   float64

	// ScalarResources
	ScalarResources map[v1.ResourceName]float64

	// MaxTaskNum is only used by predicates; it should NOT
	// be accounted in other operators, e.g. Add.
	MaxTaskNum int
	// GPUDevices used to store gpu information
}

func EmptyResource() *Resource {
	return &Resource{}
}

// NewResource create a new resource object from resource list
func NewResource(rl v1.ResourceList) *Resource {
	r := EmptyResource()
	for rName, rQuant := range rl {
		switch rName {
		case v1.ResourceCPU:
			r.MilliCPU += float64(rQuant.MilliValue())
		case v1.ResourceMemory:
			r.Memory += float64(rQuant.Value())
		case v1.ResourcePods:
			r.MaxTaskNum += int(rQuant.Value())
		default:
			r.AddScalar(rName, float64(rQuant.MilliValue()))
		}
	}
	return r
}

// AddScalar adds a resource by a scalar value of this resource.
func (r *Resource) AddScalar(name v1.ResourceName, quantity float64) {
	r.SetScalar(name, r.ScalarResources[name]+quantity)
}

// SetScalar sets a resource by a scalar value of this resource.
func (r *Resource) SetScalar(name v1.ResourceName, quantity float64) {
	// Lazily allocate scalar resource map.
	if r.ScalarResources == nil {
		r.ScalarResources = map[v1.ResourceName]float64{}
	}
	r.ScalarResources[name] = quantity
}

// Add is used to add the two resources
func (r *Resource) Add(rr *Resource) *Resource {
	r.MilliCPU += rr.MilliCPU
	r.Memory += rr.Memory

	for rName, rQuant := range rr.ScalarResources {
		if r.ScalarResources == nil {
			r.ScalarResources = map[v1.ResourceName]float64{}
		}
		r.ScalarResources[rName] += rQuant
	}

	return r
}

// GreatAny 判断 r 任意子资源 大于 rr 子资源，返回true
func (r *Resource) GreatAny(rr *Resource) bool {
	geFn := func(l, r float64) bool {
		if l > r {
			return true
		}
		return false
	}
	if geFn(r.MilliCPU, rr.MilliCPU) {
		return true
	}
	if geFn(r.Memory, rr.Memory) {
		return true
	}

	left := make(map[v1.ResourceName]float64)
	right := make(map[v1.ResourceName]float64)

	for k, v := range r.ScalarResources {
		left[k] = v

		if _, ok := rr.ScalarResources[k]; !ok {
			right[k] = 0
		}
	}

	for k, v := range rr.ScalarResources {
		right[k] = v

		if _, ok := r.ScalarResources[k]; !ok {
			left[k] = 0
		}
	}

	for k := range right {
		if geFn(left[k], right[k]) {
			return true
		}
	}
	return false
}

//Sub subtracts two Resource objects.
func (r *Resource) Sub(rr *Resource) *Resource {

	r.MilliCPU -= rr.MilliCPU
	r.Memory -= rr.Memory

	for rrName, rrQuant := range rr.ScalarResources {
		if r.ScalarResources == nil {
			return r
		}
		r.ScalarResources[rrName] -= rrQuant
	}

	return r
}