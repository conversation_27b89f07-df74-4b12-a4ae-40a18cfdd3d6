package entity

import (
	"context"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/sts"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
)

const (
	EIPCreateAction = 0
	EIPDeleteAction = 1

	BCIResourceType = "bci"

	// node annotation
	NODEAnnotationEIPInfo = "bci-node-eip-info"

	// pod annotation
	PODAnnotationEIPArgs   = "cross-vpc-eni.cce.io/eipPurchase"
	PODAnnotationEIPState  = "cross-vpc-eni.cce.io/eipState"
	PODAnnotationENIUserID = "cross-vpc-eni.cce.io/userID"

	// namespace

	// label
	PODLabelAccountID  = "AccountID"
	PODLableImageCache = "image.bci.cloud.baidu.com/image-cache"

	// finalizer
	PODFinalizerImageCache = "eip.bci.cloud.baidu.com/transfer-image"
	PODFinalizerBciEIP     = "bci.cloud.baidu.com/eip"
)

type EIPState struct {
	EIP              string `json:"eip"`
	NodeName         string `json:"nodeName"`
	NodeProviderID   string `json:"nodeProviderID"`
	EniID            string `json:"eniID"`
	State            string `json:"state"`
	CreateRetryTimes int    `json:"createRetrytimes"`
	DeleteRetryTimes int    `json:"deleteRetrytimes"`
}

func GetSingOption(ctx context.Context, stsClient *sts.Client, option *options.NetworkOptions, userID string) (
	*bce.SignOption, *bce.SignOption) {
	signOptionWithHeader := stsClient.NewSignOptionWithResourceHeader(
		ctx,
		userID,
		option.EniHexKey,
		option.ResourceAccountID,
		BCIResourceType)

	signOption := stsClient.NewSignOption(ctx, userID)

	return signOption, signOptionWithHeader
}
