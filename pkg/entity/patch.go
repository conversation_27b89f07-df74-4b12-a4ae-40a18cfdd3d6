package entity

type PatchType string

const (
	PatchTypeUpdate PatchType = "update"
	PatchTypeDelete PatchType = "delete"
)

const (
	PatchNodeName = "bci.node.name"
)

type PatchValue struct {
	Key   string
	Value string
	Type  PatchType
}

func BuildUpdatePatchValue(key string, value string) PatchValue {
	return PatchValue{
		Key:   key,
		Type:  PatchTypeUpdate,
		Value: value,
	}
}

func BuildDeletePatchValue(key string) PatchValue {
	return PatchValue{
		Key:  key,
		Type: PatchTypeDelete,
	}
}
