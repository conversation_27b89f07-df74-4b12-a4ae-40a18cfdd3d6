package entity

import (
	"errors"
	"reflect"
	"sort"
	"strings"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/klog/v2"
)

// 节点组cm相关配置
const (
	InstanceGroupCmName      = "instance-group-config"
	InstanceGroupCmNamespace = "kube-system"
	InstanceGropuCmDataKey   = "config.json"
	// 用户白名单，可以略过节点组容器规格检查
	InstanceGroupAccountWhiteListCmDataKey = "account-white-list"

	ImageCachePodCmName = "image-cache-pod-config"
)

// pod node selector 相关配置
const (
	InstanceGroupKey      = "instance-group-id"
	TenantLockKey         = "tenant-lock"
	KeepHoldTenantLockKey = "keep-hold-tenant-lock"
	InstanceTemplateKey   = "instance-template-id"
	BidMode               = "bid-mode"
	BidPrice              = "bid-custom-price"
	BiddingString         = "bidding"
	BCIEnableIPv6Key      = "bci-ipv6-enabled" // IPv6支持相关NodeSelector和NodeLabel
)

// buffer机器相关
const (
	BufferLabelKey   = "buffer-node"
	BufferLabelValue = "1"
)

const (
	PlaceholderSuffixStr = "placeholder"
)

// pod资源规格
const (
	podCPUSpecification      = "bci_internal_PodCpu"
	podMEMSpecification      = "bci_internal_PodMem"
	podGPUTypeSpecification  = "bci_internal_PodGpuType"
	podGPUCountSpecification = "bci_internal_PodGpuCount"
	BciAccountKey            = "bci_internal_AccountID"
	// bciModelKey 竞价模式下才有
	bciModelKey = "bci_internal_bidModel"
	// bciCpuType cpu类型
	bciCPUType      = "bci_internal_CpuType"
	BciMatchTypeKey = "bci_internal_matchType"
	BciRuncKey      = "bci2"
	// IPv6相关 annotation
	BciEnableIPv6AnnotationKey = "bci.baidu.com/bci-enable-ipv6"
)

// gpu资源规格
const (
	A1024GCGPU  = "baidu.com/a10_24g_cgpu"
	A10040GCGPU = "baidu.com/a100_40g_cgpu"
	A10080GCGPU = "baidu.com/a100_80g_cgpu"
	A3024GCGPU  = "baidu.com/a30_24g_cgpu"
	RTX3070CGPU = "baidu.com/rtx_3070_cgpu"
	RTX3080CGPU = "baidu.com/rtx_3080_cgpu"
	RTX3090CGPU = "baidu.com/rtx_3090_cgpu"
	RTX4090CGPU = "baidu.com/rtx_4090_cgpu"
	T416GCGPU   = "baidu.com/t4_16g_cgpu"
	V10032GCGPU = "baidu.com/v100_32g_cgpu"
	R200CGPU    = "baidu.com/R200_cgpu"
)

const (
	MatchTypeTidal = "tidal"
)

// InstanceGroupResource resource相关
type InstanceGroupResource struct {
	A1024G  string `json:"baidu.com/a10_24g_cgpu,omitempty"`
	A10040G string `json:"baidu.com/a100_40g_cgpu,omitempty"`
	A10080G string `json:"baidu.com/a100_80g_cgpu,omitempty"`
	A3024G  string `json:"baidu.com/a30_24g_cgpu,omitempty"`
	RTX3070 string `json:"baidu.com/rtx_3070_cgpu,omitempty"`
	RTX3080 string `json:"baidu.com/rtx_3080_cgpu,omitempty"`
	RTX3090 string `json:"baidu.com/rtx_3090_cgpu,omitempty"`
	RTX4090 string `json:"baidu.com/rtx_4090_cgpu,omitempty"`
	T416G   string `json:"baidu.com/t4_16g_cgpu,omitempty"`
	V10032G string `json:"baidu.com/v100_32g_cgpu,omitempty"`
	R200    string `json:"baidu.com/R200_cgpu,omitempty"`
	CPU     string `json:"cpu"`
	Memory  string `json:"memory"`
}

type InstanceGroupParams struct {
	// 潮汐node 开始运行时间段
	TidalNodeStartRunTime string `json:"tidalNodeStartRunTime,omitempty"`
	// 潮汐node 结束提交时间段
	TidalNodeEndRunTime string `json:"tidalNodeEndRunTime,omitempty"`
	// 潮汐node 开始gc时间段
	TidalNodeStartGCTime string `json:"tidalNodeStartGCTime,omitempty"`
}

// InstanceGroupCm 节点组configMap 配置结构体
type InstanceGroupCm struct {
	InstanceGroupName string `json:"instanceGroupName"`
	InstanceGroupId   string `json:"instanceGroupId"`
	Buffer            int    `json:"buffer"`
	// placeholder pod 占用node resource比例
	ResourceRatio *float64 `json:"resourceRatio"`
	// ChargingType 如果为空，支持的是后付费模式，可支持多个模式，如bidding,Postpaid
	ChargingType string `json:"chargingType,omitempty"`
	// 资源池类型，如 潮汐节点组(tidal)
	MatchType string `json:"matchType,omitempty"`
	// resource 资源向上取整，pod申请资源满足resource配置走此节点组
	MatchResources []InstanceGroupResource `json:"matchResources"`
	// CpuType 如果为空，默认支持intel类型cpu
	CPUType string `json:"cpuType,omitempty"`
	// AuthorizedUser 支持的用户列表, 如果配置了支持的用户列表，只能接受用户列表中的用户提交的pod
	AuthorizedUsers []string `json:"authorizedUsers,omitempty"`
	// 节点组配置参数
	Params InstanceGroupParams `json:"params,omitempty"`
}

// 是否为节点组支持的用户
func (ig *InstanceGroupCm) IsAuthorizedUser(accountID string) bool {
	authorizedUserFlag := false
	for _, account := range ig.AuthorizedUsers {
		if account == strings.TrimSpace(accountID) {
			authorizedUserFlag = true
			break
		}
	}
	return authorizedUserFlag
}

// 检查cpuType
func (ig *InstanceGroupCm) CheckCPUType(pod *v1.Pod) bool {
	// a. pod不存在CpuType或pod cpu类型为空，默认支持任意cpuType类型的节点组
	// b. pod cpu类型为intel, 则节点组的cpu类型也必须是intel或空
	// c. pod cpu类型为其他字段，则节点组的cpu类型必须强匹配
	cpuType, ok := pod.Annotations[bciCPUType]
	if ok && cpuType != "" {
		if strings.ToLower(cpuType) == "intel" {
			if strings.ToLower(ig.CPUType) != "" && strings.ToLower(ig.CPUType) != "intel" {
				return false
			}
		} else if strings.ToLower(cpuType) != strings.ToLower(ig.CPUType) {
			return false
		}
	}
	return true
}

// 检查物理可用区
func (ig *InstanceGroupCm) CheckPhysicalZone(physicalZones []string) bool {
	isZoneMatch := false
	for _, zone := range physicalZones {
		if strings.Contains(ig.InstanceGroupName, zone) {
			isZoneMatch = true
			break
		}
	}
	return isZoneMatch
}

// ResourceMatchPod 该pod资源是否满足调度到此节点组上
func (ig *InstanceGroupCm) ResourceMatchPod(pod *v1.Pod, accountWhiteMap map[string]bool) bool {
	// 如果是竞价模式，需要chargingType必须包含bidding关键字
	if pod.Annotations[bciModelKey] != "" {
		if !strings.Contains(ig.ChargingType, BiddingString) {
			return false
		}
	} else if ig.ChargingType == BiddingString {
		// 如果是非竞价模式，ig的chargingType不为bidding
		return false
	}

	// 匹配matchType 资源池类型
	matchType, ok := pod.Annotations[BciMatchTypeKey]
	if ok {
		// 此处按字符串, 切割，排序，判断列表是否相同
		podMatchTypeSplit := strings.Split(matchType, ",")
		igMatchTypeSplit := strings.Split(ig.MatchType, ",")
		sort.Strings(podMatchTypeSplit)
		sort.Strings(igMatchTypeSplit)

		if !reflect.DeepEqual(podMatchTypeSplit, igMatchTypeSplit) {
			return false
		}
	} else if ig.MatchType != "" {
		// pod 未使用特殊资源，不允许调度
		return false
	}

	// pod Annotation中获取资源规格，先从pod Annotation中获取，获取不到则自行计算。
	cpuSpecification := pod.Annotations[podCPUSpecification]
	memSpecification := pod.Annotations[podMEMSpecification]

	if cpuSpecification != "" && memSpecification != "" {
		bciAccountID := pod.Annotations[BciAccountKey]
		if _, ok := accountWhiteMap[bciAccountID]; ok {
			// bci用户在白名单中，资源模糊匹配
			return ig.ResourceFuzzyMatchPodByAnnotation(pod)
		}
		// 资源精确匹配
		return ig.ResourceExactMatchPodByAnnotation(pod)
	}
	// 资源模糊匹配,此处代码走不到
	return ig.ResourceFuzzyMatchPodByContainerResource(pod)
}

// pod Annotation中获取资源规格，需要严格跟节点组的资源匹配
func (ig *InstanceGroupCm) ResourceExactMatchPodByAnnotation(pod *v1.Pod) bool {
	// pod Annotation中获取资源规格，先从pod Annotation中获取，获取不到则自行计算。
	cpuSpecification := pod.Annotations[podCPUSpecification]
	memSpecification := pod.Annotations[podMEMSpecification]

	// pod 的gpu资源信息
	gpuTypeSpecification := pod.Annotations[podGPUTypeSpecification]
	gpuCountSpecification := pod.Annotations[podGPUCountSpecification]

	podCPU, err := resource.ParseQuantity(cpuSpecification)
	if err != nil {
		klog.Errorf("pod <%s/%s> Annotation cpuSpecification %s parse err %+v ", pod.Namespace, pod.Name, cpuSpecification, err)
		return false
	}

	podMem, err := resource.ParseQuantity(memSpecification)
	if err != nil {
		klog.Errorf("pod <%s/%s> Annotation memSpecification %s parse err %+v ", pod.Namespace, pod.Name, memSpecification, err)
		return false
	}

	podGPUCount := resource.Quantity{}
	emptyGPUResource := resource.Quantity{}
	if gpuCountSpecification != "" {
		podGPUCount, err = resource.ParseQuantity(gpuCountSpecification)
		if err != nil {
			klog.Errorf("pod <%s/%s> Annotation gpuCountSpecification %s parse err %+v ", pod.Namespace, pod.Name, gpuCountSpecification, err)
			return false
		}
	}

	for _, r := range ig.MatchResources {
		// 此处配置不会错误，前面有校验
		igCPU, _ := resource.ParseQuantity(r.CPU)
		igMem, _ := resource.ParseQuantity(r.Memory)
		igGPU := ig.PodGPUCountByGPUResource(gpuTypeSpecification, r)
		if gpuTypeSpecification != "" && gpuCountSpecification != "" {
			if igCPU.Cmp(podCPU) == 0 && igMem.Cmp(podMem) == 0 && igGPU.Cmp(podGPUCount) == 0 {
				klog.V(3).Infof("pod <%s/%s> Annotation cpuSpecification %s memSpecification %s "+
					"gpuTypeSpecification %s gpuCountSpecification %s select instanceGroup %s success.",
					pod.Namespace, pod.Name, cpuSpecification, memSpecification, gpuTypeSpecification, gpuCountSpecification, ig.InstanceGroupName)
				return true
			}
		} else {
			if igCPU.Cmp(podCPU) == 0 && igMem.Cmp(podMem) == 0 && igGPU == emptyGPUResource {
				klog.V(3).Infof("pod <%s/%s> Annotation cpuSpecification %s memSpecification %s select instanceGroup %s success.",
					pod.Namespace, pod.Name, cpuSpecification, memSpecification, ig.InstanceGroupName)
				return true
			}
		}
	}
	klog.V(3).Infof("pod <%s/%s> Annotation cpuSpecification %s memSpecification %s select instanceGroup %s fail. ",
		pod.Namespace, pod.Name, cpuSpecification, memSpecification, ig.InstanceGroupName)
	return false
}

// pod Annotation中获取资源规格，跟节点组的资源模糊匹配即可
func (ig *InstanceGroupCm) ResourceFuzzyMatchPodByAnnotation(pod *v1.Pod) bool {
	// pod Annotation中获取资源规格，先从pod Annotation中获取，获取不到则自行计算。
	cpuSpecification := pod.Annotations[podCPUSpecification]
	memSpecification := pod.Annotations[podMEMSpecification]

	// pod 的gpu资源信息
	gpuTypeSpecification := pod.Annotations[podGPUTypeSpecification]
	gpuCountSpecification := pod.Annotations[podGPUCountSpecification]

	podCPU, err := resource.ParseQuantity(cpuSpecification)
	if err != nil {
		klog.Errorf("pod <%s/%s> Annotation cpuSpecification %s parse err %+v ", pod.Namespace, pod.Name, cpuSpecification, err)
		return false
	}

	podMem, err := resource.ParseQuantity(memSpecification)
	if err != nil {
		klog.Errorf("pod <%s/%s> Annotation memSpecification %s parse err %+v ", pod.Namespace, pod.Name, memSpecification, err)
		return false
	}

	podGPUCount := resource.Quantity{}
	emptyGPUResource := resource.Quantity{}
	if gpuCountSpecification != "" {
		podGPUCount, err = resource.ParseQuantity(gpuCountSpecification)
		if err != nil {
			klog.Errorf("pod <%s/%s> Annotation gpuCountSpecification %s parse err %+v ", pod.Namespace, pod.Name, gpuCountSpecification, err)
			return false
		}
	}

	for _, r := range ig.MatchResources {
		// 此处配置不会错误，前面有校验
		igCPU, _ := resource.ParseQuantity(r.CPU)
		igMem, _ := resource.ParseQuantity(r.Memory)
		igGPU := ig.PodGPUCountByGPUResource(gpuTypeSpecification, r)
		if gpuTypeSpecification != "" && gpuCountSpecification != "" {
			if igCPU.Cmp(podCPU) >= 0 && igMem.Cmp(podMem) >= 0 && igGPU.Cmp(podGPUCount) >= 0 {
				klog.V(3).Infof("pod <%s/%s> Annotation cpuSpecification %s memSpecification %s "+
					"gpuTypeSpecification %s gpuCountSpecification %s select instanceGroup %s success.",
					pod.Namespace, pod.Name, cpuSpecification, memSpecification, gpuTypeSpecification, gpuCountSpecification, ig.InstanceGroupName)
				return true
			}
		} else {
			if igCPU.Cmp(podCPU) >= 0 && igMem.Cmp(podMem) >= 0 && igGPU == emptyGPUResource {
				klog.V(3).Infof("pod <%s/%s> Annotation cpuSpecification %s memSpecification %s select instanceGroup %s success.",
					pod.Namespace, pod.Name, cpuSpecification, memSpecification, ig.InstanceGroupName)
				return true
			}
		}
	}
	klog.V(3).Infof("pod <%s/%s> Annotation cpuSpecification %s memSpecification %s select instanceGroup %s fail. ",
		pod.Namespace, pod.Name, cpuSpecification, memSpecification, ig.InstanceGroupName)
	return false
}

// pod container中获取资源，跟节点组的资源模糊匹配即可
func (ig *InstanceGroupCm) ResourceFuzzyMatchPodByContainerResource(pod *v1.Pod) bool {
	// pod 的gpu资源信息
	gpuTypeSpecification := pod.Annotations[podGPUTypeSpecification]
	gpuCountSpecification := pod.Annotations[podGPUCountSpecification]
	allCPUResource, _ := resource.ParseQuantity("0")
	for _, c := range pod.Spec.Containers {
		cpu := c.Resources.Requests.Cpu()
		allCPUResource.Add(*cpu)
	}

	allMemoryResource, _ := resource.ParseQuantity("0G")
	for _, c := range pod.Spec.Containers {
		memory := c.Resources.Requests.Memory()
		allMemoryResource.Add(*memory)
	}
	for _, r := range ig.MatchResources {
		cpu, err := resource.ParseQuantity(r.CPU)
		if err != nil {
			return false
		}
		memory, err := resource.ParseQuantity(r.Memory)
		if err != nil {
			return false
		}
		if gpuTypeSpecification != "" && gpuCountSpecification != "" {
			gpuCount, _ := ig.ResourcePodGPUCount(r)
			allGPUResource, _ := resource.ParseQuantity("0")
			for _, c := range pod.Spec.Containers {
				reqMap := c.Resources.Requests
				gpuCount := resource.Quantity{}
				emptyResource := resource.Quantity{}
				if reqMap[A1024GCGPU] != emptyResource {
					gpuCount = reqMap[A1024GCGPU]
				} else if reqMap[A10040GCGPU] != emptyResource {
					gpuCount = reqMap[A10040GCGPU]
				} else if reqMap[A10080GCGPU] != emptyResource {
					gpuCount = reqMap[A10080GCGPU]
				} else if reqMap[A3024GCGPU] != emptyResource {
					gpuCount = reqMap[A3024GCGPU]
				} else if reqMap[RTX3070CGPU] != emptyResource {
					gpuCount = reqMap[RTX3070CGPU]
				} else if reqMap[RTX3080CGPU] != emptyResource {
					gpuCount = reqMap[RTX3080CGPU]
				} else if reqMap[RTX3090CGPU] != emptyResource {
					gpuCount = reqMap[RTX3090CGPU]
				} else if reqMap[T416GCGPU] != emptyResource {
					gpuCount = reqMap[T416GCGPU]
				} else if reqMap[V10032GCGPU] != emptyResource {
					gpuCount = reqMap[V10032GCGPU]
				} else if reqMap[R200CGPU] != emptyResource {
					gpuCount = reqMap[R200CGPU]
				} else if reqMap[RTX4090CGPU] != emptyResource {
					gpuCount = reqMap[RTX4090CGPU]
				}
				allGPUResource.Add(gpuCount)
			}
			if cpu.Cmp(allCPUResource) >= 0 && memory.Cmp(allMemoryResource) >= 0 && gpuCount.Cmp(allGPUResource) >= 0 {
				return true
			}
		} else if cpu.Cmp(allCPUResource) >= 0 && memory.Cmp(allMemoryResource) >= 0 {
			// 规格向上取整
			return true
		}
	}
	return false
}

// ResourcePodGPUCount 并且返回ig的gpu count
func (ig *InstanceGroupCm) ResourcePodGPUCount(igr InstanceGroupResource) (resource.Quantity, error) {
	if _, err := resource.ParseQuantity(igr.A1024G); err == nil {
		return resource.ParseQuantity(igr.A1024G)
	} else if _, err := resource.ParseQuantity(igr.A10040G); err == nil {
		return resource.ParseQuantity(igr.A10040G)
	} else if _, err := resource.ParseQuantity(igr.A10080G); err == nil {
		return resource.ParseQuantity(igr.A10080G)
	} else if _, err := resource.ParseQuantity(igr.A3024G); err == nil {
		return resource.ParseQuantity(igr.A3024G)
	} else if _, err := resource.ParseQuantity(igr.RTX3070); err == nil {
		return resource.ParseQuantity(igr.RTX3070)
	} else if _, err := resource.ParseQuantity(igr.RTX3080); err == nil {
		return resource.ParseQuantity(igr.RTX3080)
	} else if _, err := resource.ParseQuantity(igr.RTX3090); err == nil {
		return resource.ParseQuantity(igr.RTX3090)
	} else if _, err := resource.ParseQuantity(igr.RTX4090); err == nil {
		return resource.ParseQuantity(igr.RTX4090)
	} else if _, err := resource.ParseQuantity(igr.T416G); err == nil {
		return resource.ParseQuantity(igr.T416G)
	} else if _, err := resource.ParseQuantity(igr.V10032G); err == nil {
		return resource.ParseQuantity(igr.V10032G)
	} else if _, err := resource.ParseQuantity(igr.R200); err == nil {
		return resource.ParseQuantity(igr.R200)
	} else {
		return resource.Quantity{}, errors.New("gpu key error")
	}
}

// 根据gpu resource(tag中的配置)获取gpu count
func (ig *InstanceGroupCm) PodGPUCountByGPUResource(gpuResourceName string, igr InstanceGroupResource) resource.Quantity {
	if gpuResourceName == "" {
		return resource.Quantity{}
	}

	elem := reflect.TypeOf(&igr).Elem()

	// gpu 类型是以tag 设置到InstanceGroupCm 结构体中，根据tag值找到对应字段，反射获取字段对应的值
	for i := 0; i < elem.NumField(); i++ {
		tagName := elem.Field(i).Tag.Get("json")
		if !strings.Contains(tagName, gpuResourceName) {
			continue
		}
		if !(reflect.ValueOf(igr).Field(i).Type().Kind() == reflect.String) {
			continue
		}
		value := reflect.ValueOf(igr).Field(i).Interface()
		strValue := value.(string)
		result, err := resource.ParseQuantity(strValue)
		if err == nil {
			return result
		}
	}
	return resource.Quantity{}
}
