package entity

import (
	"fmt"
	"testing"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
)

var (
	bciPod = &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns1", "bciPod")),
			Name:      "bciPod",
			Namespace: "ns1",
		},
		Spec: corev1.PodSpec{
			NodeSelector: map[string]string{
				InstanceGroupKey: "ig1",
				TenantLockKey:    "user1",
			},
			NodeName: "node1",
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
		},
	}

	systemPod = &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns1", "sysPod")),
			Name:      "bciPod",
			Namespace: "sysPod",
		},
		Spec: corev1.PodSpec{
			NodeName: "node1",
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
		},
	}

	systemPod1 = &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns1", "sysPod")),
			Name:      "bciPod",
			Namespace: "sysPod",
		},
		Spec: corev1.PodSpec{
			NodeSelector: make(map[string]string),
			NodeName:     "node1",
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
		},
	}

	daemonsetPod = &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "ns1", "sysPod")),
			Name:      "bciPod",
			Namespace: "daemonsetPod",
			OwnerReferences: []metav1.OwnerReference{
				{
					Kind: "DaemonSet",
				},
			},
		},
		Spec: corev1.PodSpec{
			NodeSelector: map[string]string{},
			NodeName:     "node1",
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
		},
	}

	placeholderPod = &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "kube-system", "sysPod-placeholder")),
			Name:      "sysPod-placeholder",
			Namespace: "kube-system",
			OwnerReferences: []metav1.OwnerReference{
				{
					Kind: "ReplicaSet",
				},
			},
		},
		Spec: corev1.PodSpec{
			NodeSelector: map[string]string{},
			NodeName:     "node1",
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
		},
	}

	node1 = &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "node1",
			Labels: map[string]string{
				InstanceGroupKey: "ig1",
			},
		},
	}

	bidNode1 = &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "bidNode1",
			Labels: map[string]string{
				NodeLabelInstanceTemplateKey: "ig1",
				NodeLabelChargingTypeKey:     string(bccapi.PaymentTimingBidding),
				NodeLabelBidModeKey:          NodeLabelBidModeMarketValue,
			},
		},
	}
)

func TestNodeInfo(t *testing.T) {
	fmt.Println(podTypeDaemonset.String())
	node := NewNodeInfo(node1)
	if !node.EmptyNode() {
		t.Errorf("node is not empty node ")
		return
	}

	node.AddOrUpdatePod(systemPod)
	node.AddOrUpdatePod(daemonsetPod)
	node.AddOrUpdatePod(systemPod1)
	fmt.Println(len(node.GetPlaceholderPod()))

	if !node.EmptyNode() {
		t.Errorf("node is not empty node ")
		return
	}

	node.AddOrUpdatePod(bciPod)

	if node.EmptyNode() {
		t.Errorf("node is empty node ")
		return
	}

	node.DeletePod(systemPod)

	if node.EmptyNode() {
		t.Errorf("node is empty node ")
		return
	}

	if node.InstanceGroup() == "" {
		t.Errorf("node is instancegroup node ")
		return
	}

	copyNode := node.Node.DeepCopy()

	copyNode.Status.Conditions = append(copyNode.Status.Conditions, corev1.NodeCondition{
		Type:   corev1.NodeReady,
		Status: corev1.ConditionFalse,
	})

	node.UpdateNode(copyNode)

	if !node.NodeNotReady() {
		t.Errorf("node is not ready node ")
		return
	}

	copyNode.Labels[NodeStateLabelKey] = string(NodeStateNotReady)

	if node.CurrentNodeState() != NodeStateNotReady {
		t.Errorf("node is not ready state node ")
		return
	}

	if IsBciInstanceGroupPodByNodeSelector(nil) {
		t.Errorf("pod = nil is not bci pod")
		return
	}

	locked := node.NodeLocked()
	if locked {
		t.Errorf("node got locked ,but not locked")
		return
	}
	account := node.NodeLockedAccount()
	if account != "" {
		t.Errorf("node got account not empty")
		return
	}

	// copyNode = node.Node.DeepCopy()
	// copyNode.Labels[TenantLockKey] = "user1"
	// copyNode.Labels[NodeStateLabelKey] = string(NodeStatePodExit)
	// node.UpdateNode(copyNode)
	node.AddOrUpdatePod(placeholderPod)
	placeholderPod := node.GetPlaceholderPod()
	fmt.Println(len(placeholderPod))
	if len(placeholderPod) != 1 {
		t.Errorf("node placeholderPod count != 1")
		return
	}
}

func TestNodeInfo_BidNode(t *testing.T) {
	node := NewNodeInfo(bidNode1)
	if !node.EmptyNode() {
		t.Errorf("node is not empty node ")
		return
	}
	fmt.Println(node.NodeIsBidding())
	fmt.Println(node.InstanceSpecTemplateID())
	fmt.Println(node.BidMode())
	fmt.Println(node.BidPrice())
	fmt.Println(node.CceInstanceID())
	fmt.Println(node.InstanceID())
	fmt.Println(K8sNodeIsBid(bidNode1))
	fmt.Println(K8sNodeIsInitComplete(bidNode1))

	fmt.Println(BuildBidNodeSelectLabels(map[string]string{
		NodeLabelInstanceTemplateKey: "ig1",
		TenantLockKey:                "user1",
		NodeLabelBidModeKey:          NodeLabelBidModePriceValue,
		NodeLabelBidPriceKey:         "10",
	}))
	fmt.Println(BuildBidInstanceLockKey(map[string]string{
		NodeLabelInstanceTemplateKey: "ig1",
		TenantLockKey:                "user1",
		NodeLabelBidModeKey:          NodeLabelBidModePriceValue,
		NodeLabelBidPriceKey:         "10",
	}))
}
