package entity

// node 状态机相关
const (
	NodeStateLabelKey      = "bci-node-state-machine"
	AllPodExitTimeLabelKey = "all-pod-exit-time"
	StartCoolTimeLabelKey  = "start-cool-time"
	NodeStateTimeLabelKey  = "node-state-time"
	NodeGcStateKey         = "node-gc-state"
	NotReadyStartTimeKey   = "not-ready-start-time"
)

// AllNodeStateMap 所有node状态机存储map，用于监控打点，新增状态机需要加入到此map
var AllNodeStateMap = make(map[NodeState]struct{})

// NodeState node状态机对应的值
type NodeState string

const (
	NodeStateEmpty    NodeState = ""
	NodeStateInit     NodeState = "init"
	NodeStateReserve  NodeState = "reserve"
	NodeStateRunPod   NodeState = "runPod"
	NodeStatePodExit  NodeState = "podExit"
	NodeStateCool     NodeState = "cool"
	NodeStateNotReady NodeState = "notReady"
	// 状态机暂未使用，node上可能有污点，用于监控打点
	NodeStateTaint NodeState = "taints"
)

func init() {
	AllNodeStateMap[NodeStateEmpty] = struct{}{}
	AllNodeStateMap[NodeStateInit] = struct{}{}
	AllNodeStateMap[NodeStateReserve] = struct{}{}
	AllNodeStateMap[NodeStateRunPod] = struct{}{}
	AllNodeStateMap[NodeStatePodExit] = struct{}{}
	AllNodeStateMap[NodeStateCool] = struct{}{}
	AllNodeStateMap[NodeStateNotReady] = struct{}{}
	AllNodeStateMap[NodeStateTaint] = struct{}{}
}

// CanRunPlaceholderPod 是否能
func (s NodeState) CanRunPlaceholderPod() bool {
	if s == NodeStateRunPod || s == NodeStatePodExit || s == NodeStateNotReady {
		return false
	}
	return true
}

func (s NodeState) String() string {
	return string(s)
}

// NodeGcState node gc 状态
type NodeGcState string

const (
	NodeGcStateInit    NodeGcState = "init"
	NodeGcStateSuccess NodeGcState = "success"
	NodeGcStateFail    NodeGcState = "fail"
)

func (s NodeGcState) String() string {
	return string(s)
}
