// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam (interfaces: Interface,RuntimeCache,RuntimeClient,RuntimeClientStatusWriter)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	rpc "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
	meta "k8s.io/apimachinery/pkg/api/meta"
	runtime "k8s.io/apimachinery/pkg/runtime"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	cache "sigs.k8s.io/controller-runtime/pkg/cache"
	client "sigs.k8s.io/controller-runtime/pkg/client"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// AllocateIP mocks base method.
func (m *MockInterface) AllocateIP(arg0 context.Context, arg1, arg2, arg3 string) (*rpc.ENIMultiIPReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AllocateIP", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*rpc.ENIMultiIPReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocateIP indicates an expected call of AllocateIP.
func (mr *MockInterfaceMockRecorder) AllocateIP(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocateIP", reflect.TypeOf((*MockInterface)(nil).AllocateIP), arg0, arg1, arg2, arg3)
}

// ReleaseIP mocks base method.
func (m *MockInterface) ReleaseIP(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReleaseIP", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReleaseIP indicates an expected call of ReleaseIP.
func (mr *MockInterfaceMockRecorder) ReleaseIP(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseIP", reflect.TypeOf((*MockInterface)(nil).ReleaseIP), arg0, arg1, arg2, arg3)
}

// MockRuntimeCache is a mock of RuntimeCache interface.
type MockRuntimeCache struct {
	ctrl     *gomock.Controller
	recorder *MockRuntimeCacheMockRecorder
}

// MockRuntimeCacheMockRecorder is the mock recorder for MockRuntimeCache.
type MockRuntimeCacheMockRecorder struct {
	mock *MockRuntimeCache
}

// NewMockRuntimeCache creates a new mock instance.
func NewMockRuntimeCache(ctrl *gomock.Controller) *MockRuntimeCache {
	mock := &MockRuntimeCache{ctrl: ctrl}
	mock.recorder = &MockRuntimeCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRuntimeCache) EXPECT() *MockRuntimeCacheMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockRuntimeCache) Get(arg0 context.Context, arg1 client.ObjectKey, arg2 client.Object, arg3 ...client.GetOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// Get indicates an expected call of Get.
func (mr *MockRuntimeCacheMockRecorder) Get(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRuntimeCache)(nil).Get), arg0, arg1, arg2, arg3)
}

// GetInformer mocks base method.
func (m *MockRuntimeCache) GetInformer(arg0 context.Context, arg1 client.Object) (cache.Informer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInformer", arg0, arg1)
	ret0, _ := ret[0].(cache.Informer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInformer indicates an expected call of GetInformer.
func (mr *MockRuntimeCacheMockRecorder) GetInformer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInformer", reflect.TypeOf((*MockRuntimeCache)(nil).GetInformer), arg0, arg1)
}

// GetInformerForKind mocks base method.
func (m *MockRuntimeCache) GetInformerForKind(arg0 context.Context, arg1 schema.GroupVersionKind) (cache.Informer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInformerForKind", arg0, arg1)
	ret0, _ := ret[0].(cache.Informer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInformerForKind indicates an expected call of GetInformerForKind.
func (mr *MockRuntimeCacheMockRecorder) GetInformerForKind(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInformerForKind", reflect.TypeOf((*MockRuntimeCache)(nil).GetInformerForKind), arg0, arg1)
}

// IndexField mocks base method.
func (m *MockRuntimeCache) IndexField(arg0 context.Context, arg1 client.Object, arg2 string, arg3 client.IndexerFunc) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IndexField", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// IndexField indicates an expected call of IndexField.
func (mr *MockRuntimeCacheMockRecorder) IndexField(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IndexField", reflect.TypeOf((*MockRuntimeCache)(nil).IndexField), arg0, arg1, arg2, arg3)
}

// List mocks base method.
func (m *MockRuntimeCache) List(arg0 context.Context, arg1 client.ObjectList, arg2 ...client.ListOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "List", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// List indicates an expected call of List.
func (mr *MockRuntimeCacheMockRecorder) List(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockRuntimeCache)(nil).List), varargs...)
}

// Start mocks base method.
func (m *MockRuntimeCache) Start(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Start indicates an expected call of Start.
func (mr *MockRuntimeCacheMockRecorder) Start(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockRuntimeCache)(nil).Start), arg0)
}

// WaitForCacheSync mocks base method.
func (m *MockRuntimeCache) WaitForCacheSync(arg0 context.Context) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WaitForCacheSync", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// WaitForCacheSync indicates an expected call of WaitForCacheSync.
func (mr *MockRuntimeCacheMockRecorder) WaitForCacheSync(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitForCacheSync", reflect.TypeOf((*MockRuntimeCache)(nil).WaitForCacheSync), arg0)
}

// MockRuntimeClient is a mock of RuntimeClient interface.
type MockRuntimeClient struct {
	ctrl     *gomock.Controller
	recorder *MockRuntimeClientMockRecorder
}

// MockRuntimeClientMockRecorder is the mock recorder for MockRuntimeClient.
type MockRuntimeClientMockRecorder struct {
	mock *MockRuntimeClient
}

// NewMockRuntimeClient creates a new mock instance.
func NewMockRuntimeClient(ctrl *gomock.Controller) *MockRuntimeClient {
	mock := &MockRuntimeClient{ctrl: ctrl}
	mock.recorder = &MockRuntimeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRuntimeClient) EXPECT() *MockRuntimeClientMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRuntimeClient) Create(arg0 context.Context, arg1 client.Object, arg2 ...client.CreateOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Create", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockRuntimeClientMockRecorder) Create(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRuntimeClient)(nil).Create), varargs...)
}

// Delete mocks base method.
func (m *MockRuntimeClient) Delete(arg0 context.Context, arg1 client.Object, arg2 ...client.DeleteOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Delete", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockRuntimeClientMockRecorder) Delete(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRuntimeClient)(nil).Delete), varargs...)
}

// DeleteAllOf mocks base method.
func (m *MockRuntimeClient) DeleteAllOf(arg0 context.Context, arg1 client.Object, arg2 ...client.DeleteAllOfOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteAllOf", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAllOf indicates an expected call of DeleteAllOf.
func (mr *MockRuntimeClientMockRecorder) DeleteAllOf(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAllOf", reflect.TypeOf((*MockRuntimeClient)(nil).DeleteAllOf), varargs...)
}

// Get mocks base method.
func (m *MockRuntimeClient) Get(arg0 context.Context, arg1 client.ObjectKey, arg2 client.Object, arg3 ...client.GetOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// Get indicates an expected call of Get.
func (mr *MockRuntimeClientMockRecorder) Get(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRuntimeClient)(nil).Get), arg0, arg1, arg2, arg3)
}

// List mocks base method.
func (m *MockRuntimeClient) List(arg0 context.Context, arg1 client.ObjectList, arg2 ...client.ListOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "List", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// List indicates an expected call of List.
func (mr *MockRuntimeClientMockRecorder) List(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockRuntimeClient)(nil).List), varargs...)
}

// Patch mocks base method.
func (m *MockRuntimeClient) Patch(arg0 context.Context, arg1 client.Object, arg2 client.Patch, arg3 ...client.PatchOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Patch", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Patch indicates an expected call of Patch.
func (mr *MockRuntimeClientMockRecorder) Patch(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Patch", reflect.TypeOf((*MockRuntimeClient)(nil).Patch), varargs...)
}

// RESTMapper mocks base method.
func (m *MockRuntimeClient) RESTMapper() meta.RESTMapper {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RESTMapper")
	ret0, _ := ret[0].(meta.RESTMapper)
	return ret0
}

// RESTMapper indicates an expected call of RESTMapper.
func (mr *MockRuntimeClientMockRecorder) RESTMapper() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RESTMapper", reflect.TypeOf((*MockRuntimeClient)(nil).RESTMapper))
}

// Scheme mocks base method.
func (m *MockRuntimeClient) Scheme() *runtime.Scheme {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Scheme")
	ret0, _ := ret[0].(*runtime.Scheme)
	return ret0
}

// Scheme indicates an expected call of Scheme.
func (mr *MockRuntimeClientMockRecorder) Scheme() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scheme", reflect.TypeOf((*MockRuntimeClient)(nil).Scheme))
}

// Status mocks base method.
func (m *MockRuntimeClient) Status() client.StatusWriter {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Status")
	ret0, _ := ret[0].(client.StatusWriter)
	return ret0
}

// Status indicates an expected call of Status.
func (mr *MockRuntimeClientMockRecorder) Status() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Status", reflect.TypeOf((*MockRuntimeClient)(nil).Status))
}

// Update mocks base method.
func (m *MockRuntimeClient) Update(arg0 context.Context, arg1 client.Object, arg2 ...client.UpdateOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Update", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

func(m *MockRuntimeClient) SubResource(arg0 string) client.SubResourceClient {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubResource", arg0)
	ret0, _ := ret[0].(client.SubResourceClient)
	return ret0
}

func (mr *MockRuntimeClientMockRecorder) SubResource(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubResource", reflect.TypeOf((*MockRuntimeClient)(nil).SubResource), arg0)
}

func(m *MockRuntimeClient) GroupVersionKindFor(arg0 runtime.Object) (schema.GroupVersionKind, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GroupVersionKindFor", arg0)
	ret0, _ := ret[0].(schema.GroupVersionKind)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockRuntimeClientMockRecorder) GroupVersionKindFor(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GroupVersionKindFor", reflect.TypeOf((*MockRuntimeClient)(nil).GroupVersionKindFor), arg0)
}

func(m *MockRuntimeClient) IsObjectNamespaced(arg0 runtime.Object) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsObjectNamespaced", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockRuntimeClientMockRecorder) IsObjectNamespaced(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsObjectNamespaced", reflect.TypeOf((*MockRuntimeClient)(nil).IsObjectNamespaced), arg0)
}

// Update indicates an expected call of Update.
func (mr *MockRuntimeClientMockRecorder) Update(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRuntimeClient)(nil).Update), varargs...)
}

// MockRuntimeClientStatusWriter is a mock of RuntimeClientStatusWriter interface.
type MockRuntimeClientStatusWriter struct {
	ctrl     *gomock.Controller
	recorder *MockRuntimeClientStatusWriterMockRecorder
}

// MockRuntimeClientStatusWriterMockRecorder is the mock recorder for MockRuntimeClientStatusWriter.
type MockRuntimeClientStatusWriterMockRecorder struct {
	mock *MockRuntimeClientStatusWriter
}

// NewMockRuntimeClientStatusWriter creates a new mock instance.
func NewMockRuntimeClientStatusWriter(ctrl *gomock.Controller) *MockRuntimeClientStatusWriter {
	mock := &MockRuntimeClientStatusWriter{ctrl: ctrl}
	mock.recorder = &MockRuntimeClientStatusWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRuntimeClientStatusWriter) EXPECT() *MockRuntimeClientStatusWriterMockRecorder {
	return m.recorder
}

// Patch mocks base method.
func (m *MockRuntimeClientStatusWriter) Patch(arg0 context.Context, arg1 client.Object, arg2 client.Patch, arg3 ...client.PatchOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Patch", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Patch indicates an expected call of Patch.
func (mr *MockRuntimeClientStatusWriterMockRecorder) Patch(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Patch", reflect.TypeOf((*MockRuntimeClientStatusWriter)(nil).Patch), varargs...)
}

// Update mocks base method.
func (m *MockRuntimeClientStatusWriter) Update(arg0 context.Context, arg1 client.Object, arg2 ...client.UpdateOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Update", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRuntimeClientStatusWriterMockRecorder) Update(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRuntimeClientStatusWriter)(nil).Update), varargs...)
}
