package ipam

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	"sigs.k8s.io/controller-runtime/pkg/client"

	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
	utilenv "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/utils/env"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/utils/k8s"
)

var (
	ForceUpdateCustomResourcePeriod = 15 * time.Second
)

func (ipam *IPAM) getPodSpec(ctx context.Context, podName string, podNamespace string) (*corev1.Pod, error) {
	pod := &corev1.Pod{}
	err := ipam.Cache.Get(context.Background(), client.ObjectKey{
		Namespace: podNamespace,
		Name:      podName,
	}, pod)
	if err != nil {
		return pod, fmt.Errorf("get pod spec error: %w, %s/%s", err, podNamespace, podName)
	}
	return pod, nil
}

func (ipam *IPAM) getPodIP(ctx context.Context, podNamespace string, podName string) (string, error) {
	pod, err := ipam.getPodSpec(ctx, podName, podNamespace)
	if err != nil {
		return "", err
	}
	ip := pod.Status.PodIP
	if ip == "" {
		return ip, fmt.Errorf("pod ip not found, %s/%s", podNamespace, podName)
	}

	return ip, nil
}

func (ipam *IPAM) isXXInList(xx string, list []string) bool {
	for _, v := range list {
		if xx == v {
			return true
		}
	}
	return false
}

// update status from apiserver
func (ipam *IPAM) getBciNode(ctx context.Context) (*networkingv1.BciNode, error) {
	bciNode := &networkingv1.BciNode{}
	crdName, err := utilenv.GetNodeName(ctx)
	if err != nil {
		return bciNode, errors.New("unable to get node name in ipam")
	}

	err = ipam.Cache.Get(context.Background(), client.ObjectKey{
		Name: crdName,
	}, bciNode)
	if err != nil {
		return bciNode, fmt.Errorf("get bcinode spec failed in ipam: %s, %w", crdName, err)
	}

	return bciNode, nil
}

func (ipam *IPAM) getPodNamesOnCurrentNodeByUserID(ctx context.Context, userID string) (map[string]string, error) {
	ret := make(map[string]string)
	podIP := "none"
	nodeName, err := utilenv.GetNodeName(ctx)
	if err != nil {
		return ret, errors.New("unable to get node name in ipam")
	}

	// get all pods on current node
	pods := corev1.PodList{}
	err = ipam.Cache.List(context.Background(), &pods, client.MatchingFields{"spec.nodeName": nodeName})
	if err != nil {
		logger.Infof(ctx, "get node:%s pods error", nodeName)
		return ret, err
	}

	if userID == ADMINUSERNAME {
		podIP = "none"
		for _, pod := range pods.Items {
			if _, ok := pod.GetAnnotations()["bci_internal_AccountID"]; !ok {
				if pod.Status.PodIP != "" {
					podIP = pod.Status.PodIP
				}
				if k8s.IsPodFinished(&pod) {
					continue
				}
				ret[pod.GetName()] = fmt.Sprintf("%s/%s", pod.GetNamespace(), podIP)
			}
		}
		logger.Infof(ctx, "admin pods: %v", ret)
	} else {
		for _, pod := range pods.Items {
			podIP = "none"
			// asume userid same as namespace except admin pod
			if pod.GetNamespace() == userID {
				if k8s.IsPodFinished(&pod) {
					continue
				}
				if pod.Status.PodIP != "" {
					podIP = pod.Status.PodIP
				}
				annoMap := pod.GetAnnotations()
				if annoMap != nil {
					if multiIPs, exist := annoMap[podMultiIPAnnotation]; exist && multiIPs != "" {
						podIP = multiIPs
					}
				}
				// TODO: fixe me, if same podname in different namespace
				ret[pod.GetName()] = fmt.Sprintf("%s/%s", pod.GetNamespace(), podIP)
			}
		}
		logger.Infof(ctx, "user pods: %v", ret)
	}

	return ret, nil
}

func (ipam *IPAM) releaseIPForce(ctx context.Context, userID string, podNamespace string, podName string, usedIP string) {
	ipam.lock.Lock()
	defer ipam.lock.Unlock()

	if ipam.BciNode == nil {
		return
	}

	podOwner := fmt.Sprintf("%s/%s", podNamespace, podName)
	if _, ok := ipam.BciNode.Status.EniMultiIP.Used[userID]; !ok {
		return
	}

	for eniID, eniAlloc := range ipam.BciNode.Status.EniMultiIP.Used[userID] {
		for ip, ipAlloc := range eniAlloc.PrivateIPAddresses {
			if ipAlloc.Owner == podOwner && ip == usedIP {
				logger.Infof(ctx, "release ip: %s", ip)
				ipam.UserIdlePool[userID][eniID].Append(PayLoad{
					address: ip,
					allocationIP: networkingv1.AllocationIP{
						UserID: userID,
						EniID:  eniID,
					},
					macAddress: ipam.BciNode.Spec.EniMultiIP.Pool[userID][eniID].MacAddress,
				})
				delete(eniAlloc.PrivateIPAddresses, ip)
			}
		}
	}
}

// TODO: pod.phase filter
func (ipam *IPAM) gcIP(ctx context.Context) {
	ctx = logger.NewContext()

	if ipam.BciNode == nil {
		return
	}

	gcDelayTime := utilenv.GetGCDelayTime(ctx)
	for k, v := range ipam.WaitGCPod {
		if int(time.Since(v).Seconds()) > 3*gcDelayTime {
			delete(ipam.WaitGCPod, k)
			logger.Infof(ctx, "gc ip delete not show pod: %v", k)
		}
	}
	for userID, userAlloc := range ipam.BciNode.Status.EniMultiIP.Used {
		podNamespaceName, err := ipam.getPodNamesOnCurrentNodeByUserID(ctx, userID)
		if err != nil {
			logger.Errorf(ctx, err.Error())
			return
		}
		for _, eniAlloc := range userAlloc {
			for usedIP, ipAlloc := range eniAlloc.PrivateIPAddresses {
				usedPodName := strings.Split(ipAlloc.Owner, "/")[1]
				usedPodNamespace := strings.Split(ipAlloc.Owner, "/")[0]
				// ipam 内存中还在，但 pod 已经没了
				if info, ok := podNamespaceName[usedPodName]; !ok {
					if _, ok := ipam.WaitGCPod[ipAlloc.Owner]; ok {
						if int(time.Since(ipam.WaitGCPod[ipAlloc.Owner]).Seconds()) > gcDelayTime {
							logger.Infof(ctx, "gc ip find %s/%s", usedPodNamespace, usedPodName)
							ipam.releaseIPForce(ctx, userID, usedPodNamespace, usedPodName, usedIP)
							delete(ipam.WaitGCPod, ipAlloc.Owner)
							ipam.RefreshTrigger.TriggerWithReason("gc pod leaked ip")
						} else {
							logger.Infof(ctx, "gc ip delay %v, %v", ipAlloc.Owner, usedIP)
						}
					} else {
						ipam.WaitGCPod[ipAlloc.Owner] = time.Now()
						logger.Infof(ctx, "gc ip add pod %v to wait list %v", ipAlloc.Owner, usedIP)
					}
				} else {
					// check recorded pod ips same as running pod ips
					// if not same, we have allocated two ips to a pod, we need to retrieve not using one.
					ips := strings.Split(info, "/")[1]
					if ips != "none" && !strings.Contains(ips, usedIP) {
						logger.Infof(ctx, "gc ips find duplicate %s/%s", usedPodNamespace, usedPodName)
						ipam.releaseIPForce(ctx, userID, usedPodNamespace, usedPodName, usedIP)
						ipam.RefreshTrigger.TriggerWithReason("gc pod duplicate ips")
					}
				}
			}
		}
	}
}

func (ipam *IPAM) StartGCIPPeriod(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second) // 30 min
	quit := make(chan struct{})
	go func() {
		for {
			select {
			case <-ticker.C:
				ipam.gcIP(ctx)
			case <-quit:
				ticker.Stop()
				return
			}
		}
	}()
}

func (ipam *IPAM) UpdateStatus(ctx context.Context, reasons []string) {
	bciNode, err := ipam.getBciNode(ctx)
	if err != nil {
		return
	}
	if ipam.BciNode == nil {
		return
	}

	logger.Infof(ctx, "ipam is updating status: %v", reasons)

	bciNode.Status = ipam.BciNode.Status
	bciNode.Status.WaitReleaseIP = ipam.updatedLocalWaitReleaseIP(ctx)
	logger.Infof(ctx, "UpdateStatus %v", bciNode.Status.WaitReleaseIP)

	err = ipam.Client.Status().Update(context.Background(), bciNode.DeepCopy())
	if err != nil {
		logger.Errorf(ctx, "update status error")
		ipam.RefreshTrigger.TriggerWithReason("retry after update status error")
	}
}

func (ipam *IPAM) updatedLocalWaitReleaseIP(ctx context.Context) map[string]map[string]networkingv1.IPReleaseStatus {
	ipam.lock.Lock()
	defer ipam.lock.Unlock()

	for userIDEniID, eniRelease := range ipam.WaitReleaseIP {
		for ip, status := range eniRelease {
			if status == networkingv1.IPAMDoNotRelease || status == networkingv1.IPAMReadyForRelease {
				continue
			}

			// Remove entry from wait-release-ips
			if status == networkingv1.IPAMReleased {
				delete(ipam.WaitReleaseIP[userIDEniID], ip)
				logger.Infof(ctx, "remove released entry  %s %s", userIDEniID, ip)
			}

			if status == networkingv1.IPAMMarkForRelease {
				userID, eniID, err := splitUserIDAndEniID(userIDEniID)
				if err != nil {
					logger.Warning(ctx, err)
					continue
				}

				// set status to IPAMDoNotRelease or IPAMReadyForRelease
				// IPv4 或 IPv6 任一被占用都不允许释放
				if ipam.isIPAllocated(userID, eniID, ip) || ipam.isIPv6Allocated(userID, eniID, ip) {
					ipam.WaitReleaseIP[userIDEniID][ip] = networkingv1.IPAMDoNotRelease
					logger.Infof(ctx, "mark entry %s %s: %s", userIDEniID, ip, networkingv1.IPAMDoNotRelease)
				} else {
					ipam.WaitReleaseIP[userIDEniID][ip] = networkingv1.IPAMReadyForRelease
					logger.Infof(ctx, "mark entry %s %s: %s", userIDEniID, ip, networkingv1.IPAMReadyForRelease)
				}
			}
		}
	}

	return ipam.WaitReleaseIP
}

func splitUserIDAndEniID(userIDColonEniID string) (string, string, error) {
	result := strings.Split(userIDColonEniID, ":")
	if len(result) != 2 {
		return "", "", fmt.Errorf("%s format invalid", userIDColonEniID)
	}

	return result[0], result[1], nil
}

func (ipam *IPAM) isIPAllocated(userID, eniID, ip string) bool {
	if ipam.BciNode.Status.EniMultiIP.Used == nil {
		return false
	}

	if _, ok := ipam.BciNode.Status.EniMultiIP.Used[userID]; !ok {
		return false
	}

	if ipam.BciNode.Status.EniMultiIP.Used[userID] == nil {
		return false
	}

	if _, ok := ipam.BciNode.Status.EniMultiIP.Used[userID][eniID]; !ok {
		return false
	}

	if ipam.BciNode.Status.EniMultiIP.Used[userID][eniID].PrivateIPAddresses == nil {
		return false
	}

	_, ok := ipam.BciNode.Status.EniMultiIP.Used[userID][eniID].PrivateIPAddresses[ip]
	return ok
}

// upload local status to etcd periodly
// its the only place uploading
func (ipam *IPAM) StartUpdateStatusPeriod(ctx context.Context) {
	ticker := time.NewTicker(ForceUpdateCustomResourcePeriod) // 300 s
	quit := make(chan struct{})
	go func() {
		for {
			select {
			case <-ticker.C:
				ipam.RefreshTrigger.TriggerWithReason("period update")
			case <-quit:
				ticker.Stop()
				return
			}
		}
	}()
}

// wait eni create, then return eniID
func (ipam *IPAM) tryWaitEni(ctx context.Context, userID string, subnetID string, secID string) (string, error) {
	var sleepCount int
	logger.Infof(ctx, "start to wait eni create")
	for {
		if sleepCount > 40 {
			return "", fmt.Errorf("wait eni create done times up %ds", 30)
		}
		eniID, err := ipam.getEniIDBySubnetAndSecIDsFromSpec(ctx, userID, subnetID, secID)
		if err != nil {
			time.Sleep(time.Second)
			sleepCount++
			continue
		}
		return eniID, nil
	}
}

// only support one subnetid and one secgroupid
func (ipam *IPAM) getPodSubnetIDAndSecIDs(ctx context.Context, userID string, podName string, podNamespace string) (string, string, error) {
	if userID == ADMINUSERNAME {
		return "", "", nil
	}

	pod, err := ipam.getPodSpec(ctx, podName, podNamespace)
	if err != nil {
		return "", "", err
	}

	// 子网只有一个，安全组可能有多个，多个安全组用逗号分割
	subnetIDKey := "cross-vpc-eni.cce.io/subnetID"
	securityGroupIDsKey := "cross-vpc-eni.cce.io/securityGroupIDs"

	if pod.GetAnnotations() == nil {
		pod.Annotations = make(map[string]string)
	}

	subnetID, ok := pod.GetAnnotations()[subnetIDKey]
	if !ok {
		return "", "", fmt.Errorf("get subnetID by podname error, key %s not found", subnetIDKey)
	}

	securityGroupIDs, ok := pod.GetAnnotations()[securityGroupIDsKey]
	if !ok {
		return "", "", fmt.Errorf("get securityGroupIDs by podname error, key %s not found", securityGroupIDsKey)
	}

	if subnetID == "" {
		return "", "", errors.New("get subnetID by podname error, subnetID empty")
	}
	if securityGroupIDs == "" {
		return "", "", errors.New("get securityGroupIDs by podname error, securityGroupIDs empty")
	}
	logger.Infof(ctx, "get pod: %s, subnetID: %s, secgID: %s", podName, subnetID, securityGroupIDs)

	return subnetID, securityGroupIDs, nil
}

// regularizeSecurityGroups 将逗号分隔的安全组列表，去重，排序
func regularizeSecurityGroups(securityGroupList string) []string {
	items := strings.Split(securityGroupList, ",")

	// trim space
	for i := range items {
		items[i] = strings.TrimSpace(items[i])
	}

	// remove dup
	set := sets.NewString(items...)

	// sort
	sorted := set.List()

	return sorted
}

func regularizeSecurityGroupsToStringKey(securityGroupList string) string {
	sorted := regularizeSecurityGroups(securityGroupList)

	return strings.Join(sorted, ",")
}

func securityGroupListToStringKey(securityGroups []string) string {
	// trim space
	for i, s := range securityGroups {
		securityGroups[i] = strings.TrimSpace(s)
	}

	// remove dup
	set := sets.NewString(securityGroups...)

	// sort
	sorted := set.List()

	return strings.Join(sorted, ",")
}

func (ipam *IPAM) isPodReleased(ctx context.Context, podNameSpaceName string) (userID string, released bool) {
	if ipam.BciNode == nil {
		logger.Info(ctx, "ipam bcinode cache is nil")
		return "", true
	}

	for _, userEnis := range ipam.BciNode.Status.EniMultiIP.Used {
		for _, eni := range userEnis {
			for _, ip := range eni.PrivateIPAddresses {
				if ip.Owner == podNameSpaceName {
					logger.Infof(ctx, "pod %s is not released", podNameSpaceName)
					return ip.UserID, false
				}
			}
			// 理论上若ipv4不在PrivateIPAddresses中，则ipv6也一定不在PrivateIPv6Addresses中
			for _, ipv6 := range eni.PrivateIPv6Addresses {
				if ipv6.Owner == podNameSpaceName {
					logger.Infof(ctx, "pod %s is not released", podNameSpaceName)
					return ipv6.UserID, false
				}
			}
		}
	}

	return "", true
}

// getEniIDByPodNameSpaceName gets eni id by podnamespace/name
func (ipam *IPAM) getEniIDByPodNameSpaceName(podNameSpaceName string) string {
	for _, userEnis := range ipam.BciNode.Status.EniMultiIP.Used {
		for _, eni := range userEnis {
			for _, ip := range eni.PrivateIPAddresses {
				if ip.Owner == podNameSpaceName {
					return ip.EniID
				}
			}
		}
	}

	// not found
	return ""
}

// TODO: multiple enis?
func (ipam *IPAM) getEniIDBySubnetAndSecIDsFromSpec(ctx context.Context, userID string, subnetID string, secIDs string) (string, error) {
	ipam.lock.RLock()
	defer ipam.lock.RUnlock()

	if ipam.BciNode == nil {
		return "", errors.New("bciNode is nil")
	}

	// user enis
	if _, ok := ipam.BciNode.Spec.EniMultiIP.Pool[userID]; !ok {
		return "", errors.New("userID not fount in crd spec")
	}

	if userID == ADMINUSERNAME {
		// if admin user only have one eni
		for eniID := range ipam.BciNode.Spec.EniMultiIP.Pool[userID] {
			return eniID, nil
		}
		return "", errors.New("admin eni not found")
	}

	// iterate user enis
	for eniID, eniAlloc := range ipam.BciNode.Spec.EniMultiIP.Pool[userID] {
		// skip not ready eni
		if ipam.isXXInList(eniID, ipam.BciNode.Status.EniMultiIP.NotReady) {
			continue
		}

		// skip different subnet id
		if eniAlloc.SubnetID != subnetID {
			continue
		}

		// 比较pod的安全组信息和eni的安全组信息是否一致
		eniSecurityGroupsKey := securityGroupListToStringKey(eniAlloc.SecurityGroupIDs)
		podSecurityGroupsKey := regularizeSecurityGroupsToStringKey(secIDs)
		logger.Infof(ctx, "pod and eni security group: %s %s", podSecurityGroupsKey, eniSecurityGroupsKey)

		if podSecurityGroupsKey != eniSecurityGroupsKey {
			continue
		}

		return eniID, nil
	}

	return "", fmt.Errorf("eni belong to subnet:%s, security: %s not found", subnetID, secIDs)
}

func (ipam *IPAM) checkPodIPsHasAllocated(ctx context.Context, userID string, eniID string,
	podName string, podNamespace string, needIPNums int) ([]PayLoad, bool) {
	payLoads := make([]PayLoad, 0)
	if ipam.BciNode == nil {
		return payLoads, false
	}
	allocEnis, ok := ipam.BciNode.Status.EniMultiIP.Used[userID]
	if !ok {
		return payLoads, false
	}
	if _, ok := allocEnis[eniID]; !ok {
		return payLoads, false
	}
	allocEni := allocEnis[eniID]
	for ip, ele := range allocEni.PrivateIPAddresses {
		if ele.Owner == fmt.Sprintf("%s/%s", podNamespace, podName) {
			payLoad := PayLoad{}
			payLoad.address = ip
			payLoad.macAddress = ipam.BciNode.Spec.EniMultiIP.Pool[userID][eniID].MacAddress
			payLoad.allocationIP = networkingv1.AllocationIP{
				EniID: eniID,
			}
			payLoads = append(payLoads, payLoad)
		}
	}
	if len(payLoads) == needIPNums {
		logger.Infof(ctx, "pod %s/%s ip has allocated, payLoads is %+v", podNamespace, podName, payLoads)
		return payLoads, true
	} else if len(payLoads) != 0 && len(payLoads) != needIPNums {
		// pod申请的ip数量和已经分配的ip数量对不上，理论上走不到
		logger.Errorf(ctx, "pod %s/%s allocated ip nums %v not equals with target required ip nums %v", podNamespace, podName, len(payLoads), needIPNums)
	}
	return payLoads, false
}

func (ipam *IPAM) checkPodIPsHasAllocatedWithIPv6(ctx context.Context, userID string, eniID string,
	podName string, podNamespace string, needIPNums int, needIPv6Nums int) ([]PayLoad, bool) {
	payLoads := make([]PayLoad, 0)
	if ipam.BciNode == nil {
		return payLoads, false
	}
	allocEnis, ok := ipam.BciNode.Status.EniMultiIP.Used[userID]
	if !ok {
		return payLoads, false
	}
	if _, ok := allocEnis[eniID]; !ok {
		return payLoads, false
	}
	allocEni := allocEnis[eniID]
	for ip, ele := range allocEni.PrivateIPAddresses {
		if ele.Owner == fmt.Sprintf("%s/%s", podNamespace, podName) {
			payLoad := PayLoad{}
			payLoad.address = ip
			payLoad.macAddress = ipam.BciNode.Spec.EniMultiIP.Pool[userID][eniID].MacAddress
			payLoad.allocationIP = networkingv1.AllocationIP{
				EniID: eniID,
			}
			payLoads = append(payLoads, payLoad)
		}
	}
	if needIPv6Nums == 1 {
		for ip, ele := range allocEni.PrivateIPv6Addresses {
			if ele.Owner == fmt.Sprintf("%s/%s", podNamespace, podName) {
				payLoad := PayLoad{}
				payLoad.address = ip
				payLoad.macAddress = ipam.BciNode.Spec.EniMultiIP.Pool[userID][eniID].MacAddress
				payLoad.allocationIP = networkingv1.AllocationIP{
					EniID: eniID,
				}
				payLoads = append(payLoads, payLoad) // ipv6Address追加到payLoads中
			}
		}
	}

	if len(payLoads) == needIPNums+needIPv6Nums {
		logger.Infof(ctx, "pod %s/%s ip/ipv6 has allocated, payLoads is %+v", podNamespace, podName, payLoads)
		return payLoads, true
	} else if len(payLoads) != 0 && len(payLoads) != needIPNums+needIPv6Nums {
		// pod申请的ip数量和已经分配的ip数量对不上，理论上走不到
		logger.Errorf(ctx, "pod %s/%s allocated ip/ipv6 nums %v not equals with target required ip nums %v and ipv6 nums %v",
			podNamespace, podName, len(payLoads), needIPNums, needIPv6Nums)
	}

	return payLoads, false
}

func (ipam *IPAM) isAdminPod(ctx context.Context, podNamespace string, podName string) (bool, error) {
	pod, err := ipam.getPodSpec(ctx, podName, podNamespace)
	if err != nil {
		return false, err
	}

	if pod.GetAnnotations() == nil {
		return false, fmt.Errorf("pod (%s/%s) has no annotation", pod.GetNamespace(), pod.GetName())
	}

	if _, ok := pod.GetAnnotations()["bci_internal_AccountID"]; ok {
		return false, nil
	}

	return true, nil
}

func (ipam *IPAM) isIPv6Pod(ctx context.Context, podNamespace string, podName string) (bool, error) {
	pod, err := ipam.getPodSpec(ctx, podName, podNamespace)
	if err != nil {
		return false, err
	}

	if pod.GetAnnotations() == nil {
		return false, fmt.Errorf("pod (%s/%s) has no annotation", pod.GetNamespace(), pod.GetName())
	}

	if pod.GetAnnotations()[podEnableIPv6Annotation] == "true" {
		return true, nil
	}

	return false, nil
}

// recovery bcinode when node agent startup
// get bcinode.spec from etcd
// get bcinode.status from current pods running on node
func (ipam *IPAM) RecoveryFromReboot(ctx context.Context, bciNodeSpec *networkingv1.BciNodeSpec) {
	logger.Infof(ctx, "recovery bcinode")
	ipam.BciNode = &networkingv1.BciNode{}
	ipam.BciNode.Spec = *bciNodeSpec.DeepCopy()

	for userID := range ipam.BciNode.Spec.EniMultiIP.Pool {
		podNamespaceName, err := ipam.getPodNamesOnCurrentNodeByUserID(ctx, userID)
		if err != nil {
			logger.Errorf(ctx, err.Error())
			continue
		}
		for podName, podNamespaceIP := range podNamespaceName {
			podNamespace := strings.Split(podNamespaceIP, "/")[0]
			multiIPStr := strings.Split(podNamespaceIP, "/")[1]
			if multiIPStr == "none" {
				logger.Errorf(ctx, "pod ip not found, %s/%s", podNamespace, podName)
				continue
			}

			subnetID, secIDs, err := ipam.getPodSubnetIDAndSecIDs(ctx, userID, podName, podNamespace)
			if err != nil {
				logger.Errorf(ctx, "get pod subnet and sec error: %v", err)
				continue
			}

			eniID, err := ipam.getEniIDBySubnetAndSecIDsFromSpec(ctx, userID, subnetID, secIDs)
			if err != nil {
				logger.Errorf(ctx, err.Error())
				continue
			}

			multiIPList := strings.Split(multiIPStr, ",")
			_, markUsedError := ipam.markIPUsed(ctx, userID, eniID, multiIPList, podName, podNamespace, "")
			if markUsedError != nil {
				logger.Errorf(ctx, markUsedError.Error())
			}
		}
	}
}

// caller responsible use rlock before read
func (ipam *IPAM) LoadMetaFromDisk(ctx context.Context) error {
	file, err := os.ReadFile(BciNodeMetaFilePath)
	if err != nil {
		logger.Errorf(ctx, "read meta error, try to read from etcd")
		// TODO: read from crd and return
	}
	err = json.Unmarshal(file, &ipam.BciNode.Status)
	if err != nil {
		logger.Errorf(ctx, "unmarshal meta error")
		return err
	}

	return nil
}

// caller responsible use lock before write
func (ipam *IPAM) SaveMetaToDisk(ctx context.Context) error {
	if ipam.BciNode == nil {
		return errors.New("save meata error, bciNode nil")
	}

	file, err := json.MarshalIndent(ipam.BciNode.Status, "", "    ")
	if err != nil {
		logger.Errorf(ctx, "marshal meta error")
		return err
	}
	err = os.WriteFile(BciNodeMetaFilePath, file, 0644)
	if err != nil {
		logger.Errorf(ctx, "save meta error")
		return err
	}

	return nil
}
