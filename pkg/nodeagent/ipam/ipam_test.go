/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package ipam

import (
	"context"
	"net"
	"reflect"
	"sync"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/vishvananda/netlink"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"

	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam/mock"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/utils/trigger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/queue"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
	netlinkwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink"
	mocknetlink "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink/mock"
)

func setupEnv(t *testing.T) (
	*gomock.Controller,
	*mock.MockRuntimeCache,
	*mock.MockRuntimeClient,
) {
	ctrl := gomock.NewController(t)

	cache := mock.NewMockRuntimeCache(ctrl)
	client := mock.NewMockRuntimeClient(ctrl)

	return ctrl, cache, client
}

func TestIPAM_getEniIDBySubnetSecIDFromSpec(t *testing.T) {
	type fields struct {
		ctrl          *gomock.Controller
		lock          sync.RWMutex
		BciNodeStatus networkingv1.BciNodeStatus
		BciNodeSpec   networkingv1.BciNodeSpec
		BciNode       networkingv1.BciNode
		UserIdlePool  map[string]map[string]*queue.Queue
		Lookup        map[string]bool
		MarkIPUsed    map[string]string
		Cache         cache.Cache
		Client        client.Client
	}
	type args struct {
		ctx      context.Context
		userID   string
		subnetID string
		secID    string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "user not found",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				return fields{
					ctrl:          ctrl,
					lock:          sync.RWMutex{},
					BciNodeStatus: networkingv1.BciNodeStatus{},
					BciNodeSpec:   networkingv1.BciNodeSpec{},
					BciNode: networkingv1.BciNode{
						Spec: networkingv1.BciNodeSpec{
							InstanceID:   "",
							InstanceType: "",
							EniMultiIP: networkingv1.EniMultiIPSpec{
								Pool:              map[string]networkingv1.UserAllocationEnis{},
								MinAllocate:       0,
								MaxAllocate:       0,
								PreAllocate:       0,
								MaxAboveWatermark: 0,
							},
						},
						Status: networkingv1.BciNodeStatus{
							EniMultiIP: networkingv1.EniMultiIPStatus{
								Used:     map[string]networkingv1.UserAllocationEnis{},
								NotReady: []string{},
							},
						},
					},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					MarkIPUsed:   map[string]string{},
					Cache:        cache,
					Client:       client,
				}
			}(),
			args: args{
				ctx:      context.Background(),
				userID:   "",
				subnetID: "",
				secID:    "",
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "one security group",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				return fields{
					ctrl:          ctrl,
					lock:          sync.RWMutex{},
					BciNodeStatus: networkingv1.BciNodeStatus{},
					BciNodeSpec:   networkingv1.BciNodeSpec{},
					BciNode: networkingv1.BciNode{
						Spec: networkingv1.BciNodeSpec{
							InstanceID:   "",
							InstanceType: "",
							EniMultiIP: networkingv1.EniMultiIPSpec{
								Pool: map[string]networkingv1.UserAllocationEnis{
									"user1": {
										"eni-xxx": &networkingv1.AllocationEni{
											UserID:             "",
											EniID:              "",
											MacAddress:         "",
											SubnetID:           "sbn-1",
											SecurityGroupIDs:   []string{"g-aaa"},
											VpcID:              "",
											VpcCIDR:            "",
											PrimaryIPAddress:   "",
											PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
										},
									},
								},
								MinAllocate:       0,
								MaxAllocate:       0,
								PreAllocate:       0,
								MaxAboveWatermark: 0,
							},
						},
						Status: networkingv1.BciNodeStatus{
							EniMultiIP: networkingv1.EniMultiIPStatus{
								Used:     map[string]networkingv1.UserAllocationEnis{},
								NotReady: []string{},
							},
						},
					},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					MarkIPUsed:   map[string]string{},
					Cache:        cache,
					Client:       client,
				}
			}(),
			args: args{
				ctx:      context.Background(),
				userID:   "user1",
				subnetID: "sbn-1",
				secID:    "g-aaa",
			},
			want:    "eni-xxx",
			wantErr: false,
		},
		{
			name: "two security groups",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				return fields{
					ctrl:          ctrl,
					lock:          sync.RWMutex{},
					BciNodeStatus: networkingv1.BciNodeStatus{},
					BciNodeSpec:   networkingv1.BciNodeSpec{},
					BciNode: networkingv1.BciNode{
						Spec: networkingv1.BciNodeSpec{
							InstanceID:   "",
							InstanceType: "",
							EniMultiIP: networkingv1.EniMultiIPSpec{
								Pool: map[string]networkingv1.UserAllocationEnis{
									"user1": {
										"eni-xxx": &networkingv1.AllocationEni{
											UserID:             "",
											EniID:              "",
											MacAddress:         "",
											SubnetID:           "sbn-1",
											SecurityGroupIDs:   []string{"g-aaa"},
											VpcID:              "",
											VpcCIDR:            "",
											PrimaryIPAddress:   "",
											PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
										},
										"eni-yyy": &networkingv1.AllocationEni{
											UserID:             "",
											EniID:              "",
											MacAddress:         "",
											SubnetID:           "sbn-1",
											SecurityGroupIDs:   []string{"g-bbb", "g-aaa"},
											VpcID:              "",
											VpcCIDR:            "",
											PrimaryIPAddress:   "",
											PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
										},
									},
								},
								MinAllocate:       0,
								MaxAllocate:       0,
								PreAllocate:       0,
								MaxAboveWatermark: 0,
							},
						},
						Status: networkingv1.BciNodeStatus{
							EniMultiIP: networkingv1.EniMultiIPStatus{
								Used:     map[string]networkingv1.UserAllocationEnis{},
								NotReady: []string{},
							},
						},
					},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					MarkIPUsed:   map[string]string{},
					Cache:        cache,
					Client:       client,
				}
			}(),
			args: args{
				ctx:      context.Background(),
				userID:   "user1",
				subnetID: "sbn-1",
				secID:    "g-aaa, g-bbb",
			},
			want:    "eni-yyy",
			wantErr: false,
		},
		{
			name: "none eni is satisfied",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				return fields{
					ctrl:          ctrl,
					lock:          sync.RWMutex{},
					BciNodeStatus: networkingv1.BciNodeStatus{},
					BciNodeSpec:   networkingv1.BciNodeSpec{},
					BciNode: networkingv1.BciNode{
						Spec: networkingv1.BciNodeSpec{
							InstanceID:   "",
							InstanceType: "",
							EniMultiIP: networkingv1.EniMultiIPSpec{
								Pool: map[string]networkingv1.UserAllocationEnis{
									"user1": {
										"eni-xxx": &networkingv1.AllocationEni{
											UserID:             "",
											EniID:              "",
											MacAddress:         "",
											SubnetID:           "sbn-1",
											SecurityGroupIDs:   []string{"g-aaa"},
											VpcID:              "",
											VpcCIDR:            "",
											PrimaryIPAddress:   "",
											PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
										},
										"eni-yyy": &networkingv1.AllocationEni{
											UserID:             "",
											EniID:              "",
											MacAddress:         "",
											SubnetID:           "sbn-1",
											SecurityGroupIDs:   []string{"g-bbb", "g-aaa"},
											VpcID:              "",
											VpcCIDR:            "",
											PrimaryIPAddress:   "",
											PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
										},
									},
								},
								MinAllocate:       0,
								MaxAllocate:       0,
								PreAllocate:       0,
								MaxAboveWatermark: 0,
							},
						},
						Status: networkingv1.BciNodeStatus{
							EniMultiIP: networkingv1.EniMultiIPStatus{
								Used:     map[string]networkingv1.UserAllocationEnis{},
								NotReady: []string{},
							},
						},
					},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					MarkIPUsed:   map[string]string{},
					Cache:        cache,
					Client:       client,
				}
			}(),
			args: args{
				ctx:      context.Background(),
				userID:   "user1",
				subnetID: "sbn-1",
				secID:    "g-aaa,g-bbb,g-ccc",
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "admin eni not found",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				return fields{
					ctrl:          ctrl,
					lock:          sync.RWMutex{},
					BciNodeStatus: networkingv1.BciNodeStatus{},
					BciNodeSpec:   networkingv1.BciNodeSpec{},
					BciNode: networkingv1.BciNode{
						Spec: networkingv1.BciNodeSpec{
							EniMultiIP: networkingv1.EniMultiIPSpec{
								Pool: map[string]networkingv1.UserAllocationEnis{
									ADMINUSERNAME: map[string]*networkingv1.AllocationEni{},
								},
							},
						},
					},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					MarkIPUsed:   map[string]string{},
					Cache:        cache,
					Client:       client,
				}
			}(),
			args: args{
				ctx:      context.Background(),
				userID:   ADMINUSERNAME,
				subnetID: "",
				secID:    "",
			},
			want:    "",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			ipam := &IPAM{
				lock:         tt.fields.lock,
				BciNode:      &tt.fields.BciNode,
				UserIdlePool: tt.fields.UserIdlePool,
				Lookup:       tt.fields.Lookup,
				Cache:        tt.fields.Cache,
				Client:       tt.fields.Client,
			}
			got, err := ipam.getEniIDBySubnetAndSecIDsFromSpec(tt.args.ctx, tt.args.userID, tt.args.subnetID, tt.args.secID)
			if (err != nil) != tt.wantErr {
				t.Errorf("IPAM.getEniIDBySubnetAndSecIDsFromSpec() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IPAM.getEniIDBySubnetAndSecIDsFromSpec() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIPAM_ReleaseIP(t *testing.T) {
	type fields struct {
		ctrl           *gomock.Controller
		lock           sync.RWMutex
		UserIdlePool   map[string]map[string]*queue.Queue
		Lookup         map[string]bool
		Cache          cache.Cache
		BciNode        *networkingv1.BciNode
		RefreshTrigger *trigger.Trigger
		Client         client.Client
	}
	type args struct {
		ctx              context.Context
		podName          string
		podNameSpace     string
		podNameSpaceName string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				gomock.InOrder(
					client.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					client.EXPECT().Patch(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				)

				trigger, _ := trigger.NewTrigger(trigger.Parameters{
					MinInterval:  0,
					TriggerFunc:  func([]string) { logger.Info(context.TODO(), "test triggered") },
					ShutdownFunc: func() { logger.Info(context.TODO(), "shutdown") },
					Name:         "ut",
				})

				return fields{
					ctrl:           ctrl,
					lock:           sync.RWMutex{},
					UserIdlePool:   map[string]map[string]*queue.Queue{},
					Lookup:         map[string]bool{},
					Cache:          cache,
					RefreshTrigger: trigger,
					BciNode: &networkingv1.BciNode{
						TypeMeta: v1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: v1.ObjectMeta{},
						Spec: networkingv1.BciNodeSpec{
							InstanceID:   "",
							InstanceType: "",
							EniMultiIP: networkingv1.EniMultiIPSpec{
								Pool: map[string]networkingv1.UserAllocationEnis{
									"********************************": map[string]*networkingv1.AllocationEni{
										"eni-0vkhfb98xm78": {
											UserID:             "********************************",
											EniID:              "eni-0vkhfb98xm78",
											MacAddress:         "",
											SubnetID:           "",
											SecurityGroupIDs:   []string{},
											VpcID:              "",
											VpcCIDR:            "",
											PrimaryIPAddress:   "",
											PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
										},
									},
								},
								MinAllocate:       0,
								MaxAllocate:       0,
								PreAllocate:       0,
								MaxAboveWatermark: 0,
							},
						},
						Status: networkingv1.BciNodeStatus{
							EniMultiIP: networkingv1.EniMultiIPStatus{
								Used: map[string]networkingv1.UserAllocationEnis{
									"********************************": map[string]*networkingv1.AllocationEni{
										"eni-0vkhfb98xm78": {
											UserID:           "********************************",
											EniID:            "eni-0vkhfb98xm78",
											MacAddress:       "",
											SubnetID:         "",
											SecurityGroupIDs: []string{},
											VpcID:            "",
											VpcCIDR:          "",
											PrimaryIPAddress: "",
											PrivateIPAddresses: map[string]networkingv1.AllocationIP{
												"*************": {
													UserID:      "********************************",
													EniID:       "eni-0vkhfb98xm78",
													EIP:         nil,
													Owner:       "********************************/busybox",
													ContainerID: "2704632908dbacba91297d1e19c092f4e9586f616e77f4611788c5ce50570dc6",
												},
											},
										},
									},
								},
								NotReady: []string{},
							},
						},
					},
					Client: client,
				}
			}(),
			args: args{
				ctx:          context.Background(),
				podName:      "busybox",
				podNameSpace: "********************************",
			},
			want: "********************************",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			ipam := &IPAM{
				UserIdlePool:   tt.fields.UserIdlePool,
				Lookup:         tt.fields.Lookup,
				Cache:          tt.fields.Cache,
				BciNode:        tt.fields.BciNode,
				RefreshTrigger: tt.fields.RefreshTrigger,
				Client:         tt.fields.Client,
			}
			if err := ipam.ReleaseIP(tt.args.ctx, tt.args.podName, tt.args.podNameSpace, ""); err != nil {
				t.Errorf("ipam.ReleaseIP")
			}
		})
	}
}

func TestIPAM_MarkIPUnused(t *testing.T) {
	type fields struct {
		ctrl           *gomock.Controller
		lock           sync.RWMutex
		UserIdlePool   map[string]map[string]*queue.Queue
		Lookup         map[string]bool
		Cache          cache.Cache
		BciNode        *networkingv1.BciNode
		RefreshTrigger *trigger.Trigger
		Client         client.Client
	}
	type args struct {
		ctx              context.Context
		podName          string
		podNameSpace     string
		podNameSpaceName string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				return fields{
					ctrl:         ctrl,
					lock:         sync.RWMutex{},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					Cache:        cache,
					BciNode: &networkingv1.BciNode{
						TypeMeta: v1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: v1.ObjectMeta{},
						Spec: networkingv1.BciNodeSpec{
							InstanceID:   "",
							InstanceType: "",
							EniMultiIP:   networkingv1.EniMultiIPSpec{},
						},
						Status: networkingv1.BciNodeStatus{
							EniMultiIP: networkingv1.EniMultiIPStatus{
								Used: map[string]networkingv1.UserAllocationEnis{
									"********************************": map[string]*networkingv1.AllocationEni{
										"eni-0vkhfb98xm78": {
											UserID:           "********************************",
											EniID:            "eni-0vkhfb98xm78",
											MacAddress:       "",
											SubnetID:         "",
											SecurityGroupIDs: []string{},
											VpcID:            "",
											VpcCIDR:          "",
											PrimaryIPAddress: "",
											PrivateIPAddresses: map[string]networkingv1.AllocationIP{
												"*************": {
													UserID:      "********************************",
													EniID:       "eni-0vkhfb98xm78",
													EIP:         nil,
													Owner:       "********************************/busybox",
													ContainerID: "2704632908dbacba91297d1e19c092f4e9586f616e77f4611788c5ce50570dc6",
												},
											},
										},
									},
								},
								NotReady: []string{},
							},
						},
					},
					Client: client,
				}
			}(),
			args: args{
				ctx:          context.Background(),
				podName:      "busybox",
				podNameSpace: "********************************",
			},
			want: "********************************",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			ipam := &IPAM{
				UserIdlePool:   tt.fields.UserIdlePool,
				Lookup:         tt.fields.Lookup,
				Cache:          tt.fields.Cache,
				BciNode:        tt.fields.BciNode,
				RefreshTrigger: tt.fields.RefreshTrigger,
				Client:         tt.fields.Client,
			}
			if err := ipam.markIPUnused(tt.args.ctx, tt.args.podName, tt.args.podNameSpace, ""); err != nil {
				t.Errorf("ipam.ReleaseIP")
			}
		})
	}
}

func TestIPAM_AllocateIP_PFS(t *testing.T) {
	RequireUniqueRouteTable = true
	defer func() {
		RequireUniqueRouteTable = false
	}()

	type fields struct {
		ctrl           *gomock.Controller
		lock           sync.RWMutex
		UserIdlePool   map[string]map[string]*queue.Queue
		Lookup         map[string]bool
		Cache          cache.Cache
		BciNode        *networkingv1.BciNode
		RefreshTrigger *trigger.Trigger
		WaitGCPod      map[string]time.Time
		Nlink          netlinkwrapper.Interface
		Client         client.Client
	}
	type args struct {
		ctx          context.Context
		podName      string
		podNameSpace string
		podID        string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *rpc.ENIMultiIPReply
		wantErr bool
	}{
		{
			name: "pfs normal process",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)
				nlink := mocknetlink.NewMockInterface(ctrl)

				ctx := context.TODO()

				trigger, _ := trigger.NewTrigger(trigger.Parameters{
					MinInterval:  0,
					TriggerFunc:  func([]string) { logger.Info(ctx, "test triggered") },
					ShutdownFunc: func() { logger.Info(ctx, "shutdown") },
					Name:         "ut",
				})

				pod := corev1.Pod{
					ObjectMeta: v1.ObjectMeta{
						Name:              "",
						Namespace:         "",
						Generation:        0,
						CreationTimestamp: v1.Time{},
						DeletionTimestamp: &v1.Time{},
						Labels:            map[string]string{},
						Annotations: map[string]string{
							"cross-vpc-eni.cce.io/subnetID":         "sbn-1",
							"cross-vpc-eni.cce.io/securityGroupIDs": "g-aaa, g-bbb",
							"bci_internal_AccountID":                "",
						},
					},
					Spec: corev1.PodSpec{
						Volumes:                       []corev1.Volume{},
						InitContainers:                []corev1.Container{},
						Containers:                    []corev1.Container{},
						EphemeralContainers:           []corev1.EphemeralContainer{},
						RestartPolicy:                 "",
						TerminationGracePeriodSeconds: nil,
						ActiveDeadlineSeconds:         nil,
						DNSPolicy:                     "",
						NodeSelector: map[string]string{
							"": "",
						},
						ServiceAccountName:           "",
						DeprecatedServiceAccount:     "",
						AutomountServiceAccountToken: nil,
						NodeName:                     "",
						HostNetwork:                  false,
						HostPID:                      false,
						HostIPC:                      false,
						ShareProcessNamespace:        nil,
						SecurityContext: &corev1.PodSecurityContext{
							SELinuxOptions: &corev1.SELinuxOptions{
								User:  "",
								Role:  "",
								Type:  "",
								Level: "",
							},
							WindowsOptions: &corev1.WindowsSecurityContextOptions{
								GMSACredentialSpecName: nil,
								GMSACredentialSpec:     nil,
								RunAsUserName:          nil,
								HostProcess:            nil,
							},
							RunAsUser:           nil,
							RunAsGroup:          nil,
							RunAsNonRoot:        nil,
							SupplementalGroups:  []int64{},
							FSGroup:             nil,
							Sysctls:             []corev1.Sysctl{},
							FSGroupChangePolicy: nil,
							SeccompProfile: &corev1.SeccompProfile{
								Type:             "",
								LocalhostProfile: nil,
							},
						},
						ImagePullSecrets: []corev1.LocalObjectReference{},
						Hostname:         "",
						Subdomain:        "",
						Affinity: &corev1.Affinity{
							NodeAffinity: &corev1.NodeAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
									NodeSelectorTerms: []corev1.NodeSelectorTerm{},
								},
								PreferredDuringSchedulingIgnoredDuringExecution: []corev1.PreferredSchedulingTerm{},
							},
							PodAffinity: &corev1.PodAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
								PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
							},
							PodAntiAffinity: &corev1.PodAntiAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
								PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
							},
						},
						SchedulerName:     "",
						Tolerations:       []corev1.Toleration{},
						HostAliases:       []corev1.HostAlias{},
						PriorityClassName: "",
						Priority:          nil,
						DNSConfig: &corev1.PodDNSConfig{
							Nameservers: []string{},
							Searches:    []string{},
							Options:     []corev1.PodDNSConfigOption{},
						},
						ReadinessGates:     []corev1.PodReadinessGate{},
						RuntimeClassName:   nil,
						EnableServiceLinks: nil,
						PreemptionPolicy:   nil,
						Overhead: map[corev1.ResourceName]resource.Quantity{
							"": {
								Format: "",
							},
						},
						TopologySpreadConstraints: []corev1.TopologySpreadConstraint{},
						SetHostnameAsFQDN:         nil,
					},
					Status: corev1.PodStatus{
						Phase:             "",
						Conditions:        []corev1.PodCondition{},
						Message:           "",
						Reason:            "",
						NominatedNodeName: "",
						HostIP:            "",
						PodIP:             "",
						PodIPs:            []corev1.PodIP{},
						StartTime: &v1.Time{
							Time: time.Time{},
						},
						InitContainerStatuses:      []corev1.ContainerStatus{},
						ContainerStatuses:          []corev1.ContainerStatus{},
						QOSClass:                   "",
						EphemeralContainerStatuses: []corev1.ContainerStatus{},
					},
				}

				gomock.InOrder(
					// isAdminPod
					cache.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, pod).Return(nil),
					// getPodSubnetIDAndSecIDs
					cache.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, pod).Return(nil),

					// PatchBciInternalIP
					client.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, pod).Return(nil),
					client.EXPECT().Patch(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),

					// FindEniLinkByHWAddress with retry
					nlink.EXPECT().LinkList().Return([]netlink.Link{
						&netlink.Dummy{
							LinkAttrs: netlink.LinkAttrs{
								Index:        0,
								Name:         "eth1",
								HardwareAddr: []byte{},
							},
						},
					}, nil),
					nlink.EXPECT().LinkList().Return([]netlink.Link{
						&netlink.Dummy{
							LinkAttrs: netlink.LinkAttrs{
								Index:        0,
								Name:         "eth1",
								HardwareAddr: net.HardwareAddr{0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
							},
						},
					}, nil),
				)

				return fields{
					ctrl:         ctrl,
					lock:         sync.RWMutex{},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup: map[string]bool{
						"": false,
					},
					Cache: cache,
					BciNode: &networkingv1.BciNode{
						Spec: networkingv1.BciNodeSpec{
							InstanceID:   "",
							InstanceType: "",
							EniMultiIP: networkingv1.EniMultiIPSpec{
								Pool: map[string]networkingv1.UserAllocationEnis{
									"user1": {
										"eni-xxx": &networkingv1.AllocationEni{
											UserID:             "",
											EniID:              "",
											MacAddress:         "",
											SubnetID:           "sbn-1",
											SecurityGroupIDs:   []string{"g-aaa"},
											VpcID:              "",
											VpcCIDR:            "",
											PrimaryIPAddress:   "",
											PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
										},
										"eni-yyy": &networkingv1.AllocationEni{
											UserID:             "",
											EniID:              "",
											MacAddress:         "ff:ff:ff:ff:ff:ff",
											SubnetID:           "sbn-1",
											SecurityGroupIDs:   []string{"g-bbb", "g-aaa"},
											VpcID:              "",
											VpcCIDR:            "",
											PrimaryIPAddress:   "",
											PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
										},
									},
								},
								MinAllocate:       0,
								MaxAllocate:       0,
								PreAllocate:       0,
								MaxAboveWatermark: 0,
							},
						},
						Status: networkingv1.BciNodeStatus{
							EniMultiIP: networkingv1.EniMultiIPStatus{
								Used: map[string]networkingv1.UserAllocationEnis{
									"user1": {
										"eni-yyy": &networkingv1.AllocationEni{
											PrivateIPAddresses: map[string]networkingv1.AllocationIP{
												"*******": {
													Owner: "user1/p-xxx",
												},
											},
										},
									},
								},
								NotReady: []string{},
							},
						},
					},
					RefreshTrigger: trigger,
					WaitGCPod:      map[string]time.Time{},
					Nlink:          nlink,
					Client:         client,
				}
			}(),
			args: args{
				ctx:          context.Background(),
				podName:      "p-xxx",
				podNameSpace: "user1",
				podID:        "",
			},
			want: &rpc.ENIMultiIPReply{
				IP:                         "*******",
				Mac:                        "ff:ff:ff:ff:ff:ff",
				Gateway:                    "***************",
				EniNetns:                   "/proc/1/ns/net",
				EniRequireUniqueRouteTable: true,
				EniRouteTable:              128,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ipam := &IPAM{
				lock:           tt.fields.lock,
				UserIdlePool:   tt.fields.UserIdlePool,
				Lookup:         tt.fields.Lookup,
				Cache:          tt.fields.Cache,
				BciNode:        tt.fields.BciNode,
				RefreshTrigger: tt.fields.RefreshTrigger,
				WaitGCPod:      tt.fields.WaitGCPod,
				Nlink:          tt.fields.Nlink,
				Client:         tt.fields.Client,
			}
			got, err := ipam.AllocateIP(tt.args.ctx, tt.args.podName, tt.args.podNameSpace, tt.args.podID)
			if (err != nil) != tt.wantErr {
				t.Errorf("IPAM.AllocateIP() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("IPAM.AllocateIP() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIPAM_ipIsUsed(t *testing.T) {
	type fields struct {
		lock           sync.RWMutex
		UserIdlePool   map[string]map[string]*queue.Queue
		Lookup         map[string]bool
		Cache          cache.Cache
		BciNode        *networkingv1.BciNode
		RefreshTrigger *trigger.Trigger
		WaitGCPod      map[string]time.Time
		WaitReleaseIP  map[string]map[string]networkingv1.IPReleaseStatus
		Nlink          netlinkwrapper.Interface
		Client         client.Client
	}
	type args struct {
		userID string
		eniID  string
		ip     string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "case 1",
			fields: fields{
				lock:         sync.RWMutex{},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				Cache:        nil,
				BciNode: &networkingv1.BciNode{
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"userID": map[string]*networkingv1.AllocationEni{
									"eniID": {
										PrivateIPAddresses: map[string]networkingv1.AllocationIP{
											"*******": {},
										},
									},
								},
							},
							NotReady: []string{},
						},
						WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					},
				},
				RefreshTrigger: &trigger.Trigger{},
				WaitGCPod:      map[string]time.Time{},
				WaitReleaseIP:  map[string]map[string]networkingv1.IPReleaseStatus{},
				Nlink:          nil,
				Client:         nil,
			},
			args: args{
				userID: "userID",
				eniID:  "eniID",
				ip:     "*******",
			},
			want: true,
		},
		{
			name: "case 2",
			fields: fields{
				lock:         sync.RWMutex{},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				Cache:        nil,
				BciNode: &networkingv1.BciNode{
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"": map[string]*networkingv1.AllocationEni{
									"": {},
								},
							},
							NotReady: []string{},
						},
						WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					},
				},
				RefreshTrigger: &trigger.Trigger{},
				WaitGCPod:      map[string]time.Time{},
				WaitReleaseIP:  map[string]map[string]networkingv1.IPReleaseStatus{},
				Nlink:          nil,
				Client:         nil,
			},
			args: args{
				userID: "userID",
				eniID:  "eniID",
				ip:     "*******",
			},
			want: false,
		},
		{
			name: "case 3",
			fields: fields{
				lock:         sync.RWMutex{},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				Cache:        nil,
				BciNode: &networkingv1.BciNode{
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"userID": map[string]*networkingv1.AllocationEni{
									"": {},
								},
							},
							NotReady: []string{},
						},
						WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					},
				},
				RefreshTrigger: &trigger.Trigger{},
				WaitGCPod:      map[string]time.Time{},
				WaitReleaseIP:  map[string]map[string]networkingv1.IPReleaseStatus{},
				Nlink:          nil,
				Client:         nil,
			},
			args: args{
				userID: "userID",
				eniID:  "eniID",
				ip:     "*******",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ipam := &IPAM{
				lock:           tt.fields.lock,
				UserIdlePool:   tt.fields.UserIdlePool,
				Lookup:         tt.fields.Lookup,
				Cache:          tt.fields.Cache,
				BciNode:        tt.fields.BciNode,
				RefreshTrigger: tt.fields.RefreshTrigger,
				WaitGCPod:      tt.fields.WaitGCPod,
				WaitReleaseIP:  tt.fields.WaitReleaseIP,
				Nlink:          tt.fields.Nlink,
				Client:         tt.fields.Client,
			}
			if got := ipam.ipIsUsed(tt.args.userID, tt.args.eniID, tt.args.ip); got != tt.want {
				t.Errorf("IPAM.ipIsUsed() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIPAM_RebuildUserIdlePool(t *testing.T) {
	type fields struct {
		lock           sync.RWMutex
		UserIdlePool   map[string]map[string]*queue.Queue
		Lookup         map[string]bool
		Cache          cache.Cache
		BciNode        *networkingv1.BciNode
		RefreshTrigger *trigger.Trigger
		WaitGCPod      map[string]time.Time
		WaitReleaseIP  map[string]map[string]networkingv1.IPReleaseStatus
		Nlink          netlinkwrapper.Interface
		Client         client.Client
	}
	type args struct {
		ctx            context.Context
		allocationPool networkingv1.AllocationMap
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "",
			fields: fields{
				lock:         sync.RWMutex{},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				Cache:        nil,
				BciNode: &networkingv1.BciNode{
					TypeMeta: v1.TypeMeta{
						Kind:       "",
						APIVersion: "",
					},
					ObjectMeta: v1.ObjectMeta{
						Name:            "",
						GenerateName:    "",
						Namespace:       "",
						SelfLink:        "",
						UID:             "",
						ResourceVersion: "",
						Generation:      0,
						CreationTimestamp: v1.Time{
							Time: time.Time{},
						},
						DeletionTimestamp: &v1.Time{
							Time: time.Time{},
						},
						DeletionGracePeriodSeconds: nil,
						Labels: map[string]string{
							"": "",
						},
						Annotations: map[string]string{
							"": "",
						},
						OwnerReferences: []v1.OwnerReference{},
						Finalizers:      []string{},
						ManagedFields:   []v1.ManagedFieldsEntry{},
					},
					Spec: networkingv1.BciNodeSpec{
						InstanceID:   "",
						InstanceType: "",
						EniMultiIP: networkingv1.EniMultiIPSpec{
							Pool: map[string]networkingv1.UserAllocationEnis{
								"": map[string]*networkingv1.AllocationEni{
									"": {
										UserID:           "",
										EniID:            "",
										MacAddress:       "",
										SubnetID:         "",
										SecurityGroupIDs: []string{},
										VpcID:            "",
										VpcCIDR:          "",
										PrimaryIPAddress: "",
										PrivateIPAddresses: map[string]networkingv1.AllocationIP{
											"": {
												UserID:      "",
												EniID:       "",
												EIP:         nil,
												Owner:       "",
												ContainerID: "",
											},
										},
									},
								},
							},
							MinAllocate:       0,
							MaxAllocate:       0,
							PreAllocate:       0,
							MaxAboveWatermark: 0,
						},
					},
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"user1": map[string]*networkingv1.AllocationEni{
									"eni1": {
										PrivateIPAddresses: map[string]networkingv1.AllocationIP{
											"*******": {},
										},
									},
								},
							},
							NotReady: []string{},
						},
						WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					},
				},
				RefreshTrigger: &trigger.Trigger{},
				WaitGCPod:      map[string]time.Time{},
				WaitReleaseIP:  map[string]map[string]networkingv1.IPReleaseStatus{},
				Nlink:          nil,
				Client:         nil,
			},
			args: args{
				ctx: context.TODO(),
				allocationPool: map[string]networkingv1.UserAllocationEnis{
					"user1": map[string]*networkingv1.AllocationEni{
						"eni1": {
							UserID:           "",
							EniID:            "",
							MacAddress:       "",
							SubnetID:         "",
							SecurityGroupIDs: []string{},
							VpcID:            "",
							VpcCIDR:          "",
							PrimaryIPAddress: "",
							PrivateIPAddresses: map[string]networkingv1.AllocationIP{
								"*******": {},
								"*******": {},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ipam := &IPAM{
				lock:           tt.fields.lock,
				UserIdlePool:   tt.fields.UserIdlePool,
				Lookup:         tt.fields.Lookup,
				Cache:          tt.fields.Cache,
				BciNode:        tt.fields.BciNode,
				RefreshTrigger: tt.fields.RefreshTrigger,
				WaitGCPod:      tt.fields.WaitGCPod,
				WaitReleaseIP:  tt.fields.WaitReleaseIP,
				Nlink:          tt.fields.Nlink,
				Client:         tt.fields.Client,
			}
			ipam.RebuildUserIdlePool(tt.args.ctx, tt.args.allocationPool)
		})
	}
}

func TestIPAM_PatchBciInternalIP(t *testing.T) {
	type fields struct {
		ctrl           *gomock.Controller
		lock           sync.RWMutex
		UserIdlePool   map[string]map[string]*queue.Queue
		Lookup         map[string]bool
		Cache          cache.Cache
		BciNode        *networkingv1.BciNode
		RefreshTrigger *trigger.Trigger
		WaitGCPod      map[string]time.Time
		WaitReleaseIP  map[string]map[string]networkingv1.IPReleaseStatus
		Nlink          netlinkwrapper.Interface
		Client         client.Client
	}
	type args struct {
		ctx       context.Context
		namespace string
		name      string
		ip        string
		ips       string
		eniID     string
		undo      bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "normal process",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				pod := corev1.Pod{
					ObjectMeta: v1.ObjectMeta{
						Name:              "",
						Namespace:         "",
						Generation:        0,
						CreationTimestamp: v1.Time{},
						DeletionTimestamp: &v1.Time{},
						Labels:            map[string]string{},
						Annotations: map[string]string{
							"cross-vpc-eni.cce.io/subnetID":         "sbn-1",
							"cross-vpc-eni.cce.io/securityGroupIDs": "g-aaa, g-bbb",
							"bci_internal_AccountID":                "",
						},
					},
					Spec: corev1.PodSpec{
						Volumes:                       []corev1.Volume{},
						InitContainers:                []corev1.Container{},
						Containers:                    []corev1.Container{},
						EphemeralContainers:           []corev1.EphemeralContainer{},
						RestartPolicy:                 "",
						TerminationGracePeriodSeconds: nil,
						ActiveDeadlineSeconds:         nil,
						DNSPolicy:                     "",
						NodeSelector: map[string]string{
							"": "",
						},
						ServiceAccountName:           "",
						DeprecatedServiceAccount:     "",
						AutomountServiceAccountToken: nil,
						NodeName:                     "",
						HostNetwork:                  false,
						HostPID:                      false,
						HostIPC:                      false,
						ShareProcessNamespace:        nil,
						SecurityContext: &corev1.PodSecurityContext{
							SELinuxOptions: &corev1.SELinuxOptions{
								User:  "",
								Role:  "",
								Type:  "",
								Level: "",
							},
							WindowsOptions: &corev1.WindowsSecurityContextOptions{
								GMSACredentialSpecName: nil,
								GMSACredentialSpec:     nil,
								RunAsUserName:          nil,
								HostProcess:            nil,
							},
							RunAsUser:           nil,
							RunAsGroup:          nil,
							RunAsNonRoot:        nil,
							SupplementalGroups:  []int64{},
							FSGroup:             nil,
							Sysctls:             []corev1.Sysctl{},
							FSGroupChangePolicy: nil,
							SeccompProfile: &corev1.SeccompProfile{
								Type:             "",
								LocalhostProfile: nil,
							},
						},
						ImagePullSecrets: []corev1.LocalObjectReference{},
						Hostname:         "",
						Subdomain:        "",
						Affinity: &corev1.Affinity{
							NodeAffinity: &corev1.NodeAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
									NodeSelectorTerms: []corev1.NodeSelectorTerm{},
								},
								PreferredDuringSchedulingIgnoredDuringExecution: []corev1.PreferredSchedulingTerm{},
							},
							PodAffinity: &corev1.PodAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
								PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
							},
							PodAntiAffinity: &corev1.PodAntiAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
								PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
							},
						},
						SchedulerName:     "",
						Tolerations:       []corev1.Toleration{},
						HostAliases:       []corev1.HostAlias{},
						PriorityClassName: "",
						Priority:          nil,
						DNSConfig: &corev1.PodDNSConfig{
							Nameservers: []string{},
							Searches:    []string{},
							Options:     []corev1.PodDNSConfigOption{},
						},
						ReadinessGates:     []corev1.PodReadinessGate{},
						RuntimeClassName:   nil,
						EnableServiceLinks: nil,
						PreemptionPolicy:   nil,
						Overhead: map[corev1.ResourceName]resource.Quantity{
							"": {
								Format: "",
							},
						},
						TopologySpreadConstraints: []corev1.TopologySpreadConstraint{},
						SetHostnameAsFQDN:         nil,
					},
					Status: corev1.PodStatus{
						Phase:             "",
						Conditions:        []corev1.PodCondition{},
						Message:           "",
						Reason:            "",
						NominatedNodeName: "",
						HostIP:            "",
						PodIP:             "",
						PodIPs:            []corev1.PodIP{},
						StartTime: &v1.Time{
							Time: time.Time{},
						},
						InitContainerStatuses:      []corev1.ContainerStatus{},
						ContainerStatuses:          []corev1.ContainerStatus{},
						QOSClass:                   "",
						EphemeralContainerStatuses: []corev1.ContainerStatus{},
					},
				}

				gomock.InOrder(
					client.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, pod).Return(nil),
					client.EXPECT().Patch(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				)

				return fields{
					ctrl:           ctrl,
					lock:           sync.RWMutex{},
					UserIdlePool:   map[string]map[string]*queue.Queue{},
					Lookup:         map[string]bool{},
					Cache:          cache,
					RefreshTrigger: &trigger.Trigger{},
					WaitGCPod:      map[string]time.Time{},
					WaitReleaseIP:  map[string]map[string]networkingv1.IPReleaseStatus{},
					Nlink:          nil,
					Client:         client,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				namespace: "default",
				name:      "busybox",
				ip:        "*******",
				ips:       "*******,*******,*******",
				eniID:     "eni-test",
				undo:      false,
			},
			wantErr: false,
		},
		{
			name: "normal undo process",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				pod := corev1.Pod{
					ObjectMeta: v1.ObjectMeta{
						Name:              "",
						Namespace:         "",
						Generation:        0,
						CreationTimestamp: v1.Time{},
						DeletionTimestamp: &v1.Time{},
						Labels:            map[string]string{},
						Annotations: map[string]string{
							"cross-vpc-eni.cce.io/subnetID":         "sbn-1",
							"cross-vpc-eni.cce.io/securityGroupIDs": "g-aaa, g-bbb",
							"bci_internal_AccountID":                "",
						},
					},
					Spec: corev1.PodSpec{
						Volumes:                       []corev1.Volume{},
						InitContainers:                []corev1.Container{},
						Containers:                    []corev1.Container{},
						EphemeralContainers:           []corev1.EphemeralContainer{},
						RestartPolicy:                 "",
						TerminationGracePeriodSeconds: nil,
						ActiveDeadlineSeconds:         nil,
						DNSPolicy:                     "",
						NodeSelector: map[string]string{
							"": "",
						},
						ServiceAccountName:           "",
						DeprecatedServiceAccount:     "",
						AutomountServiceAccountToken: nil,
						NodeName:                     "",
						HostNetwork:                  false,
						HostPID:                      false,
						HostIPC:                      false,
						ShareProcessNamespace:        nil,
						SecurityContext: &corev1.PodSecurityContext{
							SELinuxOptions: &corev1.SELinuxOptions{
								User:  "",
								Role:  "",
								Type:  "",
								Level: "",
							},
							WindowsOptions: &corev1.WindowsSecurityContextOptions{
								GMSACredentialSpecName: nil,
								GMSACredentialSpec:     nil,
								RunAsUserName:          nil,
								HostProcess:            nil,
							},
							RunAsUser:           nil,
							RunAsGroup:          nil,
							RunAsNonRoot:        nil,
							SupplementalGroups:  []int64{},
							FSGroup:             nil,
							Sysctls:             []corev1.Sysctl{},
							FSGroupChangePolicy: nil,
							SeccompProfile: &corev1.SeccompProfile{
								Type:             "",
								LocalhostProfile: nil,
							},
						},
						ImagePullSecrets: []corev1.LocalObjectReference{},
						Hostname:         "",
						Subdomain:        "",
						Affinity: &corev1.Affinity{
							NodeAffinity: &corev1.NodeAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
									NodeSelectorTerms: []corev1.NodeSelectorTerm{},
								},
								PreferredDuringSchedulingIgnoredDuringExecution: []corev1.PreferredSchedulingTerm{},
							},
							PodAffinity: &corev1.PodAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
								PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
							},
							PodAntiAffinity: &corev1.PodAntiAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
								PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
							},
						},
						SchedulerName:     "",
						Tolerations:       []corev1.Toleration{},
						HostAliases:       []corev1.HostAlias{},
						PriorityClassName: "",
						Priority:          nil,
						DNSConfig: &corev1.PodDNSConfig{
							Nameservers: []string{},
							Searches:    []string{},
							Options:     []corev1.PodDNSConfigOption{},
						},
						ReadinessGates:     []corev1.PodReadinessGate{},
						RuntimeClassName:   nil,
						EnableServiceLinks: nil,
						PreemptionPolicy:   nil,
						Overhead: map[corev1.ResourceName]resource.Quantity{
							"": {
								Format: "",
							},
						},
						TopologySpreadConstraints: []corev1.TopologySpreadConstraint{},
						SetHostnameAsFQDN:         nil,
					},
					Status: corev1.PodStatus{
						Phase:             "",
						Conditions:        []corev1.PodCondition{},
						Message:           "",
						Reason:            "",
						NominatedNodeName: "",
						HostIP:            "",
						PodIP:             "",
						PodIPs:            []corev1.PodIP{},
						StartTime: &v1.Time{
							Time: time.Time{},
						},
						InitContainerStatuses:      []corev1.ContainerStatus{},
						ContainerStatuses:          []corev1.ContainerStatus{},
						QOSClass:                   "",
						EphemeralContainerStatuses: []corev1.ContainerStatus{},
					},
				}

				gomock.InOrder(
					client.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, pod).Return(nil),
					client.EXPECT().Patch(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				)

				return fields{
					ctrl:           ctrl,
					lock:           sync.RWMutex{},
					UserIdlePool:   map[string]map[string]*queue.Queue{},
					Lookup:         map[string]bool{},
					Cache:          cache,
					RefreshTrigger: &trigger.Trigger{},
					WaitGCPod:      map[string]time.Time{},
					WaitReleaseIP:  map[string]map[string]networkingv1.IPReleaseStatus{},
					Nlink:          nil,
					Client:         client,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				namespace: "default",
				name:      "busybox",
				ip:        "*******",
				ips:       "*******,*******,*******",
				eniID:     "eni-test",
				undo:      true,
			},
			wantErr: false,
		},
		{
			name: "normal process",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				pod := corev1.Pod{
					ObjectMeta: v1.ObjectMeta{
						Name:              "",
						Namespace:         "",
						Generation:        0,
						CreationTimestamp: v1.Time{},
						DeletionTimestamp: &v1.Time{},
						Labels:            map[string]string{},
						Annotations: map[string]string{
							"cross-vpc-eni.cce.io/subnetID":         "sbn-1",
							"cross-vpc-eni.cce.io/securityGroupIDs": "g-aaa, g-bbb",
							"bci_internal_AccountID":                "",
						},
					},
					Spec: corev1.PodSpec{
						Volumes:                       []corev1.Volume{},
						InitContainers:                []corev1.Container{},
						Containers:                    []corev1.Container{},
						EphemeralContainers:           []corev1.EphemeralContainer{},
						RestartPolicy:                 "",
						TerminationGracePeriodSeconds: nil,
						ActiveDeadlineSeconds:         nil,
						DNSPolicy:                     "",
						NodeSelector: map[string]string{
							"": "",
						},
						ServiceAccountName:           "",
						DeprecatedServiceAccount:     "",
						AutomountServiceAccountToken: nil,
						NodeName:                     "",
						HostNetwork:                  false,
						HostPID:                      false,
						HostIPC:                      false,
						ShareProcessNamespace:        nil,
						SecurityContext: &corev1.PodSecurityContext{
							SELinuxOptions: &corev1.SELinuxOptions{
								User:  "",
								Role:  "",
								Type:  "",
								Level: "",
							},
							WindowsOptions: &corev1.WindowsSecurityContextOptions{
								GMSACredentialSpecName: nil,
								GMSACredentialSpec:     nil,
								RunAsUserName:          nil,
								HostProcess:            nil,
							},
							RunAsUser:           nil,
							RunAsGroup:          nil,
							RunAsNonRoot:        nil,
							SupplementalGroups:  []int64{},
							FSGroup:             nil,
							Sysctls:             []corev1.Sysctl{},
							FSGroupChangePolicy: nil,
							SeccompProfile: &corev1.SeccompProfile{
								Type:             "",
								LocalhostProfile: nil,
							},
						},
						ImagePullSecrets: []corev1.LocalObjectReference{},
						Hostname:         "",
						Subdomain:        "",
						Affinity: &corev1.Affinity{
							NodeAffinity: &corev1.NodeAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
									NodeSelectorTerms: []corev1.NodeSelectorTerm{},
								},
								PreferredDuringSchedulingIgnoredDuringExecution: []corev1.PreferredSchedulingTerm{},
							},
							PodAffinity: &corev1.PodAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
								PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
							},
							PodAntiAffinity: &corev1.PodAntiAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
								PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
							},
						},
						SchedulerName:     "",
						Tolerations:       []corev1.Toleration{},
						HostAliases:       []corev1.HostAlias{},
						PriorityClassName: "",
						Priority:          nil,
						DNSConfig: &corev1.PodDNSConfig{
							Nameservers: []string{},
							Searches:    []string{},
							Options:     []corev1.PodDNSConfigOption{},
						},
						ReadinessGates:     []corev1.PodReadinessGate{},
						RuntimeClassName:   nil,
						EnableServiceLinks: nil,
						PreemptionPolicy:   nil,
						Overhead: map[corev1.ResourceName]resource.Quantity{
							"": {
								Format: "",
							},
						},
						TopologySpreadConstraints: []corev1.TopologySpreadConstraint{},
						SetHostnameAsFQDN:         nil,
					},
					Status: corev1.PodStatus{
						Phase:             "",
						Conditions:        []corev1.PodCondition{},
						Message:           "",
						Reason:            "",
						NominatedNodeName: "",
						HostIP:            "",
						PodIP:             "",
						PodIPs:            []corev1.PodIP{},
						StartTime: &v1.Time{
							Time: time.Time{},
						},
						InitContainerStatuses:      []corev1.ContainerStatus{},
						ContainerStatuses:          []corev1.ContainerStatus{},
						QOSClass:                   "",
						EphemeralContainerStatuses: []corev1.ContainerStatus{},
					},
				}

				gomock.InOrder(
					client.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, pod).Return(nil),
					client.EXPECT().Patch(gomock.Any(), gomock.Any(), gomock.Any()).Return(net.ErrClosed),
				)

				return fields{
					ctrl:           ctrl,
					lock:           sync.RWMutex{},
					UserIdlePool:   map[string]map[string]*queue.Queue{},
					Lookup:         map[string]bool{},
					Cache:          cache,
					RefreshTrigger: &trigger.Trigger{},
					WaitGCPod:      map[string]time.Time{},
					WaitReleaseIP:  map[string]map[string]networkingv1.IPReleaseStatus{},
					Nlink:          nil,
					Client:         client,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				namespace: "default",
				name:      "busybox",
				ip:        "*******",
				ips:       "*******,*******,*******",
				eniID:     "eni-test",
				undo:      false,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ipam := &IPAM{
				lock:           tt.fields.lock,
				UserIdlePool:   tt.fields.UserIdlePool,
				Lookup:         tt.fields.Lookup,
				Cache:          tt.fields.Cache,
				BciNode:        tt.fields.BciNode,
				RefreshTrigger: tt.fields.RefreshTrigger,
				WaitGCPod:      tt.fields.WaitGCPod,
				WaitReleaseIP:  tt.fields.WaitReleaseIP,
				Nlink:          tt.fields.Nlink,
				Client:         tt.fields.Client,
			}
			if err := ipam.PatchBciInternalIP(tt.args.ctx, tt.args.namespace, tt.args.name, tt.args.ip, tt.args.ips, tt.args.eniID, tt.args.undo); (err != nil) != tt.wantErr {
				t.Errorf("IPAM.PatchBciInternalIP() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
