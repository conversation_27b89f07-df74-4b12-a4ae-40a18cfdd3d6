/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controllers

import (
	"context"
	"net"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/vishvananda/netlink"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"

	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	metadata "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/meta-data"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam"
	netlinkwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink"
)

func TestBciNodeReconciler_addFromEniRouteTable(t *testing.T) {
	type fields struct {
		ctrl         *gomock.Controller
		Client       client.Client
		Scheme       *runtime.Scheme
		bciNodeCache networkingv1.BciNode
		ipamCtrl     *ipam.IPAM
		nodeName     string
		nlink        netlinkwrapper.Interface
		metaclient   metadata.Interface
	}
	type args struct {
		ctx  context.Context
		eni  *networkingv1.AllocationEni
		intf netlink.Link
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "正常流程",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					meta.EXPECT().GetLinkGateway(gomock.Any(), gomock.Any()).Return("*******", nil),
					nlink.EXPECT().RouteReplace(gomock.Any()).Return(nil),
				)

				return fields{
					ctrl:         ctrl,
					Client:       client,
					Scheme:       &runtime.Scheme{},
					bciNodeCache: networkingv1.BciNode{},
					ipamCtrl:     &ipam.IPAM{},
					nodeName:     "",
					nlink:        nlink,
					metaclient:   meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:             "",
					EniID:              "",
					MacAddress:         "",
					SubnetID:           "",
					SecurityGroupIDs:   []string{},
					VpcID:              "",
					VpcCIDR:            "",
					PrimaryIPAddress:   "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
				},
				intf: &netlink.Dummy{
					LinkAttrs: netlink.LinkAttrs{
						Index:        0,
						Name:         "eth1",
						HardwareAddr: []byte{},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "RouteReplace 失败流程",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					meta.EXPECT().GetLinkGateway(gomock.Any(), gomock.Any()).Return("*******", nil),
					nlink.EXPECT().RouteReplace(gomock.Any()).Return(net.ErrClosed),
				)

				return fields{
					ctrl:         ctrl,
					Client:       client,
					Scheme:       &runtime.Scheme{},
					bciNodeCache: networkingv1.BciNode{},
					ipamCtrl:     &ipam.IPAM{},
					nodeName:     "",
					nlink:        nlink,
					metaclient:   meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:             "",
					EniID:              "",
					MacAddress:         "",
					SubnetID:           "",
					SecurityGroupIDs:   []string{},
					VpcID:              "",
					VpcCIDR:            "",
					PrimaryIPAddress:   "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
				},
				intf: &netlink.Dummy{
					LinkAttrs: netlink.LinkAttrs{
						Index:        0,
						Name:         "eth1",
						HardwareAddr: []byte{},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		if tt.fields.ctrl != nil {
			defer tt.fields.ctrl.Finish()
		}

		t.Run(tt.name, func(t *testing.T) {
			r := &BciNodeReconciler{
				Client:       tt.fields.Client,
				Scheme:       tt.fields.Scheme,
				bciNodeCache: tt.fields.bciNodeCache,
				ipamCtrl:     tt.fields.ipamCtrl,
				nodeName:     tt.fields.nodeName,
				nlink:        tt.fields.nlink,
				metaclient:   tt.fields.metaclient,
			}
			if err := r.addFromEniRouteTable(tt.args.ctx, tt.args.eni, tt.args.intf); (err != nil) != tt.wantErr {
				t.Errorf("BciNodeReconciler.addFromEniRouteTable() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestBciNodeReconciler_getEniGatewayWithFallback(t *testing.T) {
	type fields struct {
		ctrl         *gomock.Controller
		Client       client.Client
		Scheme       *runtime.Scheme
		bciNodeCache networkingv1.BciNode
		ipamCtrl     *ipam.IPAM
		nodeName     string
		nlink        netlinkwrapper.Interface
		metaclient   metadata.Interface
	}
	type args struct {
		ctx  context.Context
		eni  *networkingv1.AllocationEni
		intf netlink.Link
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    net.IP
		wantErr bool
	}{
		{
			name: "正常流程",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					meta.EXPECT().GetLinkGateway(gomock.Any(), gomock.Any()).Return("*******", nil),
				)

				return fields{
					ctrl:       ctrl,
					Client:     client,
					Scheme:     &runtime.Scheme{},
					nodeName:   "",
					nlink:      nlink,
					metaclient: meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:           "",
					EniID:            "",
					MacAddress:       "",
					SubnetID:         "",
					SecurityGroupIDs: []string{},
					VpcID:            "",
					VpcCIDR:          "",
					PrimaryIPAddress: "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{
						"": {
							UserID:      "",
							EniID:       "",
							EIP:         nil,
							Owner:       "",
							ContainerID: "",
						},
					},
				},
				intf: nil,
			},
			want:    net.ParseIP("*******"),
			wantErr: false,
		},
		{
			name: "fallback 正常流程",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					meta.EXPECT().GetLinkGateway(gomock.Any(), gomock.Any()).Return("", net.ErrClosed),
					nlink.EXPECT().RouteList(gomock.Any(), gomock.Any()).Return([]netlink.Route{
						{
							Dst: nil,
							Gw:  net.ParseIP("*******"),
						},
					}, nil),
				)

				return fields{
					ctrl:       ctrl,
					Client:     client,
					Scheme:     &runtime.Scheme{},
					nodeName:   "",
					nlink:      nlink,
					metaclient: meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:           "",
					EniID:            "",
					MacAddress:       "",
					SubnetID:         "",
					SecurityGroupIDs: []string{},
					VpcID:            "",
					VpcCIDR:          "",
					PrimaryIPAddress: "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{
						"": {
							UserID:      "",
							EniID:       "",
							EIP:         nil,
							Owner:       "",
							ContainerID: "",
						},
					},
				},
				intf: &netlink.Dummy{
					LinkAttrs: netlink.LinkAttrs{
						Index:        0,
						Name:         "bond0",
						HardwareAddr: []byte{},
					},
				},
			},
			want:    net.ParseIP("*******"),
			wantErr: false,
		},
		{
			name: "fallback 失败",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					meta.EXPECT().GetLinkGateway(gomock.Any(), gomock.Any()).Return("", net.ErrClosed),
					nlink.EXPECT().RouteList(gomock.Any(), gomock.Any()).Return(nil, &net.ParseError{}),
				)

				return fields{
					ctrl:       ctrl,
					Client:     client,
					Scheme:     &runtime.Scheme{},
					nodeName:   "",
					nlink:      nlink,
					metaclient: meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:           "",
					EniID:            "",
					MacAddress:       "",
					SubnetID:         "",
					SecurityGroupIDs: []string{},
					VpcID:            "",
					VpcCIDR:          "",
					PrimaryIPAddress: "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{
						"": {
							UserID:      "",
							EniID:       "",
							EIP:         nil,
							Owner:       "",
							ContainerID: "",
						},
					},
				},
				intf: &netlink.Dummy{
					LinkAttrs: netlink.LinkAttrs{
						Index:        0,
						Name:         "bond0",
						HardwareAddr: []byte{},
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			r := &BciNodeReconciler{
				Client:       tt.fields.Client,
				Scheme:       tt.fields.Scheme,
				bciNodeCache: tt.fields.bciNodeCache,
				ipamCtrl:     tt.fields.ipamCtrl,
				nodeName:     tt.fields.nodeName,
				nlink:        tt.fields.nlink,
				metaclient:   tt.fields.metaclient,
			}
			got, err := r.getEniGatewayWithFallback(tt.args.ctx, tt.args.eni, tt.args.intf)
			if (err != nil) != tt.wantErr {
				t.Errorf("BciNodeReconciler.getEniGatewayWithFallback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BciNodeReconciler.getEniGatewayWithFallback() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBciNodeReconciler_addEniVPCRoute(t *testing.T) {
	type fields struct {
		ctrl         *gomock.Controller
		Client       client.Client
		Scheme       *runtime.Scheme
		bciNodeCache networkingv1.BciNode
		ipamCtrl     *ipam.IPAM
		nodeName     string
		nlink        netlinkwrapper.Interface
		metaclient   metadata.Interface
	}
	type args struct {
		ctx  context.Context
		eni  *networkingv1.AllocationEni
		intf netlink.Link
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "正常流程",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					meta.EXPECT().GetLinkGateway(gomock.Any(), gomock.Any()).Return("*******", nil),
					nlink.EXPECT().RouteReplace(gomock.Any()).Return(nil),
				)

				return fields{
					ctrl:       ctrl,
					Client:     client,
					Scheme:     &runtime.Scheme{},
					nodeName:   "",
					nlink:      nlink,
					metaclient: meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:             "",
					EniID:              "",
					MacAddress:         "",
					SubnetID:           "",
					SecurityGroupIDs:   []string{},
					VpcID:              "",
					VpcCIDR:            "10.0.0.0/8",
					PrimaryIPAddress:   "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
				},
				intf: &netlink.Dummy{
					LinkAttrs: netlink.LinkAttrs{
						Index:        0,
						Name:         "bond0",
						HardwareAddr: []byte{},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "VPC 网段错误流程",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				return fields{
					ctrl:       ctrl,
					Client:     client,
					Scheme:     &runtime.Scheme{},
					nodeName:   "",
					nlink:      nlink,
					metaclient: meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:             "",
					EniID:              "",
					MacAddress:         "",
					SubnetID:           "",
					SecurityGroupIDs:   []string{},
					VpcID:              "",
					VpcCIDR:            "10.0.0.0/fsd",
					PrimaryIPAddress:   "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
				},
				intf: &netlink.Dummy{
					LinkAttrs: netlink.LinkAttrs{
						Index:        0,
						Name:         "bond0",
						HardwareAddr: []byte{},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "Gw 获取错误流程",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					meta.EXPECT().GetLinkGateway(gomock.Any(), gomock.Any()).Return("", net.ErrClosed),
					nlink.EXPECT().RouteList(gomock.Any(), gomock.Any()).Return(nil, &net.ParseError{}),
				)

				return fields{
					ctrl:       ctrl,
					Client:     client,
					Scheme:     &runtime.Scheme{},
					nodeName:   "",
					nlink:      nlink,
					metaclient: meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:             "",
					EniID:              "",
					MacAddress:         "",
					SubnetID:           "",
					SecurityGroupIDs:   []string{},
					VpcID:              "",
					VpcCIDR:            "10.0.0.0/8",
					PrimaryIPAddress:   "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
				},
				intf: &netlink.Dummy{
					LinkAttrs: netlink.LinkAttrs{
						Index:        0,
						Name:         "bond0",
						HardwareAddr: []byte{},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "RouteReplace 错误流程",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					meta.EXPECT().GetLinkGateway(gomock.Any(), gomock.Any()).Return("*******", nil),
					nlink.EXPECT().RouteReplace(gomock.Any()).Return(net.ErrClosed),
				)

				return fields{
					ctrl:       ctrl,
					Client:     client,
					Scheme:     &runtime.Scheme{},
					nodeName:   "",
					nlink:      nlink,
					metaclient: meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:             "",
					EniID:              "",
					MacAddress:         "",
					SubnetID:           "",
					SecurityGroupIDs:   []string{},
					VpcID:              "",
					VpcCIDR:            "10.0.0.0/8",
					PrimaryIPAddress:   "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
				},
				intf: &netlink.Dummy{
					LinkAttrs: netlink.LinkAttrs{
						Index:        0,
						Name:         "bond0",
						HardwareAddr: []byte{},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			r := &BciNodeReconciler{
				Client:       tt.fields.Client,
				Scheme:       tt.fields.Scheme,
				bciNodeCache: tt.fields.bciNodeCache,
				ipamCtrl:     tt.fields.ipamCtrl,
				nodeName:     tt.fields.nodeName,
				nlink:        tt.fields.nlink,
				metaclient:   tt.fields.metaclient,
			}
			if err := r.addEniVPCRoute(tt.args.ctx, tt.args.eni, tt.args.intf); (err != nil) != tt.wantErr {
				t.Errorf("BciNodeReconciler.addEniVPCRoute() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestBciNodeReconciler_AddEniWithoutNetns(t *testing.T) {
	type fields struct {
		ctrl         *gomock.Controller
		Client       client.Client
		Scheme       *runtime.Scheme
		bciNodeCache networkingv1.BciNode
		ipamCtrl     *ipam.IPAM
		nodeName     string
		nlink        netlinkwrapper.Interface
		metaclient   metadata.Interface
	}
	type args struct {
		ctx context.Context
		eni *networkingv1.AllocationEni
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "mac 查找 eni 失败",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkList().Return(nil, netlink.ErrNotImplemented),
				)

				return fields{
					ctrl:       ctrl,
					Client:     client,
					Scheme:     &runtime.Scheme{},
					nodeName:   "",
					nlink:      nlink,
					metaclient: meta,
				}
			}(),
			args: args{
				ctx: context.Background(),
				eni: &networkingv1.AllocationEni{
					UserID:             "",
					EniID:              "",
					MacAddress:         "",
					SubnetID:           "",
					SecurityGroupIDs:   []string{},
					VpcID:              "",
					VpcCIDR:            "",
					PrimaryIPAddress:   "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
				},
			},
			wantErr: true,
		},
		{
			name: "eni link up 失败",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkList().Return([]netlink.Link{
						&netlink.Dummy{
							LinkAttrs: netlink.LinkAttrs{
								Index:        0,
								Name:         "bond0",
								HardwareAddr: []byte{0xee, 0xee, 0xee, 0xee, 0xee, 0xee},
							},
						},
					}, nil),
					nlink.EXPECT().LinkSetUp(gomock.Any()).Return(netlink.ErrNotImplemented),
				)

				return fields{
					ctrl:       ctrl,
					Client:     client,
					Scheme:     &runtime.Scheme{},
					nodeName:   "",
					nlink:      nlink,
					metaclient: meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:             "",
					EniID:              "",
					MacAddress:         "ee:ee:ee:ee:ee:ee",
					SubnetID:           "",
					SecurityGroupIDs:   []string{},
					VpcID:              "",
					VpcCIDR:            "",
					PrimaryIPAddress:   "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
				},
			},
			wantErr: true,
		},
		{
			name: "add primary ip 失败",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkList().Return([]netlink.Link{
						&netlink.Dummy{
							LinkAttrs: netlink.LinkAttrs{
								Index:        0,
								Name:         "bond0",
								HardwareAddr: []byte{0xee, 0xee, 0xee, 0xee, 0xee, 0xee},
							},
						},
					}, nil),
					nlink.EXPECT().LinkSetUp(gomock.Any()).Return(nil),
					meta.EXPECT().GetLinkMask(gomock.Any(), gomock.Any()).Return("", net.ErrClosed),
				)

				return fields{
					ctrl:       ctrl,
					Client:     client,
					Scheme:     &runtime.Scheme{},
					nodeName:   "",
					nlink:      nlink,
					metaclient: meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:             "",
					EniID:              "",
					MacAddress:         "ee:ee:ee:ee:ee:ee",
					SubnetID:           "",
					SecurityGroupIDs:   []string{},
					VpcID:              "",
					VpcCIDR:            "",
					PrimaryIPAddress:   "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
				},
			},
			wantErr: true,
		},
		{
			name: "add eni route table 失败",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkList().Return([]netlink.Link{
						&netlink.Dummy{
							LinkAttrs: netlink.LinkAttrs{
								Index:        0,
								Name:         "eth1",
								HardwareAddr: []byte{0xee, 0xee, 0xee, 0xee, 0xee, 0xee},
							},
						},
					}, nil),
					nlink.EXPECT().LinkSetUp(gomock.Any()).Return(nil),
					meta.EXPECT().GetLinkMask(gomock.Any(), gomock.Any()).Return("*********", nil),
					nlink.EXPECT().AddrList(gomock.Any(), gomock.Any()).Return([]netlink.Addr{
						{IPNet: &net.IPNet{IP: net.ParseIP("*******")}},
					}, nil),
					nlink.EXPECT().AddrAdd(gomock.Any(), gomock.Any()).Return(nil),
					meta.EXPECT().GetLinkGateway(gomock.Any(), gomock.Any()).Return("*******", nil),
					nlink.EXPECT().RouteReplace(gomock.Any()).Return(net.ErrClosed),
				)

				return fields{
					ctrl:       ctrl,
					Client:     client,
					Scheme:     &runtime.Scheme{},
					nodeName:   "",
					nlink:      nlink,
					metaclient: meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:             "",
					EniID:              "",
					MacAddress:         "ee:ee:ee:ee:ee:ee",
					SubnetID:           "",
					SecurityGroupIDs:   []string{},
					VpcID:              "",
					VpcCIDR:            "",
					PrimaryIPAddress:   "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
				},
			},
			wantErr: true,
		},
		{
			name: "add vpc route dev eni 失败",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkList().Return([]netlink.Link{
						&netlink.Dummy{
							LinkAttrs: netlink.LinkAttrs{
								Index:        0,
								Name:         "eth1",
								HardwareAddr: []byte{0xee, 0xee, 0xee, 0xee, 0xee, 0xee},
							},
						},
					}, nil),
					nlink.EXPECT().LinkSetUp(gomock.Any()).Return(nil),

					meta.EXPECT().GetLinkMask(gomock.Any(), gomock.Any()).Return("*********", nil),
					nlink.EXPECT().AddrList(gomock.Any(), gomock.Any()).Return([]netlink.Addr{
						{IPNet: &net.IPNet{IP: net.ParseIP("*******")}},
					}, nil),
					nlink.EXPECT().AddrAdd(gomock.Any(), gomock.Any()).Return(nil),
					meta.EXPECT().GetLinkGateway(gomock.Any(), gomock.Any()).Return("*******", nil),
					nlink.EXPECT().RouteReplace(gomock.Any()).Return(nil),

					meta.EXPECT().GetLinkGateway(gomock.Any(), gomock.Any()).Return("", net.ErrClosed),
					nlink.EXPECT().RouteList(gomock.Any(), gomock.Any()).Return(nil, &net.ParseError{}),
				)

				return fields{
					ctrl:       ctrl,
					Client:     client,
					Scheme:     &runtime.Scheme{},
					nodeName:   "",
					nlink:      nlink,
					metaclient: meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:             "",
					EniID:              "",
					MacAddress:         "ee:ee:ee:ee:ee:ee",
					SubnetID:           "",
					SecurityGroupIDs:   []string{},
					VpcID:              "",
					VpcCIDR:            "10.0.0.0/8",
					PrimaryIPAddress:   "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
				},
			},
			wantErr: true,
		},
		{
			name: "正常流程",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkList().Return([]netlink.Link{
						&netlink.Dummy{
							LinkAttrs: netlink.LinkAttrs{
								Index:        0,
								Name:         "eth1",
								HardwareAddr: []byte{0xee, 0xee, 0xee, 0xee, 0xee, 0xee},
							},
						},
					}, nil),
					nlink.EXPECT().LinkSetUp(gomock.Any()).Return(nil),

					meta.EXPECT().GetLinkMask(gomock.Any(), gomock.Any()).Return("*********", nil),
					nlink.EXPECT().AddrList(gomock.Any(), gomock.Any()).Return([]netlink.Addr{
						{IPNet: &net.IPNet{IP: net.ParseIP("*******")}},
					}, nil),
					nlink.EXPECT().AddrAdd(gomock.Any(), gomock.Any()).Return(nil),
					meta.EXPECT().GetLinkGateway(gomock.Any(), gomock.Any()).Return("*******", nil),
					nlink.EXPECT().RouteReplace(gomock.Any()).Return(nil),

					meta.EXPECT().GetLinkGateway(gomock.Any(), gomock.Any()).Return("*******", nil),
					nlink.EXPECT().RouteReplace(gomock.Any()).Return(nil),
				)

				return fields{
					ctrl:       ctrl,
					Client:     client,
					Scheme:     &runtime.Scheme{},
					nodeName:   "",
					nlink:      nlink,
					metaclient: meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				eni: &networkingv1.AllocationEni{
					UserID:             "",
					EniID:              "",
					MacAddress:         "ee:ee:ee:ee:ee:ee",
					SubnetID:           "",
					SecurityGroupIDs:   []string{},
					VpcID:              "",
					VpcCIDR:            "10.0.0.0/8",
					PrimaryIPAddress:   "",
					PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.fields.ctrl != nil {
			defer tt.fields.ctrl.Finish()
		}

		t.Run(tt.name, func(t *testing.T) {
			r := &BciNodeReconciler{
				Client:       tt.fields.Client,
				Scheme:       tt.fields.Scheme,
				bciNodeCache: tt.fields.bciNodeCache,
				ipamCtrl:     tt.fields.ipamCtrl,
				nodeName:     tt.fields.nodeName,
				nlink:        tt.fields.nlink,
				metaclient:   tt.fields.metaclient,
			}

			if err := r.AddEniWithoutNetns(tt.args.ctx, tt.args.eni); (err != nil) != tt.wantErr {
				t.Errorf("BciNodeReconciler.AddEniWithoutNetns() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestBciNodeReconciler_HandleDel(t *testing.T) {
	RequireUniqueRouteTable = true
	defer func() {
		RequireUniqueRouteTable = false
	}()
	type fields struct {
		ctrl         *gomock.Controller
		Client       client.Client
		Scheme       *runtime.Scheme
		bciNodeCache networkingv1.BciNode
		ipamCtrl     *ipam.IPAM
		nodeName     string
		nlink        netlinkwrapper.Interface
		metaclient   metadata.Interface
	}
	type args struct {
		ctx          context.Context
		userAllocDel networkingv1.AllocationMap
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "pfs 正常场景",
			fields: fields{
				Client:     nil,
				Scheme:     &runtime.Scheme{},
				nodeName:   "",
				nlink:      nil,
				metaclient: nil,
			},
			args: args{
				ctx: context.TODO(),
				userAllocDel: map[string]networkingv1.UserAllocationEnis{
					"uuuuuuu": map[string]*networkingv1.AllocationEni{
						"xxxxxxx": {
							UserID:             "",
							EniID:              "",
							MacAddress:         "",
							SubnetID:           "",
							SecurityGroupIDs:   []string{},
							VpcID:              "",
							VpcCIDR:            "",
							PrimaryIPAddress:   "",
							PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			r := &BciNodeReconciler{
				Client:       tt.fields.Client,
				Scheme:       tt.fields.Scheme,
				bciNodeCache: tt.fields.bciNodeCache,
				ipamCtrl:     tt.fields.ipamCtrl,
				nodeName:     tt.fields.nodeName,
				nlink:        tt.fields.nlink,
				metaclient:   tt.fields.metaclient,
			}

			if err := r.HandleDel(tt.args.ctx, tt.args.userAllocDel); (err != nil) != tt.wantErr {
				t.Errorf("BciNodeReconciler.HandleDel() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestBciNodeReconciler_HandleAdd(t *testing.T) {
	RequireUniqueRouteTable = true
	defer func() {
		RequireUniqueRouteTable = false
	}()

	type fields struct {
		ctrl         *gomock.Controller
		Client       client.Client
		Scheme       *runtime.Scheme
		bciNodeCache networkingv1.BciNode
		ipamCtrl     *ipam.IPAM
		nodeName     string
		nlink        netlinkwrapper.Interface
		metaclient   metadata.Interface
	}
	type args struct {
		ctx          context.Context
		userAllocAdd networkingv1.AllocationMap
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "pfs 正常流程",
			fields: func() fields {
				ctrl, client, nlink, meta := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkList().Return(nil, netlink.ErrNotImplemented),
				)

				return fields{
					ctrl:         ctrl,
					Client:       client,
					Scheme:       &runtime.Scheme{},
					bciNodeCache: networkingv1.BciNode{},
					ipamCtrl:     &ipam.IPAM{},
					nodeName:     "",
					nlink:        nlink,
					metaclient:   meta,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				userAllocAdd: map[string]networkingv1.UserAllocationEnis{
					"uuuu": map[string]*networkingv1.AllocationEni{
						"eni-1": {
							UserID:             "",
							EniID:              "eni-1",
							MacAddress:         "",
							SubnetID:           "",
							SecurityGroupIDs:   []string{},
							VpcID:              "",
							VpcCIDR:            "",
							PrimaryIPAddress:   "",
							PrivateIPAddresses: map[string]networkingv1.AllocationIP{},
						},
					},
				},
			},
			want:    []string{"eni-1"},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			r := &BciNodeReconciler{
				Client:       tt.fields.Client,
				Scheme:       tt.fields.Scheme,
				bciNodeCache: tt.fields.bciNodeCache,
				ipamCtrl:     tt.fields.ipamCtrl,
				nodeName:     tt.fields.nodeName,
				nlink:        tt.fields.nlink,
				metaclient:   tt.fields.metaclient,
			}
			got, err := r.HandleAdd(tt.args.ctx, tt.args.userAllocAdd)
			if (err != nil) != tt.wantErr {
				t.Errorf("BciNodeReconciler.HandleAdd() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BciNodeReconciler.HandleAdd() = %v, want %v", got, tt.want)
			}
		})
	}
}
