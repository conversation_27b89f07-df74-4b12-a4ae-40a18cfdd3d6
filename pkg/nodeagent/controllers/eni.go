package controllers

import (
	"context"
	"errors"
	"fmt"
	"net"
	"os"
	"runtime"
	"syscall"
	"time"

	"github.com/containernetworking/plugins/pkg/ns"
	"github.com/vishvananda/netlink"
	"github.com/vishvananda/netns"
	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/utils/enilink"
)

var (
	ADMINUSERNAME = "admin"
)

func (r *BciNodeReconciler) findENILinkByMac(macAddress string) (netlink.Link, error) {
	var eniIntf netlink.Link
	i := 0
	// list all interfaces, and find ENI by mac address
	for {
		if i >= 3 {
			return nil, fmt.Errorf("eni with mac address %v not found", macAddress)
		}

		interfaces, err := r.nlink.LinkList()
		if err != nil {
			return nil, fmt.Errorf("failed to list interfaces: %v", interfaces)
		}

		for _, intf := range interfaces {
			if intf.Attrs().HardwareAddr.String() == macAddress {
				eniIntf = intf
				break
			}
		}

		if eniIntf == nil {
			i = i + 1
			time.Sleep(3 * time.Second)
			continue
		}
		return eniIntf, nil
	}
}

func createEniNs(eniNsName string) error {
	runtime.LockOSThread()
	defer runtime.UnlockOSThread()
	origns, _ := netns.Get()
	defer origns.Close()
	eniNsHandle, err := netns.NewNamed(eniNsName)
	defer eniNsHandle.Close()
	if err != nil {
		return err
	}
	err = netns.Set(origns)
	if err != nil {
		return err
	}
	return nil
}

func delEniNs(eniNsName string) error {
	runtime.LockOSThread()
	defer runtime.UnlockOSThread()

	return netns.DeleteNamed(eniNsName)
}

func (r *BciNodeReconciler) HandleAdd(ctx context.Context, userAllocAdd networkingv1.AllocationMap) ([]string, error) {
	var errEniIDs []string
	for user, userEni := range userAllocAdd {
		if user == ADMINUSERNAME {
			continue
		}
		for eniID, eni := range userEni {
			if RequireUniqueRouteTable {
				if err := r.AddEniWithoutNetns(ctx, eni); err != nil {
					errEniIDs = append(errEniIDs, eniID)
					logger.Errorf(ctx, "Add eni %s without netns failed, message: %v", eniID, err)
				}
			} else {
				if error := r.AddEni(ctx, eni); error != nil {
					errEniIDs = append(errEniIDs, eniID)
					_ = r.DelEni(ctx, eniID)
					logger.Errorf(ctx, "Add eni %s failed, message: %v", eniID, error)
				}
			}
		}
	}
	if len(errEniIDs) > 0 {
		return errEniIDs, fmt.Errorf("eni ids %v init failed", errEniIDs)
	}
	return nil, nil
}

func (r *BciNodeReconciler) HandleDel(ctx context.Context, userAllocDel networkingv1.AllocationMap) error {
	for user, userEni := range userAllocDel {
		if user == ADMINUSERNAME {
			continue
		}
		for eniID, eni := range userEni {
			if RequireUniqueRouteTable {
				continue
			}
			if error := r.DelEni(ctx, eni.EniID); error != nil {
				logger.Errorf(ctx, "Del eni %s failed, message: %v", eniID, error)
				// not return, continue to del eni
			}
		}
	}
	return nil
}

func (r *BciNodeReconciler) AddEni(ctx context.Context, eni *networkingv1.AllocationEni) error {
	// use ns path to judge is ns exists
	// any better way ?
	nsPath := fmt.Sprintf("/run/netns/enins-%s", eni.EniID)
	if _, err := os.Stat(nsPath); err == nil || os.IsExist(err) {
		logger.Infof(ctx, "netns %s exists", nsPath)
		return nil
	}

	eniIntf, err := r.findENILinkByMac(eni.MacAddress)
	if err != nil {
		logger.Errorf(ctx, "find eni: %s , mac: %s error", eni.EniID, eni.MacAddress)
		return err
	}

	// create eni ns
	eniNsName := fmt.Sprintf("enins-%s", eni.EniID)
	err = createEniNs(eniNsName)
	if err != nil {
		logger.Errorf(ctx, "create eni ns error")
		return err
	}
	// get netns handler
	netns, err := ns.GetNS(nsPath)
	defer netns.Close()
	if err != nil {
		return err
	}
	// set eni dev to eni ns
	err = netlink.LinkSetNsFd(eniIntf, int(netns.Fd()))
	if err != nil {
		logger.Errorf(ctx, "set eni to ns error")
		return err
	}

	_ = netns.Do(func(_ ns.NetNS) error {
		eniIntf, err = r.findENILinkByMac(eni.MacAddress)
		if err != nil {
			logger.Errorf(ctx, "find eni: %s , mac: %s error", eni.EniID, eni.MacAddress)
			return err
		}
		// set eni up
		err = r.setLinkUP(ctx, eniIntf)
		if err != nil {
			logger.Errorf(ctx, "set eni up error")
			return err
		}
		//add eni primary ip address
		err = r.addPrimaryIP(ctx, eniIntf, eni.PrimaryIPAddress)
		if err != nil {
			logger.Errorf(ctx, "add primary ip error")
			return err
		}

		err = r.addENIRule(ctx, eni, eniIntf)
		if err != nil {
			logger.Errorf(ctx, "add scope link route error")
			return err
		}

		err = r.delScopeLinkRoute(ctx, eniIntf)
		if err != nil {
			logger.Errorf(ctx, "del scope link route error")
			return err
		}

		return nil
	})

	return nil
}

func (r *BciNodeReconciler) DelEni(ctx context.Context, eniID string) error {
	// network controllr responsible for no pods are using this eni
	eniNsName := fmt.Sprintf("enins-%s", eniID)
	err := delEniNs(eniNsName)
	if err != nil {
		logger.Errorf(ctx, "delete eni ns error: %s", eniNsName)
		return err
	}
	logger.Infof(ctx, "Delete eni %s successfully", eniNsName)
	return nil
}

func (r *BciNodeReconciler) AddEniWithoutNetns(ctx context.Context, eni *networkingv1.AllocationEni) error {
	eniIntf, err := r.findENILinkByMac(eni.MacAddress)
	if err != nil {
		logger.Errorf(ctx, "find eni: %s , mac: %s error", eni.EniID, eni.MacAddress)
		return err
	}

	// set eni up
	err = r.setLinkUP(ctx, eniIntf)
	if err != nil {
		logger.Errorf(ctx, "set eni up error: %v", err)
		return err
	}
	//add eni primary ip address
	err = r.addPrimaryIP(ctx, eniIntf, eni.PrimaryIPAddress)
	if err != nil {
		logger.Errorf(ctx, "add primary ip error: %v", err)
		return err
	}

	// add unique route table
	err = r.addFromEniRouteTable(ctx, eni, eniIntf)
	if err != nil {
		logger.Errorf(ctx, "add from eni route table: %v", err)
		return err
	}

	// add vpc cidr in main route table
	err = r.addEniVPCRoute(ctx, eni, eniIntf)
	if err != nil {
		logger.Errorf(ctx, "add eni vpc route error: %v", err)
		return err
	}

	return nil
}

func (r *BciNodeReconciler) setLinkUP(ctx context.Context, intf netlink.Link) error {
	if intf.Attrs().Flags&net.FlagUp != 0 {
		logger.Infof(ctx, "link %v is already up", intf.Attrs().Name)
		return nil
	}
	logger.Warningf(ctx, "link %v is down, will bring it up", intf.Attrs().Name)
	err := r.nlink.LinkSetUp(intf)
	if err != nil {
		return err
	}
	logger.Infof(ctx, "set eni up success")
	return nil
}

func (r *BciNodeReconciler) addPrimaryIP(ctx context.Context, intf netlink.Link, primaryIP string) error {
	// primary ip address format: xxx.xxx.xxx.xxx
	mask, err := r.metaclient.GetLinkMask(intf.Attrs().HardwareAddr.String(), primaryIP)
	if err != nil {
		logger.Errorf(ctx, "get primary ip mask from meta client error")
		return err
	}
	logger.Infof(ctx, "primary ip mask: %v", mask)
	addrs, err := r.nlink.AddrList(intf, netlink.FAMILY_V4)
	if err != nil {
		logger.Errorf(ctx, "failed to list addresses of link %v: %v", intf.Attrs().Name, err)
		return err
	}

	for _, addr := range addrs {
		// primary IP already on link
		if addr.IP.String() == primaryIP {
			return nil
		}
	}

	logger.Infof(ctx, "start to add primary IP %v to link %v", primaryIP, intf.Attrs().Name)
	// mask is in prefix format
	addr := &netlink.Addr{
		IPNet: &net.IPNet{
			IP:   net.ParseIP(primaryIP),
			Mask: net.IPMask(net.ParseIP(mask).To4()),
		},
	}

	err = r.nlink.AddrAdd(intf, addr)
	if err != nil && !IsExistsError(err) {
		logger.Errorf(ctx, "failed to add primary IP %v to link %v", addr.String(), intf.Attrs().Name)
		return err
	}

	logger.Infof(ctx, "add primary IP %v to link %v successfully", addr.String(), intf.Attrs().Name)
	return nil
}

func (r *BciNodeReconciler) delScopeLinkRoute(ctx context.Context, intf netlink.Link) error {
	addrs, err := r.nlink.AddrList(intf, netlink.FAMILY_V4)
	if err != nil {
		return err
	}

	for _, addr := range addrs {
		dst := net.IPNet{
			IP:   addr.IP.Mask(addr.Mask),
			Mask: addr.Mask,
		}
		err = r.nlink.RouteDel(&netlink.Route{
			Dst:       &dst,
			Scope:     netlink.SCOPE_LINK,
			LinkIndex: intf.Attrs().Index,
		})
		if err != nil && !IsNotExistError(err) {
			return err
		}
	}

	ipv6Addrs, err := r.nlink.AddrList(intf, netlink.FAMILY_V6)
	if err != nil {
		return err
	}

	for _, addr := range ipv6Addrs {
		dst := net.IPNet{
			IP:   addr.IP.Mask(addr.Mask),
			Mask: addr.Mask,
		}
		err = r.nlink.RouteDel(&netlink.Route{
			Dst:       &dst,
			Scope:     netlink.SCOPE_LINK,
			LinkIndex: intf.Attrs().Index,
		})
		if err != nil && !IsNotExistError(err) {
			return err
		}
	}
	return nil
}

func (r *BciNodeReconciler) addENIRule(ctx context.Context, eni *networkingv1.AllocationEni, intf netlink.Link) error {
	var gateway net.IP
	defaultRt, err := r.findLinkDefaultRoute(ctx, intf)
	if err != nil {
		logger.Infof(ctx, "failed to get gateway of eni %v from link default route: %v", eni.EniID, err)
		logger.Infof(ctx, "fall back to get gateway of eni %v from meta-data", eni.EniID)
		// fallback to meta-data api
		// TODO: check primary address mask by metaclient or by prefix from network controller
		gw, err := r.getLinkGateway(ctx, eni.MacAddress, eni.PrimaryIPAddress)
		if err != nil {
			logger.Errorf(ctx, "failed to get gateway of eni %v from meta-data: %v", eni.EniID, err)
			return err
		}
		gateway = gw
	} else {
		gateway = defaultRt.Gw
	}

	logger.Infof(ctx, "eni %v with primary IP %v has gateway: %v", eni.EniID, eni.PrimaryIPAddress, gateway)

	// ip route replace default via {eniGW} dev ethX table {rtTable} onlink
	// 添加 IPv4 默认路由
	_, IPv4ZeroCIDR, _ := net.ParseCIDR("0.0.0.0/0")
	rt := &netlink.Route{
		LinkIndex: intf.Attrs().Index,
		Dst:       IPv4ZeroCIDR,
		Gw:        gateway,
	}
	// rt.SetFlag(netlink.FLAG_ONLINK)
	err = r.nlink.RouteAdd(rt)
	if err != nil {
		msg := fmt.Sprintf("failed to add IPv4 default route %+v: %v", *rt, err)
		logger.Error(ctx, msg)
		return err
	}
	logger.Infof(ctx, "add IPv4 default route %+v successfully", *rt)

	// 添加 IPv6 默认路由（动态获取网关）
	err = r.addIPv6DefaultRoute(ctx, eni, intf)
	if err != nil {
		logger.Errorf(ctx, "failed to add IPv6 default route for eni %v: %v", eni.EniID, err)
		return err
	}

	return nil
}

func (r *BciNodeReconciler) addIPv6DefaultRoute(ctx context.Context, eni *networkingv1.AllocationEni, intf netlink.Link) error {
	if len(eni.PrivateIPv6Addresses) == 0 {
		logger.Infof(ctx, "no IPv6 addresses to add for eni %v", eni.EniID)
		return nil
	}

	// 获取 IPv6 网关
	ipv6Gateway, err := r.getIPv6GatewayWithFallback(ctx, eni, intf)
	if err != nil {
		logger.Errorf(ctx, "failed to get IPv6 gateway for eni %v: %v", eni.EniID, err)
		return err
	}

	_, IPv6ZeroCIDR, _ := net.ParseCIDR("::/0")
	rtv6 := &netlink.Route{
		LinkIndex: intf.Attrs().Index,
		Dst:       IPv6ZeroCIDR,
		Gw:        ipv6Gateway,
	}

	err = r.nlink.RouteAdd(rtv6)
	if err != nil {
		logger.Error(ctx, "failed to add IPv6 default route %+v: %v", *rtv6, err)
		// 如果添加失败，尝试替换
		err = r.nlink.RouteReplace(rtv6)
		if err != nil {
			msg := fmt.Sprintf("failed to add/replace IPv6 default route %+v: %v", *rtv6, err)
			logger.Error(ctx, msg)
			return err
		}
	}

	logger.Infof(ctx, "add IPv6 default route %+v successfully", *rtv6)
	return nil
}

func (r *BciNodeReconciler) getIPv6GatewayWithFallback(ctx context.Context, eni *networkingv1.AllocationEni, intf netlink.Link) (net.IP, error) {
	// 首先尝试从neighbor表中获取IPv6网关
	for i := 0; i < 3; i++ {
		ipv6Gateway, err := r.getIPv6GatewayFromNeighbor(ctx, intf)
		if err == nil && ipv6Gateway != nil {
			logger.Infof(ctx, "found IPv6 gateway %v from neighbor table for eni %v", ipv6Gateway, eni.EniID)
			return ipv6Gateway, nil
		}
		time.Sleep(2 * time.Second)
	}

	// 尝试从网络接口的 IPv6 默认路由中获取网关
	ipv6DefaultRt, err := r.findLinkIPv6DefaultRoute(ctx, intf)
	if err == nil && ipv6DefaultRt.Gw != nil && ipv6DefaultRt.Gw.To4() == nil {
		logger.Infof(ctx, "found IPv6 gateway %v from link default route for eni %v", ipv6DefaultRt.Gw, eni.EniID)
		return ipv6DefaultRt.Gw, nil
	}

	// 最后回退到链路本地网关
	logger.Infof(ctx, "falling back to link-local gateway fe80::1 for eni %v", eni.EniID)
	return net.ParseIP("fe80::1"), nil
}

func (r *BciNodeReconciler) findLinkIPv6DefaultRoute(ctx context.Context, intf netlink.Link) (*netlink.Route, error) {
	// ip -6 route show dev ethX
	routes, err := r.nlink.RouteList(intf, netlink.FAMILY_V6)
	if err != nil {
		return nil, fmt.Errorf("failed to list IPv6 dev %v routes: %w", intf.Attrs().Name, err)
	}
	// find eni IPv6 default route
	for _, r := range routes {
		if r.Dst == nil || r.Dst.String() == "::/0" {
			return &r, nil
		}
	}

	return nil, fmt.Errorf("IPv6 default route of %v not found", intf.Attrs().Name)
}

// getIPv6GatewayFromNeighbor 从neighbor表中获取IPv6网关
func (r *BciNodeReconciler) getIPv6GatewayFromNeighbor(ctx context.Context, intf netlink.Link) (net.IP, error) {
	// 获取接口的neighbor表
	neighs, err := r.nlink.NeighList(intf.Attrs().Index, netlink.FAMILY_V6)
	if err != nil {
		logger.Errorf(ctx, "failed to list IPv6 neighbors for interface %v: %v", intf.Attrs().Name, err)
		return nil, fmt.Errorf("failed to list IPv6 neighbors for interface %v: %v", intf.Attrs().Name, err)
	}

	// 打印所有neighbor条目的详细信息用于调试
	for i, neigh := range neighs {
		if neigh.IP != nil {
			logger.Infof(ctx, "IPv6 neighbor[%d]: IP=%v, State=%v, Flags=0x%x", i, neigh.IP, neigh.State, neigh.Flags)
		}
	}

	// 查找路由器类型的neighbor条目
	for _, neigh := range neighs {
		// 检查是否是路由器（通常网关是路由器）
		if neigh.State == netlink.NUD_DELAY || neigh.State == netlink.NUD_REACHABLE || neigh.State == netlink.NUD_STALE {
			// 检查IP地址是否是IPv6地址
			if neigh.IP != nil && neigh.IP.To4() == nil {
				// 检查是否是链路本地地址（fe80::/10）
				if neigh.IP.IsLinkLocalUnicast() {
					logger.Infof(ctx, "found IPv6 link-local gateway %v from neighbor table for interface %v", neigh.IP, intf.Attrs().Name)
					return neigh.IP, nil
				}
				// 检查是否是全局单播地址
				if neigh.IP.IsGlobalUnicast() {
					logger.Infof(ctx, "found IPv6 global gateway %v from neighbor table for interface %v", neigh.IP, intf.Attrs().Name)
					return neigh.IP, nil
				}
			}
		}
	}

	// 如果没有找到路由器，尝试查找任何可达的IPv6 neighbor
	for _, neigh := range neighs {
		if neigh.IP != nil && neigh.IP.To4() == nil &&
			(neigh.State == netlink.NUD_REACHABLE || neigh.State == netlink.NUD_STALE || neigh.State == netlink.NUD_DELAY) {
			logger.Infof(ctx, "found IPv6 neighbor %v from neighbor table for interface %v", neigh.IP, intf.Attrs().Name)
			return neigh.IP, nil
		}
	}
	logger.Infof(ctx, "no suitable IPv6 neighbor found in neighbor table for interface %v", intf.Attrs().Name)

	return nil, nil
}

func (r *BciNodeReconciler) addFromEniRouteTable(ctx context.Context, eni *networkingv1.AllocationEni, intf netlink.Link) error {
	eniInterfaceSuffix, err := enilink.GetEniLinkNameSuffix(intf)
	if err != nil {
		return err
	}

	rtTable := enilink.EniRouteTableBase + eniInterfaceSuffix
	logger.Infof(ctx, "eni %v has route table index: %v", eni.EniID, rtTable)

	gateway, err := r.getEniGatewayWithFallback(ctx, eni, intf)
	if err != nil {
		return err
	}

	_, IPv4ZeroCIDR, _ := net.ParseCIDR("0.0.0.0/0")
	rt := &netlink.Route{
		LinkIndex: intf.Attrs().Index,
		Dst:       IPv4ZeroCIDR,
		Gw:        gateway,
		Table:     rtTable,
	}
	rt.SetFlag(netlink.FLAG_ONLINK)

	err = r.nlink.RouteReplace(rt)
	if err != nil {
		logger.Errorf(ctx, "failed to replace eni route table: %v", err)
		return err
	}

	return nil
}

func (r *BciNodeReconciler) getEniGatewayWithFallback(ctx context.Context, eni *networkingv1.AllocationEni, intf netlink.Link) (net.IP, error) {
	var (
		gateway  net.IP
		err      error
		finalErr = errors.New("failed to get eni gateway via meta-data and netlink")
	)

	gateway, err = r.getLinkGateway(ctx, eni.MacAddress, eni.PrimaryIPAddress)
	if err != nil {
		logger.Errorf(ctx, "failed to get get gateway of eni %v from meta-data: %v, try to fallback", eni.EniID, err)
		rt, err := r.findLinkDefaultRoute(ctx, intf)
		if err != nil {
			logger.Errorf(ctx, "failed to get gateway of eni %v from link default route: %v", eni.EniID, err)
			return nil, finalErr
		}
		gateway = rt.Gw
	}

	if gateway == nil {
		return nil, finalErr
	}

	return gateway, nil
}

func (r *BciNodeReconciler) addEniVPCRoute(ctx context.Context, eni *networkingv1.AllocationEni, intf netlink.Link) error {
	_, vpcCIDR, err := net.ParseCIDR(eni.VpcCIDR)
	if err != nil {
		return err
	}

	gateway, err := r.getEniGatewayWithFallback(ctx, eni, intf)
	if err != nil {
		return err
	}

	err = r.nlink.RouteReplace(&netlink.Route{
		Gw:        gateway,
		LinkIndex: intf.Attrs().Index,
		Dst:       vpcCIDR,
	})
	if err != nil {
		return err
	}

	return nil
}

func (r *BciNodeReconciler) findLinkDefaultRoute(ctx context.Context, intf netlink.Link) (*netlink.Route, error) {
	// ip route show dev ethX
	routes, err := r.nlink.RouteList(intf, netlink.FAMILY_V4)
	if err != nil {
		return nil, fmt.Errorf("failed to list dev %v routes: %w", intf.Attrs().Name, err)
	}
	// find eni default route
	for _, r := range routes {
		if r.Dst == nil || r.Dst.String() == "0.0.0.0/0" {
			return &r, nil
		}
	}

	return nil, fmt.Errorf("default route of %v not found", intf.Attrs().Name)
}

func (r *BciNodeReconciler) getLinkGateway(ctx context.Context, macAddress string, ip string) (net.IP, error) {
	gateway, err := r.metaclient.GetLinkGateway(macAddress, ip)
	if err != nil {
		return nil, err
	}

	gw := net.ParseIP(gateway)
	if gw == nil {
		return nil, fmt.Errorf("error parsing gateway IP address: %v", gateway)
	}
	return gw, nil
}

func IsExistsError(err error) bool {
	if errno, ok := err.(syscall.Errno); ok {
		return errno == syscall.EEXIST
	}
	return false
}

func IsNotExistError(err error) bool {
	if errno, ok := err.(syscall.Errno); ok {
		return errno == syscall.ENOENT || errno == syscall.ESRCH
	}

	return false
}
