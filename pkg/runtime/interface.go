package runtime

import (
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"

	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"
)

// webhook server 初始化函数
type NewWebhookServer func(option *options.ServerRunOptions, mgr manager.Manager, decoder *admission.Decoder) admission.Handler

// controller 初始化函数
type NewController func(*options.ServerRunOptions, manager.Manager) (interface{}, error)
