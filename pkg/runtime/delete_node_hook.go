package runtime

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"
)

var (
	customizeDeleteNodeFilterList = make([]CustomizeDeleteNodeFilter, 0)
	preDeleteNodeHookList         = make([]CustomizeNodeGCHook, 0)
)

// CustomizeDeleteNodeFilter 自定义删除node逻辑，在node状态机外
type CustomizeDeleteNodeFilter interface {
	// 是否能够删除此node
	CanDeleteNode(node *corev1.Node) bool
}

// CustomizeNodeGCHook node gc hook
type CustomizeNodeGCHook interface {
	// 执行自定义gc操作
	DoCustomizeNodeGC(source NodeGCSource, node *corev1.Node) error
}

// NodeGCSource 来源
type NodeGCSource string

const (
	// DeleteNodeGC 删除node 调用
	DeleteNodeGC NodeGCSource = "deleteNode"
	// StateMachineGC node状态机流转
	StateMachineGC NodeGCSource = "stateMachine"
	// GCRetrySuccessNotDeleteNode gc 重试成功后不删除node
	GCRetrySuccessNotDeleteNode NodeGCSource = "gcRetrySuccessNotDeleteNode"
	// 删除竞价+潮汐node
	DeleteBidNodeGC NodeGCSource = "deleteBidNode"
)

// RegisterCustomizeDeleteNodeFilter 注册自定义删除node hook
func RegisterCustomizeDeleteNodeFilter(filter CustomizeDeleteNodeFilter) {
	customizeDeleteNodeFilterList = append(customizeDeleteNodeFilterList, filter)
}

// RegisterCustomizeNodeGCHook 注册CustomizeNodeGCHook hook
func RegisterCustomizeNodeGCHook(preDeleteNodeHook CustomizeNodeGCHook) {
	preDeleteNodeHookList = append(preDeleteNodeHookList, preDeleteNodeHook)
}

// RunCanDeleteNodeCustomizeFilter 运行是否能够删除node hook
func RunCanDeleteNodeCustomizeFilter(node *corev1.Node) bool {

	if len(customizeDeleteNodeFilterList) == 0 {
		return false
	}

	// 必须所有filter 返回true 才可删除
	for _, filter := range customizeDeleteNodeFilterList {
		if !filter.CanDeleteNode(node) {
			return false
		}
	}

	klog.V(3).Infof("RunCanDeleteNodeCustomizeFilter can delete node %s ", node.Name)
	return true
}

// RunCustomizeNodeGCHook 删除node前先执行自定义逻辑
func RunCustomizeNodeGCHook(source NodeGCSource, node *corev1.Node) (success bool) {
	if len(preDeleteNodeHookList) == 0 {
		return true
	}
	var allSuccess bool = true
	for _, filter := range preDeleteNodeHookList {
		// 需要在每个插件中从informer中获取最新node
		if err := filter.DoCustomizeNodeGC(source, node); err != nil {
			klog.Errorf("RunCustomizeNodeGCHook filter %+v DoCustomizeNodeGC err %+v ", filter, err)
			allSuccess = false
		}
	}
	return allSuccess
}
