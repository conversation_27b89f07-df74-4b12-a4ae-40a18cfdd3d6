package network

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	bcinode "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/iam"
	bcinode_listers "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/generated/listers/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/sts"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	toolscache "k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"sigs.k8s.io/controller-runtime/pkg/source"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime/inject"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
)

var (
	createEIPFailCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "eip_create_fail_total",
			Help:      "The EIP Create Fail Total Count .",
		},
		[]string{"tenant_id"},
	)

	attachEIPFailCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "eip_attach_fail_total",
			Help:      "The EIP Attach Fail Total Count .",
		},
		[]string{"tenant_id", "node"},
	)

	detachEIPFailCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "eip_detach_fail_total",
			Help:      "The EIP Detach Fail Total Count .",
		},
		[]string{"tenant_id", "node"},
	)

	deleteEIPFailCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "eip_delete_fail_total",
			Help:      "The EIP Delete Fail Total Count .",
		},
		[]string{"tenant_id"},
	)
)

func init() {
	metrics.Registry.MustRegister(createEIPFailCounter)
	metrics.Registry.MustRegister(attachEIPFailCounter)
	metrics.Registry.MustRegister(detachEIPFailCounter)
	metrics.Registry.MustRegister(deleteEIPFailCounter)
}

var _ reconcile.Reconciler = &EIPController{}
var _ inject.StartFuncInjector = &EIPController{}

// Controller eip
type EIPController struct {
	enable  bool
	options *options.NetworkOptions

	// informer lister
	bcinodeLister bcinode_listers.BciNodeLister

	podLister  corev1_listers.PodLister
	nodeLister corev1_listers.NodeLister

	// 与k8s apiserver 交互的client
	client client.Client

	// 与IaaS交互的stsClient
	stsClient *sts.Client

	eniClient *eni.Client
	eipClient *eip.Client
}

// NewController returns a new *Controller
func NewEIPController(option *options.ServerRunOptions, mgr manager.Manager) (interface{}, error) {
	if !option.NetworkOptions.EnableEIP {
		return &EIPController{enable: false}, nil
	}

	// check region for IaaS API
	if _, ok := sts.Endpoints[option.NetworkOptions.Region]; !ok {
		klog.Errorf("eipController unknown region err %v", option.NetworkOptions.Region)
		return nil, nil
	}

	cache := mgr.GetCache()
	bcinodeInformer, err := cache.GetInformer(context.TODO(), &bcinode.BciNode{})
	if err != nil {
		klog.Errorf("eipController get bcinodeInformer err %+v ", err)
		return nil, err
	}
	bcinodeLister := bcinode_listers.NewBciNodeLister(bcinodeInformer.(toolscache.SharedIndexInformer).GetIndexer())

	podInformer, err := cache.GetInformer(context.TODO(), &corev1.Pod{})
	if err != nil {
		klog.Errorf("eipController get podInformer err %+v ", err)
		return nil, err
	}
	podLister := corev1_listers.NewPodLister(podInformer.(toolscache.SharedIndexInformer).GetIndexer())

	nodeInformer, err := cache.GetInformer(context.TODO(), &corev1.Node{})
	if err != nil {
		klog.Errorf("eniController get nodeInformer err %+v ", err)
		return nil, err
	}
	nodeLister := corev1_listers.NewNodeLister(nodeInformer.(toolscache.SharedIndexInformer).GetIndexer())

	c := &EIPController{
		enable:        true,
		options:       option.NetworkOptions,
		bcinodeLister: bcinodeLister,
		podLister:     podLister,
		nodeLister:    nodeLister,
		client:        mgr.GetClient(),
	}

	// init IaaS client
	c.initStsClient()

	controller, err := controller.New("eip-controller", mgr, controller.Options{
		Reconciler:              c,
		RateLimiter:             util.DefaultControllerRateLimiter(option),
		MaxConcurrentReconciles: option.NetworkOptions.EIPWorkerCount,
	})

	if err != nil {
		klog.Errorf("New eipController err %+v ", err)
		return nil, err
	}

	err = controller.Watch(source.Kind(mgr.GetCache(), &corev1.Pod{}), &handler.EnqueueRequestForObject{}, predicate.Funcs{
		CreateFunc: func(ce event.CreateEvent) bool {
			newPod := ce.Object.(*corev1.Pod)
			return c.shouldHandlePodCreate(newPod)
		},
		UpdateFunc: func(ue event.UpdateEvent) bool {
			newPod := ue.ObjectNew.(*corev1.Pod)
			return c.shouldHandlePodUpdate(newPod)
		},
		DeleteFunc: func(de event.DeleteEvent) bool {
			return false
		},
		GenericFunc: func(ge event.GenericEvent) bool {
			return false
		},
	})

	if err != nil {
		klog.Errorf("eipController watch corev1.Pod err %+v ", err)
		return nil, err
	}

	return c, nil
}

func (c *EIPController) initStsClient() {
	stsClient := sts.NewClient(context.TODO(), &bce.Config{
		Endpoint: sts.Endpoints[c.options.Region],
		Checksum: true,
		Timeout:  10 * time.Second,
		Region:   c.options.Region,
	}, &bce.Config{
		Endpoint: iam.Endpoints[c.options.Region],
		Checksum: true,
		Timeout:  10 * time.Second,
		Region:   c.options.Region,
	}, c.options.IamRoleName,
		c.options.IamUserName,
		c.options.IamConsolePassword)

	eniEndpoint := eni.Endpoint[c.options.Region]
	if c.options.Region == "sandbox" {
		eniEndpoint = "bcc.bj.qasandbox.baidu-int.com"
	}
	eniClient := eni.NewClient(eni.NewConfig(&bce.Config{
		Checksum: true,
		Timeout:  10 * time.Second,
		Endpoint: eniEndpoint,
	}))

	eipClient := eip.NewClient(&bce.Config{
		Checksum: true,
		Timeout:  10 * time.Second,
		Endpoint: eip.Endpoint[c.options.Region],
	})

	eniClient.SetDebug(true)
	eipClient.SetDebug(true)

	c.stsClient = stsClient
	c.eniClient = eniClient
	c.eipClient = eipClient
}

func (c *EIPController) shouldHandlePodCreate(pod *corev1.Pod) bool {
	// skip deleting-pod during retry bind eip
	if pod.DeletionTimestamp != nil {
		return false
	}
	if _, ok := pod.Annotations[entity.PODAnnotationEIPArgs]; ok {
		return true
	}
	return false
}

func (c *EIPController) shouldHandlePodUpdate(pod *corev1.Pod) bool {
	// 1. image cache pod: handle delete event
	// 2. bci pod: (1) handle PodPhase equal Succeeded or Failed, (2) handle pod delete event
	if _, ok := pod.Annotations[entity.PODAnnotationEIPArgs]; ok {
		_, isImageCachePod := pod.GetLabels()[entity.PODLableImageCache]
		if isImageCachePod && pod.DeletionTimestamp != nil {
			// imagecache pod, delete event
			return true
		} else if !isImageCachePod {
			// bci pod, PodPhase equal Succeeded or Failed
			if pod.Status.Phase == "Succeeded" || pod.Status.Phase == "Failed" {
				return true
			}
			// delete event
			if pod.DeletionTimestamp != nil {
				return true
			}
		}
	}
	return false
}

// Reconcile
func (c *EIPController) Reconcile(_ context.Context, request reconcile.Request) (
	res reconcile.Result, retErr error) {
	key := request.NamespacedName.String()
	pod, err := c.podLister.Pods(request.Namespace).Get(request.Name)
	if errors.IsNotFound(err) {
		klog.Infof("eipController pod has been deleted %v", key)
		return reconcile.Result{}, nil
	}
	if err != nil || pod == nil {
		klog.Errorf("eipController get pod %s err %+v ", key, err)
		return reconcile.Result{}, nil
	}

	// event: image-cache pod delete, or bci pod Succeeded or Failed
	if c.shouldHandlePodUpdate(pod) {
		return c.handlePodDelete(pod, key)
	}

	// event: pod create
	if c.shouldHandlePodCreate(pod) {
		return c.handlePodCreate(pod, key)
	}
	return reconcile.Result{}, nil
}

// handle pod delete event
func (c *EIPController) handlePodDelete(pod *corev1.Pod, key string) (res reconcile.Result, retErr error) {
	eipBciState := entity.EIPState{}
	str, ok := pod.GetAnnotations()[entity.PODAnnotationEIPState]
	if !ok {
		c.removeEIPPodFinalizer(pod)
		return
	}
	_ = json.Unmarshal([]byte(str), &eipBciState)

	userAccountID := pod.GetAnnotations()[entity.PODAnnotationENIUserID]
	ctx := context.TODO()
	signOption, signOptionWithHeader := entity.GetSingOption(ctx, c.stsClient, c.options, userAccountID)

	// 1. get pod's eip
	// if eip is empty, find eip by query eni info
	if len(eipBciState.EIP) == 0 {
		if len(eipBciState.EniID) == 0 || len(pod.Status.PodIP) == 0 {
			klog.Errorf("eipController eip is empty, eni or podIP is empty, delete pod %s %s eni %s", key, pod.Status.PodIP, eipBciState.EniID)
			c.removeEIPPodFinalizer(pod)
			// if eip is empty and eni is empty, cannot find eip
			return
		}

		// 2. query eni from IaaS
		eniStatResp, err := c.eniClient.StatENI(ctx, eipBciState.EniID, signOptionWithHeader)
		if err != nil || eniStatResp == nil {
			klog.Errorf("eipController stat eni %s err %+v, delete pod %s %s", eipBciState.EniID, err, key, pod.Status.PodIP)
			// stat eni shouldn't fail, so retry
			// TODO: check resp contain 'not exist'
			return c.actionRetry(pod, &eipBciState, entity.EIPDeleteAction)
		}
		for _, ipSet := range eniStatResp.PrivateIPSet {
			if ipSet.PrivateIPAddress == pod.Status.PodIP {
				eipBciState.EIP = ipSet.PublicIPAddress
				break
			}
		}
		if len(eipBciState.EIP) == 0 {
			klog.Infof("eipController eip is emepty, may already release, delete pod %s %s %s", key, pod.Status.PodIP, eipBciState.EIP)
			c.removeEIPPodFinalizer(pod)
			return reconcile.Result{}, nil
		}
	}

	// 3. can not handle 'creating' eip, wait
	eipGetArgs := &eip.GetEIPsArgs{
		EIP: eipBciState.EIP,
	}
	eipStates, err := c.eipClient.GetEIPs(ctx, eipGetArgs, signOption)
	if err != nil {
		klog.Errorf("eipController stat eip %s err %+v, delete pod %s %s", eipBciState.EIP, err, key, pod.Status.PodIP)
		// get eip state should not error, so retry
		return c.actionRetry(pod, &eipBciState, entity.EIPDeleteAction)
	}
	if len(eipStates) == 0 {
		klog.Infof("eipController stat eip %s failed, may not exist, delete pod %s %s", eipBciState.EIP, key, pod.Status.PodIP)
		c.removeEIPPodFinalizer(pod)
		return
	}
	if eipStates[0].Status == eip.EIPCreating {
		// cannot handle 'creating' eip, waiting
		klog.Infof("eipController eip %s %s, wait then delete", eipBciState.EIP, eipStates[0].Status)
		return c.actionRetry(pod, &eipBciState, entity.EIPDeleteAction)
	}
	// 4. unbid eip, if eip is bound with this pod
	if eipStates[0].Status == eip.EIPBinded && eipStates[0].InstanceIP == pod.Status.PodIP {
		if err := c.eipClient.UnbindEIP(ctx, eipBciState.EIP, signOption); err != nil {
			klog.Errorf("eipController unbind eip %s err %+v, delete pod %s %s",
				eipBciState.EIP, err, key, pod.Status.PodIP)
			detachEIPFailCounter.WithLabelValues(userAccountID, pod.Spec.NodeName).Inc()
			return c.actionRetry(pod, &eipBciState, entity.EIPDeleteAction)
		}
	}

	// 5. delete eip, if needed
	if !c.whetherDeleteEIP(pod, eipStates[0], &eipBciState) {
		c.removeEIPPodFinalizer(pod)
		return reconcile.Result{}, nil
	}

	if err := c.eipClient.DeleteEIP(ctx, eipBciState.EIP, signOption); err != nil {
		klog.Errorf("eipController delete eip %s err %+v, delete pod %s %s",
			eipBciState.EIP, err, key, pod.Status.PodIP)
		// Error Message: "The specified resource does not exist.", Error Code: "ResourceNotFound",
		// Status Code: 404, Request Id: "xxxx"
		if !strings.Contains(err.Error(), "ResourceNotFound") {
			// release eip shouldn't fail, so retry
			return c.actionRetry(pod, &eipBciState, entity.EIPDeleteAction)
		}
		deleteEIPFailCounter.WithLabelValues(userAccountID).Inc()
	}

	c.removeEIPPodFinalizer(pod)
	klog.Infof("eipController delete succ, eip %s pod %s %s %s", eipBciState.EIP, key, pod.Status.Phase, pod.Status.PodIP)
	return reconcile.Result{}, nil
}

// handle pod create event
/*
 * 1. image-transfer pod: create and attach eip
 * 2. bci pod: only attach eip
 */
func (c *EIPController) handlePodCreate(pod *corev1.Pod, key string) (res reconcile.Result, retErr error) {
	eipState := entity.EIPState{}
	str, ok := pod.GetAnnotations()[entity.PODAnnotationEIPState]
	if ok {
		_ = json.Unmarshal([]byte(str), &eipState)

		// this pod is rescheduled to a new node
		if len(eipState.NodeName) != 0 && eipState.NodeName != pod.Spec.NodeName {
			klog.Infof("rescheduled pod %s, eip %s", key, eipState.EIP)
			eipIP := c.unbindEIPForRescheduledPod(pod, eipState.EIP)
			eipState = entity.EIPState{
				EIP: eipIP,
			}
		}
	}
	klog.Infof("pod %s eip State when entering handlePodCreate, %s", key, str)

	// filter out already binded eip, when restart controller
	if eipState.State == string(eip.EIPBinded) {
		return
	}

	// always wait pod's nodeName ready, until pod ready or Reconcile return pod IsNotFound
	if len(pod.Spec.NodeName) == 0 {
		return reconcile.Result{Requeue: true, RequeueAfter: 5 * time.Second}, nil
	}
	if eipState.NodeName == "" {
		eipState.NodeName = pod.Spec.NodeName
		eipState.NodeProviderID = c.getNodeProviderID(pod.Spec.NodeName)
	}

	// only for bci-pod, in case state info not contain eip info, when delete eip
	_, isImageCachePod := pod.GetLabels()[entity.PODLableImageCache]
	if len(eipState.EIP) == 0 && (!isImageCachePod || strings.Contains(pod.GetAnnotations()[entity.PODAnnotationEIPArgs], "user_specified")) {
		eipArgsStr := pod.GetAnnotations()[entity.PODAnnotationEIPArgs]
		if strings.Contains(eipArgsStr, "state") {
			eipArgs := &entity.EIPState{}
			err := json.Unmarshal([]byte(eipArgsStr), eipArgs)
			if err != nil {
				klog.Errorf("eipController parse eip's bci internal state %s err %+v, creating pod %s", eipArgsStr, err, key)
			}
			eipState.EIP = eipArgs.EIP
		} else {
			eipState.EIP = eipArgsStr
		}
	}

	// search eniID need pod private ip. for pod create event, retry
	if len(pod.Status.PodIP) == 0 {
		klog.Infof("eipController wait pod ip ready, %s", key)
		return c.actionRetry(pod, &eipState, entity.EIPCreateAction)
	}

	// 1. get pod's eniId from bcinode
	if eipState.EniID == "" {
		podEniID := c.getPodEniID(pod)
		if len(podEniID) == 0 {
			klog.Errorf("eipController not found eni, creating pod %s %s node %s", key, pod.Status.PodIP, pod.Spec.NodeName)
			return c.actionRetry(pod, &eipState, entity.EIPCreateAction)
		}
		eipState.EniID = podEniID
	}

	// 2. create an eip from IaaS, and wait eip ok
	// userAccountID := pod.GetLabels()[PODLabelAccountID]
	userAccountID := pod.GetAnnotations()[entity.PODAnnotationENIUserID]
	ctx := context.TODO()
	signOption, signOptionWithHeader := entity.GetSingOption(ctx, c.stsClient, c.options, userAccountID)

	if len(eipState.EIP) == 0 {
		isNewEIP := false
		eipArgsStr := pod.GetAnnotations()[entity.PODAnnotationEIPArgs]
		var eipPublicIP string
		if strings.Contains(eipArgsStr, "bandwidth") {
			// create an new eip, for image-transfer pod
			eipArgs := &eip.CreateEIPArgs{}
			err := json.Unmarshal([]byte(eipArgsStr), eipArgs)
			if err != nil {
				klog.Errorf("eipController parse eip's create request %s err %+v, creating pod %s", eipArgsStr, err, key)
				return
			}
			eipPublicIP, err = c.eipClient.CreateEIP(ctx, eipArgs, signOption)
			if err != nil || len(eipPublicIP) == 0 {
				klog.Errorf("eipController create eip %s err %+v, creating pod %s %s",
					eipPublicIP, err, key, pod.Status.PodIP)
				createEIPFailCounter.WithLabelValues(userAccountID).Inc()
				return c.actionRetry(pod, &eipState, entity.EIPCreateAction)
			}
			isNewEIP = true
		} else if strings.Contains(eipArgsStr, "state") {
			// get bci tc-pod's eip
			eipArgs := &entity.EIPState{}
			err := json.Unmarshal([]byte(eipArgsStr), eipArgs)
			if err != nil {
				klog.Errorf("eipController get bci tc-pod's eip %s fail, creating pod %s %s", eipArgsStr, key, pod.Status.PodIP)
				return
			}
			eipPublicIP = eipArgs.EIP
		} else {
			// get bci pod's eip
			eipPublicIP = eipArgsStr
			if len(eipPublicIP) == 0 {
				klog.Errorf("eipController get bci pod's eip %s fail, creating pod %s %s", eipPublicIP, key, pod.Status.PodIP)
				return
			}
		}
		eipState.EIP = eipPublicIP

		// wait eip status transfer from creating to available
		// only update eip info, then retry
		if isNewEIP {
			klog.Infof("eipController create new eip, pod %s eip %s", key, eipPublicIP)
			// new eip's state 'creating' to 'available', need some time
			c.updateEIPState(pod, &eipState)
			return reconcile.Result{Requeue: true, RequeueAfter: 10 * time.Second}, nil
		}
	}

	// wait eip to available
	eipGetArgs := &eip.GetEIPsArgs{
		EIP: eipState.EIP,
	}
	eipStates, err := c.eipClient.GetEIPs(ctx, eipGetArgs, signOption)
	if err != nil {
		klog.Errorf("eipController stat eip %s err %+v", eipState.EIP, err)
		// get eip state should not error, so retry
		return c.actionRetry(pod, &eipState, entity.EIPCreateAction)
	}
	if len(eipStates) == 0 {
		klog.Warningf("eipController stat eip %s failed, may not exist", eipState.EIP)
		return
	}
	eipState.State = string(eipStates[0].Status)
	if eipStates[0].Status == eip.EIPBinded {
		// check eip bind relationship
		if eipStates[0].InstanceIP == pod.Status.PodIP {
			c.updateEIPState(pod, &eipState)
			return
		}
		klog.Warningf("eipController eip conflict with already binded, may use same eip, eip %s binded %s wanted %s",
			eipState.EIP, eipStates[0].InstanceIP, pod.Status.PodIP)
		eipState.State = "errorAlreadyBinded"
		c.updateEIPState(pod, &eipState)
		return c.actionRetry(pod, &eipState, entity.EIPCreateAction)
	}
	if eipStates[0].Status != eip.EIPAvailable {
		klog.Infof("eipController %s wait eip %s %s to be available", pod.Status.PodIP, eipState.EIP, eipStates[0].Status)
		return c.actionRetry(pod, &eipState, entity.EIPCreateAction)
	}

	// 3. bind eni eip by IaaS. if bind failed, delete this eip
	argsAttachEip := eni.AttachEIPArgs{
		PrivateIPAddress: pod.Status.PodIP,
		PublicIPAddress:  eipState.EIP,
		ENIID:            eipState.EniID,
	}
	err = c.eniClient.AttachEIP(ctx, &argsAttachEip, signOptionWithHeader)
	if err != nil {
		klog.Errorf("eipController attach eip %s err %+v, creating pod %s %s %s",
			eipState.EIP, err, key, pod.Status.PodIP, eipState.EniID)
		attachEIPFailCounter.WithLabelValues(userAccountID, pod.Spec.NodeName).Inc()
		return c.actionRetry(pod, &eipState, entity.EIPCreateAction)
	}
	eipState.State = string(eip.EIPBinded)
	c.updateEIPState(pod, &eipState)
	klog.Infof("eipController attach succ, pod %s %s eip %s", key, pod.Status.PodIP, eipState.EIP)
	return
}

func (c *EIPController) whetherDeleteEIP(pod *corev1.Pod, eipIaaSState *eip.EIP, eipBciState *entity.EIPState) bool {
	// image-transfer pod should delete eip,如果是复用用户已经存在eip，不能删除
	if _, isImageCachePod := pod.GetLabels()[entity.PODLableImageCache]; isImageCachePod {
		if strings.Contains(pod.GetAnnotations()[entity.PODAnnotationEIPArgs], "user_specified") {
			return false
		}
		return true
	}

	// don't release user-specified eip
	if strings.Contains(pod.GetAnnotations()[entity.PODAnnotationEIPArgs], "user_specified") {
		klog.Infof("eipController donot delete user-specified eip, eip %s pod %s/%s %s",
			eipBciState.EIP, pod.Namespace, pod.Name, pod.Status.PodIP)
		c.removeEIPPodFinalizer(pod)
		return false
	}

	// can't delete pre-paid eip
	if eipIaaSState.PaymentTiming == string(eip.PaymentTimingPrepaid) {
		klog.Infof("eipController cannot delete pre-paid eip, eip %s pod %s/%s %s",
			eipBciState.EIP, pod.Namespace, pod.Name, pod.Status.PodIP)
		c.removeEIPPodFinalizer(pod)
		return false
	}

	// node has been delete
	if pod.Spec.NodeName != "" {
		node, err := c.nodeLister.Get(pod.Spec.NodeName)
		if errors.IsNotFound(err) {
			klog.Infof("eipController node has been deleted, node %s eip %s pod %s/%s %s",
				pod.Spec.NodeName, eipBciState.EIP, pod.Namespace, pod.Name, pod.Status.PodIP)
			return true
		}

		if err != nil || node == nil {
			klog.Errorf("eipController get node err %+v, node %s pod %s/%s %s",
				err, pod.Spec.NodeName, pod.Namespace, pod.Name, pod.Status.PodIP)
			return false
		}

		// node ip may be recycled
		// double check: node.spec.providerID
		if node.Spec.ProviderID == "" {
			klog.Errorf("eipController node providerID is empty, node %s", pod.Spec.NodeName)
			return false
		}
		if eipBciState.NodeProviderID != "" && eipBciState.NodeProviderID != node.Spec.ProviderID {
			klog.Infof("eipController node has been deleted, then ip be recycled, node %s eip %s pod %s/%s %s",
				pod.Spec.NodeName, eipBciState.EIP, pod.Namespace, pod.Name, pod.Status.PodIP)
			return true
		}
	}

	return false
}

func (c *EIPController) getNodeProviderID(nodeName string) string {
	if nodeName == "" {
		return ""
	}
	node, err := c.nodeLister.Get(nodeName)
	if errors.IsNotFound(err) {
		klog.Infof("eipController node has been deleted, node %s", nodeName)
		return ""
	}

	if err != nil || node == nil {
		klog.Errorf("eipController get node err %+v, node %s", err, nodeName)
		return ""
	}

	if node.Spec.ProviderID == "" {
		klog.Errorf("eipController node providerID is empty, node %s", nodeName)
	}
	return node.Spec.ProviderID
}

// unbind eip from old eni, when reschedule pod
func (c *EIPController) unbindEIPForRescheduledPod(pod *corev1.Pod, eipIP string) string {
	if eipIP == "" {
		return ""
	}
	userAccountID := pod.GetAnnotations()[entity.PODAnnotationENIUserID]
	ctx := context.TODO()
	signOption, _ := entity.GetSingOption(ctx, c.stsClient, c.options, userAccountID)

	eipGetArgs := &eip.GetEIPsArgs{
		EIP: eipIP,
	}
	eipStates, err := c.eipClient.GetEIPs(ctx, eipGetArgs, signOption)
	if err != nil {
		klog.Errorf("eipController stat eip %s err %+v, reschedule pod %s/%s %s", eipIP, err, pod.Namespace, pod.Name, pod.Status.PodIP)
		return ""
	}
	if len(eipStates) == 0 {
		klog.Infof("eipController stat eip %s failed, may not exist, reschedule pod %s/%s %s", eipIP, pod.Namespace, pod.Name, pod.Status.PodIP)
		return ""
	}
	if eipStates[0].Status == eip.EIPBinded {
		if err := c.eipClient.UnbindEIP(ctx, eipIP, signOption); err != nil {
			klog.Errorf("eipController unbind eip %s err %+v, reschedule pod %s/%s %s",
				eipIP, err, pod.Namespace, pod.Name, pod.Status.PodIP)
			detachEIPFailCounter.WithLabelValues(userAccountID, pod.Spec.NodeName).Inc()
		}
	}
	return eipIP
}

func (c *EIPController) getPodEniID(pod *corev1.Pod) string {
	nodeName := pod.Spec.NodeName
	podIP := pod.Status.PodIP
	// userAccountID := pod.GetLabels()[PODLabelAccountID]
	userAccountID := pod.GetAnnotations()[entity.PODAnnotationENIUserID]

	bcinodeCrd, err := c.bcinodeLister.Get(nodeName)
	if errors.IsNotFound(err) {
		klog.Errorf("eipController not found bcinode %v", nodeName)
		return ""
	}
	if err != nil || bcinodeCrd == nil {
		klog.Errorf("eipController get bcinode %s err %+v ", nodeName, err)
		return ""
	}

	// AllocationMap is a map of user enis indexed by userID
	userEnis, ok := bcinodeCrd.Status.EniMultiIP.Used[userAccountID]
	if !ok {
		klog.Errorf("eipController not found eni in bcinode %s for pod %s/%s",
			nodeName, pod.Namespace, pod.Name)
		return ""
	}
	for eniID, userEni := range userEnis {
		if _, ok := userEni.PrivateIPAddresses[podIP]; ok {
			return eniID
		}
	}
	return ""
}

func (c *EIPController) updateEIPState(pod *corev1.Pod, eipState *entity.EIPState) {
	tmp, _ := json.Marshal(&eipState)
	patchValue := entity.PatchValue{
		Key:   entity.PODAnnotationEIPState,
		Value: string(tmp),
		Type:  entity.PatchTypeUpdate,
	}
	err := util.PatchPodAnnotation(c.client, pod, []entity.PatchValue{patchValue})
	if err != nil {
		klog.Errorf("eipController update eip's state err %+v, pod %s/%s content %s",
			err, pod.Namespace, pod.Name, string(tmp))
	}
}

func (c *EIPController) actionRetry(pod *corev1.Pod, eipState *entity.EIPState, action int) (
	res reconcile.Result, retErr error) {
	retryTimes := 0
	if action == entity.EIPCreateAction {
		retryTimes = eipState.CreateRetryTimes
		eipState.CreateRetryTimes++
	} else if action == entity.EIPDeleteAction {
		retryTimes = eipState.DeleteRetryTimes
		eipState.DeleteRetryTimes++
	}
	if retryTimes < c.options.EIPMaxRetryTimes {
		c.updateEIPState(pod, eipState)
		return reconcile.Result{Requeue: true, RequeueAfter: c.options.EIPRetryInterval}, nil
	}

	if action == entity.EIPDeleteAction {
		c.removeEIPPodFinalizer(pod)
	}
	return reconcile.Result{}, nil
}

func (c *EIPController) removeEIPPodFinalizer(pod *corev1.Pod) {
	if pod.Finalizers == nil {
		return
	}
	// get latest pod's info
	newPod, err := c.podLister.Pods(pod.Namespace).Get(pod.Name)
	if err != nil {
		if errors.IsNotFound(err) {
			return
		}
		klog.Errorf("eipController get pod err %+v, pod %s/%s", err, pod.Namespace, pod.Name)
		return
	}
	// check pod has eip finalizer
	if !existEIPFinalizer(newPod) {
		return
	}
	// remove eip finalizer
	tmp := newPod.DeepCopy()
	if _, isImageCachePod := pod.GetLabels()[entity.PODLableImageCache]; isImageCachePod {
		tmp.Finalizers = nil
	} else {
		newFinalizers := make([]string, 0, len(tmp.Finalizers))
		for _, finalizer := range tmp.Finalizers {
			if finalizer != entity.PODFinalizerBciEIP {
				newFinalizers = append(newFinalizers, finalizer)
			}
		}
		tmp.Finalizers = newFinalizers
	}
	err = c.client.Update(context.Background(), tmp)
	if err != nil {
		klog.Errorf("eipController update pod finalizer err %+v, pod %s/%s", err, pod.Namespace, pod.Name)
	}
}

func existEIPFinalizer(pod *corev1.Pod) bool {
	if pod.Finalizers == nil || len(pod.Finalizers) == 0 {
		return false
	}

	eipFinalizer := entity.PODFinalizerBciEIP
	if _, isImageCachePod := pod.GetLabels()[entity.PODLableImageCache]; isImageCachePod {
		eipFinalizer = entity.PODFinalizerImageCache
	}
	for _, finalizer := range pod.Finalizers {
		if finalizer == eipFinalizer {
			return true
		}
	}

	return false
}

func (c *EIPController) cleanup() {
	allPods, err := c.podLister.List(labels.Everything())
	if err != nil {
		klog.Errorf("eipController cleanup list pod err %+v", err)
		return
	}

	for _, pod := range allPods {
		if c.shouldHandlePodUpdate(pod) && existEIPFinalizer(pod) {
			key := fmt.Sprintf("%s/%s", pod.Namespace, pod.Name)
			klog.Infof("eipController cleanup pod %s, finalizers %s", key, pod.Finalizers)
			if _, err := c.handlePodDelete(pod, key); err != nil {
				klog.Infof("eipController cleanup pod err %+v, pod %s", err, key)
			}
		}
	}
}

func (c *EIPController) Start(stopCh <-chan struct{}) error {
	defer runtime.HandleCrash()
	// if controller is not enabled, just skip start
	if !c.enable {
		klog.V(2).Info("eip controller is not enabled, just skip startting.")
		return nil
	}

	klog.V(2).Info("Start eip controller")
	go wait.Until(c.cleanup, 10*time.Second, stopCh)
	<-stopCh
	klog.V(2).Info("Shutting down workers")
	return nil
}
