package instancegroup

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"testing"
	"time"

	ccev2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	appv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/informers"
	k8sfake "k8s.io/client-go/kubernetes/fake"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	appv1_listers "k8s.io/client-go/listers/apps/v1"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

var (
	scheme = runtime.NewScheme()
)

func init() {
	err := clientgoscheme.AddToScheme(scheme)
	fmt.Print(err)
}

func newController(cms []*corev1.ConfigMap, deployments []*appv1.Deployment) *TimedSacleUPController {
	option := options.NewServerRunOptions()

	c := &TimedSacleUPController{
		region: "test",
		option: option,
	}

	k8sclient := k8sfake.NewSimpleClientset()
	// client := fake.NewFakeClient()
	client := fake.NewClientBuilder().WithScheme(scheme).Build()
	c.client = client

	informerFactory := informers.NewSharedInformerFactory(k8sclient, 0)
	cmInformer := informerFactory.Core().V1().ConfigMaps()
	cmIndexer := cmInformer.Informer().GetIndexer()
	c.cmLister = corev1_listers.NewConfigMapLister(cmIndexer)

	deploymentInformer := informerFactory.Apps().V1().Deployments()
	deploymentIndexer := deploymentInformer.Informer().GetIndexer()
	c.deploymentLister = appv1_listers.NewDeploymentLister(deploymentIndexer)

	hiclient := util.NewHiClient("http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d8850854d6fc851d861ee0cab7523e822-1")
	// hiclient := util.NewHiClient("http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d9fcfe7922f8d56685a36c93a21039a9b")

	c.hiClient = hiclient

	for _, cm := range cms {
		err := cmIndexer.Add(cm)
		if err != nil {
			panic(err)
		}
		err = client.Create(context.Background(), cm)
		if err != nil {
			panic(err)
		}
	}

	for _, deployment := range deployments {
		err := deploymentIndexer.Add(deployment)
		if err != nil {
			panic(err)
		}
		err = client.Create(context.Background(), deployment)
		if err != nil {
			panic(err)
		}
	}
	return c
}

func TestSyncTimedCaDeployment(t *testing.T) {
	subOneHourDuration, _ := time.ParseDuration("-1h")
	subTwoHourDuration, _ := time.ParseDuration("-2h")
	addOneHourDuration, _ := time.ParseDuration("1h")

	autoScaleUpStartTime := time.Now().Add(subTwoHourDuration)
	expectScaleUpSuccessTime := time.Now().Add(subOneHourDuration)
	autoScaleUpEndTime := time.Now().Add(addOneHourDuration)

	tests := []struct {
		name              string
		igList            []entity.InstanceGroupCm
		timedScaleUpCm    *entity.InstanceGroupTimedScaleUpCm
		timedCaDeployment *appv1.Deployment
		igNodeMap         map[string]*ccev2.ListInstancesByInstanceGroupIDResponse
		igTaskMap         map[string]*ccev2.ListTaskResp
		result            reconcile.Result
		statisticsTask    bool
	}{
		{
			name: "节点组不存在",
			igList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "test",
					InstanceGroupName: "zoneA-c2m8-cpu",
				},
			},
			timedScaleUpCm: &entity.InstanceGroupTimedScaleUpCm{
				MaxScaleupNodeCount: 3,
				InstanceGroups: []*entity.TimedScaleUpParams{
					{
						InstanceGroupID:          "cce-ig-f7ig6xak",
						AutoScaleUpStartTime:     "xx",
						AutoScaleUpEndTime:       autoScaleUpEndTime.Format("2006-01-02 15:04:05"),
						ExpectScaleUpSuccessTime: expectScaleUpSuccessTime.Format("2006-01-02 15:04:05"),
						ExpectNodeCount:          3,
						ExpectAutoDeleteNodeTime: "",
					},
				},
			},
			result: reconcile.Result{RequeueAfter: 30 * time.Second},
		},
		{
			name: "timed config 配置错误",
			igList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
				},
			},
			timedScaleUpCm: &entity.InstanceGroupTimedScaleUpCm{
				MaxScaleupNodeCount: 3,
				InstanceGroups: []*entity.TimedScaleUpParams{
					{
						InstanceGroupID:          "cce-ig-f7ig6xak",
						AutoScaleUpStartTime:     "xx",
						AutoScaleUpEndTime:       autoScaleUpEndTime.Format("2006-01-02 15:04:05"),
						ExpectScaleUpSuccessTime: expectScaleUpSuccessTime.Format("2006-01-02 15:04:05"),
						ExpectNodeCount:          3,
						ExpectAutoDeleteNodeTime: "",
					},
				},
			},
			result: reconcile.Result{RequeueAfter: 30 * time.Second},
		},

		{
			name: "新增timed config 配置,创建新的ca deployment",
			igList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
				},
			},
			timedScaleUpCm: &entity.InstanceGroupTimedScaleUpCm{
				MaxScaleupNodeCount: 3,
				InstanceGroups: []*entity.TimedScaleUpParams{
					{
						InstanceGroupID:          "cce-ig-f7ig6xak",
						AutoScaleUpStartTime:     autoScaleUpStartTime.Format("2006-01-02 15:04:05"),
						AutoScaleUpEndTime:       autoScaleUpEndTime.Format("2006-01-02 15:04:05"),
						ExpectScaleUpSuccessTime: expectScaleUpSuccessTime.Format("2006-01-02 15:04:05"),
						ExpectNodeCount:          3,
						ExpectAutoDeleteNodeTime: "",
					},
				},
			},
		},

		{
			name: "修改timed config 配置,扩容梯度值发生变化",
			igList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
				},
			},
			timedScaleUpCm: &entity.InstanceGroupTimedScaleUpCm{
				MaxScaleupNodeCount: 3,
				InstanceGroups: []*entity.TimedScaleUpParams{
					{
						InstanceGroupID:          "cce-ig-f7ig6xak",
						AutoScaleUpStartTime:     autoScaleUpStartTime.Format("2006-01-02 15:04:05"),
						AutoScaleUpEndTime:       autoScaleUpEndTime.Format("2006-01-02 15:04:05"),
						ExpectScaleUpSuccessTime: expectScaleUpSuccessTime.Format("2006-01-02 15:04:05"),
						ExpectNodeCount:          3,
						ExpectAutoDeleteNodeTime: "",
					},
				},
			},
			timedCaDeployment: func() *appv1.Deployment {
				deployment := createNewTimedCaDeployment("testImage", "test")
				command := buildTimedCaCommand(make(map[string]*entity.TimedScaleUpParams), 10)
				deployment.Spec.Template.Spec.Containers[0].Command = command
				return deployment
			}(),
		},

		{
			name: "定时任务统计,还未扩容成功,task 运行时长超过10分钟,且存在创建失败node",
			igList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
				},
			},
			timedScaleUpCm: &entity.InstanceGroupTimedScaleUpCm{
				MaxScaleupNodeCount: 3,
				InstanceGroups: []*entity.TimedScaleUpParams{
					{
						InstanceGroupID:          "cce-ig-f7ig6xak",
						AutoScaleUpStartTime:     autoScaleUpStartTime.Format("2006-01-02 15:04:05"),
						AutoScaleUpEndTime:       autoScaleUpEndTime.Format("2006-01-02 15:04:05"),
						ExpectScaleUpSuccessTime: expectScaleUpSuccessTime.Format("2006-01-02 15:04:05"),
						ExpectNodeCount:          3,
						ExpectAutoDeleteNodeTime: "",
					},
				},
			},
			igNodeMap: map[string]*ccev2.ListInstancesByInstanceGroupIDResponse{
				"cce-ig-f7ig6xak": &ccev2.ListInstancesByInstanceGroupIDResponse{
					CommonResponse: ccev2.CommonResponse{
						RequestID: "reqID",
					},
					Page: ccev2.ListInstancesByInstanceGroupIDPage{
						TotalCount: 2,
						List: []*ccev2.Instance{
							{
								Status: &ccev2.InstanceStatus{
									InstancePhase: types.InstancePhaseCreateFailed,
								},
							},
							{
								Status: &ccev2.InstanceStatus{
									InstancePhase: types.InstancePhaseRunning,
								},
							},
						},
					},
				},
			},
			igTaskMap: map[string]*ccev2.ListTaskResp{
				"cce-ig-f7ig6xak": &ccev2.ListTaskResp{
					CommonResponse: ccev2.CommonResponse{
						RequestID: "reqID",
					},
					Page: ccev2.ListTaskPage{
						TotalCount: 1,
						Items: []*types.Task{
							{
								StartTime: time.Now().Add(subOneHourDuration),
								Phase:     types.TaskPhaseProcessing,
							},
						},
					},
				},
			},
			statisticsTask: true,
		},

		{
			name: "timed config 配置已过期,删除创建的ca deployment",
			igList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
					Buffer:            3,
				},
			},
			timedScaleUpCm: &entity.InstanceGroupTimedScaleUpCm{
				MaxScaleupNodeCount: 3,
				InstanceGroups: []*entity.TimedScaleUpParams{
					{
						InstanceGroupID:          "cce-ig-f7ig6xak",
						AutoScaleUpStartTime:     "2023-05-06 11:00:00",
						AutoScaleUpEndTime:       "2023-05-06 13:00:00",
						ExpectScaleUpSuccessTime: "2023-05-06 12:00:00",
						ExpectNodeCount:          3,
						ExpectAutoDeleteNodeTime: "2023-05-06 14:00:00",
					},
				},
			},
			timedCaDeployment: func() *appv1.Deployment {
				deployment := createNewTimedCaDeployment("testImage", "test")
				return deployment
			}(),
		},
	}

	for i, test := range tests {
		if i != len(tests)-1 {
			// continue
		}
		t.Run(test.name, func(t *testing.T) {
			cms := make([]*corev1.ConfigMap, 0)
			if len(test.igList) > 0 {
				data, _ := json.Marshal(test.igList)
				igCm := &corev1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      entity.InstanceGroupCmName,
						Namespace: entity.InstanceGroupCmNamespace,
					},
					Data: map[string]string{
						entity.InstanceGropuCmDataKey: string(data),
					},
				}
				cms = append(cms, igCm)
			}
			if test.timedScaleUpCm != nil {
				data, _ := json.Marshal(test.timedScaleUpCm)
				timedCm := &corev1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Namespace: entity.TimedSacleUPCmNamespace,
						Name:      entity.TimedSacleUPCmName,
					},
					Data: map[string]string{
						entity.InstanceGropuCmDataKey: string(data),
					},
				}
				cms = append(cms, timedCm)
			}

			deployments := make([]*appv1.Deployment, 0)
			if test.timedCaDeployment != nil {
				deployments = append(deployments, test.timedCaDeployment)
			}
			controller := newController(cms, deployments)

			cceClient := newfakeCceClient(false, test.igList, test.igNodeMap, test.igTaskMap)
			controller.cceClient = cceClient

			if !test.statisticsTask {
				got, _ := controller.Reconcile(context.TODO(), reconcile.Request{})
				if !reflect.DeepEqual(got, test.result) {
					t.Errorf("got %+v expect %+v ", got, test.result)
				}
			} else {
				stopChan := make(<-chan struct{})
				_ = controller.Start(stopChan)
				controller.instanceGroupTaskStatistics()
			}
		})
	}
}
