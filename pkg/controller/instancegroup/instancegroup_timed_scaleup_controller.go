package instancegroup

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	appv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	appv1_listers "k8s.io/client-go/listers/apps/v1"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	toolscache "k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"sigs.k8s.io/controller-runtime/pkg/source"
)

const (
	timedCADeploymentName      = "cluster-autoscaler-timed"
	timedCADeploymentNamespace = "kube-system"
)

var _ reconcile.Reconciler = &TimedSacleUPController{}

// 节点组定点扩缩容controller,一期针对潮汐节点组
type TimedSacleUPController struct {
	sync.Mutex
	option *options.ServerRunOptions

	cmLister         corev1_listers.ConfigMapLister
	deploymentLister appv1_listers.DeploymentLister

	// 与cce交互client
	cceClient util.CceClient
	// 与k8s apiserver 交互的client
	client   client.Client
	hiClient util.Alerter
	region   string
}

func NewTimedSacleUPController(option *options.ServerRunOptions, mgr manager.Manager) (interface{}, error) {

	cache := mgr.GetCache()
	cmInformer, err := cache.GetInformer(context.TODO(), &corev1.ConfigMap{})
	if err != nil {
		klog.Errorf("timedSacleUPController get cmInformer err %+v", err)
		return nil, err
	}
	deploymentInformer, err := cache.GetInformer(context.TODO(), &appv1.Deployment{})
	if err != nil {
		klog.Errorf("timedSacleUPController get deploymentInformer err %+v", err)
		return nil, err
	}

	cmLister := corev1_listers.NewConfigMapLister(cmInformer.(toolscache.SharedIndexInformer).GetIndexer())
	deploymentLister := appv1_listers.NewDeploymentLister(deploymentInformer.(toolscache.SharedIndexInformer).GetIndexer())

	cceClient, err := util.NewCceClient(option.CceOptions)
	if err != nil {
		klog.Errorf("timedSacleUPController NewCceClient err %+v", err)
		return nil, err
	}

	c := &TimedSacleUPController{
		option:           option,
		cmLister:         cmLister,
		deploymentLister: deploymentLister,
		cceClient:        cceClient,
		client:           mgr.GetClient(),
		region:           option.NetworkOptions.Region,
	}

	hiClient := util.NewHiClient(option.InstanceGroupOptions.HiClientTokenURL)
	c.hiClient = hiClient

	controller, err := controller.New("timed-sacleup-controller", mgr, controller.Options{
		Reconciler:              c,
		MaxConcurrentReconciles: option.InstanceGroupOptions.InstanceGroupControllerWorkers,
		RateLimiter:             util.DefaultControllerRateLimiter(option),
	})

	if err != nil {
		klog.Errorf("timedSacleUPController new err %+v ", err)
		return nil, err
	}

	err = controller.Watch(source.Kind(mgr.GetCache(), &corev1.ConfigMap{}), &handler.EnqueueRequestForObject{}, predicate.Funcs{
		CreateFunc: func(ce event.CreateEvent) bool {
			cm := ce.Object.(*corev1.ConfigMap)
			if cm.Namespace == entity.TimedSacleUPCmNamespace && cm.Name == entity.TimedSacleUPCmName {
				return true
			}
			return false
		},
		UpdateFunc: func(ue event.UpdateEvent) bool {
			cm := ue.ObjectNew.(*corev1.ConfigMap)
			if cm.Namespace == entity.TimedSacleUPCmNamespace && cm.Name == entity.TimedSacleUPCmName {
				return true
			}
			return false
		},
		GenericFunc: func(ge event.GenericEvent) bool {
			return false
		},
	})

	if err != nil {
		klog.Errorf("timedSacleUPController watch cm informer  %+v ", err)
		return nil, err
	}
	return c, nil
}

func (c *TimedSacleUPController) Reconcile(_ context.Context, request reconcile.Request) (res reconcile.Result, retErr error) {
	// 加锁串行处理
	c.Lock()
	defer c.Unlock()

	timedScaleUpCm, err := c.getTimedSacleUPConfigMap()
	if err != nil {
		errMsg := c.buildHiMessage("解析自动扩缩节点组configmap失败", err.Error())
		_ = c.hiClient.SendMarkDownMessage(context.Background(), errMsg)
		return reconcile.Result{RequeueAfter: 30 * time.Second}, nil
	}

	igMap, _, err := util.GetInstanceGroupCm(c.cmLister)
	if err != nil {
		klog.Errorf("timedSacleUPController get instanceGroupMap err %+v", err)
		return reconcile.Result{RequeueAfter: 30 * time.Second}, nil
	}

	if err = timedScaleUpCm.CheckParams(igMap); err != nil {
		klog.Errorf("timedSacleUPController CheckParams err ")
		errMsg := c.buildHiMessage("自动扩缩节点组configmap参数校验失败", err.Error())
		_ = c.hiClient.SendMarkDownMessage(context.Background(), errMsg)
		return reconcile.Result{RequeueAfter: 30 * time.Second}, nil
	}

	// inAutoScaleIgMap 在自动扩容时间段内的节点组
	// outAutoScaleIgMap 不在自动扩容时间段内的节点组
	inAutoScaleIgMap, outAutoScaleIgMap := timedScaleUpCm.ParseAutoScaleUpInstanceGroup(igMap)

	// 修改节点组buffer数
	err = c.updateInstanceGroupCmWithRetry(func(igList []entity.InstanceGroupCm) (result []entity.InstanceGroupCm, changed bool) {
		// 节点组buffer 是否发生变更
		changed = false

		for _, ig := range igList {
			// 拷贝一份
			result = append(result, ig)
		}

		for i := range result {
			timeupIgConfig, ok := inAutoScaleIgMap[result[i].InstanceGroupId]
			// 在自动扩容时间段内
			if ok && result[i].Buffer != timeupIgConfig.ExpectNodeCount {
				changed = true
				result[i].Buffer = timeupIgConfig.ExpectNodeCount
				continue
			}

			// 配置了自动删除node，当过了ExpectAutoDeleteNodeTime时间后，自动设置节点组buffer为0
			timeupIgConfig, ok = outAutoScaleIgMap[result[i].InstanceGroupId]
			if ok && timeupIgConfig.AfterExpectAutoDeleteNodeTime() && result[i].Buffer != 0 {
				changed = true
				result[i].Buffer = 0
			}
		}
		return result, changed
	})
	if err != nil {
		return reconcile.Result{RequeueAfter: 30 * time.Second}, nil
	}

	// 关闭cce 节点组自动扩缩容
	for ig, params := range inAutoScaleIgMap {
		resp, err := c.cceClient.GetInstanceGroupDetail(ig)
		if err != nil {
			klog.Errorf("timedSacleUPController instanceGroupID %s GetInstanceGroupDetail err %+v", ig, err)
			delete(inAutoScaleIgMap, ig)
			continue
		}
		// 已经关闭自动扩缩
		if !resp.InstanceGroup.Spec.ClusterAutoscalerSpec.Enabled {
			continue
		}
		changeResp, err := c.cceClient.ChangeInstanceGroupAutoscalerConfig(false, ig, params.ExpectNodeCount)
		klog.V(3).Infof("timedSacleUPController instanceGroupID %s ChangeInstanceGroupAutoscalerConfig resp %+v err %+v", ig, changeResp, err)
		if err != nil {
			delete(inAutoScaleIgMap, ig)
		}
	}

	// 创建或更新潮汐ca deployment
	if err = c.syncTimedCaDeployment(inAutoScaleIgMap, timedScaleUpCm.MaxScaleupNodeCount); err != nil {
		klog.Errorf("timedSacleUPController syncTimedCaDeployment err %+v ", err)
		return reconcile.Result{}, nil
	}
	return
}

func (c *TimedSacleUPController) syncTimedCaDeployment(inAutoSacleIgMap map[string]*entity.TimedScaleUpParams, maxScaleupNodeCount int) error {

	timedCADeployment, err := c.deploymentLister.Deployments(timedCADeploymentNamespace).Get(timedCADeploymentName)

	if len(inAutoSacleIgMap) == 0 {
		// 删除ca deployment
		if err != nil && errors.IsNotFound(err) {
			return nil
		} else if err != nil {
			return err
		}
		err = c.client.Delete(context.Background(), timedCADeployment)
		klog.V(3).Infof("timedSacleUPController delete timedCADeployment err %+v ", err)
		return err
	}

	caCommand := buildTimedCaCommand(inAutoSacleIgMap, maxScaleupNodeCount)
	// create or update ca deployment
	if err != nil && errors.IsNotFound(err) {
		newTimedCaDeployment := createNewTimedCaDeployment(c.option.InstanceGroupOptions.TimedCaImage,
			c.region)
		newTimedCaDeployment.Spec.Template.Spec.Containers[0].Command = caCommand

		err = c.client.Create(context.Background(), newTimedCaDeployment)
		klog.V(3).Infof("timedSacleUPController create timedCADeployment err %+v ", err)
		return err
	} else if err != nil {
		return err
	}
	oldCaCommand := timedCADeployment.Spec.Template.Spec.Containers[0].Command
	if reflect.DeepEqual(oldCaCommand, caCommand) {
		// command 没有变化，不进行更新
		klog.V(5).Infof("timedSacleUPController update timedCADeployment command not change ignore ")
		return nil
	}
	copyDeployment := timedCADeployment.DeepCopy()
	copyDeployment.Spec.Template.Spec.Containers[0].Command = caCommand
	// 如更新失败，后续进行重试
	err = c.client.Update(context.Background(), copyDeployment)
	klog.V(3).Infof("timedSacleUPController update timedCADeployment err %+v ", err)
	return err
}

func (c *TimedSacleUPController) updateInstanceGroupCmWithRetry(updateBufferFn func(igList []entity.InstanceGroupCm) (
	result []entity.InstanceGroupCm, changed bool)) error {

	err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		cm, err := c.cmLister.ConfigMaps(entity.InstanceGroupCmNamespace).Get(entity.InstanceGroupCmName)
		if errors.IsNotFound(err) {
			return nil
		}
		if err != nil {
			return err
		}

		copyCm := cm.DeepCopy()
		_, igList, err := util.GetInstanceGroupCm(c.cmLister)
		if err != nil {
			klog.Errorf("timedSacleUPController GetInstanceGroupCm err %+v ", err)
			return nil
		}

		result, changed := updateBufferFn(igList)
		if !changed {
			klog.V(5).Info("timedSacleUPController update instanceGroup buffer not change")
			return nil
		}
		oldData, _ := json.MarshalIndent(igList, "", "    ")
		klog.V(3).Infof("timedSacleUPController update instanceGroup buffer Before data %s", string(oldData))

		bytes, _ := json.MarshalIndent(result, "", "    ")
		copyCm.Data[entity.InstanceGropuCmDataKey] = string(bytes)

		return c.client.Update(context.Background(), copyCm)
	})
	if err != nil {
		klog.Errorf("timedSacleUPController update instanceGroup buffer err %+v", err)
	}
	return err
}

func (c *TimedSacleUPController) getTimedSacleUPConfigMap() (*entity.InstanceGroupTimedScaleUpCm, error) {
	params := &entity.InstanceGroupTimedScaleUpCm{}
	timedSacleUPCM, err := c.cmLister.ConfigMaps(entity.TimedSacleUPCmNamespace).Get(entity.TimedSacleUPCmName)
	if err != nil {
		klog.Errorf("timedSacleUPController Reconcile get timedSacleUPCM err %+v ", err)
		return params, err
	}

	data, ok := timedSacleUPCM.Data[entity.InstanceGropuCmDataKey]
	if !ok {
		err = fmt.Errorf("timedSacleUPController Reconcile get timedSacleUPCM data empty ")
		return params, err
	}

	err = json.Unmarshal([]byte(data), params)
	if err != nil {
		klog.Error("timedSacleUPController Reconcile Unmarshal timedSacleUPCM data  %s err %+v ", data, err)
		return params, err
	}
	return params, nil
}

func (c *TimedSacleUPController) Start(stopChan <-chan struct{}) error {

	// 进程选到主后,如未创建cm,则进行初始化
	_, err := c.cmLister.ConfigMaps(entity.TimedSacleUPCmNamespace).Get(entity.TimedSacleUPCmName)
	if err != nil && errors.IsNotFound(err) {
		// 创建 TimedSacleUPCmName cm
		cm := &entity.InstanceGroupTimedScaleUpCm{
			MaxScaleupNodeCount: 50,
			InstanceGroups:      make([]*entity.TimedScaleUpParams, 0),
		}
		bytes, _ := json.MarshalIndent(cm, "", "    ")
		initCm := &corev1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Name:      entity.TimedSacleUPCmName,
				Namespace: entity.TimedSacleUPCmNamespace,
			},
			Data: map[string]string{
				entity.InstanceGropuCmDataKey: string(bytes),
			},
		}
		if err := c.client.Create(context.Background(), initCm); err != nil {
			klog.Errorf("timedSacleUPController create TimedSacleUPCm err %+v ", err)
		}
	}

	go func() {
		ticker := time.NewTicker(c.option.InstanceGroupOptions.DefaultSyncInstanceGroupInterval)
		for {
			select {
			case <-ticker.C:
				// 定期执行Reconcile函数
				_, _ = c.Reconcile(context.Background(), reconcile.Request{})
				c.instanceGroupTaskStatistics()
			}
		}
	}()
	return nil
}

func createNewTimedCaDeployment(caImage string, region string) *appv1.Deployment {
	containerRequestResource := make(map[corev1.ResourceName]resource.Quantity, 0)
	cpuRequest, _ := resource.ParseQuantity("100m")
	memoryRequest, _ := resource.ParseQuantity("300Mi")
	containerRequestResource[corev1.ResourceCPU] = cpuRequest
	containerRequestResource[corev1.ResourceMemory] = memoryRequest

	containerLimitResource := make(map[corev1.ResourceName]resource.Quantity, 0)
	cpuLimit, _ := resource.ParseQuantity("2")
	memoryLimit, _ := resource.ParseQuantity("2Gi")
	containerLimitResource[corev1.ResourceCPU] = cpuLimit
	containerLimitResource[corev1.ResourceMemory] = memoryLimit

	// 副本数
	var replicas int32 = 1
	var defaultMode int32 = 420

	timedCaDeployment := &appv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: timedCADeploymentNamespace,
			Name:      timedCADeploymentName,
		},
		Spec: appv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app.kubernetes.io/name": "cluster-autoscaler-timed",
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app.kubernetes.io/name": "cluster-autoscaler-timed",
					},
				},
				Spec: corev1.PodSpec{
					HostNetwork: true,
					// 亲和性相关设置,部署到master节点上
					NodeSelector: map[string]string{
						"cluster-role": "master",
					},
					// container相关设置
					Containers: []corev1.Container{
						{
							Image:           caImage,
							ImagePullPolicy: corev1.PullIfNotPresent,
							Name:            "cce-cluster-autoscaler-timed",
							Resources: corev1.ResourceRequirements{
								Requests: containerRequestResource,
								Limits:   containerLimitResource,
							},
							Env: []corev1.EnvVar{
								{
									Name: "CCE_GATEWAY_ENDPOINT",
									// 复用eni 启动参数的region
									Value: "cce-gateway." + region + ".baidubce.com",
								},
							},
							Ports: []corev1.ContainerPort{
								{
									Name:          "metrics",
									ContainerPort: 8085,
									HostPort:      8085,
									Protocol:      "TCP",
								},
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "cloudconfig",
									MountPath: "/etc/kubernetes/cloud.config",
									ReadOnly:  true,
								},
								{
									Name:      "auth-token",
									MountPath: "/var/run/secrets/cce/cce-plugin-token",
									ReadOnly:  true,
								},
							},
						},
					},
					DNSPolicy:          corev1.DNSClusterFirst,
					PriorityClassName:  "system-cluster-critical",
					RestartPolicy:      corev1.RestartPolicyAlways,
					ServiceAccountName: "cluster-autoscaler-timed",
					Tolerations: []corev1.Toleration{
						{
							Key:      "node-role.kubernetes.io/master",
							Effect:   "NoSchedule",
							Operator: "Equal",
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: "cloudconfig",
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: "/etc/kubernetes/cloud.config",
								},
							},
						},
						{
							Name: "auth-token",
							VolumeSource: corev1.VolumeSource{
								Secret: &corev1.SecretVolumeSource{
									DefaultMode: &defaultMode,
									SecretName:  "cce-plugin-token",
								},
							},
						},
					},
				},
			},
		},
	}
	return timedCaDeployment
}

func buildTimedCaCommand(inAutoSacleIgMap map[string]*entity.TimedScaleUpParams, maxScaleupNodeCount int) []string {
	sortIgList := make([]string, 0, len(inAutoSacleIgMap))

	timedCaCommand := make([]string, 0)
	timedCaCommand = append(timedCaCommand, "./cluster-autoscaler")
	timedCaCommand = append(timedCaCommand, "--cloud-provider=baiducloud")
	timedCaCommand = append(timedCaCommand, "--leader-elect=true")
	for ig := range inAutoSacleIgMap {
		sortIgList = append(sortIgList, ig)
	}

	sort.Strings(sortIgList)
	for _, ig := range sortIgList {
		param := inAutoSacleIgMap[ig]
		expectNodeCountStr := strconv.Itoa(param.ExpectNodeCount)
		timedCaCommand = append(timedCaCommand, "--nodes=0:"+expectNodeCountStr+":"+ig)
	}
	// 选主key
	timedCaCommand = append(timedCaCommand, "--leader-elect-resource-name=cluster-autoscaler-timed")
	// max-nodes-per-scaleup，梯度扩容值
	timedCaCommand = append(timedCaCommand, "--max-nodes-per-scaleup="+strconv.Itoa(maxScaleupNodeCount))
	timedCaCommand = append(timedCaCommand, "--cloud-config=/etc/kubernetes/cloud.config")
	timedCaCommand = append(timedCaCommand, "--logtostderr=true")
	timedCaCommand = append(timedCaCommand, "--scale-down-enabled=false")
	timedCaCommand = append(timedCaCommand, "--expander=random")
	timedCaCommand = append(timedCaCommand, "--v=4")

	return timedCaCommand
}

func (c *TimedSacleUPController) buildHiMessage(title, message string) string {
	// 构建markdown类型

	builder := strings.Builder{}
	builder.WriteString("title: **" + title + "**\n")
	builder.WriteString("region: " + c.region + "\n")
	builder.WriteString("message: " + message + "\n")
	// builder.WriteString("time: " + time.Now().Format("2006-01-02 15:04") + "\n")

	return builder.String()
}

// 定时统计节点组自动扩容task是否扩容成功
func (c *TimedSacleUPController) instanceGroupTaskStatistics() {
	timedScaleUpCm, _ := c.getTimedSacleUPConfigMap()
	igMap, _, _ := util.GetInstanceGroupCm(c.cmLister)

	// 在自动扩容时间段内的节点组
	inAutoScaleIgMap, _ := timedScaleUpCm.ParseAutoScaleUpInstanceGroup(igMap)

	if len(inAutoScaleIgMap) == 0 {
		klog.V(5).Infof("timedSacleUPController instanceGroupTaskStatistics inAutoScaleIgMap is empty ")
		return
	}

	for ig, params := range inAutoScaleIgMap {
		c.doInstanceGroupTaskStatistics(ig, params)
	}
	return
}

func (c *TimedSacleUPController) doInstanceGroupTaskStatistics(instanceGroupID string, params *entity.TimedScaleUpParams) {
	resp, err := c.cceClient.InstanceGroupTasks(instanceGroupID, 1, 20)
	if err != nil {
		klog.Errorf("timedSacleUPController doInstanceGroupTaskStatistics instanceGroupID %s get InstanceGroupTasks err %+v ", instanceGroupID, err)
		return
	}

	shouldSendHiMessage := false
	for _, task := range resp.Page.Items {
		if task.Phase == types.TaskPhaseAborted || task.Phase == types.TaskPhaseDone {
			continue
		}
		// 查询pending 或 Processing 状态的task,如运行时间超过10分钟，则发送hi消息
		costMinutes := time.Now().Sub(task.StartTime).Minutes()
		if costMinutes > float64(c.option.InstanceGroupOptions.TimedInstanceGroupTaskMaxCostMinute) {
			shouldSendHiMessage = true
		}
	}

	if shouldSendHiMessage {
		strMinute := strconv.Itoa(c.option.InstanceGroupOptions.TimedInstanceGroupTaskMaxCostMinute)
		errMsg := c.buildHiMessage("节点组task执行时间超时", instanceGroupID+" 存在task执行时间超过"+strMinute+"分钟未达到终态")
		_ = c.hiClient.SendMarkDownMessage(context.Background(), errMsg)
	}
	if !params.AfterExpectScaleUpSuccessTime() {
		return
	}

	// 统计当前node ready 数量
	nodeResp, err := c.cceClient.GetInstanceGroupNodes(instanceGroupID)

	klog.V(4).Infof("timedSacleUPController doInstanceGroupTaskStatistics instanceGroupID %s GetInstanceGroupNodes err %+v",
		instanceGroupID, err)

	// 是否存在创建失败的node
	hasCreateFailedNode := false
	// 节点组node 数是否达到预期值
	arriveExpectNodeCount := false

	currentNodeCount := 0
	if nodeResp != nil {
		for _, instance := range nodeResp.Page.List {
			if instance.Status == nil {
				continue
			}
			// node 创建失败
			if instance.Status.InstancePhase == types.InstancePhaseCreateFailed {
				hasCreateFailedNode = true
			}
		}
		currentNodeCount = nodeResp.Page.TotalCount
		if nodeResp.Page.TotalCount >= params.ExpectNodeCount {
			arriveExpectNodeCount = true
		}
	}

	if hasCreateFailedNode {
		errMsg := c.buildHiMessage("节点组存在创建失败node", instanceGroupID+" 存在创建失败node")
		_ = c.hiClient.SendMarkDownMessage(context.Background(), errMsg)
	}

	if !arriveExpectNodeCount {
		errMsg := c.buildHiMessage("节点组node数未达到期望值",
			instanceGroupID+";当前节点数: "+strconv.Itoa(currentNodeCount)+";期望节点数: "+strconv.Itoa(params.ExpectNodeCount))
		_ = c.hiClient.SendMarkDownMessage(context.Background(), errMsg)
	}

}
