package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"testing"
	"time"

	ccev2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	appv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8s_types "k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/informers"
	k8sfake "k8s.io/client-go/kubernetes/fake"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

type fakeCceClient struct {
	hasErr    bool
	igList    []entity.InstanceGroupCm
	igNodeMap map[string]*ccev2.ListInstancesByInstanceGroupIDResponse
	igTaskMap map[string]*ccev2.ListTaskResp
}

func newfakeCceClient(hasErr bool, igList []entity.InstanceGroupCm,
	igNodeMap map[string]*ccev2.ListInstancesByInstanceGroupIDResponse,
	igTaskMap map[string]*ccev2.ListTaskResp) util.CceClient {

	return &fakeCceClient{
		hasErr:    hasErr,
		igList:    igList,
		igNodeMap: igNodeMap,
		igTaskMap: igTaskMap,
	}
}

func (fake *fakeCceClient) ChangeInstanceGroupAutoscalerConfig(enableAutoscaler bool, instanceGroupID string, maxReplicas int) (
	*ccev2.UpdateInstanceGroupClusterAutoscalerSpecResponse, error) {
	res := &ccev2.UpdateInstanceGroupClusterAutoscalerSpecResponse{
		CommonResponse: ccev2.CommonResponse{
			RequestID: "reqId",
		},
	}
	return res, nil
}

func (fake *fakeCceClient) GetInstanceGroupNodes(instanceGroupID string) (*ccev2.ListInstancesByInstanceGroupIDResponse, error) {
	resp, ok := fake.igNodeMap[instanceGroupID]
	if !ok {
		return nil, fmt.Errorf("ig not exist")
	}
	return resp, nil
}

func (fake *fakeCceClient) GetInstanceGroupDetail(instanceGroupID string) (*ccev2.GetInstanceGroupResponse, error) {

	if fake.hasErr {
		return nil, errors.New("err")
	}
	for _, ig := range fake.igList {
		if ig.InstanceGroupId == instanceGroupID {
			res := &ccev2.GetInstanceGroupResponse{
				CommonResponse: ccev2.CommonResponse{
					RequestID: "reqId",
				},
				InstanceGroup: &ccev2.InstanceGroup{
					Spec: &ccev2.InstanceGroupSpec{
						InstanceGroupName:  ig.InstanceGroupName,
						CCEInstanceGroupID: ig.InstanceGroupId,
						ClusterAutoscalerSpec: &ccev2.ClusterAutoscalerSpec{
							Enabled:     true,
							MinReplicas: 1,
							MaxReplicas: 32,
						},
						InstanceTemplate: ccev2.InstanceTemplate{
							types.InstanceSpec{
								InstanceResource: types.InstanceResource{
									CPU: 2,
									MEM: 8,
								},
							},
						},
					},
				},
			}

			return res, nil
		}
	}
	return nil, nil
}

func (fake *fakeCceClient) RemoveInstanceGroupNodes(instanceGroupID string, removeInstances []string) (*ccev2.CreateTaskResp, error) {
	res := &ccev2.CreateTaskResp{
		CommonResponse: ccev2.CommonResponse{
			RequestID: "reqId",
		},
	}
	if fake.hasErr {
		return nil, errors.New("err")
	}
	return res, nil
}

func (fake *fakeCceClient) GetInstanceGroupList() (*ccev2.ListInstanceGroupResponse, error) {
	if fake.hasErr {
		return nil, errors.New("err")
	}
	res := &ccev2.ListInstanceGroupResponse{
		CommonResponse: ccev2.CommonResponse{
			RequestID: "reqID",
		},
		Page: ccev2.ListInstanceGroupPage{
			List: []*ccev2.InstanceGroup{},
		},
	}

	for _, ig := range fake.igList {
		res.Page.List = append(res.Page.List, &ccev2.InstanceGroup{
			Spec: &ccev2.InstanceGroupSpec{
				CCEInstanceGroupID: ig.InstanceGroupId,
				InstanceGroupName:  ig.InstanceGroupName,
			},
		})
	}
	return res, nil
}

func (fake *fakeCceClient) GetKubeconfig(clusterID string) (*ccev2.GetKubeConfigResponse, error) {
	return nil, nil
}

func (fake *fakeCceClient) GetInstance(instanceID string) (resp *ccev2.GetInstanceResponse, err error) {
	return nil, nil
}

func (fake *fakeCceClient) CreateInstances(instanceSet []*ccev2.InstanceSet) (resp *ccev2.CreateInstancesResponse, err error) {
	return nil, nil
}

func (fake *fakeCceClient) DeleteInstances(removeCCEInstanceIds []string) (resp *ccev2.DeleteInstancesResponse, err error) {
	return nil, nil
}

func (fake *fakeCceClient) InstanceGroupTasks(instanceGroupID string, pageNo, pageSize int) (resp *ccev2.ListTaskResp, err error) {
	resp, ok := fake.igTaskMap[instanceGroupID]
	if !ok {
		return nil, fmt.Errorf("ig not exist")
	}
	return resp, nil
}

func TestBuildInstanceGroupResource(t *testing.T) {
	cpu := float64(10) * float64(2) / float64(3)   // 单位为core
	memory := float64(8) * float64(2) / float64(3) // 单位为GB

	cpuStr := fmt.Sprintf("%f", cpu)
	memoryStr := fmt.Sprintf("%fG", memory)
	fmt.Println(cpuStr)
	fmt.Println(memoryStr)
	cpuQuantity, err := resource.ParseQuantity(cpuStr)
	if err != nil {
		panic(err)
	}
	memoryQuantity, err := resource.ParseQuantity(memoryStr)
	if err != nil {
		panic(err)
	}

	fmt.Println(cpuQuantity)
	fmt.Println(memoryQuantity)
}

func TestInstanceGroupReconcile(t *testing.T) {

	var replica int32 = 5
	tests := []struct {
		name           string
		igList         []entity.InstanceGroupCm
		placeholders   []*appv1.Deployment
		igCm           *corev1.ConfigMap
		reconcileParam k8s_types.NamespacedName
		cceErr         bool
		initIgCm       bool
		initIgCmErr    bool // cm 配置错误
		startFn        bool
		res            reconcile.Result
	}{
		{
			name:   "无节点组",
			igList: []entity.InstanceGroupCm{},
			reconcileParam: k8s_types.NamespacedName{
				Namespace: entity.InstanceGroupCmNamespace,
				Name:      entity.InstanceGroupCmName,
			},
			cceErr: false,
			res:    reconcile.Result{},
		},
		{
			name: "空ig cm 配置",
			igList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
				},
			},
			igCm: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      entity.InstanceGroupCmName,
					Namespace: entity.InstanceGroupCmNamespace,
				},
				Data: map[string]string{},
			},
			reconcileParam: k8s_types.NamespacedName{
				Namespace: entity.InstanceGroupCmNamespace,
				Name:      entity.InstanceGroupCmName,
			},
			res: reconcile.Result{},
		},
		{
			name: "一个节点组，创建placeholder",
			igList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
				},
			},
			igCm: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      entity.InstanceGroupCmName,
					Namespace: entity.InstanceGroupCmNamespace,
				},
				Data: map[string]string{},
			},
			reconcileParam: k8s_types.NamespacedName{
				Namespace: entity.InstanceGroupCmNamespace,
				Name:      entity.InstanceGroupCmName,
			},
			initIgCm: true,
			res:      reconcile.Result{},
		},
		{
			name: "instanceGroup cm 配置错误",
			igList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
				},
			},
			igCm: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      entity.InstanceGroupCmName,
					Namespace: entity.InstanceGroupCmNamespace,
				},
				Data: map[string]string{},
			},
			reconcileParam: k8s_types.NamespacedName{
				Namespace: entity.InstanceGroupCmNamespace,
				Name:      entity.InstanceGroupCmName,
			},
			initIgCm:    true,
			initIgCmErr: true,
			res:         reconcile.Result{RequeueAfter: 5 * time.Second},
		},
		{
			name: "sync placeholder 找不到instanceGroup 配置",
			igList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
				},
			},
			igCm: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      entity.InstanceGroupCmName,
					Namespace: entity.InstanceGroupCmNamespace,
				},
				Data: map[string]string{},
			},
			reconcileParam: k8s_types.NamespacedName{
				Namespace: entity.InstanceGroupCmNamespace,
				Name:      "zoneA-c2m8-cpu-placeholder",
			},
			initIgCm: true,
			res:      reconcile.Result{},
		},
		{
			name: "sync placeholder delete ",
			igList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
				},
			},
			igCm: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      entity.InstanceGroupCmName,
					Namespace: entity.InstanceGroupCmNamespace,
				},
				Data: map[string]string{},
			},
			reconcileParam: k8s_types.NamespacedName{
				Namespace: entity.InstanceGroupCmNamespace,
				Name:      "zonea-c2m8-cpu-placeholder",
			},
			initIgCm: true,
			res:      reconcile.Result{},
		},
		{
			name: "sync placeholder replica  ",
			igList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
					Buffer:            10,
				},
			},
			igCm: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      entity.InstanceGroupCmName,
					Namespace: entity.InstanceGroupCmNamespace,
				},
				Data: map[string]string{},
			},
			placeholders: []*appv1.Deployment{
				&appv1.Deployment{
					ObjectMeta: metav1.ObjectMeta{
						Namespace: entity.InstanceGroupCmNamespace,
						Name:      "zonea-c2m8-cpu-placeholder",
					},
					Spec: appv1.DeploymentSpec{
						Replicas: &replica,
					},
				},
			},
			reconcileParam: k8s_types.NamespacedName{
				Namespace: entity.InstanceGroupCmNamespace,
				Name:      "zonea-c2m8-cpu-placeholder",
			},
			initIgCm: true,
			res:      reconcile.Result{},
		},

		{
			name: "sync instanceGroup replica  ",
			igList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
					Buffer:            10,
				},
			},
			igCm: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      entity.InstanceGroupCmName,
					Namespace: entity.InstanceGroupCmNamespace,
				},
				Data: map[string]string{},
			},
			placeholders: []*appv1.Deployment{
				&appv1.Deployment{
					ObjectMeta: metav1.ObjectMeta{
						Namespace: entity.InstanceGroupCmNamespace,
						Name:      "zonea-c2m8-cpu-placeholder",
					},
					Spec: appv1.DeploymentSpec{
						Replicas: &replica,
					},
				},
			},
			reconcileParam: k8s_types.NamespacedName{
				Namespace: entity.InstanceGroupCmNamespace,
				Name:      "zoneA-c2m8-cpu",
			},
			initIgCm: true,
			res:      reconcile.Result{},
		},
		{
			name:   "开启start函数",
			igList: []entity.InstanceGroupCm{},
			reconcileParam: k8s_types.NamespacedName{
				Namespace: entity.InstanceGroupCmNamespace,
				Name:      entity.InstanceGroupCmName,
			},
			cceErr:  false,
			startFn: true,
			res:     reconcile.Result{},
		},
	}

	for i, test := range tests {
		if i != len(tests)-1 {
			// continue
		}
		t.Run(test.name, func(t *testing.T) {
			k8sclient := k8sfake.NewSimpleClientset()
			client := fake.NewFakeClient()

			informerFactory := informers.NewSharedInformerFactory(k8sclient, 0)
			cmInformer := informerFactory.Core().V1().ConfigMaps()

			deploymentInformer := informerFactory.Apps().V1().Deployments()
			podInformer := informerFactory.Core().V1().Pods()
			nodeInformer := informerFactory.Core().V1().Nodes()

			options := options.NewServerRunOptions()
			options.InstanceGroupOptions.DefaultSyncInstanceGroupInterval = 1 * time.Second
			cceClient := newfakeCceClient(test.cceErr, test.igList,
				make(map[string]*ccev2.ListInstancesByInstanceGroupIDResponse),
				make(map[string]*ccev2.ListTaskResp))

			c := &Controller{
				option:           options,
				cmLister:         cmInformer.Lister(),
				deploymentLister: deploymentInformer.Lister(),
				podLister:        podInformer.Lister(),
				nodeLister:       nodeInformer.Lister(),
				client:           client,
				cceClient:        cceClient,
			}

			if test.startFn {
				stopChan := make(chan struct{})
				c.Start(stopChan)
				go func() {
					time.Sleep(2 * time.Second)
					close(stopChan)
				}()
				time.Sleep(3 * time.Second)
			}
			if test.igCm != nil {
				if test.initIgCm {

					bytes, _ := json.Marshal(test.igList)
					if test.initIgCmErr {
						test.igCm.Data[entity.InstanceGropuCmDataKey] = "xx"
					} else {
						test.igCm.Data[entity.InstanceGropuCmDataKey] = string(bytes)
					}
				}
				cmInformer.Informer().GetIndexer().Add(test.igCm)
			}

			for _, placeholder := range test.placeholders {
				deploymentInformer.Informer().GetIndexer().Add(placeholder)
				c.client.Create(context.Background(), placeholder)
			}

			result, _ := c.Reconcile(context.Background(), reconcile.Request{NamespacedName: test.reconcileParam})

			if !reflect.DeepEqual(result, test.res) {
				t.Errorf("igController Reconcile get %+v , expect %+v ", result, test.res)
				return
			}
		})
	}

}

func TestSyncCceInstanceGroupTask(t *testing.T) {
	tests := []struct {
		name         string
		cceIgList    []entity.InstanceGroupCm
		cmIgList     []entity.InstanceGroupCm
		placeholders []*appv1.Deployment
		igCm         *corev1.ConfigMap
		cceErr       bool
		initIgCm     bool
		initIgCmErr  bool // cm 配置错误
		hasErr       bool
	}{
		{
			name: "节点组为空",
		},
		{
			name: "初始化创建节点组 配置文件",
			cceIgList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
					Buffer:            10,
				},
			},
		},

		{
			name: "新创建节点组",
			cceIgList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
					Buffer:            10,
				},
				// 新增
				{
					InstanceGroupId:   "cce-ig-f7ig6xax",
					InstanceGroupName: "zoneD-c2m8-cpu",
					Buffer:            10,
				},
			},
			cmIgList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
					Buffer:            10,
				},
				// 删除
				{
					InstanceGroupId:   "cce-ig-f7ig6xasda",
					InstanceGroupName: "zoneE-c2m8-cpu",
					Buffer:            10,
				},
			},
			igCm: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      entity.InstanceGroupCmName,
					Namespace: entity.InstanceGroupCmNamespace,
				},
				Data: map[string]string{},
			},
			placeholders: []*appv1.Deployment{
				&appv1.Deployment{
					ObjectMeta: metav1.ObjectMeta{
						Namespace: entity.InstanceGroupCmNamespace,
						Name:      "zonee-c2m8-cpu-placeholder",
					},
					Spec: appv1.DeploymentSpec{},
				},
			},
			initIgCm: true,
		},
		{
			name: "空节点组配置",
			cceIgList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
					Buffer:            10,
				},
			},
			igCm: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      entity.InstanceGroupCmName,
					Namespace: entity.InstanceGroupCmNamespace,
				},
				Data: map[string]string{},
			},
			hasErr: true,
		},

		{
			name: "节点组重命名，删除命名前的placeholder",
			cceIgList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
					Buffer:            10,
				},
			},
			cmIgList: []entity.InstanceGroupCm{
				{
					InstanceGroupId:   "cce-ig-f7ig6xak",
					InstanceGroupName: "zoneA-c2m8-cpu",
					Buffer:            10,
				},
			},
			igCm: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      entity.InstanceGroupCmName,
					Namespace: entity.InstanceGroupCmNamespace,
				},
				Data: map[string]string{},
			},
			placeholders: []*appv1.Deployment{
				{
					ObjectMeta: metav1.ObjectMeta{
						Namespace: entity.InstanceGroupCmNamespace,
						Name:      "zonea-c2m8-cpu-placeholder",
					},
					Spec: appv1.DeploymentSpec{},
				},
				{
					ObjectMeta: metav1.ObjectMeta{
						Namespace: entity.InstanceGroupCmNamespace,
						Name:      "zonee-c2m8-cpu-placeholder",
					},
					Spec: appv1.DeploymentSpec{},
				},
			},
			initIgCm: true,
		},
	}

	for i, test := range tests {
		if i != len(tests)-1 {
			// continue
		}
		t.Run(test.name, func(t *testing.T) {

			k8sclient := k8sfake.NewSimpleClientset()
			client := fake.NewFakeClient()

			informerFactory := informers.NewSharedInformerFactory(k8sclient, 0)
			cmInformer := informerFactory.Core().V1().ConfigMaps()

			deploymentInformer := informerFactory.Apps().V1().Deployments()
			podInformer := informerFactory.Core().V1().Pods()
			nodeInformer := informerFactory.Core().V1().Nodes()

			options := options.NewServerRunOptions()
			options.InstanceGroupOptions.DefaultSyncInstanceGroupInterval = 1 * time.Second
			cceClient := newfakeCceClient(test.cceErr, test.cceIgList,
				map[string]*ccev2.ListInstancesByInstanceGroupIDResponse{},
				make(map[string]*ccev2.ListTaskResp))

			c := &Controller{
				option:           options,
				cmLister:         cmInformer.Lister(),
				deploymentLister: deploymentInformer.Lister(),
				podLister:        podInformer.Lister(),
				nodeLister:       nodeInformer.Lister(),
				client:           client,
				cceClient:        cceClient,
			}

			if test.igCm != nil {
				if test.initIgCm {
					bytes, _ := json.Marshal(test.cmIgList)
					if test.initIgCmErr {
						test.igCm.Data[entity.InstanceGropuCmDataKey] = "xx"
					} else {
						test.igCm.Data[entity.InstanceGropuCmDataKey] = string(bytes)
					}
				}
				cmInformer.Informer().GetIndexer().Add(test.igCm)
				client.Create(context.Background(), test.igCm)
			}

			for _, placeholder := range test.placeholders {
				deploymentInformer.Informer().GetIndexer().Add(placeholder)
				client.Create(context.Background(), placeholder)
			}

			err := c.syncCceInstanceGroupTask()
			c.deleteUnusePlaceholder()
			getErr := (err != nil)
			if getErr != test.hasErr {
				t.Errorf("syncCceInstanceGroupTask getErr %+v ,expect err %+v ", getErr, test.hasErr)
			}
		})
	}
}
