package instancegroup

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	ccev2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	appv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/klog/v2"
)

// 定期同步cce节点组信息，更新k8s集群configMap
func (ssc *Controller) syncCceInstanceGroupTask() error {
	resp, err := ssc.cceClient.GetInstanceGroupList()
	if err != nil || resp == nil {
		klog.Errorf("instancegroupController syncCceInstanceGroupTask GetInstanceGroupList err %+v ", err)
		return err
	}

	cceInstanceGroup := ssc.buildCceInstanceGroupMap(resp)
	klog.V(5).Infof("instancegroupController syncCceInstanceGroupTask requestId %+v resp %+v ", resp.CommonResponse.RequestID, cceInstanceGroup)
	if len(cceInstanceGroup) == 0 {
		return nil
	}

	// 初始化创建或更新
	deleteInstanceGroupList, err := ssc.createOrUpdateInstanceGroupCm(cceInstanceGroup)
	if err != nil {
		return err
	}

	// 删除placeholder对应的deployment
	for _, deleteIg := range deleteInstanceGroupList {
		placeholderName := placeholderName(deleteIg.InstanceGroupName)
		placeholder, err := ssc.deploymentLister.Deployments(entity.InstanceGroupCmNamespace).Get(placeholderName)
		if err != nil {
			klog.Warningf("instancegroupController get placeholder name %+v err %+v ", placeholderName, err)
			continue
		}
		if err := ssc.client.Delete(context.TODO(), placeholder); err != nil {
			klog.Errorf("instancegroupController delete placeholder %+v err %+v ", placeholderName, err)
		}
		klog.Infof("instancegroupController delete placeholder %+v success ", placeholderName)
	}
	return nil
}

func (ssc *Controller) deleteUnusePlaceholder() error {
	igList, err := ssc.getInstanceGroupFromCm()
	if err != nil {
		return err
	}
	// 封装现存placeholder name
	currentPlaceholderNameMap := make(map[string]struct{})
	for _, ig := range igList {
		currentPlaceholderNameMap[placeholderName(ig.InstanceGroupName)] = struct{}{}
	}

	// 节点组重命名 -> 创建一个新的placeholder deployment，删除命名前的deployment
	allDeployment, err := ssc.deploymentLister.List(labels.Everything())
	if err != nil {
		return err
	}
	// 过滤 placeholder
	placeholderDeployment := make(map[string]*appv1.Deployment)
	for _, deployment := range allDeployment {
		if deployment.Namespace == entity.InstanceGroupCmNamespace && strings.Contains(deployment.Name, entity.PlaceholderSuffixStr) {
			placeholderDeployment[deployment.Name] = deployment
		}
	}

	for name, placeholder := range placeholderDeployment {
		if _, ok := currentPlaceholderNameMap[name]; !ok {
			err = ssc.client.Delete(context.Background(), placeholder)
			klog.V(3).Infof("instancegroupController deleteUnusePlaceholder %+v  , err = %+v ", placeholder.Name, err)
		}
	}
	return err
}

func (ssc *Controller) createOrUpdateInstanceGroupCm(cceInstanceGroup map[string]entity.InstanceGroupCm) ([]entity.InstanceGroupCm, error) {
	cm, err := ssc.cmLister.ConfigMaps(entity.InstanceGroupCmNamespace).Get(entity.InstanceGroupCmName)

	initInstanceGroupCm := func() error {
		// 初始化configMap 配置
		init := make([]entity.InstanceGroupCm, 0)
		for _, ig := range cceInstanceGroup {
			init = append(init, ig)
		}
		bytes, _ := json.MarshalIndent(init, "", "    ")

		klog.Infof("instancegroupController init instanceGroupCm %+v ", string(bytes))

		initCm := &corev1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Name:      entity.InstanceGroupCmName,
				Namespace: entity.InstanceGroupCmNamespace,
			},
			Data: map[string]string{
				entity.InstanceGropuCmDataKey: string(bytes),
			},
		}
		if err := ssc.client.Create(context.Background(), initCm); err != nil {
			klog.Errorf("instancegroupController create instancegroupCm err %+v ", err)
			return err
		}
		return nil
	}

	if err != nil && errors.IsNotFound(err) {
		return nil, initInstanceGroupCm()
	}
	if err != nil {
		klog.Errorf("instancegroupController get instancegroupCm err %+v ", err)
		return nil, err
	}

	// diff , create | update
	// if len(cm.Data) == 0 || cm.Data[entity.InstanceGropuCmDataKey] == "" {
	// 	return nil, initInstanceGroupCm()
	// }

	k8sConfigs := make([]entity.InstanceGroupCm, 0)
	if err := json.Unmarshal([]byte(cm.Data[entity.InstanceGropuCmDataKey]), &k8sConfigs); err != nil {
		// 解析失败，此处监控打点
		err = fmt.Errorf("instancegroupController Unmarshal instancegroupCm %+v err %+v ", cm.Data[entity.InstanceGropuCmDataKey], err)
		klog.Error(err)
		igParseErrCounter.WithLabelValues().Inc()
		return nil, err
	}
	k8sConfigMap := make(map[string]entity.InstanceGroupCm)
	for _, k8sConfig := range k8sConfigs {
		k8sConfigMap[k8sConfig.InstanceGroupId] = k8sConfig
	}

	deleteIgList := make([]entity.InstanceGroupCm, 0)
	createIgList := make([]entity.InstanceGroupCm, 0)

	for k8sIgKey, k8sIgVal := range k8sConfigMap {
		if _, ok := cceInstanceGroup[k8sIgKey]; !ok {
			deleteIgList = append(deleteIgList, k8sIgVal)
		}
	}

	for cceIgKey, cceIgVal := range cceInstanceGroup {
		if _, ok := k8sConfigMap[cceIgKey]; !ok {
			createIgList = append(createIgList, cceIgVal)
		}
	}

	if len(deleteIgList) == 0 && len(createIgList) == 0 {
		return nil, nil
	}

	klog.Infof("instancegroupController instanceGroup config change deleteIgList %+v , createIgList %+v ", deleteIgList, createIgList)

	for _, deleteIg := range deleteIgList {
		delete(k8sConfigMap, deleteIg.InstanceGroupId)
	}

	for _, createIgval := range createIgList {
		k8sConfigMap[createIgval.InstanceGroupId] = createIgval
	}

	newIgConfig := make([]entity.InstanceGroupCm, 0)
	for _, newIgVal := range k8sConfigMap {
		newIgConfig = append(newIgConfig, newIgVal)
	}

	bytes, _ := json.MarshalIndent(newIgConfig, "", "    ")

	copyCm := cm.DeepCopy()
	copyCm.Data[entity.InstanceGropuCmDataKey] = string(bytes)

	err = ssc.client.Update(context.Background(), copyCm)
	klog.Infof("instancegroupController update instanceGroup config %+v ,err %+v ", string(bytes), err)
	return deleteIgList, err
}

func (ssc *Controller) buildCceInstanceGroupMap(resp *ccev2.ListInstanceGroupResponse) map[string]entity.InstanceGroupCm {
	result := make(map[string]entity.InstanceGroupCm, 0)

	for _, ig := range resp.Page.List {
		if ig.Spec == nil {
			continue
		}
		result[ig.Spec.CCEInstanceGroupID] = entity.InstanceGroupCm{
			InstanceGroupName: ig.Spec.InstanceGroupName,
			InstanceGroupId:   ig.Spec.CCEInstanceGroupID,
			Buffer:            ssc.option.InstanceGroupOptions.DefaultBufferCount,
			MatchResources: []entity.InstanceGroupResource{
				// 默认创建一个空的
				{},
			},
		}
	}

	return result
}
