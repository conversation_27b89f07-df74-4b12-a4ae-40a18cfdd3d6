package instancegroup

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	appv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	appv1_listers "k8s.io/client-go/listers/apps/v1"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	toolscache "k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"sigs.k8s.io/controller-runtime/pkg/source"
)

var _ reconcile.Reconciler = &Controller{}

var (
	// 解析节点组配置失败打点
	igParseErrCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "instance_group_parse_err_total",
			Help:      "Parse InstanceGroup Config Err Count.",
		},
		[]string{},
	)
)

var defaultPlaceholderResourceRatio float64 = float64(2) / float64(3)

var (
	// 预留一定的buffer，buffer不能超过bci最小容器规格0.25c0.5m，因此默认预留0.2c0.4m
	reserveCPUResourceForPlaceholdr, _    = resource.ParseQuantity("0.2")
	reserveMemoryResourceForPlaceholdr, _ = resource.ParseQuantity("400Mi")
)

func init() {
	metrics.Registry.MustRegister(igParseErrCounter)
}

// New 初始化InstanceGroupController，此处无法写单测
func New(option *options.ServerRunOptions, mgr manager.Manager) (interface{}, error) {
	cache := mgr.GetCache()
	cmInformer, err := cache.GetInformer(context.TODO(), &corev1.ConfigMap{})
	if err != nil {
		klog.Errorf("instancegroupController get cmInformer err %+v", err)
		return nil, err
	}
	deploymentInformer, err := cache.GetInformer(context.TODO(), &appv1.Deployment{})
	if err != nil {
		klog.Errorf("instancegroupController get deploymentInformer err %+v", err)
		return nil, err
	}
	nodeInformer, err := cache.GetInformer(context.TODO(), &corev1.Node{})
	if err != nil {
		klog.Errorf("instancegroupController get nodeInformer err %+v", err)
		return nil, err
	}
	podInformer, err := cache.GetInformer(context.TODO(), &corev1.Pod{})
	if err != nil {
		klog.Errorf("instancegroupController get podInformer err %+v", err)
		return nil, err
	}

	cmLister := corev1_listers.NewConfigMapLister(cmInformer.(toolscache.SharedIndexInformer).GetIndexer())
	deploymentLister := appv1_listers.NewDeploymentLister(deploymentInformer.(toolscache.SharedIndexInformer).GetIndexer())
	nodeLister := corev1_listers.NewNodeLister(nodeInformer.(toolscache.SharedIndexInformer).GetIndexer())
	podLister := corev1_listers.NewPodLister(podInformer.(toolscache.SharedIndexInformer).GetIndexer())

	cceClient, err := util.NewCceClient(option.CceOptions)
	if err != nil {
		klog.Errorf("instancegroupController NewCceClient err %+v", err)
		return nil, err
	}

	c := &Controller{
		option:           option,
		cmLister:         cmLister,
		deploymentLister: deploymentLister,
		nodeLister:       nodeLister,
		podLister:        podLister,
		cceClient:        cceClient,
		client:           mgr.GetClient(),
	}

	controller, err := controller.New("instanceGroup-controller", mgr, controller.Options{
		Reconciler:              c,
		MaxConcurrentReconciles: option.InstanceGroupOptions.InstanceGroupControllerWorkers,
		RateLimiter:             util.DefaultControllerRateLimiter(option),
	})

	// instanceGroup cm config
	controller.Watch(source.Kind(mgr.GetCache(), &corev1.ConfigMap{}), &handler.EnqueueRequestForObject{}, predicate.Funcs{
		CreateFunc: func(ce event.CreateEvent) bool {
			cm := ce.Object.(*corev1.ConfigMap)
			if cm.Namespace == entity.InstanceGroupCmNamespace && cm.Name == entity.InstanceGroupCmName {
				return true
			}
			return false
		},
		UpdateFunc: func(ue event.UpdateEvent) bool {
			cm := ue.ObjectNew.(*corev1.ConfigMap)
			if cm.Namespace == entity.InstanceGroupCmNamespace && cm.Name == entity.InstanceGroupCmName {
				return true
			}
			return false
		},
	})

	// placeholder
	controller.Watch(source.Kind(mgr.GetCache(), &appv1.Deployment{}), &handler.EnqueueRequestForObject{}, predicate.Funcs{
		CreateFunc: func(ce event.CreateEvent) bool {
			deployment := ce.Object.(*appv1.Deployment)
			if deployment.Namespace == entity.InstanceGroupCmNamespace && strings.Contains(deployment.Name, entity.PlaceholderSuffixStr) {
				return true
			}
			return false
		},
		UpdateFunc: func(ue event.UpdateEvent) bool {
			deployment := ue.ObjectNew.(*appv1.Deployment)
			if deployment.Namespace == entity.InstanceGroupCmNamespace && strings.Contains(deployment.Name, entity.PlaceholderSuffixStr) {
				return true
			}
			return false
		},
	})

	return c, nil
}

// Controller 节点组controller实现
type Controller struct {
	sync.Mutex
	option *options.ServerRunOptions

	cmLister         corev1_listers.ConfigMapLister
	deploymentLister appv1_listers.DeploymentLister
	nodeLister       corev1_listers.NodeLister
	podLister        corev1_listers.PodLister

	// 与cce交互client
	cceClient util.CceClient

	// 与k8s apiserver 交互的client
	client client.Client
}

// Reconcile 节点组&placeholder sync逻辑
func (ssc *Controller) Reconcile(_ context.Context, request reconcile.Request) (res reconcile.Result, retErr error) {
	ssc.Lock()
	defer ssc.Unlock()

	igList, err := ssc.getInstanceGroupFromCm()

	if err != nil {
		return reconcile.Result{RequeueAfter: 5 * time.Second}, nil
	}
	// 是否是placeholder
	placeholder := strings.Contains(request.Name, entity.PlaceholderSuffixStr)
	if !placeholder {
		for _, ig := range igList {
			klog.Infof("instancegroupController sync instanceGroup%+v", request.Name)
			if err = ssc.syncInstanceGroup(ig); err != nil {
				return reconcile.Result{RequeueAfter: 5 * time.Second}, nil
			}
		}
		return reconcile.Result{}, nil
	}

	ig := getInstanceGroupBuffer(request.Name, igList)
	if ig == nil {
		klog.Errorf("instancegroupController placeholder %+v get instanceGroup empty", request.Name)
		return reconcile.Result{}, nil
	}
	// placeholder 被删除，在重新创建
	placeholderDeployment, err := ssc.getPlaceholderDeployment(request.Name)
	if placeholderDeployment == nil && err == nil {
		err = ssc.createPlaceholder(*ig)
		klog.Infof("instancegroupController create placeholder %+v for instanceGroup%+v", (*ig), request.Name)
		return reconcile.Result{}, err
	}

	if err != nil {
		klog.Errorf("instancegroupController create placeholder %+v for instanceGroup%+v , err: %+v", (*ig), request.Name, err)
		return reconcile.Result{}, err
	}
	if err = ssc.syncPlaceholderBuffer(placeholderDeployment, int32(ig.Buffer)); err != nil {
		klog.Errorf("instancegroupController sync placeholder %+v buffer %+v, err: %+v", placeholderDeployment.Name, ig.Buffer, err)
		return reconcile.Result{RequeueAfter: 5 * time.Second}, nil
	}
	return reconcile.Result{}, nil
}

func (ssc *Controller) syncPlaceholderBuffer(placeholder *appv1.Deployment, buffer int32) error {
	var err error
	// 调节placeholder buffer 数
	if placeholder.Spec.Replicas == nil || *placeholder.Spec.Replicas != buffer {
		copyPlaceholder := placeholder.DeepCopy()
		copyPlaceholder.Spec.Replicas = &buffer
		err = ssc.client.Update(context.TODO(), copyPlaceholder)
		klog.V(3).Infof("instancegroupController update placeholder %+v Replicas %+v err %+v ", placeholder.Name, buffer, err)
	}
	return err
}

// syncInstanceGroup 创建placeholder | 更新buffer 数量
func (ssc *Controller) syncInstanceGroup(instanceGroup entity.InstanceGroupCm) error {
	placeholder, err := ssc.getPlaceholderDeployment(placeholderName(instanceGroup.InstanceGroupName))
	// 有可能是deployment被删除，或者从informer get 失败
	if placeholder == nil && err == nil {
		// 创建placeholder deployment
		return ssc.createPlaceholder(instanceGroup)
	}
	if err != nil {
		return err
	}
	buffer := int32(instanceGroup.Buffer)
	return ssc.syncPlaceholderBuffer(placeholder, buffer)
}

func (ssc *Controller) getPlaceholderDeployment(name string) (*appv1.Deployment, error) {
	placeholder, err := ssc.deploymentLister.Deployments(entity.InstanceGroupCmNamespace).Get(name)
	if errors.IsNotFound(err) {
		klog.Infof("instancegroupController placeholder has been deleted %v", name)
		return nil, nil
	}
	if err != nil {
		klog.Errorf("instancegroupController deploymentLister get placeholder %s err %+v ", name, err)
		return nil, err
	}
	return placeholder, err
}

// getInstanceGroupFromCm 封装解析函数
func (ssc *Controller) getInstanceGroupFromCm() (result []entity.InstanceGroupCm, err error) {

	defer func() {
		if err != nil {
			igParseErrCounter.WithLabelValues().Inc()
		}
	}()

	instanceGroupCm, err := ssc.cmLister.ConfigMaps(entity.InstanceGroupCmNamespace).Get(entity.InstanceGroupCmName)
	if errors.IsNotFound(err) {
		klog.Infof("instancegroupController instanceGroupCm has been deleted")
		return result, nil
	}
	if err != nil {
		err = fmt.Errorf("instancegroupController instanceGroupCm get err %+v ", err)
		klog.Error(err)
		return result, err
	}
	if len(instanceGroupCm.Data) == 0 || instanceGroupCm.Data[entity.InstanceGropuCmDataKey] == "" {
		klog.Error("instancegroupController instanceGroupCm data is empty ")
		return result, nil
	}

	data := instanceGroupCm.Data[entity.InstanceGropuCmDataKey]
	klog.Infof("instancegroupController instanceGroupCm data %+v, result %+v ", data, result)
	if err = json.Unmarshal([]byte(data), &result); err != nil {
		klog.Errorf("instancegroupController instanceGroupCm data %+v Unmarshal err %+v, result %+v ", data, err, result)
	}
	return result, err
}

func (ssc *Controller) createPlaceholder(instanceGroup entity.InstanceGroupCm) error {

	containerResource := make(map[corev1.ResourceName]resource.Quantity, 0)
	nodes, err := ssc.getNodesByInstanceGroupID(instanceGroup.InstanceGroupId)
	if err != nil {
		klog.Errorf("instancegroupController getNodesByInstanceGroupID instance group %s err %+v", instanceGroup.InstanceGroupId, err)
		return err
	}

	// 同一节点组下的节点规格一致，获取首个ready节点
	findReadNode, node := findFirstReadyNode(nodes)
	if findReadNode {
		// 获取节点用与支持placeholder的可用资源
		cpu, memory, err := ssc.getAllocatableResourceForPlaceholderByNode(node)
		if err != nil {
			return err
		}
		if cpu.CmpInt64(0) == -1 {
			return fmt.Errorf("instancegroupController getAllocatableResourceForPlaceholder cpu is small than 0")
		}
		if memory.CmpInt64(0) == -1 {
			return fmt.Errorf("instancegroupController getAllocatableResourceForPlaceholder memory is small than 0")
		}
		containerResource = map[corev1.ResourceName]resource.Quantity{
			corev1.ResourceCPU:    *cpu,
			corev1.ResourceMemory: *memory,
		}
	} else {
		// 查询节点组详情
		detail, err := ssc.cceClient.GetInstanceGroupDetail(instanceGroup.InstanceGroupId)
		if err != nil {
			klog.Errorf("instancegroupController GetInstanceGroupDetail %s err %+v ", instanceGroup.InstanceGroupName, err)
			return err
		}

		if detail.InstanceGroup == nil || detail.InstanceGroup.Spec == nil {
			err = fmt.Errorf("instancegroupController query instanceGroup %s detail  is empty ", instanceGroup.InstanceGroupName)
			klog.Error(err)
			return err
		}

		igResource := detail.InstanceGroup.Spec.InstanceTemplate.InstanceResource

		resourceRatio := instanceGroup.ResourceRatio
		if resourceRatio == nil {
			resourceRatio = &defaultPlaceholderResourceRatio
		}
		cpu := float64(igResource.CPU) * (*resourceRatio)    // 单位为core
		memory := float64(igResource.MEM) * (*resourceRatio) // 单位为GB

		cpuStr := fmt.Sprintf("%f", cpu)
		memoryStr := fmt.Sprintf("%fG", memory)

		cpuQuantity, _ := resource.ParseQuantity(cpuStr)
		memoryQuantity, _ := resource.ParseQuantity(memoryStr)

		containerResource = map[corev1.ResourceName]resource.Quantity{
			corev1.ResourceCPU:    cpuQuantity,
			corev1.ResourceMemory: memoryQuantity,
		}
	}

	var terminationGracePeriodSeconds int64 = 0
	placeholderName := placeholderName(instanceGroup.InstanceGroupName)
	buffer := int32(instanceGroup.Buffer)
	placeholder := &appv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: entity.InstanceGroupCmNamespace,
			Name:      placeholderName,
		},
		Spec: appv1.DeploymentSpec{
			Replicas: &buffer,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app":                   "placeholder",
					entity.InstanceGroupKey: instanceGroup.InstanceGroupId,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app":                   "placeholder",
						entity.InstanceGroupKey: instanceGroup.InstanceGroupId,
					},
				},
				Spec: corev1.PodSpec{
					// 适配2.1 使用主机网络
					HostNetwork: true,
					// 亲和性相关设置
					Affinity: &corev1.Affinity{
						NodeAffinity: &corev1.NodeAffinity{
							RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
								NodeSelectorTerms: []corev1.NodeSelectorTerm{
									{
										MatchExpressions: []corev1.NodeSelectorRequirement{
											{
												Key:      entity.InstanceGroupKey,
												Operator: corev1.NodeSelectorOpIn,
												Values:   []string{instanceGroup.InstanceGroupId},
											},
											{
												Key:      entity.BufferLabelKey,
												Operator: corev1.NodeSelectorOpIn,
												Values:   []string{entity.BufferLabelValue},
											},
										},
									},
								},
							},
						},
					},
					// container相关设置
					Containers: []corev1.Container{
						{
							Image:           ssc.option.InstanceGroupOptions.PlaceholderImage,
							ImagePullPolicy: corev1.PullIfNotPresent,
							Name:            "placeholder",
							Resources: corev1.ResourceRequirements{
								Requests: containerResource,
								Limits:   containerResource,
							},
						},
					},

					DNSPolicy:                     corev1.DNSClusterFirst,
					PriorityClassName:             "low-priority",
					RestartPolicy:                 corev1.RestartPolicyAlways,
					TerminationGracePeriodSeconds: &terminationGracePeriodSeconds,
				},
			},
		},
	}

	err = ssc.client.Create(context.Background(), placeholder)
	klog.V(3).Infof("instancegroupController create placeholder %+v ,err %+v ", instanceGroup.InstanceGroupName, err)
	return err
}

// Start 开启sync 节点组定时任务
func (ssc *Controller) Start(stopChan <-chan struct{}) error {
	go func() {
		interval := ssc.option.InstanceGroupOptions.DefaultSyncInstanceGroupInterval
		ticker := time.NewTicker(interval)

		time.Sleep(interval)

		for {
			select {
			case <-stopChan:
				return
			case <-ticker.C:
				ssc.syncCceInstanceGroupTask()
				ssc.deleteUnusePlaceholder()
				ssc.syncPlaceholderSize()
			}
		}
	}()
	return nil
}

// 更新Placeholder的规格（资源），更新CPU资源即可
func (ssc *Controller) syncPlaceholderSize() {
	placeholderDeployments, err := ssc.getAllPlaceholderDeployment()
	if err != nil {
		klog.Errorf("instancegroupController getAllPlaceholderDeployment err %+v ", err)
		return
	}

	needUpdateDeployments := make([]*appv1.Deployment, 0)
	// 获取需要更新placeholder规格的deployment
	for _, placeholderDeployment := range placeholderDeployments {
		// 获取placeholder deployment指定的节点组ID
		instanceGroupIDs := entity.GetInstanceGroupIDByNodeAffinity(placeholderDeployment.Spec.Template.Spec.Affinity)
		if len(instanceGroupIDs) == 0 {
			klog.V(3).Infof("instancegroupController deployment %s/%s GetInstanceGroupIDByNodeAffinity has no instance group id",
				placeholderDeployment.Namespace, placeholderDeployment.Name)
			continue
		}
		// 如果存在多个节点组ID，获取首个节点组ID即可
		instanceGroupID := instanceGroupIDs[0]
		nodes, err := ssc.getNodesByInstanceGroupID(instanceGroupID)
		if err != nil {
			klog.Errorf("instancegroupController deployment %s/%s getNodesByInstanceGroupID instance group %s err %+v ",
				placeholderDeployment.Namespace, placeholderDeployment.Name, instanceGroupID, err)
			continue
		}
		if len(nodes) == 0 {
			klog.V(3).Infof("instancegroupController deployment %s/%s getNodesByInstanceGroupID instance group %s has no node",
				placeholderDeployment.Namespace, placeholderDeployment.Name, instanceGroupID)
			continue
		}
		// 同一节点组下的节点规格一致，获取首个ready节点
		findReadNode, node := findFirstReadyNode(nodes)
		if !findReadNode {
			klog.V(3).Infof("instancegroupController deployment %s/%s findFirstReadyNode has no ready node",
				placeholderDeployment.Namespace, placeholderDeployment.Name, instanceGroupID)
			continue
		}
		// 获取节点用与支持placeholder的可用资源
		cpu, memory, err := ssc.getAllocatableResourceForPlaceholderByNode(node)
		if err != nil {
			klog.Errorf("instancegroupController deployment %s/%s node %s getAllocatableResourceForPlaceholderByNode err %+v",
				placeholderDeployment.Namespace, placeholderDeployment.Name, node.Name, err)
			continue
		}
		if cpu.CmpInt64(0) == -1 || memory.CmpInt64(0) == -1 {
			klog.V(3).Infof("instancegroupController deployment %s/%s node %s getAllocatableResourceForPlaceholder cpu: %s, memory: %s",
				placeholderDeployment.Namespace, placeholderDeployment.Name, node.Name, cpu.String(), memory.String())
			continue
		}
		// placeholder的规格是否需要更新
		if cpu.Cmp(*placeholderDeployment.Spec.Template.Spec.Containers[0].Resources.Requests.Cpu()) != 0 ||
			memory.Cmp(*placeholderDeployment.Spec.Template.Spec.Containers[0].Resources.Requests.Memory()) != 0 {
			klog.V(3).Infof("instancegroupController syncPlaceholderSize deployment %s/%s, origin cpu: %s, updated cpu: %s, origin memory: %s, updated memory: %s",
				placeholderDeployment.Namespace, placeholderDeployment.Name, placeholderDeployment.Spec.Template.Spec.Containers[0].Resources.Requests.Cpu().String(),
				cpu.String(), placeholderDeployment.Spec.Template.Spec.Containers[0].Resources.Requests.Memory().String(), memory.String())
			needUpdateDeployment := placeholderDeployment.DeepCopy()
			containerResource := map[corev1.ResourceName]resource.Quantity{
				corev1.ResourceCPU:    *cpu,
				corev1.ResourceMemory: *memory,
			}
			needUpdateDeployment.Spec.Template.Spec.Containers[0].Resources.Requests = containerResource
			needUpdateDeployment.Spec.Template.Spec.Containers[0].Resources.Limits = containerResource
			needUpdateDeployments = append(needUpdateDeployments, needUpdateDeployment)
		}
	}
	// 删除重建placeholder deployment的规格
	for _, deployment := range needUpdateDeployments {
		// 删除即可，重建依赖Reconcile逻辑
		err := ssc.client.Delete(context.TODO(), deployment)
		if err != nil {
			klog.Errorf("instancegroupController syncPlaceholderSize delete placeholder deployment %s err %+v ", deployment.Name, err)
			continue
		}
		klog.V(3).Infof("instancegroupController syncPlaceholderSize delete placeholder deployment %s/%s success", deployment.Namespace, deployment.Name)
	}
	return
}

func (ssc *Controller) getAllDeamonSetPodByNode(node *corev1.Node) (deamonSetPods []*corev1.Pod, err error) {
	pods, err := ssc.podLister.List(labels.Everything())
	if err != nil {
		return
	}
	for _, pod := range pods {
		if pod.Spec.NodeName == node.Name && entity.IsDaemonSetPod(pod) {
			klog.V(3).Infof("instancegroupController deamonset pod name %s node name %s", pod.Name, node.Name)
			deamonSetPods = append(deamonSetPods, pod)
		}
	}
	return
}

func sumDaemonSetPodRequestResource(daemonSetPods []*corev1.Pod) (cpu, memory resource.Quantity) {
	cpu, _ = resource.ParseQuantity("0")
	memory, _ = resource.ParseQuantity("0Gi")
	for _, pod := range daemonSetPods {
		podRequestCPU, _ := resource.ParseQuantity("0")
		podRequestMemory, _ := resource.ParseQuantity("0Gi")
		// containers
		containers := pod.Spec.Containers
		for i := range containers {
			if containers[i].Resources.Requests.Cpu() != nil {
				podRequestCPU.Add(*containers[i].Resources.Requests.Cpu())
			}
			if containers[i].Resources.Requests.Memory() != nil {
				podRequestMemory.Add(*containers[i].Resources.Requests.Memory())
			}
		}
		// initContainers
		initContainers := pod.Spec.InitContainers
		for i := range initContainers {
			if initContainers[i].Resources.Requests.Cpu() != nil {
				if podRequestCPU.Cmp(*initContainers[i].Resources.Requests.Cpu()) == -1 {
					podRequestCPU = *initContainers[i].Resources.Requests.Cpu()
				}
			}
			if initContainers[i].Resources.Requests.Memory() != nil {
				if podRequestMemory.Cmp(*initContainers[i].Resources.Requests.Memory()) == -1 {
					podRequestMemory = *initContainers[i].Resources.Requests.Memory()
				}
			}
		}
		cpu.Add(podRequestCPU)
		memory.Add(podRequestMemory)
	}
	return
}

func findFirstReadyNode(nodes []*corev1.Node) (ret bool, node *corev1.Node) {
	// 同一节点组下的节点规格一致，获取首个ready节点
	for _, item := range nodes {
		for i := range item.Status.Conditions {
			if item.Status.Conditions[i].Type == corev1.NodeReady && item.Status.Conditions[i].Status == corev1.ConditionTrue {
				return true, item
			}
		}
	}
	return false, nil
}

func (ssc *Controller) getNodesByInstanceGroupID(instanceGroupID string) (nodes []*corev1.Node, err error) {
	// 根据节点组ID，获取相关节点
	selector := labels.SelectorFromSet(labels.Set(map[string]string{entity.InstanceGroupKey: instanceGroupID}))
	nodes, err = ssc.nodeLister.List(selector)
	return
}

// 获取节点用与支持placeholder的可用资源
// 计算方式：placeholerPodRequestCpu = node.Allocatable.cpu - sum(daemonSetRequestCPU) - reservedCPU
func (ssc *Controller) getAllocatableResourceForPlaceholderByNode(node *corev1.Node) (cpu, memory *resource.Quantity, err error) {
	// 获取node上的deamonset pod
	daemonSetPods, err := ssc.getAllDeamonSetPodByNode(node)
	if err != nil {
		return
	}
	// 计算daemonset pod请求的cpu/memory
	daemonSetRequestCPU, daemonSetRequestMemory := sumDaemonSetPodRequestResource(daemonSetPods)
	klog.V(3).Infof("instancegroupController getAllocatableResourceForPlaceholderByNode node %s, daemonSetRequestCPU: %s, daemonSetRequestMemory: %s",
		node.Name, daemonSetRequestCPU.String(), daemonSetRequestMemory.String())

	// 除去daemonSet和预留占据的资源，node可分配给placeholer的资源
	cpu, memory = ssc.getAllocatableResourceForPlaceholder(node, &daemonSetRequestCPU, &daemonSetRequestMemory)
	return
}

func (ssc *Controller) getAllocatableResourceForPlaceholder(node *corev1.Node, daemonSetRequestCPU,
	daemonSetRequestMemory *resource.Quantity) (cpu, memory *resource.Quantity) {
	// 除去daemonSet和预留占据的资源，node可分配的资源
	// cpu
	nodeAllocatableCPU := node.Status.Allocatable.Cpu().DeepCopy()
	nodeAllocatableCPU.Sub(*daemonSetRequestCPU)
	nodeAllocatableCPU.Sub(reserveCPUResourceForPlaceholdr)
	// memory
	nodeAllocatableMemory := node.Status.Allocatable.Memory().DeepCopy()
	nodeAllocatableMemory.Sub(*daemonSetRequestMemory)
	nodeAllocatableMemory.Sub(reserveMemoryResourceForPlaceholdr)
	return &nodeAllocatableCPU, &nodeAllocatableMemory
}

func (ssc *Controller) getAllPlaceholderDeployment() (placeholderDeployments []*appv1.Deployment, err error) {
	// 获取所有的deployment
	allDeployment, err := ssc.deploymentLister.List(labels.Everything())
	if err != nil {
		return
	}
	// 获取placeholder deployment
	placeholderDeployments = make([]*appv1.Deployment, 0)
	for _, deployment := range allDeployment {
		if deployment.Namespace == entity.InstanceGroupCmNamespace && strings.Contains(deployment.Name, entity.PlaceholderSuffixStr) {
			placeholderDeployments = append(placeholderDeployments, deployment)
		}
	}
	return
}

func placeholderName(instanceGroupName string) string {
	return strings.ToLower(instanceGroupName) + "-" + entity.PlaceholderSuffixStr
}

func getInstanceGroupBuffer(placeholderName string, igList []entity.InstanceGroupCm) *entity.InstanceGroupCm {
	for _, ig := range igList {
		if strings.Contains(placeholderName, strings.ToLower(ig.InstanceGroupName)) {
			return &ig
		}
	}
	return nil
}
