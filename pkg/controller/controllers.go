package controller

import (
	"context"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/eni"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/event"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/instancegroup"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/network"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/node"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/nodegc"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/ops"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/sidecar"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime/inject"

	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/manager"
)

var (
	controllerAddFuncs []runtime.NewController
	controllers        []interface{}
)

func init() {
	controllerAddFuncs = append(controllerAddFuncs, instancegroup.New)
	controllerAddFuncs = append(controllerAddFuncs, node.New)
	controllerAddFuncs = append(controllerAddFuncs, nodegc.New)
	controllerAddFuncs = append(controllerAddFuncs, monitorsync.New)
	controllerAddFuncs = append(controllerAddFuncs, sidecar.New)
	controllerAddFuncs = append(controllerAddFuncs, network.NewEIPController)
	controllerAddFuncs = append(controllerAddFuncs, eni.New)
	controllerAddFuncs = append(controllerAddFuncs, event.New)
	controllerAddFuncs = append(controllerAddFuncs, ops.New)
	controllerAddFuncs = append(controllerAddFuncs, instancegroup.NewTimedSacleUPController)
}

func SetupWithManager(serverOptions *options.ServerRunOptions, m manager.Manager) error {
	for _, f := range controllerAddFuncs {
		controller, err := f(serverOptions, m)
		if err != nil {
			if kindMatchErr, ok := err.(*meta.NoKindMatchError); ok {
				klog.Infof("CRD %v is not installed, its controller will perform noops!", kindMatchErr.GroupKind)
				continue
			}
			return err
		}
		controllers = append(controllers, controller)
	}
	return nil
}

func Start(ctx context.Context) {
	for _, controller := range controllers {
		if c, ok := controller.(inject.StartFuncInjector); ok {
			go func() {
				if err := c.Start(ctx.Done()); err != nil {
					klog.Errorf("start controller %+v err %+v ", c, err)
				}
			}()
		}
	}
}
