package ops

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/sts"
	ops_task_crd "icode.baidu.com/baidu/bci2/bci-ops-agent/api/bciops/v1"
	ops_task_lister "icode.baidu.com/baidu/bci2/bci-ops-agent/pkg/generated/listers/bciops/v1"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	"k8s.io/apimachinery/pkg/api/errors"
	toolscache "k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"sigs.k8s.io/controller-runtime/pkg/source"
)

const (
	waitStorageInfoAnnotationKey = "bci_internal_wait_storage_info"
)

// Controller 此controller用于用户临时ak sk 申请，并有相关缓存机制
type Controller struct {
	client        client.Client
	opsTaskLister ops_task_lister.BciOpsTaskLister
	options       *options.ServerRunOptions
	stsClient     *sts.Client
}

func New(options *options.ServerRunOptions, mgr manager.Manager) (interface{}, error) {
	cache := mgr.GetCache()
	opsTaskInformer, err := cache.GetInformer(context.TODO(), &ops_task_crd.BciOpsTask{})
	if err != nil {
		klog.Errorf("opsTokenController get opsTaskInformer err %+v ", err)
		return nil, err
	}

	opsTaskLister := ops_task_lister.NewBciOpsTaskLister(opsTaskInformer.(toolscache.SharedIndexInformer).GetIndexer())

	c := &Controller{
		opsTaskLister: opsTaskLister,
		client:        mgr.GetClient(),
		options:       options,
	}

	controller, err := controller.New("ops-token-controller", mgr, controller.Options{
		Reconciler:              c,
		MaxConcurrentReconciles: 3,
		RateLimiter:             util.DefaultControllerRateLimiter(options),
	})

	if err != nil {
		klog.Errorf("opsTokenController new Controller err %+v ", err)
		return nil, err
	}

	err = controller.Watch(source.Kind(mgr.GetCache(), &ops_task_crd.BciOpsTask{}), &handler.EnqueueRequestForObject{}, predicate.Funcs{
		CreateFunc: func(ce event.CreateEvent) bool {
			return false
		},
		UpdateFunc: func(ue event.UpdateEvent) bool {
			opsTask := ue.ObjectNew.(*ops_task_crd.BciOpsTask)
			return shouldHandleEvent(opsTask)
		},
		DeleteFunc: func(event.DeleteEvent) bool {
			return false
		},
		GenericFunc: func(event.GenericEvent) bool {
			return false
		},
	})
	if err != nil {
		klog.Errorf("opsTokenController watch BciOpsTask err %+v ", err)
		return nil, err
	}
	c.initStsClient()
	return nil, nil
}

func shouldHandleEvent(opsTask *ops_task_crd.BciOpsTask) bool {
	if opsTask == nil {
		return false
	}
	for k := range opsTask.Annotations {
		if k == waitStorageInfoAnnotationKey {
			return true
		}
	}
	return false
}

// Reconcile 获取用户临时 ak/sk token
func (c *Controller) Reconcile(_ context.Context, request reconcile.Request) (res reconcile.Result, retErr error) {
	klog.V(3).Infof("opsTokenController Reconcile request %+v ", request)
	opsTask, err := c.opsTaskLister.BciOpsTasks(request.Namespace).Get(request.Name)
	if err != nil {
		klog.Warningf("opsTokenController opsTaskLister get opsTask <%s/%s> err %+v", request.Namespace, request.Name, err)
		return
	}

	accountID := opsTask.Spec.AccountID
	credential, err := c.stsClient.GetCredential(context.Background(), accountID)

	if err != nil {
		klog.Errorf("opsTokenController opsTask %s GetCredential err %+v", opsTask.Name, err)
		return
	}

	param := opsTask.Spec.StorageParam
	paramMap := make(map[string]string)
	err = json.Unmarshal([]byte(param), &paramMap)

	if err != nil {
		klog.Errorf("opsTokenController Unmarshal opsTask %s StorageParam %s err", opsTask.Name, param)
		return
	}

	akskToken := make([]string, 3)
	akskToken[0] = credential.AccessKeyID
	akskToken[1] = credential.SecretAccessKey
	akskToken[2] = credential.SessionToken

	bytes, _ := json.Marshal(akskToken)

	encodeStr := base64.StdEncoding.EncodeToString(bytes)
	paramMap["storageConfig"] = encodeStr

	bytes, _ = json.Marshal(paramMap)

	// 更新crd
	err = c.updateCrdStorageConfig(opsTask, string(bytes))
	if err != nil {
		klog.Errorf("opsTokenController update opsTask %s StorageConfig % err %+v ", opsTask.Name, string(bytes), err)
	}
	return
}

func (c *Controller) updateCrdStorageConfig(task *ops_task_crd.BciOpsTask, storageParam string) error {

	return retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		opsTask, err := c.opsTaskLister.BciOpsTasks(task.Namespace).Get(task.Name)
		if err != nil {
			if errors.IsNotFound(err) {
				return nil
			}
			return err
		}
		copyTask := opsTask.DeepCopy()
		copyTask.Spec.StorageParam = storageParam

		klog.V(3).Infof("opsTokenController opsTask <%s/%s> storageParam %+v ", opsTask.Namespace, opsTask.Name, storageParam)

		err = c.client.Update(context.Background(), copyTask)
		return err
	})
}

func (c *Controller) initStsClient() {
	stsClient := sts.NewClient(context.Background(), &bce.Config{
		Endpoint: sts.Endpoints[c.options.NetworkOptions.Region],
		Checksum: true,
		Timeout:  10 * time.Second,
		Region:   c.options.NetworkOptions.Region,
	}, &bce.Config{
		Endpoint: iam.Endpoints[c.options.NetworkOptions.Region],
		Checksum: true,
		Timeout:  10 * time.Second,
		Region:   c.options.NetworkOptions.Region,
	}, c.options.NetworkOptions.IamRoleName, c.options.NetworkOptions.IamUserName, c.options.NetworkOptions.IamConsolePassword)

	c.stsClient = stsClient
}
