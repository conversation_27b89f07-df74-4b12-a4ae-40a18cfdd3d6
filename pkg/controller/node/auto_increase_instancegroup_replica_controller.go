package node

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
)

const (
	lastIncreaseTimeKey = "bci_internal_last-increase-time"
)

var (
	// pending pod数量统计信息
	pendingPodCountGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Subsystem: "bci",
			Name:      "pending_pod_count",
			Help:      "The InstanceGroup Bci Pending Pod Count.",
		},
		[]string{"instance_group"},
	)
)

func init() {
	metrics.Registry.MustRegister(pendingPodCountGauge)
}

func (ssc *Controller) increaseInstanceGroupMaxNodeCountTask() map[string]int {
	increaseNodeCount := make(map[string]int)
	instanceGroupPods, daemonsetPods, err := ssc.computePendingPod()
	if err != nil {
		return increaseNodeCount
	}
	ssc.increaseInstanceGroupMaxNodeCount(instanceGroupPods, daemonsetPods, increaseNodeCount)
	return increaseNodeCount
}

func (ssc *Controller) computePendingPod() (map[string][]*corev1.Pod, map[string][]*corev1.Pod, error) {
	// key 为instanceGroupId
	instanceGroupPods := make(map[string][]*corev1.Pod)
	// key 为nodeName
	daemonsetPods := make(map[string][]*corev1.Pod)

	// prometheus 监控打点pod
	monitorPods := make(map[string][]*corev1.Pod)

	allPods, err := ssc.podLister.List(labels.Everything())
	if err != nil {
		klog.Errorf("nodeController computePendingPod list allPod err %+v ", err)
		return instanceGroupPods, daemonsetPods, err
	}

	type pendingPod struct {
		Namespace   string
		Name        string
		PendingTime time.Duration
	}

	logPods := make([]pendingPod, 0)

	for _, pod := range allPods {
		// 封装daemonset pod
		if entity.IsDaemonsetRunningPod(pod) {
			if _, ok := daemonsetPods[pod.Spec.NodeName]; !ok {
				daemonsetPods[pod.Spec.NodeName] = make([]*corev1.Pod, 0)
			}
			daemonsetPods[pod.Spec.NodeName] = append(daemonsetPods[pod.Spec.NodeName], pod)
		}
		if !ssc.shouldHandlePod(pod) {
			continue
		}

		instanceGroupIDs := []string{}
		switch podType := entity.GetBciPodType(pod); podType {
		case entity.BciPodByNodeSelector:
			instanceGroupIDs = append(instanceGroupIDs, pod.Spec.NodeSelector[entity.InstanceGroupKey])
		case entity.BciPodByNodeAffinity:
			instanceGroupIDs = append(instanceGroupIDs, entity.GetInstanceGroupIDFromNodeAffinity(pod)...)
		default:
			continue
		}

		changeIgReplica, monitorPendingPod := ssc.shouldDealPendingPod(pod)
		if monitorPendingPod {
			for _, instanceGroupID := range instanceGroupIDs {
				monitorPods[instanceGroupID] = append(monitorPods[instanceGroupID], pod)
			}
		}
		if !changeIgReplica {
			continue
		}

		logPods = append(logPods, pendingPod{
			Namespace:   pod.Namespace,
			Name:        pod.Name,
			PendingTime: time.Now().Sub(pod.CreationTimestamp.Time),
		})

		for _, instainstanceGroupID := range instanceGroupIDs {
			igPod, ok := instanceGroupPods[instainstanceGroupID]
			if !ok {
				igPod = make([]*corev1.Pod, 0)
				instanceGroupPods[instainstanceGroupID] = igPod
			}
			instanceGroupPods[instainstanceGroupID] = append(instanceGroupPods[instainstanceGroupID], pod)
		}
	}

	// log pending pods
	bytes, _ := json.Marshal(logPods)
	klog.V(3).Infof("nodeController monitorPendingPod info %+v ", string(bytes))

	// 打点前先重置一下
	pendingPodCountGauge.Reset()
	for ig, pendingPods := range monitorPods {
		pendingPodCountGauge.WithLabelValues(ig).Set(float64(len(pendingPods)))
	}

	return instanceGroupPods, daemonsetPods, nil
}

func (ssc *Controller) increaseInstanceGroupMaxNodeCount(instanceGroupPendingPods map[string][]*corev1.Pod,
	daemonsetPods map[string][]*corev1.Pod, increaseNodeCount map[string]int) {
	if !ssc.options.NodeOptions.EnableAutoChangeInstanceGroupReplicas {
		return
	}

	// 执行预调度逻辑，按照节点组分组
	for ig, pods := range instanceGroupPendingPods {
		nodeCount := 0
		selector := labels.SelectorFromSet(labels.Set(map[string]string{entity.InstanceGroupKey: ig}))
		nodes, err := ssc.nodeLister.List(selector)
		if err != nil {
			klog.Errorf("nodeController monitorPendingPod list instanceGroup %s node err %+v ", ig, err)
			continue
		}
		if len(nodes) == 0 {
			nodeCount, err = computeAddNodeCount(nil, pods, daemonsetPods)
		} else {
			nodeCount, err = computeAddNodeCount(nodes[0], pods, daemonsetPods)
		}
		if err != nil {
			continue
		}
		finalIncreaseNodeCount, err := ssc.doIncreaseIgMaxNodeCount(ig, nodeCount, pods)
		if err != nil {
			klog.Errorf("nodeController doIncreaseIgMaxNodeCount instanceGroup %s node err %+v ", ig, err)
			continue
		}
		increaseNodeCount[ig] = finalIncreaseNodeCount
	}
	return
}

func (ssc *Controller) doIncreaseIgMaxNodeCount(instanceGroup string, nodeCount int, pendingPods []*corev1.Pod) (int, error) {

	igDetail, err := ssc.cceClient.GetInstanceGroupDetail(instanceGroup)
	if err != nil {
		klog.Errorf("nodeController query instanceGroup %s detail err %+v ", instanceGroup, err)
		return 0, err
	}

	if igDetail.InstanceGroup == nil || igDetail.InstanceGroup.Spec == nil ||
		igDetail.InstanceGroup.Spec.ClusterAutoscalerSpec == nil {
		err = fmt.Errorf("nodeController query instanceGroup %s detail ClusterAutoscalerSpec is empty ", instanceGroup)
		klog.Error(err)
		return 0, err
	}

	if !igDetail.InstanceGroup.Spec.ClusterAutoscalerSpec.Enabled {
		err = fmt.Errorf("nodeController instanceGroup %s ClusterAutoscaler is stopped ", instanceGroup)
		klog.Warning(err)
		return 0, err
	}

	currentMaxReplicaCount := igDetail.InstanceGroup.Spec.ClusterAutoscalerSpec.MaxReplicas // ca max node配置
	currentReplicaCount := igDetail.InstanceGroup.Spec.Replicas                             // 当前期望运行node数
	maxReplicas := ssc.options.NodeOptions.AutoChangeInstanceGroupMaxReplicas

	// 取max(当前副本数,ca最大副本数)
	expectMaxReplicaCount := currentMaxReplicaCount
	if currentReplicaCount > currentMaxReplicaCount {
		expectMaxReplicaCount = currentReplicaCount
	}

	klog.V(3).Infof("nodeController instanceGroup %s IncreaseReplicas: %+v ,currentReplicaCount %+v ,currentMaxReplicaCount: %+v, len(pendingPods) %+v ",
		instanceGroup, nodeCount, currentReplicaCount, expectMaxReplicaCount, len(pendingPods))
	finalMaxReplicaCount := expectMaxReplicaCount + nodeCount

	// 只有当 当前节点数+增加node数 > 节点组最大副本数，才修改节点组最大节点数(每次修改需要重启ca组件)
	if currentReplicaCount+nodeCount > expectMaxReplicaCount {

		if finalMaxReplicaCount > maxReplicas {
			klog.Warningf("instanceGroup %s currentMaxReplicas(%+v) + IncreaseNodeCount(%+v) > maxReplicas %+v ",
				instanceGroup, expectMaxReplicaCount, nodeCount, maxReplicas)
			// 超过最大副本数时，设置为最大副本数
			finalMaxReplicaCount = maxReplicas
		}
		changeResp, err := ssc.cceClient.ChangeInstanceGroupAutoscalerConfig(true, instanceGroup, finalMaxReplicaCount)
		klog.V(3).Infof("nodeController ChangeInstanceGroupMaxReplicas instanceGroup %s resp %+v err %+v ", instanceGroup, changeResp, err)
		if err != nil {
			return 0, err
		}
	}

	patchPodAnnotation := func(pod *corev1.Pod) {
		copyPod := pod.DeepCopy()

		podAnnotations := copyPod.ObjectMeta.Annotations
		if podAnnotations == nil {
			podAnnotations = make(map[string]string)
		}
		podAnnotations[lastIncreaseTimeKey] = strconv.FormatInt(time.Now().Unix(), 10)

		metaData := map[string]map[string]string{
			"annotations": podAnnotations,
		}
		patchData, _ := json.Marshal(&map[string]interface{}{
			"metadata": &metaData,
		})

		klog.V(3).Infof("nodeController patch pod <%s/%s> Annotation %s ", pod.Namespace, pod.Name, string(patchData))
		var err error
		for i := 0; i < 3; i++ {
			err = ssc.client.Patch(context.TODO(), copyPod, client.RawPatch(types.StrategicMergePatchType, []byte(patchData)))
			if err == nil {
				break
			}
			klog.Errorf("nodeController patch pod %s lastIncreaseTimeKey index %+v , err %+v ", copyPod.Name, i, err)
		}
	}
	for _, pod := range pendingPods {
		patchPodAnnotation(pod)
	}
	return finalMaxReplicaCount - expectMaxReplicaCount, nil
}

func (ssc *Controller) shouldDealPendingPod(pod *corev1.Pod) (changeIgReplica bool, monitorPendingPod bool) {
	// 从创建时间到当前，pending 超过30s
	duration, _ := time.ParseDuration("30s")
	if !pod.CreationTimestamp.Add(duration).Before(time.Now()) {
		return false, false
	}
	if len(pod.Annotations) == 0 {
		return true, true
	}

	lastTime, ok := pod.Annotations[lastIncreaseTimeKey]
	if !ok {
		return true, true
	}

	lastTimeIntVal, err := strconv.ParseInt(lastTime, 10, 64)
	if err != nil {
		return true, true
	}
	// 5分钟以后还是pending，则处理
	// TODO 此时间后续可配置
	duration5m, _ := time.ParseDuration("5m")
	covertTime := time.Unix(lastTimeIntVal, 0)
	if time.Now().After(covertTime.Add(duration5m)) {
		return true, true
	}
	return false, true
}

func computeAddNodeCount(node *corev1.Node, pods []*corev1.Pod, daemonsetPod map[string][]*corev1.Pod) (int, error) {
	// 节点组下没有node，先创建一个
	if node == nil {
		return 1, nil
	}

	nodeResource := computeNodeResource(node, daemonsetPod)
	// 需要减去daemonset pod 的数量

	addResource := entity.EmptyResource()
	// 需要新添加的node数量
	needNodeCount := 1
	for _, pod := range pods {
		podResource := computePodResource(pod)
		// pod 申请的资源(任意一个资源) > node 资源
		if podResource.GreatAny(nodeResource) {
			err := fmt.Errorf("nodeController computeAddNodeCount pod <%s/%s> resource %+v GreatAny node resource %+v ",
				pod.Namespace, pod.Name, podResource, nodeResource)
			klog.Error(err)
			return 0, err
		}
		addResource.Add(podResource)
		if addResource.GreatAny(nodeResource) {
			needNodeCount++
			addResource = entity.EmptyResource()
			addResource.Add(podResource)
		}
	}
	return needNodeCount, nil
}

// 计算node 可用resource，排除daemonset pod占用
func computeNodeResource(node *corev1.Node, daemonsetPod map[string][]*corev1.Pod) *entity.Resource {
	nodeResource := entity.NewResource(node.Status.Allocatable)

	daemonsetPods, ok := daemonsetPod[node.Name]
	if !ok {
		return nodeResource
	}
	daemonsetResource := entity.EmptyResource()

	for _, daemonsetPod := range daemonsetPods {
		daemonsetResource.Add(computePodResource(daemonsetPod))
	}

	return nodeResource.Sub(daemonsetResource)
}

func computePodResource(pod *corev1.Pod) *entity.Resource {
	podResource := entity.EmptyResource()
	for _, container := range pod.Spec.Containers {
		podResource.Add(entity.NewResource(container.Resources.Requests))
	}
	return podResource
}
