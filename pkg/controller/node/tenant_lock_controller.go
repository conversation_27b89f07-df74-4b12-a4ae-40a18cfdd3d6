package node

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

var (
	// 节点组锁node数量打点统计
	lockNodeTotalCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "tenant_lock_node_total",
			Help:      "The Tenant Lock Node Total Count .",
		},
		[]string{"instance_group", "tenant_id"},
	)
)

func init() {
	metrics.Registry.MustRegister(lockNodeTotalCounter)
}

// bci pending pod 是否可以入队逻辑
func (ssc *Controller) shouldHandlePod(pod *corev1.Pod) bool {
	// 非bci pod, 不处理
	if ret := entity.GetBciPodType(pod); ret == entity.NotBciPod {
		return false
	}
	if len(pod.Status.Conditions) == 0 {
		return false
	}
	// 如果是pod使用可用区打散，不需要通过resource-controller patch租户锁，直接在用户的专有节点组上增加租户锁label
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/kdS9OIVGfc/7jFKzU7wHbWC5t
	if pod.Spec.TopologySpreadConstraints != nil {
		return false
	}
	// 判断是否经过调度器调度
	schedulerScheduled := false
	for _, condition := range pod.Status.Conditions {
		if condition.Type == corev1.PodScheduled &&
			(condition.Status == corev1.ConditionFalse || condition.Status == corev1.ConditionUnknown) {
			schedulerScheduled = true
			break
		}
	}
	// 容器正在启动，phase为pending
	if schedulerScheduled && pod.Status.Phase == corev1.PodPending && pod.Spec.NodeName == "" {
		klog.V(4).Infof("nodeController pending pod <%s/%s> enqueue ", pod.Namespace, pod.Name)
		return true
	}
	return false
}

func (ssc *Controller) getInstanceGroupLock(ig string) *sync.Mutex {
	ssc.RLock()
	lock, ok := ssc.instanceGroupLockMap[ig]
	ssc.RUnlock()
	if ok {
		return lock
	}
	ssc.Lock()
	defer ssc.Unlock()
	lock, ok = ssc.instanceGroupLockMap[ig]
	if ok {
		return lock
	}

	lock = &sync.Mutex{}
	ssc.instanceGroupLockMap[ig] = lock
	return lock
}

// 获取竞价实例的锁标记, 每个规格&价格一个排他锁
func (ssc *Controller) getBidNodeLock(bidInstanceLockKey string) *sync.Mutex {
	ssc.RLock()
	lock, ok := ssc.bidInstanceLockMap[bidInstanceLockKey]
	ssc.RUnlock()
	if ok {
		return lock
	}
	ssc.Lock()
	defer ssc.Unlock()
	lock, ok = ssc.bidInstanceLockMap[bidInstanceLockKey]
	if ok {
		return lock
	}

	lock = &sync.Mutex{}
	ssc.bidInstanceLockMap[bidInstanceLockKey] = lock
	return lock
}

// 节点组管理节点的锁操作
func (ssc *Controller) tenantLockInstanceGroupNode(pod *corev1.Pod, instanceGroupID string) (*sync.Mutex, map[string]string) {
	lock := ssc.getInstanceGroupLock(instanceGroupID)
	nodeSelectLabels := map[string]string{entity.InstanceGroupKey: instanceGroupID}
	return lock, nodeSelectLabels
}

// 竞价节点的锁操作
func (ssc *Controller) tenantLockBidNode(pod *corev1.Pod) (*sync.Mutex, map[string]string) {
	nodeSelector := pod.Spec.NodeSelector
	bidInstanceLockKey := entity.BuildBidInstanceLockKey(nodeSelector)
	lock := ssc.getBidNodeLock(bidInstanceLockKey)
	nodeSelectLabels := entity.BuildBidNodeSelectLabels(nodeSelector)
	return lock, nodeSelectLabels
}

// 尝试锁定节点
// 三个返回值：reconcile.Result, bool, error
// 其中第二参数表示是否需要重试，在指定多可用区时生效
// 重试场景
// 1. 没有候选节点，会触发扩容节点组，需要重试
func (ssc *Controller) tryTenantLockInstanceGroupNode(pod *corev1.Pod, instanceGroupPodFlag bool, instanceGroupID string, lock *sync.Mutex,
	nodeSelectLabels map[string]string) (reconcile.Result, bool, error) {
	lock.Lock()
	defer lock.Unlock()

	podKey := podKeyFn(pod.Namespace, pod.Name)
	// 系统之间异步处理，有可能controller 锁标签成功，但是调度器还没调度，此时又触发锁node操作，此处做一个延时处理
	if success := ssc.deletePatchPodIfPossible(podKey); success {
		klog.V(3).Infof("nodeController pending pod <%s/%s> have lockNode success retry after 30s", pod.Namespace, pod.Name)
		return reconcile.Result{RequeueAfter: 30 * time.Second}, false, nil
	}

	candidateNodes, err := ssc.getCandidateNodes(nodeSelectLabels)
	if err != nil {
		return reconcile.Result{}, false, err
	}

	if len(candidateNodes) == 0 {
		// 1. 没有候选节点，会触发扩容节点组，需要重试
		err := fmt.Errorf("nodeController pod <%s/%s> tenantLockNode has no node to lock ", pod.Namespace, pod.Name)
		klog.Warningf(err.Error())
		return reconcile.Result{RequeueAfter: 5 * time.Second}, true, nil
	}

	err = ssc.tryTenantLockOneNode(pod, candidateNodes, instanceGroupPodFlag, instanceGroupID, podKey)
	if err != nil {
		return reconcile.Result{}, false, err
	}
	return reconcile.Result{}, false, nil
}

// 获取可用节点候选集
func (ssc *Controller) getCandidateNodes(nodeSelectLabels map[string]string) (candidateNodes []*corev1.Node, err error) {
	candidateNodes = make([]*corev1.Node, 0)
	selector := labels.SelectorFromSet(labels.Set(nodeSelectLabels))
	igNodes, err := ssc.nodeLister.List(selector)
	if err != nil {
		klog.Errorf("nodeController nodeLister List selector %+v err %+v ", selector.String(), err)
		return
	}

	klog.V(3).Infof("nodeController getCandidateNodes selector=%s found %d nodes", selector.String(), len(igNodes))

	coolNodes := make([]*corev1.Node, 0)
	initNodes := make([]*corev1.Node, 0)
	filteredByOtherTenant := 0
	filteredByUnschedulable := 0
	filteredByInvalidState := 0

	for _, node := range igNodes {
		// 过滤被其他租户锁定的节点
		tenantLock := node.Labels[entity.TenantLockKey]
		if tenantLock != "" {
			filteredByOtherTenant++
			klog.V(4).Infof("nodeController getCandidateNodes skip node %s, already locked by different tenant %s", node.Name, tenantLock)
			continue
		}
		// 节点被屏蔽，不选
		if node.Spec.Unschedulable {
			filteredByUnschedulable++
			klog.V(4).Infof("nodeController getCandidateNodes skip node %s, unschedulable", node.Name)
			continue
		}

		nodeStatus := node.Labels[entity.NodeStateLabelKey]
		if nodeStatus == entity.NodeStateInit.String() {
			initNodes = append(initNodes, node)
			klog.V(4).Infof("nodeController getCandidateNodes found init node %s", node.Name)
			continue
		}
		// 节点GC成功
		if nodeStatus == entity.NodeStateCool.String() {
			nodeGcStatus := node.Labels[entity.NodeGcStateKey]
			if nodeGcStatus == entity.NodeGcStateSuccess.String() {
				coolNodes = append(coolNodes, node)
				klog.V(4).Infof("nodeController getCandidateNodes found cool node %s", node.Name)
			} else {
				filteredByInvalidState++
				klog.V(4).Infof("nodeController getCandidateNodes skip cool node %s, gc status is %s (expected: %s)",
					node.Name, nodeGcStatus, entity.NodeGcStateSuccess.String())
			}
		} else {
			filteredByInvalidState++
			klog.V(4).Infof("nodeController getCandidateNodes skip node %s, invalid state %s (expected: %s or %s)",
				node.Name, nodeStatus, entity.NodeStateInit.String(), entity.NodeStateCool.String())
		}
	}
	// 先从cool中获取节点
	candidateNodes = append(candidateNodes, coolNodes...)
	candidateNodes = append(candidateNodes, initNodes...)

	klog.V(3).Infof("nodeController getCandidateNodes final candidates: %d cool nodes, %d init nodes, total %d",
		len(coolNodes), len(initNodes), len(candidateNodes))

	// 如果没有找到候选节点，记录详细的调试信息
	if len(candidateNodes) == 0 {
		klog.Warningf("nodeController getCandidateNodes no candidates found with selector %s", selector.String())
		klog.Warningf("nodeController getCandidateNodes total nodes found: %d, filtered out: tenant-locked=%d, unschedulable=%d, invalid-state=%d",
			len(igNodes), filteredByOtherTenant, filteredByUnschedulable, filteredByInvalidState)
	}

	return
}

// 尝试锁定一个节点
func (ssc *Controller) tryTenantLockOneNode(pod *corev1.Pod, candidateNodes []*corev1.Node, instanceGroupPodFlag bool,
	instanceGroupID, podKey string) (err error) {
	nodeSelector := pod.Spec.NodeSelector
	tenantID := nodeSelector[entity.TenantLockKey]

	retryCount := 0
	for _, node := range candidateNodes {
		// 节点组锁node数量打点统计
		if instanceGroupPodFlag {
			lockNodeTotalCounter.WithLabelValues(instanceGroupID, tenantID).Inc()
		}
		// 竞价实例暂不打点
		err := ssc.doLockNode(tenantID, node, pod)
		if err == nil {
			// patch 成功，记录patch成功的pod
			ssc.patchPodStore.Store(podKey, struct{}{})
			return nil
		}
		retryCount++
		if retryCount == 3 {
			return err
		}
	}
	return err
}

// 锁节点操作
func (ssc *Controller) tenantLockNode(pod *corev1.Pod) (reconcile.Result, error) {
	switch podType := entity.GetBciPodType(pod); podType {
	case entity.BciPodByBid:
		// 竞价实例
		lock, nodeSelectLabels := ssc.tenantLockBidNode(pod)
		result, _, err := ssc.tryTenantLockInstanceGroupNode(pod, false, "", lock, nodeSelectLabels)
		return result, err
	case entity.BciPodByNodeSelector:
		instanceGroupID := pod.Spec.NodeSelector[entity.InstanceGroupKey]
		lock, nodeSelectLabels := ssc.tenantLockInstanceGroupNode(pod, instanceGroupID)
		result, _, err := ssc.tryTenantLockInstanceGroupNode(pod, true, instanceGroupID, lock, nodeSelectLabels)
		return result, err
	case entity.BciPodByNodeAffinity:
		// 遍历节点组候选集，针对每节点组遍历所有节点，如果找到首个可用节点，锁定
		instanceGroupIDs := entity.GetInstanceGroupIDFromNodeAffinity(pod)
		for _, instanceGroupID := range instanceGroupIDs {
			klog.V(3).Infof("nodeController pending pod <%s/%s> instance group <%s> try to lock node", pod.Namespace, pod.Name, instanceGroupID)
			lock, nodeSelectLabels := ssc.tenantLockInstanceGroupNode(pod, instanceGroupID)
			result, retry, err := ssc.tryTenantLockInstanceGroupNode(pod, true, instanceGroupID, lock, nodeSelectLabels)
			if err != nil {
				klog.V(3).Infof("nodeController pending pod <%s/%s> instance group <%s> try to lock node err: %+v", pod.Namespace, pod.Name, instanceGroupID, err)
				continue
			}
			if retry {
				continue
			}
			return result, nil
		}
		klog.Warningf("nodeController pending pod <%s/%s> lockNode fail", pod.Namespace, pod.Name)
		// 没有找到可用节点，一段时候后重试
		return reconcile.Result{RequeueAfter: 5 * time.Second}, nil
	default:
		return reconcile.Result{}, fmt.Errorf("nodeController pending pod <%s/%s> is not bci pod", pod.Namespace, pod.Name)
	}
}

func (ssc *Controller) doLockNode(tenantID string, node *corev1.Node, pod *corev1.Pod) error {
	klog.V(3).Infof("nodeController pod <%s/%s> patch node %s tenantID %s", pod.Namespace, pod.Name, node.Name, tenantID)
	// patch node label
	copyNode := node.DeepCopy()
	nodeLabels := copyNode.ObjectMeta.Labels
	if nodeLabels == nil {
		nodeLabels = make(map[string]string)
	}

	nodeLabels[entity.TenantLockKey] = tenantID

	// 检查Pod是否需要IPv6支持，如果需要则为节点打上IPv6标签
	nodeSelector := pod.Spec.NodeSelector
	if nodeSelector != nil && nodeSelector[entity.BCIEnableIPv6Key] == "true" {
		// 为节点打上支持IPv6标签
		nodeLabels[entity.BCIEnableIPv6Key] = "true"
		klog.V(3).Infof("nodeController pod <%s/%s> requires IPv6 support, adding IPv6 label to node %s", pod.Namespace, pod.Name, node.Name)
	}

	metaData := map[string]map[string]string{
		"labels": nodeLabels,
	}
	patchData, _ := json.Marshal(&map[string]interface{}{
		"metadata": &metaData,
	})

	var err error
	for i := 0; i < 3; i++ {
		err = ssc.client.Patch(context.TODO(), copyNode, client.RawPatch(types.StrategicMergePatchType, []byte(patchData)))
		klog.V(3).Infof("nodeController patch node %s tenantID %s index %+v , err %+v ", node.Name, tenantID, i, err)
		if err == nil {
			return nil
		}
	}

	return err
}

func (ssc *Controller) deletePatchPodIfPossible(podKey string) bool {
	_, ok := ssc.patchPodStore.Load(podKey)
	if !ok {
		return false
	}
	ssc.patchPodStore.Delete(podKey)
	return true
}

func podKeyFn(namespace string, name string) string {
	return namespace + ":" + name
}
