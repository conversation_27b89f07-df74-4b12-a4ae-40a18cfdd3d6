package node

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	ccev2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/prometheus/client_golang/prometheus"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	toolscache "k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/workqueue"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
)

var (
	// instanceGroup维度各状态机节点数量监控
	instanceGroupNodeStateCountGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Subsystem: "bci",
			Name:      "node_state_count",
			Help:      "The InstanceGroup Node State Count.",
		},
		[]string{"instance_group", "state"},
	)
	// 用户维度各InstanceGroup下的节点数量监控
	userInstanceGroupNodeGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Subsystem: "bci",
			Name:      "user_instance_group_node_count",
			Help:      "AccountID Use InstanceGroup Node Count. ",
		},
		[]string{"account_id", "instance_group"},
	)
	// 用户维度各规格各价格下的节点数量监控-竞价实例
	userInstanceTemplateBidModeNodeGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Subsystem: "bci",
			Name:      "user_instance_spec_price_bid_node_count",
			Help:      "AccountID Use Bid Node Count. ",
		},
		[]string{"account_id", "instance_spec", "price"},
	)

	// node 通过npd上报的fault数量
	nodeConditionFaultCountGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Subsystem: "bci",
			Name:      "node_condition_fault_count",
			Help:      "The Node Condition Fault Count.",
		},
		[]string{"fault_type"},
	)
)

const (
	evictNotReadyNodePod = "bci_internal_evictNotReadyNodePod"
)

func init() {
	metrics.Registry.MustRegister(instanceGroupNodeStateCountGauge, userInstanceGroupNodeGauge, userInstanceTemplateBidModeNodeGauge, nodeConditionFaultCountGauge)
}

// StateMachineController node状态机controller实现
type StateMachineController struct {
	sync.Mutex

	client    client.Client
	cceClient util.CceClient

	options *options.NodeOptions

	Nodes    map[string]*entity.NodeInfo
	BidNodes map[string]*entity.NodeInfo

	podInformer  cache.Informer
	nodeInformer cache.Informer

	podLister  corev1_listers.PodLister
	nodeLister corev1_listers.NodeLister
	cmLister   corev1_listers.ConfigMapLister
}

// NewStateMachineController 初始化状态机controller
func NewStateMachineController(options *options.NodeOptions, client client.Client, cceClient util.CceClient,
	podInformer cache.Informer, nodeInformer cache.Informer, cmInformer cache.Informer) *StateMachineController {
	podLister := corev1_listers.NewPodLister(podInformer.(toolscache.SharedIndexInformer).GetIndexer())
	nodeLister := corev1_listers.NewNodeLister(nodeInformer.(toolscache.SharedIndexInformer).GetIndexer())
	cmLister := corev1_listers.NewConfigMapLister(cmInformer.(toolscache.SharedIndexInformer).GetIndexer())

	c := StateMachineController{
		client:       client,
		options:      options,
		cceClient:    cceClient,
		Nodes:        make(map[string]*entity.NodeInfo),
		BidNodes:     make(map[string]*entity.NodeInfo),
		podInformer:  podInformer,
		nodeInformer: nodeInformer,
		podLister:    podLister,
		nodeLister:   nodeLister,
		cmLister:     cmLister,
	}

	return &c
}

// Start 定时任务定期刷新node 状态机
func (c *StateMachineController) Start(stopChan <-chan struct{}) {
	// 注册informer 监听事件
	c.podInformer.AddEventHandler(toolscache.ResourceEventHandlerFuncs{
		AddFunc:    c.addPod,
		UpdateFunc: c.updatePod,
		DeleteFunc: c.deletePod,
	})

	// 开启rsync，10分钟执行一次
	c.nodeInformer.AddEventHandlerWithResyncPeriod(toolscache.ResourceEventHandlerFuncs{
		UpdateFunc: c.updateNode,
		DeleteFunc: c.deleteNode,
	}, 10*time.Minute)

	// 启动定时任务
	go func() {
		// 启动时先sleep 定时任务间隔
		time.Sleep(c.options.SyncNodeStateTaskInterval)
		ticker := time.NewTicker(c.options.SyncNodeStateTaskInterval)

		for {
			select {
			case <-stopChan:
				return
			case <-ticker.C:
				c.syncNodes()
				c.nodeStateStatistics()
			}
		}
	}()
}

func (c *StateMachineController) podSyncFn(pod *corev1.Pod, podEventFn func(nodeInfo *entity.NodeInfo)) {
	klog.V(5).Infof("StateMachineController sync pod <%s/%s> ", pod.Namespace, pod.Name)
	// pod sync 逻辑: 若pod不存在node直接返回, 否则从缓存中获取, 缓存若中取不到则将其加入
	nodeInfo := c.getOrCreateNodeInfo(pod)

	if nodeInfo == nil {
		return
	}
	podEventFn(nodeInfo)
	if err := c.syncNodeState(nodeInfo); err != nil {
		klog.Errorf("StateMachineController pod <%s/%s> syncNodeState node %s err %+v ",
			pod.Namespace, pod.Name, nodeInfo.Name, err)
	}
	return
}

func (c *StateMachineController) syncNodes() {
	c.Lock()
	defer c.Unlock()

	start := time.Now()
	defer func() {
		klog.V(5).Infof("StateMachineController syncNodes task cost %+v ", time.Now().Sub(start))
	}()

	allNodeInfos := make([]*entity.NodeInfo, 0)
	for _, node := range c.Nodes {
		allNodeInfos = append(allNodeInfos, node)
	}
	for _, node := range c.BidNodes {
		allNodeInfos = append(allNodeInfos, node)
	}

	allPods, err := c.podLister.List(labels.Everything())
	if err != nil {
		klog.Errorf("StateMachineController syncNodes get allPods err %+v ", err)
		return
	}

	nodePodMap := make(map[string][]*corev1.Pod)
	for _, pod := range allPods {
		nodeName := pod.Spec.NodeName
		if nodeName == "" {
			continue
		}
		// 此处不关注pod phase，在nodeInfo判断是否是empty node 时关注
		_, ok := nodePodMap[nodeName]
		if !ok {
			nodePodMap[nodeName] = make([]*corev1.Pod, 0)
		}
		nodePodMap[nodeName] = append(nodePodMap[nodeName], pod)
	}

	syncOneNode := func(index int) {
		nodeInfo := allNodeInfos[index]
		node, err := c.nodeLister.Get(nodeInfo.Name)
		if err != nil {
			if errors.IsNotFound(err) {
				klog.Warningf("StateMachineController syncNode %s informer not found skip sync ", nodeInfo.Name)
				// 函数入口加锁处理
				delete(c.Nodes, nodeInfo.Name)
				delete(c.BidNodes, nodeInfo.Name)
				return
			}
			return
		}

		nodePods, ok := nodePodMap[nodeInfo.Name]
		if !ok {
			nodePods = make([]*corev1.Pod, 0)
		}
		nodeInfo.UpdateNode(node)
		nodeInfo.SyncNodePods(nodePods)

		if err := c.syncNodeState(nodeInfo); err != nil {
			klog.Errorf("StateMachineController syncNodes Task node %s err %+v ", nodeInfo.Name, err)
		}
	}

	workqueue.ParallelizeUntil(context.Background(), 16, len(allNodeInfos), syncOneNode)
}

func (c *StateMachineController) getOrCreateNodeInfo(pod *corev1.Pod) *entity.NodeInfo {
	nodeName := pod.Spec.NodeName
	if nodeName == "" {
		klog.V(5).Infof("StateMachineController add pod <%s/%s> nodeName is empty , ignore ", pod.Namespace, pod.Name)
		return nil
	}
	nodeInfo, nodesContains := c.Nodes[nodeName]
	bidNodeInfo, bidNodesContains := c.BidNodes[nodeName]

	if nodesContains {
		return nodeInfo
	}
	if bidNodesContains {
		return bidNodeInfo
	}

	// 既不在普通实例缓存中也不在竞价实例缓存中
	node, err := c.nodeLister.Get(nodeName)
	if err != nil {
		klog.Warningf("StateMachineController nodeLister get node %s err %+v", nodeName, err)
		return nil
	}
	// 通过是否含有CCE集群标签判断节点是否创建完成
	if !entity.K8sNodeIsInitComplete(node) {
		return nil
	}
	newNodeInfo := entity.NewNodeInfo(node)
	if newNodeInfo.NodeIsBidding() {
		c.BidNodes[nodeName] = newNodeInfo
	} else {
		c.Nodes[nodeName] = newNodeInfo
	}
	return newNodeInfo
}

// 删除node上placeholder pod，让placeholder pod 触发ca 扩容来满足buffer数; 仅针对普通实例
func (c *StateMachineController) deletePlaceholderPodIfPossible(nodeInfo *entity.NodeInfo) {
	if nodeInfo.CurrentNodeState().CanRunPlaceholderPod() {
		return
	}
	placeholderPods := nodeInfo.GetPlaceholderPod()
	for _, placeholderPod := range placeholderPods {
		copyPod := placeholderPod.DeepCopy()
		err := c.client.Delete(context.Background(), copyPod)
		klog.V(3).Infof("StateMachineController delete placeholder pod %s on node %s state %+v err %+v ", copyPod.Name, nodeInfo.Name, nodeInfo.CurrentNodeState(), err)
	}
}

func (c *StateMachineController) evictNotReadyNodeBciPods(nodeInfo *entity.NodeInfo) {

	notReadyStartTime, ok := nodeInfo.Node.Labels[entity.NotReadyStartTimeKey]
	if !ok {
		return
	}

	unixNotReadyStartTime, err := strconv.ParseInt(notReadyStartTime, 10, 64)
	if err != nil {
		klog.Errorf("StateMachineController notReady node %s parse notReadyStartTime %+v err %+v ",
			nodeInfo.Name, notReadyStartTime, err)
		return
	}
	covertTime := time.Unix(unixNotReadyStartTime, 0)

	if !time.Now().After(covertTime.Add(c.options.EvictNotReadyNodePodTime)) {
		return
	}

	pods := nodeInfo.GetBciPendingOrRunningPods()
	for _, pod := range pods {
		patchPod := entity.PatchValue{
			Key:   evictNotReadyNodePod,
			Type:  entity.PatchTypeUpdate,
			Value: "evicted",
		}
		err := util.PatchPodAnnotation(c.client, pod, []entity.PatchValue{patchPod})
		klog.V(3).Infof("StateMachineController evict notReady node %s pod %s err %+v ",
			nodeInfo.Name, pod.Name, err)
	}

	// 删除notReady node已绑定的跨租户eni,not ready 状态已超过5分钟;
	// 防止后续人工删除bcc node,eni 泄漏
	node, err := c.nodeLister.Get(nodeInfo.Name)
	if err != nil {
		klog.Errorf("StateMachineController evict notReady node %s not found in informer ", nodeInfo.Name)
		return
	}
	if !runtime.RunCustomizeNodeGCHook(runtime.StateMachineGC, node) {
		klog.V(3).Infof("StateMachineController evict notReady node %s RunCustomizeNodeGCHook not success ", node.Name)
	}
}

func (c *StateMachineController) syncNodeState(nodeInfo *entity.NodeInfo) error {
	// 只处理节点组管理的node或者是竞价实例的node
	if nodeInfo.InstanceGroup() == "" && !nodeInfo.NodeIsBidding() {
		return nil
	}

	// 临时逻辑，镜像转储切换过程中不允许调度，后续基于node condition 来做故障屏蔽&解屏蔽
	// kubernetes.io/stargz-ready: "true"
	ready, ok := nodeInfo.Node.Labels["kubernetes.io/stargz-ready"]
	if !ok || ready != "true" {
		klog.V(3).Infof("StateMachineController node %s instanceGroupID %s stargz not ready, skip to sync ",
			nodeInfo.Name, nodeInfo.InstanceGroup())
		return nil
	}

	patchValue := make([]entity.PatchValue, 0)

	// 获取node对应的CCEInstanceID并标记至label, 供后续GC时使用
	if instanceID := nodeInfo.InstanceID(); instanceID != "" && nodeInfo.CceInstanceID() == "" {
		instanceInfo, err := c.cceClient.GetInstance(instanceID)
		if err != nil {
			klog.Errorf("StateMachineController node %s GetInstance %s err %+v", nodeInfo.Name, instanceID, err)
		} else {
			cceInstanceID := instanceInfo.Instance.Spec.CCEInstanceID
			// 获取 竞价实例+潮汐node label
			biddingNodeTypePatch, err := c.buildBiddingNodeTypePatchLabel(nodeInfo.Node, instanceInfo)
			if cceInstanceID != "" && err == nil {
				patchValue = append(patchValue, entity.BuildUpdatePatchValue(entity.NodeLabelCCEInstanceIDKey, cceInstanceID))
			}
			if len(biddingNodeTypePatch) > 0 {
				patchValue = append(patchValue, biddingNodeTypePatch...)
			}
		}
	}

	// ②: node处于notReady状态
	if nodeInfo.NodeNotReady() {

		c.evictNotReadyNodeBciPods(nodeInfo)

		patchValue := []entity.PatchValue{
			{
				Key:   entity.NodeStateLabelKey,
				Type:  entity.PatchTypeUpdate,
				Value: entity.NodeStateNotReady.String(),
			},
		}
		// 添加not ready 开始时间
		if _, ok := nodeInfo.Node.Labels[entity.NotReadyStartTimeKey]; !ok {
			patchValue = append(patchValue, entity.PatchValue{
				Key:   entity.NotReadyStartTimeKey,
				Type:  entity.PatchTypeUpdate,
				Value: strconv.FormatInt(time.Now().Unix(), 10),
			})
		}
		patchValue = append(patchValue, entity.BuildUpdatePatchValue(entity.NodeStateLabelKey, entity.NodeStateNotReady.String()))
		patchValue = append(patchValue, entity.BuildUpdatePatchValue(entity.NodeStateTimeLabelKey, strconv.FormatInt(time.Now().Unix(), 10)))
		return c.patchNodeLabels(nodeInfo, patchValue)
	}

	// 若node Label为notReady状态, 则表示notReady->ready
	// 更新内存中node状态为empty, 等待后续patch成功后刷为正确值
	if nodeInfo.CurrentNodeState() == entity.NodeStateNotReady {
		klog.V(3).Infof("StateMachineController node %s state notReady -> Ready ", nodeInfo.Name)
		delete(nodeInfo.Node.Labels, entity.NodeStateLabelKey)
		patchValue = append(patchValue, entity.BuildUpdatePatchValue(entity.NodeStateTimeLabelKey, strconv.FormatInt(time.Now().Unix(), 10)))
	}

	// 跟据node上是否有pending/running状态的bciPod走不同的更新方案
	emptyNode := nodeInfo.EmptyNode()
	if emptyNode {
		return c.syncEmptyNodeState(nodeInfo, patchValue)
	}
	return c.syncRunPodNodeState(nodeInfo, patchValue)
}

// node 上运行bci pod 处理逻辑
func (c *StateMachineController) syncRunPodNodeState(nodeInfo *entity.NodeInfo, patch []entity.PatchValue) error {
	// 删除notReady开始时间戳
	patch = append(patch, entity.PatchValue{
		Key:  entity.NotReadyStartTimeKey,
		Type: entity.PatchTypeDelete,
	})

	nodeState := nodeInfo.CurrentNodeState()
	// for log
	if nodeState != entity.NodeStateRunPod {
		logStateChange(nodeInfo, nodeState, entity.NodeStateRunPod)
		patch = append(patch, entity.BuildUpdatePatchValue(entity.NodeStateTimeLabelKey, strconv.FormatInt(time.Now().Unix(), 10)))
	}
	// 状态机更新为runPod
	patch = append(patch, entity.BuildUpdatePatchValue(entity.NodeStateLabelKey, entity.NodeStateRunPod.String()))

	// 删除cool & podExit 相关时间戳，if exist
	patch = append(patch, entity.BuildDeletePatchValue(entity.AllPodExitTimeLabelKey))
	patch = append(patch, entity.BuildDeletePatchValue(entity.StartCoolTimeLabelKey))

	// 删除gc状态标签，if exist
	patch = append(patch, entity.BuildDeletePatchValue(entity.NodeGcStateKey))

	// 节点组管理的node: 添加buffer标签，可被placeholder pod占用
	if nodeInfo.InstanceGroup() != "" {
		patch = append(patch, entity.BuildUpdatePatchValue(entity.BufferLabelKey, entity.BufferLabelValue))
	}

	return c.patchNodeLabels(nodeInfo, patch)
}

// node 上无运行bci pod 处理逻辑
func (c *StateMachineController) syncEmptyNodeState(nodeInfo *entity.NodeInfo, patch []entity.PatchValue) error {
	// 删除notReady开始时间戳
	patch = append(patch, entity.PatchValue{
		Key:  entity.NotReadyStartTimeKey,
		Type: entity.PatchTypeDelete,
	})

	nodeState := nodeInfo.CurrentNodeState()
	nodeLocked := nodeInfo.NodeLocked()

	// 节点状态为空且未被锁定: 新创建的节点会是该场景, 后续转为init后由tenantLockController为其打标签
	if nodeState == entity.NodeStateEmpty && !nodeLocked {
		// 打印日志
		logStateChange(nodeInfo, nodeState, entity.NodeStateInit)
		// 状态机更新为init
		patch = append(patch, entity.BuildUpdatePatchValue(entity.NodeStateLabelKey, entity.NodeStateInit.String()))
		patch = append(patch, entity.BuildUpdatePatchValue(entity.NodeStateTimeLabelKey, strconv.FormatInt(time.Now().Unix(), 10)))

		// 节点组管理的node: 添加buffer标签，可被placeholder pod占用
		if nodeInfo.InstanceGroup() != "" {
			patch = append(patch, entity.BuildUpdatePatchValue(entity.BufferLabelKey, entity.BufferLabelValue))
		}
		// 此处删除lock标签
		patch = append(patch, entity.BuildDeletePatchValue(entity.TenantLockKey))
		// 删除BCIEnableIPv6Key标签
		patch = append(patch, entity.BuildDeletePatchValue(entity.BCIEnableIPv6Key))
	}

	canChangeToPodExitStateMap := map[entity.NodeState]struct{}{
		entity.NodeStateRunPod: {}, // 用户运行过pod退出
		entity.NodeStateCool:   {}, // cool 状态node重新绑定用户
		entity.NodeStateInit:   {}, // 初始化node 锁定用户后无pod运行
		entity.NodeStateEmpty:  {}, // 由notReady -> ready
	}

	if _, ok := canChangeToPodExitStateMap[nodeState]; ok && nodeLocked {
		logStateChange(nodeInfo, nodeState, entity.NodeStatePodExit)

		// 记录node上bci用户pod全部退出的时间
		if _, ok := nodeInfo.Node.Labels[entity.AllPodExitTimeLabelKey]; !ok {
			patch = append(patch, entity.BuildUpdatePatchValue(entity.AllPodExitTimeLabelKey, strconv.FormatInt(time.Now().Unix(), 10)))
		}
		// 状态机更新为pod exit
		patch = append(patch, entity.BuildUpdatePatchValue(entity.NodeStateLabelKey, entity.NodeStatePodExit.String()))
		patch = append(patch, entity.BuildUpdatePatchValue(entity.NodeStateTimeLabelKey, strconv.FormatInt(time.Now().Unix(), 10)))
		// 删除buffer标签，不可被placeholder pod占用
		patch = append(patch, entity.BuildDeletePatchValue(entity.BufferLabelKey))
	} else if nodeState == entity.NodeStateRunPod && !nodeLocked {
		// node状态机为runPod，但未被锁定，状态机变为cool
		logStateChange(nodeInfo, nodeState, entity.NodeStateCool)
		patch = append(patch, c.buildCoolStateLabelPatch(nodeInfo, patch)...)
	}

	nodeLabel := nodeInfo.Node.Labels
	if nodeState == entity.NodeStatePodExit && nodeLabel[entity.KeepHoldTenantLockKey] != "true" {
		holdTimeEnough, err := podExitStateHoldTimeEnough(nodeInfo, c.options)
		if err != nil {
			klog.Error(err)
			return err
		}
		if holdTimeEnough {
			logStateChange(nodeInfo, nodeState, entity.NodeStateCool)
			// 状态机变为cool
			patch = append(patch, c.buildCoolStateLabelPatch(nodeInfo, patch)...)
		}
	}
	return c.patchNodeLabels(nodeInfo, patch)
}

func (c *StateMachineController) buildCoolStateLabelPatch(nodeInfo *entity.NodeInfo, patch []entity.PatchValue) []entity.PatchValue {
	// 状态机变为cool
	patch = append(patch, entity.BuildUpdatePatchValue(entity.NodeStateLabelKey, entity.NodeStateCool.String()))
	patch = append(patch, entity.BuildUpdatePatchValue(entity.NodeStateTimeLabelKey, strconv.FormatInt(time.Now().Unix(), 10)))
	// 删除podExitTime label
	patch = append(patch, entity.BuildDeletePatchValue(entity.AllPodExitTimeLabelKey))
	// 删除tenant-lock 标签
	patch = append(patch, entity.BuildDeletePatchValue(entity.TenantLockKey))
	// 删除BCIEnableIPv6Key标签
	patch = append(patch, entity.BuildDeletePatchValue(entity.BCIEnableIPv6Key))
	// 添加start-cool-time 标签
	patch = append(patch, entity.BuildUpdatePatchValue(entity.StartCoolTimeLabelKey, strconv.FormatInt(time.Now().Unix(), 10)))
	// 节点组管理的node: 添加buffer标签，可被placeholder pod占用
	if nodeInfo.InstanceGroup() != "" {
		patch = append(patch, entity.BuildUpdatePatchValue(entity.BufferLabelKey, entity.BufferLabelValue))
	}
	// 删除NodeGcState 标签
	patch = append(patch, entity.BuildDeletePatchValue(entity.NodeGcStateKey))

	return patch
}

func (c *StateMachineController) patchNodeLabels(nodeInfo *entity.NodeInfo, patchValues []entity.PatchValue) error {
	if len(patchValues) == 0 {
		return nil
	}
	// 向node label中添加node唯一标示，在node gc中依据此值获取cceInstanceID
	patchValues = append(patchValues, entity.BuildUpdatePatchValue(entity.PatchNodeName, nodeInfo.Name))

	var err error
	var node *corev1.Node
	for i := 0; i < 3; i++ {
		// 从informer中获取最新的
		node, err = c.nodeLister.Get(nodeInfo.Name)
		if err != nil {
			if errors.IsNotFound(err) {
				klog.Warningf("StateMachineController patchNodeLabels node %s is not found in informer ", nodeInfo.Name)
				return nil
			}
			continue
		}

		copyNode := node.DeepCopy()
		nodeLabels := copyNode.ObjectMeta.Labels
		if nodeLabels == nil {
			nodeLabels = make(map[string]string)
		}

		newLabels := make(map[string]interface{})

		for k, v := range nodeLabels {
			newLabels[k] = v
		}

		// label 是否改变
		labelChange := false

		for _, patch := range patchValues {
			val := nodeLabels[patch.Key]
			// 删除某个label
			if patch.Type == entity.PatchTypeDelete && val != "" {
				newLabels[patch.Key] = nil
				labelChange = true
				continue
			}
			// 更新某个label
			if patch.Type == entity.PatchTypeUpdate && val != patch.Value {
				newLabels[patch.Key] = patch.Value
				labelChange = true
			}
		}

		if !labelChange {
			klog.V(5).Infof("StateMachineController patch node %s patch value %+v not change ", nodeInfo.Name, patchValues)
			return nil
		}

		metaData := map[string]map[string]interface{}{
			"labels": newLabels,
		}
		patchData, _ := json.Marshal(&map[string]interface{}{
			"metadata": &metaData,
		})

		// 单测使用
		if newState, ok := newLabels[entity.NodeStateLabelKey]; ok {
			if newStateVal, ok := newState.(string); ok {
				nodeInfo.State = entity.NodeState(newStateVal)
			}
		}

		// nodeInfo.State = newLabels[entity.NodeStateLabelKey].(entity.NodeState)

		err = c.client.Patch(context.TODO(), copyNode, client.RawPatch(types.StrategicMergePatchType, []byte(patchData)))
		klog.V(3).Infof("StateMachineController patch node %s patch value %+v index %+v , err %+v ", nodeInfo.Name, patchValues, i, err)
		if err == nil {
			return nil
		}
	}

	return err
}

func logStateChange(nodeInfo *entity.NodeInfo, oldState entity.NodeState, newState entity.NodeState) {
	klog.V(3).Infof("StateMachineController node %s state change old %+v -> new %+v ", nodeInfo.Name, oldState, newState)
}

// 统计节点状态, 用于监控打点
func (c *StateMachineController) nodeStateStatistics() {
	c.instanceGroupNodeStatistics()
	c.bidNodeStatistics()
	c.nodeConditionStatistics()
}

// 节点组内node状态统计，用于监控打点
func (c *StateMachineController) instanceGroupNodeStatistics() {
	// instanceGroup : state : nodeCount
	igNodeMap := make(map[string]map[entity.NodeState]int)
	// accountID : instanceGroup : nodeCount
	userNodeMap := make(map[string]map[string]int)
	// instanceGroup key
	allIgList := make([]entity.InstanceGroupCm, 0)

	igConfigMap, err := c.cmLister.ConfigMaps(entity.InstanceGroupCmNamespace).Get(entity.InstanceGroupCmName)

	if err == nil && len(igConfigMap.Data) > 0 {
		if data, ok := igConfigMap.Data[entity.InstanceGropuCmDataKey]; ok {
			_ = json.Unmarshal([]byte(data), &allIgList)
		}
	}
	c.Lock()
	defer c.Unlock()

	for _, node := range c.Nodes {
		instanceGroup := node.InstanceGroup()
		if instanceGroup == "" {
			continue
		}
		if _, ok := igNodeMap[instanceGroup]; !ok {
			igNodeMap[instanceGroup] = make(map[entity.NodeState]int)
		}

		nodeState := node.CurrentNodeState()
		igNodeMap[instanceGroup][nodeState]++

		// 统计含有taint的node，当node 网络非ready时，node.kubernetes.io/network-unavailable:NoSchedule，导致调度失败，pod pending。
		// 此时应报警出来，人工处理.
		if len(node.Node.Spec.Taints) > 0 {
			igNodeMap[instanceGroup][entity.NodeStateTaint]++
		}
		// 统计用户在节点组下使用了多少node
		accountID := node.NodeLockedAccount()
		if accountID == "" {
			continue
		}

		if _, ok := userNodeMap[accountID]; !ok {
			userNodeMap[accountID] = make(map[string]int)
		}
		userNodeMap[accountID][instanceGroup]++
	}

	instanceGroupNodeStateCountGauge.Reset()
	for ig, stateMap := range igNodeMap {
		for nodeState, nodeCount := range stateMap {
			instanceGroupNodeStateCountGauge.WithLabelValues(ig, nodeState.String()).Set(float64(nodeCount))
		}
		// 节点组中目前未存在的状态机node 打点置为0，确保打点能看到所有node 状态机
		for nodeState := range entity.AllNodeStateMap {
			if _, ok := stateMap[nodeState]; ok {
				continue
			}
			instanceGroupNodeStateCountGauge.WithLabelValues(ig, nodeState.String()).Set(0)
		}
	}

	// 节点组没有机器的也进行打点
	for _, ig := range allIgList {
		if _, ok := igNodeMap[ig.InstanceGroupId]; ok {
			continue
		}
		for nodeState := range entity.AllNodeStateMap {
			instanceGroupNodeStateCountGauge.WithLabelValues(ig.InstanceGroupId, nodeState.String()).Set(0)
		}
	}

	// 打点之前重置一下
	userInstanceGroupNodeGauge.Reset()
	for accountID, instanceGroupNode := range userNodeMap {
		for instanceGroup, nodeCount := range instanceGroupNode {
			userInstanceGroupNodeGauge.WithLabelValues(accountID, instanceGroup).Set(float64(nodeCount))
		}
	}
}

// 竞价实例node状态统计，用于监控打点
func (c *StateMachineController) bidNodeStatistics() {
	// accountID : instanceTemplate : price : nodeCount
	userNodeMap := make(map[string]*entity.BidInstanceTemplateStatistics)

	c.Lock()
	defer c.Unlock()
	for _, node := range c.BidNodes {
		if !node.NodeIsBidding() {
			continue
		}
		// 用户信息
		accountID := node.NodeLockedAccount()
		if accountID == "" {
			continue
		}
		if _, ok := userNodeMap[accountID]; !ok {
			userNodeMap[accountID] = &entity.BidInstanceTemplateStatistics{Info: make(map[string]*entity.BidPriceStatistics)}
		}
		itMap := userNodeMap[accountID]

		// 节点模板信息
		instanceTemplateID := node.InstanceSpecTemplateID()
		if instanceTemplateID == "" {
			continue
		}
		if _, ok := itMap.Info[instanceTemplateID]; !ok {
			itMap.Info[instanceTemplateID] = &entity.BidPriceStatistics{CountInfo: make(map[string]int)}
		}
		priceMapInfo := itMap.Info[instanceTemplateID]

		// 价格信息
		bidMode := node.BidMode()
		if bidMode == entity.NodeLabelBidModeMarketValue {
			priceMapInfo.CountInfo[bidMode]++
		} else {
			priceMapInfo.CountInfo[node.BidPrice()]++
		}
	}

	// 打点之前重置一下
	userInstanceTemplateBidModeNodeGauge.Reset()
	for accountID, itMap := range userNodeMap {
		for it, priceMap := range itMap.Info {
			for price, nodeCount := range priceMap.CountInfo {
				userInstanceTemplateBidModeNodeGauge.WithLabelValues(accountID, it, price).Set(float64(nodeCount))
			}
		}
	}
}

func podExitStateHoldTimeEnough(nodeInfo *entity.NodeInfo, options *options.NodeOptions) (bool, error) {
	podExitTime, ok := nodeInfo.Node.Labels[entity.AllPodExitTimeLabelKey]
	if !ok {
		return false, fmt.Errorf("StateMachineController syncEmptyNode %s state podExit, pod exit time label empty", nodeInfo.Name)
	}
	unixPodExitTime, err := strconv.ParseInt(podExitTime, 10, 64)
	if err != nil {
		return false, fmt.Errorf("StateMachineController node %s parse podExitTime %+v err %w ", nodeInfo.Name, podExitTime, err)
	}
	covertTime := time.Unix(unixPodExitTime, 0)
	return time.Now().After(covertTime.Add(options.PodExitStateHoldTime)), nil
}

// 竞价实例、潮汐实例，patch kubernetes.io/bidding-node-type，给eni controller 回收eni使用
func (c *StateMachineController) buildBiddingNodeTypePatchLabel(node *corev1.Node, instanceInfo *ccev2.GetInstanceResponse) ([]entity.PatchValue, error) {

	result := make([]entity.PatchValue, 0)
	if instanceInfo == nil {
		return result, nil
	}
	if !instanceInfo.Instance.Spec.Bid {
		return result, nil
	}

	igMap, _, err := util.GetInstanceGroupCm(c.cmLister)
	if err != nil {
		return result, err
	}
	// 是竞价实例node
	instanceGroupID := instanceInfo.Instance.Spec.InstanceGroupID
	// 判断是否是潮汐node
	igConfig, ok := igMap[instanceGroupID]
	if !ok {
		klog.Warningf("StateMachineController buildBiddingNodeTypePatchLabel node %s instanceGroupID %s not found in cm ",
			node.Name, instanceGroupID)
		return result, nil
	}

	// 潮汐节点组node
	if strings.Contains(igConfig.MatchType, entity.MatchTypeTidal) {
		patch := entity.BuildUpdatePatchValue(entity.NodeLabelBiddingNodeType, entity.NodeLabelBiddingNodeTypeTidal)
		result = append(result, patch)
		return result, nil
	}

	patch := entity.BuildUpdatePatchValue(entity.NodeLabelBiddingNodeType, entity.NodeLabelBiddingNodeTypeBidding)
	result = append(result, patch)
	return result, nil
}

// 添加node condition 相关监控打点，暂时只关注磁盘只读，后续node 状态机添加 fault 状态(包含 -> notReady、磁盘只读、僵尸进程等)
// TODO 当node 状态机进入fault 对node做自动屏蔽，node 故障恢复，自动解除屏蔽
func (c *StateMachineController) nodeConditionStatistics() {
	allNodes, err := c.nodeLister.List(labels.Everything())
	if err != nil {
		klog.Errorf("StateMachineController nodeConditionStatistics list allNodes err %+v", err)
		return
	}

	// 需要报警的condition
	conditionFaults := strings.Split(c.options.NodeConditionFaultTypes, ",")
	if len(conditionFaults) <= 0 {
		return
	}

	faultConditionMap := make(map[string]int)
	for _, faultType := range conditionFaults {
		faultConditionMap[faultType] = 0
	}

	for _, node := range allNodes {
		if len(node.Status.Conditions) <= 0 {
			continue
		}
		for _, condition := range node.Status.Conditions {
			if _, ok := faultConditionMap[string(condition.Type)]; !ok {
				continue
			}
			if condition.Status == corev1.ConditionTrue {
				// 需要打点
				faultConditionMap[string(condition.Type)]++
				klog.V(4).Infof("StateMachineController nodeConditionStatistics node %s have fault condtion, condition type: %s",
					node.Name, condition.Type)
				// TODO：先临时在此处屏蔽，后续屏蔽&解屏蔽 逻辑迁移到node 状态机核心流程中
				_ = c.cordonNode(node)
			}
		}
	}

	// 打点
	nodeConditionFaultCountGauge.Reset()
	for conditionType, count := range faultConditionMap {
		if count == 0 {
			continue
		}
		nodeConditionFaultCountGauge.WithLabelValues(conditionType).Set(float64(count))
	}
}

func (c *StateMachineController) cordonNode(node *corev1.Node) error {
	// 从informer中获取最新node
	node, _ = c.nodeLister.Get(node.Name)
	if node == nil {
		return nil
	}
	// 节点已经被屏蔽了
	if node.Spec.Unschedulable {
		return nil
	}

	copyNode := node.DeepCopy()
	// 屏蔽节点
	specData := map[string]bool{ // 屏蔽
		"unschedulable": true,
	}
	nodeUpdateEle := map[string]interface{}{
		"spec": &specData,
	}
	patchData, _ := json.Marshal(&nodeUpdateEle)

	var err error
	for i := 0; i < 3; i++ {
		err = c.client.Patch(context.TODO(), copyNode, client.RawPatch(types.StrategicMergePatchType, []byte(patchData)))
		klog.V(3).Infof("StateMachineController cordon node %+v err %+v ", node.Name, err)
		if err == nil {
			return nil
		}
	}
	return err
}
