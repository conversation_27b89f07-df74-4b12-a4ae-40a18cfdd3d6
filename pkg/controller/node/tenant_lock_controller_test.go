package node

import (
	"context"
	"fmt"
	"reflect"
	"sync"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/informers"
	k8sfake "k8s.io/client-go/kubernetes/fake"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

func buildPodWithNodeSelector(namespace, name string, nodeSelector map[string]string, p corev1.PodPhase, conditions []corev1.PodCondition) *corev1.Pod {
	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", namespace, name)),
			Name:      name,
			Namespace: namespace,
		},
		Spec: corev1.PodSpec{
			NodeSelector: nodeSelector,
		},
		Status: corev1.PodStatus{
			Phase:      p,
			Conditions: conditions,
		},
	}
}

func buildNodeWithLabels(name string, labels map[string]string) *corev1.Node {
	return &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   name,
			Labels: labels,
		},
		Spec: corev1.NodeSpec{
			ProviderID: "cce://fake-i-qI1XJAgP",
		},
		Status: corev1.NodeStatus{
			Conditions: []corev1.NodeCondition{
				{
					Type:"ReadonlyFilesystem",
					Status:   "True",
				},
			},
		},
	}
}

func TestReconcilePod(t *testing.T) {

	tests := []struct {
		name           string
		pods           []*corev1.Pod
		nodes          []*corev1.Node
		reconcileParam types.NamespacedName
		reconcileMore  bool
		res            reconcile.Result
	}{
		{
			name: "无空闲机器提供绑定",
			pods: []*corev1.Pod{
				buildPodWithNodeSelector("ns1", "pod1", map[string]string{
					entity.InstanceGroupKey: "ig1",
					entity.TenantLockKey:    "user1",
				}, corev1.PodPending, []corev1.PodCondition{
					{
						Type:   corev1.PodScheduled,
						Status: corev1.ConditionFalse,
					},
				}),
			},
			nodes: []*corev1.Node{
				buildNodeWithLabels("n1", map[string]string{}),
				buildNodeWithLabels("n2", map[string]string{
					entity.InstanceGroupKey: "ig2",
				}),
				buildNodeWithLabels("n3", map[string]string{
					entity.InstanceGroupKey: "ig1",
					entity.TenantLockKey:    "user1",
				}),
				buildNodeWithLabels("n4", map[string]string{
					entity.InstanceGroupKey:  "ig2",
					entity.NodeStateLabelKey: entity.NodeStateCool.String(),
				}),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "ns1",
				Name:      "pod1",
			},
			res: reconcile.Result{RequeueAfter: 5 * time.Second},
		},
		{
			name: "非用户提交pod,placeholder pod",
			pods: []*corev1.Pod{
				buildPodWithNodeSelector("ns1", "pod1", map[string]string{
					entity.InstanceGroupKey: "ig1",
				}, corev1.PodPending, []corev1.PodCondition{
					{
						Type:   corev1.PodScheduled,
						Status: corev1.ConditionFalse,
					},
				}),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "ns1",
				Name:      "pod1",
			},
			res: reconcile.Result{},
		},
		{
			name: "打标签成功",
			pods: []*corev1.Pod{
				buildPodWithNodeSelector("ns1", "pod1", map[string]string{
					entity.InstanceGroupKey: "ig1",
					entity.TenantLockKey:    "user1",
				}, corev1.PodPending, []corev1.PodCondition{
					{
						Type:   corev1.PodScheduled,
						Status: corev1.ConditionFalse,
					},
				}),
			},
			nodes: []*corev1.Node{
				buildNodeWithLabels("n1", map[string]string{}),
				buildNodeWithLabels("n2", map[string]string{
					entity.InstanceGroupKey: "ig1",
				}),
				buildNodeWithLabels("n3", map[string]string{
					entity.InstanceGroupKey: "ig1",
					entity.TenantLockKey:    "user1",
				}),
				buildNodeWithLabels("n4", map[string]string{
					entity.InstanceGroupKey:  "ig1",
					entity.NodeStateLabelKey: entity.NodeStateCool.String(),
					entity.NodeGcStateKey:    entity.NodeGcStateSuccess.String(),
				}),
				func() *corev1.Node {
					// init 状态node被屏蔽
					n := buildNodeWithLabels("n5", map[string]string{
						entity.InstanceGroupKey:  "ig1",
						entity.NodeStateLabelKey: entity.NodeStateInit.String(),
					})
					n.Spec.Unschedulable = true
					return n
				}(),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "ns1",
				Name:      "pod1",
			},
			reconcileMore: true,
			res:           reconcile.Result{RequeueAfter: 30 * time.Second},
		},
	}

	for i, test := range tests {
		if i != len(tests)-1 {
			// continue
		}
		t.Run(test.name, func(t *testing.T) {

			k8sclient := k8sfake.NewSimpleClientset()
			client := fake.NewFakeClient()

			informerFactory := informers.NewSharedInformerFactory(k8sclient, 0)
			podInformer := informerFactory.Core().V1().Pods()
			nodeInformer := informerFactory.Core().V1().Nodes()
			podIndexer := podInformer.Informer().GetIndexer()
			nodeIndexer := nodeInformer.Informer().GetIndexer()

			c := Controller{
				podLister:            podInformer.Lister(),
				nodeLister:           nodeInformer.Lister(),
				instanceGroupLockMap: make(map[string]*sync.Mutex),
				client:               client,
			}

			for _, node := range test.nodes {
				nodeIndexer.Add(node)
				client.Create(context.Background(), node)
			}

			for _, pod := range test.pods {
				podIndexer.Add(pod)
				client.Create(context.Background(), pod)
			}

			result, _ := c.Reconcile(context.Background(), reconcile.Request{
				NamespacedName: test.reconcileParam,
			})
			fmt.Println(result)

			if test.reconcileMore {
				result, _ = c.Reconcile(context.Background(), reconcile.Request{
					NamespacedName: test.reconcileParam,
				})
			}
			if !reflect.DeepEqual(result, test.res) {
				t.Errorf("nodeController Reconcile got res %+v ,expect %+v ", result, test.res)
			}
		})
	}

}
