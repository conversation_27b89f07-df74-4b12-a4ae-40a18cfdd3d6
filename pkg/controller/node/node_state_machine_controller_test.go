package node

import (
	"context"
	"flag"
	"fmt"
	"strconv"
	"testing"
	"time"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/informers"
	k8sfake "k8s.io/client-go/kubernetes/fake"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	k8s_cache "k8s.io/client-go/tools/cache"
	toolscache "k8s.io/client-go/tools/cache"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

func buildRunningPod(namespace, name string, nodeSelector map[string]string, nodeName string) *corev1.Pod {
	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", namespace, name)),
			Name:      name,
			Namespace: namespace,
		},
		Spec: corev1.PodSpec{
			NodeSelector: nodeSelector,
			NodeName:     nodeName,
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
		},
	}
}

func buildNodeWithLabelsForStateMachine(name string, labels map[string]string) *corev1.Node {
	return &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   name,
			Labels: labels,
		},
		Spec: corev1.NodeSpec{
			ProviderID: "cce://fake-i-qI1XJAgP",
		},
		Status: corev1.NodeStatus{
			Conditions: []corev1.NodeCondition{
				{
					Type:   "ReadonlyFilesystem",
					Status: "True",
				},
			},
		},
	}
}

func TestNodeState(t *testing.T) {

	serverRunOptions := options.NewServerRunOptions()
	serverRunOptions.AddFlags(flag.CommandLine)
	serverRunOptions.NodeOptions.PodExitStateHoldTime = 1 * time.Second
	k8sclient := k8sfake.NewSimpleClientset()
	client := fake.NewFakeClient()
	// 调用CCE接口获取CCEInstanceID补充至nodeLabel, 供后续GC时使用
	cceClient := NewfakeCceClient(false)

	informerFactory := informers.NewSharedInformerFactory(k8sclient, 0)
	podInformer := informerFactory.Core().V1().Pods().Informer()
	nodeInformer := informerFactory.Core().V1().Nodes().Informer()
	cmInformer := informerFactory.Core().V1().ConfigMaps().Informer()
	podIndexer := podInformer.GetIndexer()
	nodeIndexer := nodeInformer.GetIndexer()

	controller := NewStateMachineController(serverRunOptions.NodeOptions, client, cceClient, podInformer, nodeInformer, cmInformer)

	// 需要节点组管理的节点的pod相关信息
	pod1 := buildRunningPod("ns1", "pod1", map[string]string{
		entity.InstanceGroupKey: "ig1",
		entity.TenantLockKey:    "user1",
	}, "node1")

	sysPod1 := buildRunningPod("ns1", "sysPod1", map[string]string{
		entity.InstanceGroupKey: "ig1",
	}, "node1")

	daemonsetPod1 := buildRunningPod("ns1", "daemonsetPod1", map[string]string{}, "node1")
	daemonsetPod1.OwnerReferences = append(daemonsetPod1.OwnerReferences, metav1.OwnerReference{
		Kind: "DaemonSet",
	})

	replicaSetPod1 := buildRunningPod("kube-system", "replicaSetPod-placeholder", map[string]string{}, "node1")
	replicaSetPod1.OwnerReferences = append(replicaSetPod1.OwnerReferences, metav1.OwnerReference{
		Kind: "ReplicaSet",
	})

	// 竞价类型的pod相关信息
	bidPod1 := buildRunningPod("ns1", "bidPod1", map[string]string{
		entity.NodeLabelInstanceTemplateKey: "ig1",
		entity.TenantLockKey:                "user1",
	}, "bidNode1")

	sysBidPod1 := buildRunningPod("ns1", "sysBidPod1", map[string]string{
		entity.NodeLabelInstanceTemplateKey: "ig1",
	}, "bidNode1")

	daemonsetBidPod1 := buildRunningPod("ns1", "daemonsetBidPod1", map[string]string{}, "bidNode1")
	daemonsetPod1.OwnerReferences = append(daemonsetPod1.OwnerReferences, metav1.OwnerReference{
		Kind: "DaemonSet",
	})

	// 初始化为空node
	node1 := buildNodeWithLabelsForStateMachine("node1", map[string]string{
		entity.InstanceGroupKey:      "ig1",
		entity.NodeLabelCCEClusterID: "test",
		"kubernetes.io/stargz-ready": "true",
		// entity.TenantLockKey:    "user1",
	})
	// 非节点组node
	node2 := buildNodeWithLabelsForStateMachine("node2", map[string]string{
		entity.NodeLabelCCEClusterID: "test",
		"kubernetes.io/stargz-ready": "true",
	})

	// 竞价实例的node
	bidNode1 := buildNodeWithLabelsForStateMachine("bidNode1", map[string]string{
		entity.NodeLabelChargingTypeKey:     string(bccapi.PaymentTimingBidding),
		entity.NodeLabelInstanceTemplateKey: "ig1",
		entity.NodeLabelCCEClusterID:        "test",
		"kubernetes.io/stargz-ready": "true",
		// entity.TenantLockKey:    "user1",
	})

	_ = nodeIndexer.Add(node1)
	_ = nodeIndexer.Add(node2)
	_ = nodeIndexer.Add(bidNode1)

	_ = podIndexer.Add(sysPod1)
	_ = podIndexer.Add(sysBidPod1)
	_ = podIndexer.Add(daemonsetPod1)
	_ = podIndexer.Add(daemonsetBidPod1)
	_ = podIndexer.Add(replicaSetPod1)
	_ = client.Create(context.Background(), pod1)
	_ = client.Create(context.Background(), bidPod1)
	_ = client.Create(context.Background(), sysPod1)
	_ = client.Create(context.Background(), sysBidPod1)
	_ = client.Create(context.Background(), daemonsetPod1)
	_ = client.Create(context.Background(), daemonsetBidPod1)
	_ = client.Create(context.Background(), replicaSetPod1)
	_ = client.Create(context.Background(), node1)
	_ = client.Create(context.Background(), node2)
	_ = client.Create(context.Background(), bidNode1)

	// 期望情况: node1,2被放入Nodes, bidNode1被放入BidNodes
	controller.updateNode(nil, node1)
	controller.updateNode(nil, node2)
	controller.updateNode(nil, bidNode1)

	// 系统pod, 期望情况: pod被放入到对应的nodeInfo中并更新了node的状态为init
	controller.addPod(sysPod1)
	controller.addPod(sysBidPod1)

	if controller.Nodes["node1"].State != entity.NodeStateInit {
		t.Errorf("node state != init , state %+v ", controller.Nodes["node1"].State)
		return
	}
	if controller.BidNodes["bidNode1"].State != entity.NodeStateInit {
		t.Errorf("bidNode state != init , state %+v ", controller.BidNodes["bidNode1"].State)
		return
	}

	// 期望情况: node1和bidNode1状态不变, node2不被处理, 状态仍然为空
	controller.syncNodes()
	if controller.Nodes["node2"].State != entity.NodeStateEmpty {
		t.Errorf("node2 state != empty , state %+v ", controller.Nodes["node2"].State)
		return
	}

	// daemonset pod 期望情况: pod被放入到nodeInfo中并触发node状态更新, 状态无变化, 仍为init
	controller.addPod(daemonsetPod1)
	controller.updatePod(nil, daemonsetPod1)
	controller.addPod(daemonsetBidPod1)
	controller.updatePod(nil, daemonsetBidPod1)

	if controller.Nodes["node1"].State != entity.NodeStateInit {
		t.Errorf("node state != init")
		return
	}
	if controller.BidNodes["bidNode1"].State != entity.NodeStateInit {
		t.Errorf("bidNode state != init , state %+v ", controller.BidNodes["bidNode1"].State)
		return
	}

	node1.Labels[entity.TenantLockKey] = "user1"
	bidNode1.Labels[entity.TenantLockKey] = "user1"

	podIndexer.Add(pod1)
	podIndexer.Add(bidPod1)

	// bci pod 期望情况: pod被放入到nodeInfo中并触发node状态更新为runPod
	controller.addPod(pod1)
	controller.addPod(bidPod1)

	if controller.Nodes["node1"].State != entity.NodeStateRunPod {
		t.Errorf("node state != RunPod")
		return
	}

	node1.Labels[entity.NodeStateLabelKey] = entity.NodeStateRunPod.String()
	// 删除bci pod 期望情况: nodeInfo缓存中会删除对应的pod, 状态会更新为podExit
	deletePod := k8s_cache.DeletedFinalStateUnknown{
		Key: pod1.Name,
		Obj: pod1,
	}

	_ = podIndexer.Delete(pod1)
	_ = podIndexer.Delete(bidPod1)

	controller.deletePod(deletePod)
	controller.deletePod(k8s_cache.DeletedFinalStateUnknown{
		Key: bidPod1.Name,
		Obj: bidPod1,
	})

	fmt.Println(controller.Nodes["node1"].State)
	if controller.Nodes["node1"].State != entity.NodeStatePodExit {
		t.Errorf("node state != podExit")
		return
	}

	// 期望情况: addPod后node1中新增了replicaSetPod, 在updateNode时会触发删除操作
	podExitNodeCopy := node1.DeepCopy()
	podExitNodeCopy.Labels[entity.NodeStateLabelKey] = entity.NodeStatePodExit.String()
	podExitNodeCopy.Labels[entity.AllPodExitTimeLabelKey] = strconv.FormatInt(time.Now().Unix(), 10)
	controller.addPod(replicaSetPod1)
	controller.updateNode(node1, podExitNodeCopy)
	_ = nodeIndexer.Update(podExitNodeCopy)

	// node 状态机 cool
	node1.Labels[entity.NodeStateLabelKey] = entity.NodeStatePodExit.String()
	node1.Labels[entity.AllPodExitTimeLabelKey] = strconv.FormatInt(time.Now().Unix(), 10)
	bidNode1.Labels[entity.NodeStateLabelKey] = entity.NodeStatePodExit.String()
	bidNode1.Labels[entity.AllPodExitTimeLabelKey] = strconv.FormatInt(time.Now().Unix(), 10)
	time.Sleep(3 * time.Second)
	controller.syncNodes()
	controller.nodeStateStatistics()
	if controller.Nodes["node1"].State != entity.NodeStateCool {
		t.Errorf("node state != StateCool")
		return
	}
	if controller.BidNodes["bidNode1"].State != entity.NodeStateCool {
		t.Errorf("bidNode state != StateCool")
		return
	}

	// node not ready
	copyNode := node1.DeepCopy()
	copyNode.Status.Conditions = []corev1.NodeCondition{
		{
			Type:   corev1.NodeReady,
			Status: corev1.ConditionFalse,
		},
		{
			Type:   "ReadonlyFilesystem",
			Status: "True",
		},
	}
	d, _ := time.ParseDuration("-6m")
	copyNode.Labels[entity.NotReadyStartTimeKey] = strconv.FormatInt(time.Now().Add(d).Unix(), 10)
	
	// Update the node info directly in the controller
	controller.Nodes["node1"].UpdateNode(copyNode)
	
	// Add a small delay to allow the informer to sync
	time.Sleep(100 * time.Millisecond)

	// Add a small delay to allow the informer to sync
	time.Sleep(100 * time.Millisecond)

	podIndexer.Add(pod1)
	// bci pod
	controller.addPod(pod1)

	copyBidNode := bidNode1.DeepCopy()
	copyBidNode.Status.Conditions = []corev1.NodeCondition{
		{
			Type:   corev1.NodeReady,
			Status: corev1.ConditionFalse,
		},
		{
			Type:   "ReadonlyFilesystem",
			Status: "True",
		},
	}
	// Update the bid node info directly in the controller
	controller.BidNodes["bidNode1"].UpdateNode(copyBidNode)

	// Call syncNodeState directly instead of syncNodes to avoid informer cache issues
	if err := controller.syncNodeState(controller.Nodes["node1"]); err != nil {
		t.Errorf("syncNodeState failed: %v", err)
		return
	}
	
	if err := controller.syncNodeState(controller.BidNodes["bidNode1"]); err != nil {
		t.Errorf("syncNodeState failed: %v", err)
		return
	}

	_ = podIndexer.Delete(pod1)
	controller.deletePod(pod1)

	if controller.Nodes["node1"].State != entity.NodeStateNotReady {
		t.Errorf("node state != NodeStateNotReady")
		return
	}
	if controller.BidNodes["bidNode1"].State != entity.NodeStateNotReady {
		t.Errorf("bidNode state != NodeStateNotReady")
		return
	}

	// notReady -> Ready
	copyNode1 := node1.DeepCopy()
	copyNode1.Labels[entity.NodeStateLabelKey] = entity.NodeStateNotReady.String()
	copyNode1.Spec.Taints = append(copyNode1.Spec.Taints, corev1.Taint{})
	controller.updateNode(node1, copyNode1)

	_ = client.Update(context.Background(), copyNode1)
	_ = nodeIndexer.Update(copyNode1)

	copyBidNode1 := bidNode1.DeepCopy()
	copyBidNode1.Labels[entity.NodeStateLabelKey] = entity.NodeStateNotReady.String()
	controller.updateNode(bidNode1, copyBidNode1)

	_ = client.Update(context.Background(), copyBidNode1)
	_ = nodeIndexer.Update(copyBidNode1)

	controller.syncNodes()
	controller.nodeStateStatistics()

	if controller.Nodes["node1"].State != entity.NodeStatePodExit {
		t.Errorf("node state != NodeStatePodExit, state %+v ", controller.Nodes["node1"].State)
		return
	}
	if controller.BidNodes["bidNode1"].State != entity.NodeStatePodExit {
		t.Errorf("bidNode state != NodeStatePodExit, state %+v ", controller.BidNodes["bidNode1"].State)
		return
	}

	stopChan := make(<-chan struct{})

	controller.Start(stopChan)

	deleteNode := k8s_cache.DeletedFinalStateUnknown{
		Key: node1.Name,
		Obj: node1,
	}
	controller.deleteNode(deleteNode)
	controller.deleteNode(k8s_cache.DeletedFinalStateUnknown{
		Key: bidNode1.Name,
		Obj: bidNode1,
	})

	time.Sleep(3 * time.Second)
	fmt.Println(len(controller.Nodes))
	fmt.Println(len(controller.BidNodes))
	// informerFactory.Start(stopChan)
	// informerFactory.WaitForCacheSync(stopChan)

}

func Test_NewDefaultNodeController(t *testing.T) {
	k8sclient := k8sfake.NewSimpleClientset()

	informerFactory := informers.NewSharedInformerFactory(k8sclient, 0)
	podInformer := informerFactory.Core().V1().Pods().Informer()
	nodeInformer := informerFactory.Core().V1().Nodes().Informer()
	podLister := corev1_listers.NewPodLister(podInformer.(toolscache.SharedIndexInformer).GetIndexer())
	nodeLister := corev1_listers.NewNodeLister(nodeInformer.(toolscache.SharedIndexInformer).GetIndexer())

	NewDefaultNodeController(podLister, nodeLister, options.NewServerRunOptions(), NewfakeCceClient(false))
}
