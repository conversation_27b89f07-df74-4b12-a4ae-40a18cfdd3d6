package node

import (
	"context"
	"sync"
	"time"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime/inject"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	toolscache "k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"sigs.k8s.io/controller-runtime/pkg/source"
)

var _ reconcile.Reconciler = &Controller{}
var _ inject.StartFuncInjector = &Controller{}

// New 创建node controller，此处无法实现单测
func New(option *options.ServerRunOptions, mgr manager.Manager) (interface{}, error) {
	cache := mgr.GetCache()
	podInformer, err := cache.GetInformer(context.TODO(), &corev1.Pod{})
	if err != nil {
		klog.Errorf("nodeController get podInformer err %+v ", err)
		return nil, err
	}
	nodeInformer, err := cache.GetInformer(context.TODO(), &corev1.Node{})
	if err != nil {
		klog.Errorf("nodeController get nodeInformer err %+v ", err)
		return nil, err
	}
	podLister := corev1_listers.NewPodLister(podInformer.(toolscache.SharedIndexInformer).GetIndexer())
	nodeLister := corev1_listers.NewNodeLister(nodeInformer.(toolscache.SharedIndexInformer).GetIndexer())
	cceClient, err := util.NewCceClient(option.CceOptions)
	if err != nil {
		klog.Errorf("nodeController NewCceClient err %+v ", err)
		return nil, err
	}

	// configmap informer
	cmInformer, err := cache.GetInformer(context.TODO(), &corev1.ConfigMap{})
	if err != nil {
		klog.Errorf("nodeController get configMapInformer err %+v ", err)
		return nil, err
	}
	c := NewDefaultNodeController(podLister, nodeLister, option, cceClient)
	c.client = mgr.GetClient()

	controller, err := controller.New("node-controller", mgr, controller.Options{
		Reconciler:              c,
		MaxConcurrentReconciles: option.NodeOptions.NodeControllerWorkers,
		RateLimiter:             util.DefaultControllerRateLimiter(option),
	})

	if err != nil {
		klog.Errorf("New nodeController err %+v ", err)
		return nil, err
	}

	// 当controller watch 有多个Kind时，每个Kind 有更新，都会触发Reconcile 函数
	if err = controller.Watch(source.Kind(mgr.GetCache(), &corev1.Pod{}), &handler.EnqueueRequestForObject{}, predicate.Funcs{
		UpdateFunc: func(ue event.UpdateEvent) bool {
			newPod := ue.ObjectNew.(*corev1.Pod)
			return c.shouldHandlePod(newPod)
		},
	}); err != nil {
		klog.Errorf("New nodeController Watch event err %+v ", err)
		return nil, err
	}

	c.stateController = NewStateMachineController(option.NodeOptions, mgr.GetClient(), cceClient, podInformer, nodeInformer, cmInformer)
	return c, nil
}

func NewDefaultNodeController(podLister corev1_listers.PodLister, nodeLister corev1_listers.NodeLister,
	option *options.ServerRunOptions, cceClient util.CceClient) *Controller {
	return &Controller{
		podLister:            podLister,
		nodeLister:           nodeLister,
		instanceGroupLockMap: make(map[string]*sync.Mutex),
		bidInstanceLockMap:   make(map[string]*sync.Mutex),
		options:              option,
		cceClient:            cceClient,
	}
}

// Controller 节点组controller实现
type Controller struct {
	sync.RWMutex

	podLister  corev1_listers.PodLister
	nodeLister corev1_listers.NodeLister

	// 每个节点组一个排他锁，Reconcile 函数是并发执行的
	instanceGroupLockMap map[string]*sync.Mutex
	// InstanceTemplateID_(market/price_10) : Mutex
	bidInstanceLockMap map[string]*sync.Mutex

	// 与k8s apiserver 交互的client
	client client.Client

	stateController *StateMachineController

	options *options.ServerRunOptions

	cceClient util.CceClient
	// 存储锁定node成功的pod信息
	patchPodStore sync.Map
}

// Reconcile 处理pod pending 触发node 打标签操作
func (ssc *Controller) Reconcile(_ context.Context, request reconcile.Request) (res reconcile.Result, retErr error) {
	key := request.NamespacedName.String()
	startTime := time.Now()

	podKey := podKeyFn(request.Namespace, request.Name)
	defer func() {
		costTime := time.Since(startTime)
		klog.V(4).Infof("nodeController Reconcile pod %s cost %+v , result %+v ", key, costTime, res)
	}()

	pod, err := ssc.podLister.Pods(request.Namespace).Get(request.Name)
	if errors.IsNotFound(err) {
		klog.Infof("pod has been deleted %v", key)
		// pod 删除，删除锁定node成功的pod
		ssc.deletePatchPodIfPossible(podKey)
		return reconcile.Result{}, nil
	}
	if err != nil {
		klog.Errorf("nodeController podLister get pod %s err %+v ", key, err)
		return reconcile.Result{}, err
	}

	// 此处有可能处理失败重试后调度成功，因此直接返回
	if !ssc.shouldHandlePod(pod) {
		return reconcile.Result{}, nil
	}

	klog.V(4).Infof("nodeController pending pod <%s/%s> try tenantLockNode ", pod.Namespace, pod.Name)
	// TODO 待补充event
	// TODO 待补充监控，锁节点qps、失败qps
	return ssc.tenantLockNode(pod)
}

func (ssc *Controller) Start(stopChan <-chan struct{}) error {
	// 选主成功后，启动状态机controller
	ssc.stateController.Start(stopChan)

	go func() {
		ticker := time.NewTicker(ssc.options.NodeOptions.MonitorPendingPodsInterval)
		time.Sleep(ssc.options.NodeOptions.MonitorPendingPodsInterval)

		for {
			select {
			case <-stopChan:
				return
			case <-ticker.C:
				ssc.increaseInstanceGroupMaxNodeCountTask()
				ssc.createBidNodesTask()
			}
		}
	}()
	return nil
}
