package node

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"testing"
	"time"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	ccev2Types "github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/informers"
	k8sfake "k8s.io/client-go/kubernetes/fake"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	toolscache "k8s.io/client-go/tools/cache"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

func buildBasePod(namespace, name string, nodeSelector map[string]string, annotation map[string]string, status corev1.PodPhase, nodeName string) *corev1.Pod {
	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:         types.UID(fmt.Sprintf("%v-%v", namespace, name)),
			Name:        name,
			Namespace:   namespace,
			Annotations: annotation,
		},
		Spec: corev1.PodSpec{
			NodeSelector: nodeSelector,
			NodeName:     nodeName,
		},
		Status: corev1.PodStatus{
			Phase: status,
			Conditions: []corev1.PodCondition{
				{
					Type:   corev1.PodScheduled,
					Status: corev1.ConditionUnknown,
				},
			},
		},
	}
}

func buildBciBidPendingPod(namespace, name string, nodeSelector map[string]string, annotation map[string]string, resource corev1.ResourceList) *corev1.Pod {
	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:         types.UID(fmt.Sprintf("%v-%v", namespace, name)),
			Name:        name,
			Namespace:   namespace,
			Annotations: annotation,
		},
		Spec: corev1.PodSpec{
			NodeSelector: nodeSelector,
			Containers: []corev1.Container{
				{
					Name: "container",
					Resources: corev1.ResourceRequirements{
						Requests: resource,
						Limits:   resource,
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
			Conditions: []corev1.PodCondition{
				{
					Type:   corev1.PodScheduled,
					Status: corev1.ConditionUnknown,
				},
			},
		},
	}
}

var (
	instanceTemplateID = "ig1"
	marketNodeSelector = map[string]string{
		entity.NodeLabelInstanceTemplateKey: instanceTemplateID,
		entity.TenantLockKey:                "user1",
		entity.NodeLabelBidModeKey:          entity.NodeLabelBidModeMarketValue,
	}
	priceNodeSelector = map[string]string{
		entity.NodeLabelInstanceTemplateKey: instanceTemplateID,
		entity.TenantLockKey:                "user1",
		entity.NodeLabelBidModeKey:          entity.NodeLabelBidModePriceValue,
		entity.NodeLabelBidPriceKey:         "10",
	}
	marketAnnotation = map[string]string{
		entity.NodeLabelInstanceTemplateKey: instanceTemplateID,
		entity.BciBidModeKey:                entity.NodeLabelBidModeMarketValue,
		entity.BciBidTimeOutKey:             "60",
		entity.BciBidReleaseEIPKey:          "False",
		entity.BciBidReleaseCDSKey:          "False",
	}
	priceAnnotation = map[string]string{
		entity.NodeLabelInstanceTemplateKey: instanceTemplateID,
		entity.BciBidModeKey:                entity.NodeLabelBidModePriceValue,
		entity.BciBidPriceKey:               "10",
		entity.BciBidTimeOutKey:             "60",
		entity.BciBidReleaseEIPKey:          "False",
		entity.BciBidReleaseCDSKey:          "False",
	}
	priceNodeLabel = map[string]string{
		entity.NodeLabelInstanceTemplateKey: instanceTemplateID,
		entity.NodeLabelChargingTypeKey:     string(bccapi.PaymentTimingBidding),
		entity.TenantLockKey:                "user1",
		entity.NodeLabelBidModeKey:          entity.NodeLabelBidModePriceValue,
		entity.NodeLabelBidPriceKey:         "10",
	}
)

func TestController_createBidNodesTask(t *testing.T) {

	tests := []struct {
		name         string
		pods         []*corev1.Pod
		bidNodes     []*corev1.Node
		cceErr       bool
		expectResult *entity.BidPriceStatistics
	}{
		{
			name: "正常扩容",
			pods: []*corev1.Pod{
				buildBciBidPendingPod("ns1", "bidPod1", marketNodeSelector, marketAnnotation, buildResourceList("1", "1Gi")),
				buildBciBidPendingPod("ns1", "bidPod2", priceNodeSelector, priceAnnotation, buildResourceList("1", "1Gi")),
				buildBciBidPendingPod("ns1", "bidPod3", priceNodeSelector, priceAnnotation, buildResourceList("1", "1Gi")),
				buildDaemonsetPod("ns1", "daemonset1", buildResourceList("0.5", "0.5Gi"), "bidNode1"),
				buildRunningPod("ns1", "runpod1", map[string]string{}, "bidNode1"),
			},
			bidNodes: []*corev1.Node{
				buildNodeWithResource("bidNode1", priceNodeLabel, buildResourceList("2", "4Gi")),
			},
			expectResult: &entity.BidPriceStatistics{CountInfo: map[string]int{"market": 1, "10": 2}},
		},
		{
			name: "调用cce扩容失败",
			pods: []*corev1.Pod{
				buildBciBidPendingPod("ns1", "bidPod1", marketNodeSelector, marketAnnotation, buildResourceList("1", "1Gi")),
				buildRunningPod("ns1", "runpod1", map[string]string{}, "n1"),
			},
			bidNodes: []*corev1.Node{
				buildNodeWithResource("bidNode1", priceNodeLabel, buildResourceList("2", "4Gi")),
			},
			cceErr:       true,
			expectResult: &entity.BidPriceStatistics{},
		},
		{
			name: "node 在扩容周期内，不需要扩容",
			pods: []*corev1.Pod{
				func() *corev1.Pod {
					pod := buildBciBidPendingPod("ns1", "bidPod1", marketNodeSelector, map[string]string{
						entity.NodeLabelInstanceTemplateKey: instanceTemplateID,
						entity.BciBidModeKey:                entity.NodeLabelBidModeMarketValue,
						entity.BciBidTimeOutKey:             "60",
						entity.BciBidReleaseEIPKey:          "False",
						entity.BciBidReleaseCDSKey:          "False",
						lastIncreaseTimeKey:                 strconv.FormatInt(time.Now().Unix(), 10),
					}, buildResourceList("1", "1Gi"))
					return pod
				}(),
				buildRunningPod("ns1", "runpod1", map[string]string{}, "n1"),
			},
			bidNodes: []*corev1.Node{
				buildNodeWithResource("bidNode1", priceNodeLabel, buildResourceList("2", "4Gi")),
			},
			expectResult: &entity.BidPriceStatistics{},
		},
		{
			name: "超过node扩容周期",
			pods: []*corev1.Pod{
				func() *corev1.Pod {
					d, _ := time.ParseDuration("-600s")
					pod := buildBciBidPendingPod("ns1", "bidPod1", marketNodeSelector, map[string]string{
						entity.NodeLabelInstanceTemplateKey: instanceTemplateID,
						entity.BciBidModeKey:                entity.NodeLabelBidModeMarketValue,
						entity.BciBidTimeOutKey:             "60",
						entity.BciBidReleaseEIPKey:          "False",
						entity.BciBidReleaseCDSKey:          "False",
						lastIncreaseTimeKey:                 strconv.FormatInt(time.Now().Add(d).Unix(), 10),
					}, buildResourceList("1", "1Gi"))
					return pod
				}(),
				buildRunningPod("ns1", "runpod1", map[string]string{}, "n1"),
			},
			bidNodes: []*corev1.Node{
				buildNodeWithResource("bidNode1", priceNodeLabel, buildResourceList("2", "4Gi")),
			},
			expectResult: &entity.BidPriceStatistics{CountInfo: map[string]int{"market": 1}},
		},
		{
			name: "pod 资源大于node资源",
			pods: []*corev1.Pod{
				buildBciBidPendingPod("ns1", "bidPod1", priceNodeSelector, marketAnnotation, buildResourceList("1", "1Gi")),
				buildRunningPod("ns1", "runpod1", map[string]string{}, "n1"),
			},
			bidNodes: []*corev1.Node{
				buildNodeWithResource("bidNode1", priceNodeLabel, buildResourceList("0.5", "1Gi")),
			},
			expectResult: &entity.BidPriceStatistics{},
		},
	}
	for i, test := range tests {
		if i != 0 {
			// continue
		}
		t.Run(test.name, func(t *testing.T) {
			k8sclient := k8sfake.NewSimpleClientset()
			client := fake.NewFakeClient()

			informerFactory := informers.NewSharedInformerFactory(k8sclient, 0)
			podInformer := informerFactory.Core().V1().Pods().Informer()
			nodeInformer := informerFactory.Core().V1().Nodes().Informer()
			podLister := corev1_listers.NewPodLister(podInformer.(toolscache.SharedIndexInformer).GetIndexer())
			nodeLister := corev1_listers.NewNodeLister(nodeInformer.(toolscache.SharedIndexInformer).GetIndexer())

			c := Controller{
				podLister:            podLister,
				nodeLister:           nodeLister,
				instanceGroupLockMap: make(map[string]*sync.Mutex),
				client:               client,
				cceClient:            NewfakeCceClient(test.cceErr),
				options:              options.NewServerRunOptions(),
			}

			for _, pod := range test.pods {
				client.Create(context.Background(), pod)
				podInformer.GetIndexer().Add(pod)
			}
			for _, node := range test.bidNodes {
				client.Create(context.Background(), node)
				nodeInformer.GetIndexer().Add(node)
			}

			resp := c.createBidNodesTask()
			bytes, _ := json.Marshal(resp)
			fmt.Println(test.name)
			fmt.Println("response: " + string(bytes))
			expect, _ := json.Marshal(test.expectResult)
			fmt.Println("expect: " + string(expect))
			fmt.Println()
		})
	}
}

func Test_constructBidOption(t *testing.T) {

	tests := []struct {
		name    string
		pod     *corev1.Pod
		wantErr bool
	}{
		{
			name: "bidPrice为空",
			pod: &corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Name:        "name",
					Namespace:   "namespace",
					Annotations: map[string]string{entity.BciBidModeKey: string(ccev2Types.BidModeCustomPrice)},
				},
			},
			wantErr: true,
		},
		{
			name: "bidTimeOut not int",
			pod: &corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Name:        "name",
					Namespace:   "namespace",
					Annotations: map[string]string{entity.BciBidModeKey: string(ccev2Types.BidModeMarketPrice), entity.BciBidTimeOutKey: "notInt"},
				},
			},
			wantErr: true,
		},
		{
			name: "bidReleaseEIP not bool",
			pod: &corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "name",
					Namespace: "namespace",
					Annotations: map[string]string{
						entity.BciBidModeKey:       string(ccev2Types.BidModeMarketPrice),
						entity.BciBidTimeOutKey:    "10",
						entity.BciBidReleaseEIPKey: "notBool"},
				},
			},
			wantErr: true,
		},
		{
			name: "bidReleaseCDS not bool",
			pod: &corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "name",
					Namespace: "namespace",
					Annotations: map[string]string{
						entity.BciBidModeKey:       string(ccev2Types.BidModeMarketPrice),
						entity.BciBidTimeOutKey:    "10",
						entity.BciBidReleaseEIPKey: "false",
						entity.BciBidReleaseCDSKey: "notBool",
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := constructBidOption(tt.pod)
			if (err != nil) != tt.wantErr {
				t.Errorf("constructBidOption() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestPatchPodLastIncreaseTimeAnnotation(t *testing.T) {
	pods := []*corev1.Pod{buildBciBidPendingPod("ns1", "bidPod1", marketNodeSelector, marketAnnotation, buildResourceList("1", "1Gi"))}
	k8sclient := k8sfake.NewSimpleClientset()
	client := fake.NewFakeClient()

	informerFactory := informers.NewSharedInformerFactory(k8sclient, 0)
	podInformer := informerFactory.Core().V1().Pods().Informer()
	nodeInformer := informerFactory.Core().V1().Nodes().Informer()
	podLister := corev1_listers.NewPodLister(podInformer.(toolscache.SharedIndexInformer).GetIndexer())
	nodeLister := corev1_listers.NewNodeLister(nodeInformer.(toolscache.SharedIndexInformer).GetIndexer())

	c := Controller{
		podLister:            podLister,
		nodeLister:           nodeLister,
		instanceGroupLockMap: make(map[string]*sync.Mutex),
		client:               client,
		cceClient:            NewfakeCceClient(false),
		options:              options.NewServerRunOptions(),
	}
	for _, pod := range pods {
		err := client.Create(context.Background(), pod)
		if err != nil {
			t.Errorf("TestPatchPodLastIncreaseTimeAnnotation err %+v", err)
			return
		}
	}
	c.patchPodLastIncreaseTimeAnnotation(pods)
}

func Test_shouldDealPendingPod(t *testing.T) {
	tests := []struct {
		name string
		pod  *corev1.Pod
		want bool
	}{
		{
			name: "新创建不处理",
			pod: func() *corev1.Pod {
				pod := buildBasePod("ns1", "bidPod1", marketNodeSelector, marketAnnotation, corev1.PodPending, "")
				pod.CreationTimestamp.Time = time.Now()
				return pod
			}(),
			want: false,
		},
		{
			name: "未增加过node处理",
			pod:  buildBasePod("ns1", "bidPod1", marketNodeSelector, marketAnnotation, corev1.PodPending, ""),
			want: true,
		},
		{
			name: "未超时不处理",
			pod: buildBasePod("ns1", "bidPod1", marketNodeSelector,
				map[string]string{lastIncreaseTimeKey: strconv.FormatInt(time.Now().Unix(), 10)}, corev1.PodPending, ""),
			want: false,
		},
		{
			name: "超时处理",
			pod: func() *corev1.Pod {
				d, _ := time.ParseDuration("-1800s")
				pod := buildBasePod("ns1", "bidPod1", marketNodeSelector,
					map[string]string{lastIncreaseTimeKey: strconv.FormatInt(time.Now().Add(d).Unix(), 10)}, corev1.PodPending, "")
				return pod
			}(),
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := shouldDealBidPendingPod(tt.pod); got != tt.want {
				t.Errorf("shouldDealPendingPod() = %v, want %v", got, tt.want)
			}
		})
	}
}
