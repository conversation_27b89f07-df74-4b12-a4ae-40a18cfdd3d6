package node

import (
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	corev1 "k8s.io/api/core/v1"
	k8s_cache "k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"
)

func (c *StateMachineController) addPod(obj interface{}) {
	c.Lock()
	defer c.Unlock()
	pod := obj.(*corev1.Pod)
	klog.V(4).Infof("StateMachineController receive add pod %s event ", pod.Name)
	c.podSyncFn(pod, func(nodeInfo *entity.NodeInfo) {
		nodeInfo.AddOrUpdatePod(pod)
	})
}

func (c *StateMachineController) updatePod(oldObj, newObj interface{}) {
	c.Lock()
	defer c.Unlock()
	pod := newObj.(*corev1.Pod)
	klog.V(4).Infof("StateMachineController receive update pod %s event ", pod.Name)
	c.podSyncFn(pod, func(nodeInfo *entity.NodeInfo) {
		nodeInfo.AddOrUpdatePod(pod)
	})
}

func (c *StateMachineController) deletePod(obj interface{}) {
	c.Lock()
	defer c.Unlock()
	pod, ok := obj.(*corev1.Pod)
	if !ok {
		tombstone, ok := obj.(k8s_cache.DeletedFinalStateUnknown)
		if !ok {
			klog.V(4).Infof("StateMachineController delete pod %+v is not DeletedFinalStateUnknown event ", obj)
			return
		}
		pod, ok = tombstone.Obj.(*corev1.Pod)
		if !ok {
			klog.V(4).Infof("StateMachineController delete pod %+v is not tombstone pod event ", tombstone)
			return
		}
	}
	klog.V(4).Infof("StateMachineController receive delete pod %s event ", pod.Name)
	c.podSyncFn(pod, func(nodeInfo *entity.NodeInfo) {
		nodeInfo.DeletePod(pod)
	})
}

func (c *StateMachineController) updateNode(oldObj, newObj interface{}) {
	c.Lock()
	defer c.Unlock()
	node := newObj.(*corev1.Node)
	klog.V(4).Infof("StateMachineController node informer update node %s ", node.Name)
	// 通过是否含有CCE集群标签判断节点是否创建完成
	if !entity.K8sNodeIsInitComplete(node) {
		return
	}
	nodeIsBid := entity.K8sNodeIsBid(node)

	nodeInfo, ok := c.Nodes[node.Name]
	if nodeIsBid {
		nodeInfo, ok = c.BidNodes[node.Name]
	}

	if !ok {
		nodeInfo = entity.NewNodeInfo(node)
		if nodeIsBid {
			c.BidNodes[node.Name] = nodeInfo
		} else {
			c.Nodes[node.Name] = nodeInfo
		}
	} else {
		nodeInfo.UpdateNode(node)
	}

	if !nodeIsBid {
		c.deletePlaceholderPodIfPossible(nodeInfo)
	}
}

func (c *StateMachineController) deleteNode(obj interface{}) {
	c.Lock()
	defer c.Unlock()
	node, ok := obj.(*corev1.Node)
	if !ok {
		tombstone, ok := obj.(k8s_cache.DeletedFinalStateUnknown)
		if !ok {
			klog.V(4).Infof("StateMachineController delete node %+v is not DeletedFinalStateUnknown event ", obj)
			return
		}
		node, ok = tombstone.Obj.(*corev1.Node)
		if !ok {
			klog.V(4).Infof("StateMachineController delete node %+v is not tombstone node event ", tombstone)
			return
		}
	}
	klog.Infof("StateMachineController node informer delete node %s ", node.Name)
	nodeIsBid := entity.K8sNodeIsBid(node)
	if nodeIsBid {
		delete(c.BidNodes, node.Name)
	} else {
		delete(c.Nodes, node.Name)
	}
}
