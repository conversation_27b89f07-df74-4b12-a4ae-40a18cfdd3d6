package node

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"sync"
	"testing"
	"time"

	ccev2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	ccev2_types "github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/informers"
	k8sfake "k8s.io/client-go/kubernetes/fake"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	toolscache "k8s.io/client-go/tools/cache"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

type fakeCceClient struct {
	hasErr bool
}

func NewfakeCceClient(hasErr bool) util.CceClient {
	return &fakeCceClient{
		hasErr: hasErr,
	}
}

func (fake *fakeCceClient) ChangeInstanceGroupAutoscalerConfig(enableAutoscaler bool, instanceGroupID string, maxReplicas int) (
	*ccev2.UpdateInstanceGroupClusterAutoscalerSpecResponse, error) {
	res := &ccev2.UpdateInstanceGroupClusterAutoscalerSpecResponse{
		CommonResponse: ccev2.CommonResponse{
			RequestID: "reqId",
		},
	}
	if fake.hasErr {
		return nil, errors.New("err")
	}
	return res, nil
}

func (fake *fakeCceClient) GetInstanceGroupNodes(instanceGroupID string) (*ccev2.ListInstancesByInstanceGroupIDResponse, error) {
	return nil, nil
}

func (fake *fakeCceClient) GetInstanceGroupDetail(instanceGroupID string) (*ccev2.GetInstanceGroupResponse, error) {
	res := &ccev2.GetInstanceGroupResponse{
		CommonResponse: ccev2.CommonResponse{
			RequestID: "reqId",
		},
		InstanceGroup: &ccev2.InstanceGroup{
			Spec: &ccev2.InstanceGroupSpec{
				ClusterAutoscalerSpec: &ccev2.ClusterAutoscalerSpec{
					MinReplicas: 1,
					MaxReplicas: 1,
				},
			},
		},
	}
	if fake.hasErr {
		return nil, errors.New("err")
	}
	return res, nil
}

func (fake *fakeCceClient) RemoveInstanceGroupNodes(instanceGroupID string, removeInstances []string) (*ccev2.CreateTaskResp, error) {
	res := &ccev2.CreateTaskResp{
		CommonResponse: ccev2.CommonResponse{
			RequestID: "reqId",
		},
	}
	if fake.hasErr {
		return nil, errors.New("err")
	}
	return res, nil
}

func (fake *fakeCceClient) GetInstanceGroupList() (*ccev2.ListInstanceGroupResponse, error) {
	res := &ccev2.ListInstanceGroupResponse{}
	if fake.hasErr {
		return nil, errors.New("err")
	}
	return res, nil
}

func (fake *fakeCceClient) GetKubeconfig(clusterID string) (*ccev2.GetKubeConfigResponse, error) {
	return nil, nil
}

func (fake *fakeCceClient) GetInstance(instanceID string) (resp *ccev2.GetInstanceResponse, err error) {
	if fake.hasErr {
		return nil, errors.New("err")
	}
	resp = &ccev2.GetInstanceResponse{
		RequestID: "fake_reqId",
		Instance: &ccev2.Instance{
			Spec: &ccev2_types.InstanceSpec{
				CCEInstanceID: "fake_CCEInstanceID",
			},
		},
	}
	return resp, nil
}

func (fake *fakeCceClient) CreateInstances(instanceSet []*ccev2.InstanceSet) (resp *ccev2.CreateInstancesResponse, err error) {
	if fake.hasErr {
		return nil, errors.New("err")
	}
	CCEInstanceIDs := make([]string, 0)
	for index, instanceInfo := range instanceSet {
		for i := 0; i < instanceInfo.Count; i++ {
			CCEInstanceIDs = append(CCEInstanceIDs, "fake-CCEInstanceIDs-"+strconv.FormatInt(int64(index), 10)+"-"+strconv.FormatInt(int64(i), 10))
		}
	}
	resp = &ccev2.CreateInstancesResponse{
		CCEInstanceIDs: CCEInstanceIDs,
	}
	return resp, nil
}

func (fake *fakeCceClient) DeleteInstances(removeCCEInstanceIds []string) (resp *ccev2.DeleteInstancesResponse, err error) {
	return nil, nil
}

func (fake *fakeCceClient) InstanceGroupTasks(instanceGroupID string, pageNo, pageSize int) (resp *ccev2.ListTaskResp, err error) {
	return nil, nil
}

func buildResourceListWithGPU(cpu string, memory string, GPU string) corev1.ResourceList {
	return corev1.ResourceList{
		corev1.ResourceCPU:    resource.MustParse(cpu),
		corev1.ResourceMemory: resource.MustParse(memory),
		"nvidia.com/gpu":      resource.MustParse(GPU),
	}
}

func buildResourceList(cpu string, memory string) corev1.ResourceList {
	return corev1.ResourceList{
		corev1.ResourceCPU:    resource.MustParse(cpu),
		corev1.ResourceMemory: resource.MustParse(memory),
	}
}

func buildBciPendingPod(namespace, name string, nodeSelector map[string]string, resource corev1.ResourceList) *corev1.Pod {
	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:         types.UID(fmt.Sprintf("%v-%v", namespace, name)),
			Name:        name,
			Namespace:   namespace,
			Annotations: make(map[string]string),
		},
		Spec: corev1.PodSpec{
			NodeSelector: nodeSelector,
			Containers: []corev1.Container{
				{
					Name: "container",
					Resources: corev1.ResourceRequirements{
						Requests: resource,
						Limits:   resource,
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending,
			Conditions: []corev1.PodCondition{
				{
					Type:   corev1.PodScheduled,
					Status: corev1.ConditionUnknown,
				},
			},
		},
	}
}

func buildDaemonsetPod(namespace, name string, resource corev1.ResourceList, nodeName string) *corev1.Pod {
	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", namespace, name)),
			Name:      name,
			Namespace: namespace,
			OwnerReferences: []metav1.OwnerReference{
				{
					Kind: "DaemonSet",
				},
			},
		},
		Spec: corev1.PodSpec{
			NodeName: nodeName,
			Containers: []corev1.Container{
				{
					Name: "container",
					Resources: corev1.ResourceRequirements{
						Requests: resource,
						Limits:   resource,
					},
				},
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
		},
	}
}

func buildNodeWithResource(name string, labels map[string]string, resource corev1.ResourceList) *corev1.Node {
	return &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   name,
			Labels: labels,
		},
		Status: corev1.NodeStatus{
			Allocatable: resource,
		},
	}
}

func TestIncreaseInstanceGroupMaxNodeCountTask(t *testing.T) {

	tests := []struct {
		name              string
		pods              []*corev1.Pod
		nodes             []*corev1.Node
		instanceGroup     string
		cceErr            bool
		maxNodes          int
		increaseNodeCount int
	}{
		{
			name: "超过最大扩容node数",
			pods: []*corev1.Pod{
				buildBciPendingPod("ns1", "pod1", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("1", "1Gi")),
				buildBciPendingPod("ns1", "pod2", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("1", "1Gi")),
				buildBciPendingPod("ns1", "pod3", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("1", "1Gi")),
				buildDaemonsetPod("ns1", "daemonset1", buildResourceList("0.5", "0.5Gi"), "n1"),
				buildRunningPod("ns1", "runpod1", map[string]string{}, "n1"),
			},
			nodes: []*corev1.Node{
				buildNodeWithResource("n1", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("2", "4Gi")),
			},
			instanceGroup:     "ig1",
			maxNodes:          2,
			increaseNodeCount: 0,
		},
		{
			name: "调用cce扩容失败",
			pods: []*corev1.Pod{
				buildBciPendingPod("ns1", "pod1", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("1", "1Gi")),
				buildRunningPod("ns1", "runpod1", map[string]string{}, "n1"),
			},
			nodes: []*corev1.Node{
				buildNodeWithResource("n1", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("2", "4Gi")),
			},
			instanceGroup:     "ig1",
			cceErr:            true,
			increaseNodeCount: 0,
		},
		{
			name: "node 在扩容周期内，不需要扩容",
			pods: []*corev1.Pod{
				func() *corev1.Pod {
					pod := buildBciPendingPod("ns1", "pod1", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("1", "1Gi"))
					pod.Annotations[lastIncreaseTimeKey] = strconv.FormatInt(time.Now().Unix(), 10)
					return pod
				}(),
				buildRunningPod("ns1", "runpod1", map[string]string{}, "n1"),
			},
			nodes: []*corev1.Node{
				buildNodeWithResource("n1", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("0.5", "1Gi")),
			},
			instanceGroup:     "ig1",
			increaseNodeCount: 0,
		},
		{
			name: "包含daemonset pod，1pending bci pod",
			pods: []*corev1.Pod{
				func() *corev1.Pod {
					pod := buildBciPendingPod("ns1", "pod1", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("1", "1Gi"))
					d, _ := time.ParseDuration("-600s")
					pod.Annotations[lastIncreaseTimeKey] = strconv.FormatInt(time.Now().Add(d).Unix(), 10)
					return pod
				}(),

				buildBciPendingPod("ns1", "pod2", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("1", "1Gi")),
				buildBciPendingPod("ns1", "pod3", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("1", "1Gi")),
				buildRunningPod("ns1", "runpod1", map[string]string{}, "n1"),
				buildDaemonsetPod("ns1", "daemonset1", buildResourceList("0.5", "0.5Gi"), "n1"),
			},
			nodes: []*corev1.Node{
				buildNodeWithResource("n1", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("2", "4Gi")),
			},
			instanceGroup:     "ig1",
			increaseNodeCount: 0,
		},
		{
			name: "pod 资源大于node资源",
			pods: []*corev1.Pod{
				buildBciPendingPod("ns1", "pod1", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("1", "1Gi")),
				buildRunningPod("ns1", "runpod1", map[string]string{}, "n1"),
			},
			nodes: []*corev1.Node{
				buildNodeWithResource("n1", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("0.5", "1Gi")),
			},
			instanceGroup:     "ig1",
			increaseNodeCount: 0,
		},
		{
			name: "节点组无可用node",
			pods: []*corev1.Pod{
				buildBciPendingPod("ns1", "pod1", map[string]string{entity.InstanceGroupKey: "ig1", entity.TenantLockKey: "user1"}, buildResourceList("1", "1Gi")),
			},
			nodes:             []*corev1.Node{},
			instanceGroup:     "ig1",
			increaseNodeCount: 0,
		},
	}

	for i, test := range tests {
		if i != 0 {
			// continue
		}
		t.Run(test.name, func(t *testing.T) {
			k8sclient := k8sfake.NewSimpleClientset()
			client := fake.NewFakeClient()

			informerFactory := informers.NewSharedInformerFactory(k8sclient, 0)
			podInformer := informerFactory.Core().V1().Pods().Informer()
			nodeInformer := informerFactory.Core().V1().Nodes().Informer()
			podLister := corev1_listers.NewPodLister(podInformer.(toolscache.SharedIndexInformer).GetIndexer())
			nodeLister := corev1_listers.NewNodeLister(nodeInformer.(toolscache.SharedIndexInformer).GetIndexer())

			c := Controller{
				podLister:            podLister,
				nodeLister:           nodeLister,
				instanceGroupLockMap: make(map[string]*sync.Mutex),
				client:               client,
				cceClient:            NewfakeCceClient(test.cceErr),
				options:              options.NewServerRunOptions(),
			}
			c.options.NodeOptions.EnableAutoChangeInstanceGroupReplicas = true
			if test.maxNodes != 0 {
				c.options.NodeOptions.AutoChangeInstanceGroupMaxReplicas = test.maxNodes
			}
			for _, pod := range test.pods {
				client.Create(context.Background(), pod)
				podInformer.GetIndexer().Add(pod)
			}

			for _, node := range test.nodes {
				client.Create(context.Background(), node)
				nodeInformer.GetIndexer().Add(node)
			}

			res := c.increaseInstanceGroupMaxNodeCountTask()
			count := res[test.instanceGroup]

			if count != test.increaseNodeCount {
				t.Errorf("increaseInstanceGroupMaxNodeCountTask got node count %+v ,expect count %+v ", count, test.increaseNodeCount)
				return
			}
		})
	}

}
