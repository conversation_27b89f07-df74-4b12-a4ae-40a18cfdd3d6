package node

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	ccev2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	ccev2Types "github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"github.com/prometheus/client_golang/prometheus"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
)

var (
	// pending bid pod数量统计信息
	pendingBidPodCountGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Subsystem: "bci",
			Name:      "pending_bid_pod_count",
			Help:      "The InstanceSpec Bci Pending Pod Count.",
		},
		[]string{"instance_spec", "price"},
	)
)

func init() {
	metrics.Registry.MustRegister(pendingBidPodCountGauge)
}

// createBidNodesTask 创建竞价实例任务
func (ssc *Controller) createBidNodesTask() map[string]*entity.BidPriceStatistics {
	bidPendingPods, daemonsetPods, err := ssc.getBidPendingPod()
	if err != nil {
		return make(map[string]*entity.BidPriceStatistics)
	}
	return ssc.createBidNodes(bidPendingPods, daemonsetPods)
}

// getBidPendingPod 获取需要竞价实例的pendingPod以及daemonsetPods
func (ssc *Controller) getBidPendingPod() (bidPendingPods map[string]*entity.BidPriceStatistics, daemonsetPods map[string][]*corev1.Pod, err error) {
	// TODO 此处和节点组都会存储全量的daemonsetPods, 后续可以考虑整合

	// instanceTemplateID : bidInfo : podList
	bidPendingPods = make(map[string]*entity.BidPriceStatistics)
	// nodeName : podList
	daemonsetPods = make(map[string][]*corev1.Pod)

	allPodList, err := ssc.podLister.List(labels.Everything())
	if err != nil {
		klog.Warningf("nodeController computePendingPod list allPod err %+v ", err)
		return bidPendingPods, daemonsetPods, err
	}

	logPods := make([]entity.PendingPod, 0)

	for _, pod := range allPodList {
		// 封装daemonset pod
		if entity.IsDaemonsetRunningPod(pod) {
			if _, ok := daemonsetPods[pod.Spec.NodeName]; !ok {
				daemonsetPods[pod.Spec.NodeName] = make([]*corev1.Pod, 0)
			}
			daemonsetPods[pod.Spec.NodeName] = append(daemonsetPods[pod.Spec.NodeName], pod)
		}
		if !entity.IsBciBidPod(pod) {
			continue
		}
		if !ssc.shouldHandlePod(pod) || !shouldDealBidPendingPod(pod) {
			continue
		}

		logPods = append(logPods, entity.NewPendingPod(pod))

		podAnnotation := pod.Annotations
		if podAnnotation == nil || len(podAnnotation) == 0 {
			continue
		}
		// 资源规格模板信息
		instanceTemplateID := pod.Spec.NodeSelector[entity.NodeLabelInstanceTemplateKey]
		if _, ok := bidPendingPods[instanceTemplateID]; !ok {
			bidPendingPods[instanceTemplateID] = &entity.BidPriceStatistics{PodInfo: make(map[string][]*corev1.Pod)}
		}
		priceMapInfo := bidPendingPods[instanceTemplateID]

		// 价格信息
		pricePodMapKey := entity.BidPodPriceStatisticKey(pod)
		if _, ok := priceMapInfo.PodInfo[pricePodMapKey]; !ok {
			priceMapInfo.PodInfo[pricePodMapKey] = make([]*corev1.Pod, 0)
		}
		priceMapInfo.PodInfo[pricePodMapKey] = append(priceMapInfo.PodInfo[pricePodMapKey], pod)
	}

	// log pending pods
	bytes, _ := json.Marshal(logPods)
	klog.V(3).Infof("nodeController monitorPendingBidPod info %+v ", string(bytes))

	// 打点之前重置一下
	pendingBidPodCountGauge.Reset()
	for it, priceMap := range bidPendingPods {
		for price, pendingPods := range priceMap.PodInfo {
			pendingBidPodCountGauge.WithLabelValues(it, price).Set(float64(len(pendingPods)))
		}
	}

	return bidPendingPods, daemonsetPods, nil
}

// 为pendingBidPods创建竞价实例
func (ssc *Controller) createBidNodes(bidPendingPods map[string]*entity.BidPriceStatistics,
	daemonsetPods map[string][]*corev1.Pod) map[string]*entity.BidPriceStatistics {

	// instanceTemplateID : price : count
	createNodeCountMap := make(map[string]*entity.BidPriceStatistics)

	// 执行预调度逻辑，按照节点组分组
	for instanceTemplateID, priceMap := range bidPendingPods {
		for bidPriceKey, pods := range priceMap.PodInfo {
			nodeCount := 0

			nodeLabelSelector := entity.BuildBidNodeSelectLabels(pods[0].Spec.NodeSelector)
			nodes, err := ssc.nodeLister.List(labels.SelectorFromSet(nodeLabelSelector))
			if err != nil {
				klog.Warningf("nodeController monitorPendingBidPod list instanceGroup %s node err %w ", instanceTemplateID, err)
				continue
			}

			if len(nodes) == 0 {
				nodeCount, err = computeAddNodeCount(nil, pods, daemonsetPods)
			} else {
				nodeCount, err = computeAddNodeCount(nodes[0], pods, daemonsetPods)
			}
			if err != nil {
				continue
			}

			finalIncreaseNodeCount, err := ssc.doCreateBidNodes(instanceTemplateID, nodeCount, pods)
			klog.V(4).Infof("doCreateBidNodes instanceTemplate<%s> bidPrice<%s> requestCount %d, finalCount %d", instanceTemplateID, bidPriceKey,
				nodeCount, finalIncreaseNodeCount)
			if err != nil {
				klog.Errorf("nodeController doCreateBidNodes instanceTemplate %s bidPrice %s node err %w", instanceTemplateID, bidPriceKey, err)
				continue
			}
			if _, ok := createNodeCountMap[instanceTemplateID]; !ok {
				createNodeCountMap[instanceTemplateID] = &entity.BidPriceStatistics{CountInfo: make(map[string]int)}
			}
			priceMapInfo := createNodeCountMap[instanceTemplateID]
			priceMapInfo.CountInfo[bidPriceKey] = finalIncreaseNodeCount
		}
	}
	return createNodeCountMap
}

// 调用CCE接口创建竞价实例, 并修改pendingPod的LastIncreaseTime
func (ssc *Controller) doCreateBidNodes(instanceTemplateID string, nodeCount int, pendingPods []*corev1.Pod) (int, error) {
	instanceGroupDetail, err := ssc.cceClient.GetInstanceGroupDetail(instanceTemplateID)
	if err != nil {
		klog.Errorf("nodeController query instanceGroup %s detail err %+v ", instanceTemplateID, err)
		return 0, err
	}
	if instanceGroupDetail.InstanceGroup == nil || instanceGroupDetail.InstanceGroup.Spec == nil {
		err = fmt.Errorf("nodeController query instanceGroup %s detail Spec is empty ", instanceTemplateID)
		klog.Error(err)
		return 0, err
	}
	instanceSpec := instanceGroupDetail.InstanceGroup.Spec.InstanceTemplate.InstanceSpec

	bidOption, err := constructBidOption(pendingPods[0])
	if err != nil {
		return 0, err
	}

	// 清除节点组相关信息, 否则节点将会创建至节点组中
	instanceSpec.InstanceGroupID = ""
	instanceSpec.InstanceGroupName = ""
	// 携带竞价相关信息
	instanceSpec.Bid = true
	instanceSpec.BidOption = *bidOption
	// only postpaid instance supports bid
	instanceSpec.InstanceChargingType = bccapi.PaymentTimingPostPaid
	// 指定label相关信息用于被调度
	bidNodeLabel := constructBidNodeLabel(instanceTemplateID, bidOption)
	originLabel := instanceSpec.Labels
	// CGPU相关label为必填(若有)否则无法应用cGPU相关插件
	if cGPUPlugin, ok := originLabel[entity.NodeLabelCGPUPlugin]; ok {
		bidNodeLabel[entity.NodeLabelCGPUPlugin] = cGPUPlugin
	}
	if cGPUPriority, ok := originLabel[entity.NodeLabelCGPUPriority]; ok {
		bidNodeLabel[entity.NodeLabelCGPUPriority] = cGPUPriority
	}
	if imageAccelerate, ok := originLabel[entity.NodeLabelImageAccelerate]; ok {
		bidNodeLabel[entity.NodeLabelImageAccelerate] = imageAccelerate
	}
	instanceSpec.Labels = bidNodeLabel

	resp, err := ssc.cceClient.CreateInstances([]*ccev2.InstanceSet{{
		Count:        nodeCount,
		InstanceSpec: instanceSpec,
	}})
	if err != nil {
		klog.Errorf("nodeController CreateInstances err %+v ", err)
		return 0, err
	}
	if resp.CCEInstanceIDs == nil || len(resp.CCEInstanceIDs) == 0 {
		klog.Errorf("nodeController CreateInstances result is empty, requestID %s", resp.RequestID)
		return 0, err
	}
	klog.V(5).Infof("nodeController CreateInstances requestInstanceTemplate %s, result: requestID %s, CCEInstanceIDs: %s",
		instanceTemplateID, resp.RequestID, resp.CCEInstanceIDs)
	ssc.patchPodLastIncreaseTimeAnnotation(pendingPods)
	return len(resp.CCEInstanceIDs), nil
}

// 为pod新增节点的最新时间
func (ssc *Controller) patchPodLastIncreaseTimeAnnotation(pods []*corev1.Pod) {
	patchPodAnnotation := func(pod *corev1.Pod) {
		copyPod := pod.DeepCopy()

		podAnnotations := copyPod.ObjectMeta.Annotations
		if podAnnotations == nil {
			podAnnotations = make(map[string]string)
		}
		podAnnotations[lastIncreaseTimeKey] = strconv.FormatInt(time.Now().Unix(), 10)

		metaData := map[string]map[string]string{"annotations": podAnnotations}
		patchData, _ := json.Marshal(&map[string]interface{}{"metadata": &metaData})

		klog.V(3).Infof("nodeController patch bid pod <%s/%s> Annotation %s ", pod.Namespace, pod.Name, string(patchData))
		var err error
		for i := 0; i < 3; i++ {
			err = ssc.client.Patch(context.TODO(), copyPod, client.RawPatch(types.StrategicMergePatchType, []byte(patchData)))
			if err == nil {
				break
			}
			klog.Errorf("nodeController patch bid pod %s lastIncreaseTimeKey index %+v , err %+v ", copyPod.Name, i, err)
		}
	}
	for _, pod := range pods {
		patchPodAnnotation(pod)
	}
}

// 是否要为竞价实例再创建节点
func shouldDealBidPendingPod(pod *corev1.Pod) bool {
	// 从创建时间到当前，pending 超过30s
	duration, _ := time.ParseDuration("30s")
	if !pod.CreationTimestamp.Add(duration).Before(time.Now()) {
		return false
	}
	if len(pod.Annotations) == 0 {
		return true
	}

	lastTime, ok := pod.Annotations[lastIncreaseTimeKey]
	if !ok {
		return true
	}

	lastTimeIntVal, err := strconv.ParseInt(lastTime, 10, 64)
	if err != nil {
		return true
	}
	// 15分钟以后还是pending，则处理
	// TODO 此时间后续可配置(竞价实例暂定为普通实例的3倍)
	duration5m, _ := time.ParseDuration("15m")
	covertTime := time.Unix(lastTimeIntVal, 0)
	if time.Now().After(covertTime.Add(duration5m)) {
		return true
	}
	return false
}

// 根据podAnnotation构造BidOption
func constructBidOption(pod *corev1.Pod) (*ccev2Types.BidOption, error) {
	podAnnotation := pod.Annotations

	bidMode := ccev2Types.BidModeMarketPrice
	bidPrice := ""
	if podAnnotation[entity.BciBidModeKey] == string(ccev2Types.BidModeCustomPrice) {
		bidMode = ccev2Types.BidModeCustomPrice
		bidPrice = podAnnotation[entity.BciBidPriceKey]
		if bidPrice == "" {
			err := fmt.Errorf("bidNodeIncreaseController pod <%s/%s> bidMode is CUSTOM_BID, bidPrice is empty", pod.Namespace, pod.Name)
			klog.Error(err)
			return nil, err
		}
	}

	bidTimeOut, err := strconv.Atoi(podAnnotation[entity.BciBidTimeOutKey])
	if err != nil {
		err = fmt.Errorf("bidNodeIncreaseController pod <%s/%s> bidTimeOut %s not int ", pod.Namespace, pod.Name, podAnnotation[entity.BciBidTimeOutKey])
		klog.Error(err)
		return nil, err
	}

	bidReleaseEIP, err := strconv.ParseBool(podAnnotation[entity.BciBidReleaseEIPKey])
	if err != nil {
		err = fmt.Errorf("bidNodeIncreaseController pod <%s/%s> bidReleaseEIP %s not boolean ", pod.Namespace, pod.Name, podAnnotation[entity.BciBidReleaseEIPKey])
		klog.Error(err)
		return nil, err
	}

	bidReleaseCDS, err := strconv.ParseBool(podAnnotation[entity.BciBidReleaseCDSKey])
	if err != nil {
		err = fmt.Errorf("bidNodeIncreaseController pod <%s/%s> bidReleaseCDS %s not boolean ", pod.Namespace, pod.Name, podAnnotation[entity.BciBidReleaseCDSKey])
		klog.Error(err)
		return nil, err
	}

	bidOption := &ccev2Types.BidOption{
		BidMode:       bidMode,
		BidPrice:      bidPrice,
		BidTimeout:    bidTimeOut,
		BidReleaseEIP: bidReleaseEIP,
		BidReleaseCDS: bidReleaseCDS,
	}
	return bidOption, nil
}

func constructBidNodeLabel(instanceTemplateID string, bidOption *ccev2Types.BidOption) map[string]string {
	nodeLabel := make(map[string]string)
	nodeLabel[entity.NodeLabelInstanceTemplateKey] = instanceTemplateID
	nodeLabel[entity.NodeLabelChargingTypeKey] = string(bccapi.PaymentTimingBidding)

	if bidOption.BidMode == ccev2Types.BidModeCustomPrice {
		nodeLabel[entity.NodeLabelBidModeKey] = entity.NodeLabelBidModePriceValue
		nodeLabel[entity.NodeLabelBidPriceKey] = bidOption.BidPrice
	} else {
		nodeLabel[entity.NodeLabelBidModeKey] = entity.NodeLabelBidModeMarketValue
	}

	return nodeLabel
}
