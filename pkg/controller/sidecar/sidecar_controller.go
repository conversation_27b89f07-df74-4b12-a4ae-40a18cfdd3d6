package sidecar

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"gopkg.in/yaml.v2"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/types"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	toolscache "k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/record"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"sigs.k8s.io/controller-runtime/pkg/source"
)

var (
	// sidecar 容器启动失败数量打点
	sidecarStartFailedTotalCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "sidecar_start_failed_total",
			Help:      "The Sidecar Container Start Failed Total Count .",
		},
		[]string{"tenant_id"},
	)
)

func init() {
	metrics.Registry.MustRegister(sidecarStartFailedTotalCounter)
}

const (
	sidecarContainerAnnotationKey   = "bci_internal_sidecarContainer"
	sidecarStartFailedAnnotationKey = "bci_internal_sidecarStartFailed"
	postStartSidecarContainerName   = "bci-internal-post-start-sidecar"
	bciInternalPrefix               = "bci-internal"
	sidecarContainerNameKey         = "bci_internal_sidecarContainer"
)

var _ reconcile.Reconciler = &Controller{}

// New 创建sidecar controller，此处无法实现单测
func New(option *options.ServerRunOptions, mgr manager.Manager) (interface{}, error) {
	cache := mgr.GetCache()
	podInformer, err := cache.GetInformer(context.TODO(), &corev1.Pod{})
	if err != nil {
		klog.Errorf("sidecarController get podInformer err %+v ", err)
		return nil, err
	}

	eventInformer, err := cache.GetInformer(context.TODO(), &corev1.Event{})
	if err != nil {
		klog.Errorf("sidecarController get eventInformer err %+v ", err)
		return nil, err
	}
	podLister := corev1_listers.NewPodLister(podInformer.(toolscache.SharedIndexInformer).GetIndexer())
	eventLister := corev1_listers.NewEventLister(eventInformer.(toolscache.SharedIndexInformer).GetIndexer())

	c := &Controller{
		podLister:   podLister,
		eventLister: eventLister,
		client:      mgr.GetClient(),
		option:      option.SidecarOptions,
	}

	controller, err := controller.New("sidecar-controller", mgr, controller.Options{
		Reconciler:              c,
		MaxConcurrentReconciles: option.SidecarOptions.SidecarControllerWorkers, // 默认3个worker
		RateLimiter:             util.DefaultControllerRateLimiter(option),
	})

	if err != nil {
		klog.Errorf("New sidecarController err %+v ", err)
		return nil, err
	}

	if err = controller.Watch(source.Kind(mgr.GetCache(), &corev1.Event{}), &handler.EnqueueRequestForObject{}, predicate.Funcs{
		CreateFunc: func(ce event.CreateEvent) bool {
			event := ce.Object.(*corev1.Event)
			return c.shouldHandleEvent(event)
		},
		UpdateFunc: func(ue event.UpdateEvent) bool {
			event := ue.ObjectNew.(*corev1.Event)
			return c.shouldHandleEvent(event)
		},
		DeleteFunc: func(event.DeleteEvent) bool {
			return false
		},
	}); err != nil {
		klog.Errorf("New sidecarController Watch event err %+v ", err)
		return nil, err
	}

	// kubelet 会丢失event，因此此处在加一个sync container status 逻辑
	if err = controller.Watch(source.Kind(mgr.GetCache(), &corev1.Pod{}), &handler.EnqueueRequestForObject{}, predicate.Funcs{
		CreateFunc: func(ce event.CreateEvent) bool {
			return false
		},
		UpdateFunc: func(ue event.UpdateEvent) bool {
			pod := ue.ObjectNew.(*corev1.Pod)
			if c.isSidecarFailedPod(pod) {
				klog.Infof("sidecarController watch pod event <%+v/%+v>, need forcePatch", pod.Namespace, pod.Name)
				_ = c.handleSidecarFailed(pod, postStartSidecarContainerName, true)
			}
			return false
		},
		DeleteFunc: func(event.DeleteEvent) bool {
			return false
		},
		GenericFunc: func(event.GenericEvent) bool {
			return false
		},
	}); err != nil {
		klog.Errorf("New sidecarController Watch pod err %+v ", err)
		return nil, err
	}

	// 初始化recoder
	c.eventRecord = mgr.GetEventRecorderFor("sidecar-controller")
	return nil, nil
}

// Controller sidecar PostStart 失败 event 监听
type Controller struct {
	option      *options.SidecarOptions
	podLister   corev1_listers.PodLister
	eventLister corev1_listers.EventLister
	client      client.Client
	eventRecord record.EventRecorder
}

// Reconcile 处理 sidecar PostStart 失败事件
func (ssc *Controller) Reconcile(_ context.Context, request reconcile.Request) (res reconcile.Result, retErr error) {
	klog.Infof("sidecarController Reconcile event <%+v/%+v> ", request.Namespace, request.Name)
	event, err := ssc.eventLister.Events(request.Namespace).Get(request.Name)
	if err != nil {
		klog.Warningf("sidecarController Reconcile event <%+v/%+v> not found ", request.Namespace, request.Name)
		return reconcile.Result{}, nil
	}

	if !ssc.shouldHandleEvent(event) {
		return reconcile.Result{}, nil
	}
	podInfo := event.InvolvedObject
	pod, err := ssc.podLister.Pods(podInfo.Namespace).Get(podInfo.Name)
	if err != nil {
		klog.Warningf("sidecarController Reconcile event <%+v/%+v> pod %+v not found ", request.Namespace, request.Name, podInfo)
		return reconcile.Result{}, nil
	}

	var accountID = ""
	if len(pod.Labels) > 0 {
		accountID = pod.Labels["bci_internal_AccountID"]
	}
	// 打点
	sidecarStartFailedTotalCounter.WithLabelValues(accountID).Inc()

	// 根据evnet时间判断是否强制删除
	forcePatch := event.CreationTimestamp.Time.Before(time.Now().Add(time.Second * time.Duration(ssc.option.SidecarFailedForcePatchDelaySecond)))

	// 处理
	err = ssc.handleSidecarFailed(pod, podInfo.FieldPath, forcePatch)
	if err != nil {
		// 进行事件重试
		klog.Warningf("sidecarController Reconcile event <%+v/%+v pod %+v error %s, need retry.", request.Namespace, request.Name, podInfo, err.Error())
		return reconcile.Result{RequeueAfter: 5 * time.Second}, nil
	}
	return reconcile.Result{}, nil
}

// handleSidecarFailed 判断是否为pod sidecar poststart 失败
func (ssc *Controller) shouldHandleEvent(event *corev1.Event) bool {

	object := event.InvolvedObject
	// 只处理pod 事件
	if object.APIVersion != "v1" || object.Kind != "Pod" {
		return false
	}

	if event.Reason != "FailedPostStartHook" {
		return false
	}

	klog.V(3).Infof("sidecarController Observed pod <%s/%s> FailedPostStartHook event FieldPath %+v ",
		object.Namespace, object.Name, object.FieldPath)
	// 判断是否是sidecar容器
	pod, err := ssc.podLister.Pods(object.Namespace).Get(object.Name)
	if err != nil {
		return false
	}
	if len(pod.Annotations) == 0 {
		return false
	}

	_, ok := pod.Annotations[sidecarContainerAnnotationKey]
	if !ok {
		return false
	}
	if strings.Contains(object.FieldPath, postStartSidecarContainerName) {
		return true
	}
	return false
}

// handleSidecarFailed 防止event丢失(判断是否为pod sidecar poststart失败)
func (ssc *Controller) isSidecarFailedPod(pod *corev1.Pod) (failed bool) {

	defer func() {
		if failed {
			klog.V(3).Infof("sidecarController watch pod <%s/%s> sidecar failed , container status %+v ",
				pod.Namespace, pod.Name, pod.Status.ContainerStatuses)
		}
	}()

	// 忽略pod删除事件
	if pod == nil {
		return false
	}

	hasSidecarContainer := false
	for _, container := range pod.Spec.Containers {
		if container.Name == postStartSidecarContainerName {
			hasSidecarContainer = true
			break
		}
	}

	if !hasSidecarContainer {
		return false
	}

	// 判断postStart Container状态
	for _, status := range pod.Status.ContainerStatuses {
		if status.Name != postStartSidecarContainerName {
			continue
		}
		if status.State.Terminated != nil &&
			status.State.Terminated.ExitCode != 0 {
			// klog.V(3).Infof("sidecarController watch pod <%s/%s> postSrart container State ExitCode not 0 ",
			// 	pod.Namespace, pod.Name)
			return true
		}

		if status.LastTerminationState.Terminated != nil &&
			status.LastTerminationState.Terminated.ExitCode != 0 {
			// klog.V(3).Infof("sidecarController watch pod <%s/%s> postSrart container LastTerminationState ExitCode not 0 ",
			// 	pod.Namespace, pod.Name)
			return true
		}
	}
	return false
}

// handleSidecarFailed 处理sidecar poststart 失败(1.event; 2.annotation)
func (ssc *Controller) handleSidecarFailed(pod *corev1.Pod, failedSidecarPath string, forcePatch bool) error {
	// 若pod已经被删除，则不需要处理
	if pod.DeletionTimestamp != nil {
		klog.Warningf("sidecarController handle sidecar failed error, pod <%+v/%+v> deleted.", pod.Namespace, pod.Name)
		return nil
	}
	// 通知sidecar失败事件
	if err := ssc.recordFailedSidecarEvent(pod, forcePatch); err != nil {
		klog.Warningf("sidecarController handle sidecar failed error, record pod <%+v/%+v> failed sidecar event error.", pod.Namespace, pod.Name)
		return err
	}
	// 保存sidecar失败的pod yaml
	if err := ssc.trySavePodYaml(pod); err != nil {
		klog.Warningf("sidecarController handle sidecar failed error, save pod yaml<%+v/%+v> error.", pod.Namespace, pod.Name)
	}
	// 更新pod siecar失败annotation
	if err := ssc.patchSidecarPostStartHookFailed(pod, failedSidecarPath); err != nil {
		klog.Warningf("sidecarController handle sidecar failed error, patch pod <%+v/%+v> failed sidecar post start hook annotation error.", pod.Namespace, pod.Name)
		return err
	}
	return nil
}

// recordFailedSidecarEvent 推送失败事件
func (ssc *Controller) recordFailedSidecarEvent(pod *corev1.Pod, forcePatch bool) error {
	// 1 获取全量sidecarContainerName
	sidecarNames := getSidecarContainerNames(pod)
	if len(sidecarNames) == 0 {
		klog.Warningf("sidecarController failed to get sidecar containers name, pod:%s", pod.Name)
		return nil
	}

	// 2 获取所有sidecarName的TerminatedMessage信息(获取不到需要重试等待)
	sidecarMsgs := getSidecarContainerTerminatedMsg(pod)
	if len(sidecarMsgs) == 0 && !forcePatch {
		klog.Warningf("sidecarController failed to get sidecar container terminated message, pod:%s", pod.Name)
		return fmt.Errorf("failed to get sidecar container terminated msg")
	}

	// 3 获取成功的sidecarNames， 从poststart msg中, 若获取不到需要进行重试(事件先来, pod yaml更新在后，需要等待)
	successedSidecarNames, err := getSuccessedSidecarContainerNames(sidecarMsgs)
	if err != nil && !forcePatch {
		klog.Warningf("sidecarController failed to get successed sidecar container names, pod:%s", pod.Name)
		return err
	}

	// 4.1 计算失败的sidecarNames
	failedSidecarNames := getFailedSidecarContainerNames(sidecarNames, successedSidecarNames)

	// 4.2 获取失败sidecar的TerminatedMessage信息中error信息(可能存在获取不到)
	failedSidecarErrorMessage := getFailedSidecarContainerErrorMessage(failedSidecarNames, sidecarMsgs)

	// 5 发送事件
	hasSendEvent := false
	for _, cs := range pod.Status.ContainerStatuses {
		if _, ok := sidecarNames[cs.Name]; !ok {
			continue
		}
		// 获取失败sidecar详细失败信息
		// 1.sidecar start failed: xxx (nfs、pfs)
		// 2.sidecar check failed: xxx (nfs、pfs、kubeproxy、bls): 在快速失败情况下不会打印该错误信息
		// 3.空 (nfs、pfs、kubeproxy、bls): 在快速失败情况下会为空
		msg, ok := failedSidecarErrorMessage[cs.Name]
		if !ok {
			klog.Warningf("sidecarController sidecar container failed message for sidecar container %s with error %s", cs.Name, msg)
			msg = ""
		}
		if cs.State.Terminated != nil && cs.State.Terminated.ExitCode != 0 {
			ssc.eventRecord.Eventf(pod, "Warning", "BciSidecarStartError", buildSidecarEventMessage(cs.Name, msg))
			hasSendEvent = true
			continue
		}
		if cs.LastTerminationState.Terminated != nil &&
			cs.LastTerminationState.Terminated.ExitCode != 0 {
			ssc.eventRecord.Eventf(pod, "Warning", "BciSidecarStartError", buildSidecarEventMessage(cs.Name, msg))
			hasSendEvent = true
		}
	}
	if !hasSendEvent {
		ssc.eventRecord.Eventf(pod, "Warning", "BciSidecarStartError", buildSidecarEventMessage("", ""))
	}
	return nil
}

// savePodYaml 保存pod yaml
func (ssc *Controller) trySavePodYaml(pod *corev1.Pod) error {
	copyPod := pod.DeepCopy()
	now := time.Now()
	day := now.Format("2006-01-02")
	// 创建当天目录
	_, err := os.Stat(ssc.option.SidecarContainerFailedPodSavePath + "/" + day)
	if err != nil && os.IsNotExist(err) {
		_ = os.MkdirAll(ssc.option.SidecarContainerFailedPodSavePath+"/"+day, 0777)
	}
	// 打开文件
	file, err := os.OpenFile(ssc.option.SidecarContainerFailedPodSavePath+"/"+day+"/"+copyPod.Namespace+":"+copyPod.Name, os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		klog.Warningf("sidecarController failed to open pod file, pod <%s/%s>", copyPod.Namespace, copyPod.Name)
		return err
	}
	defer file.Close()
	copyPod.ManagedFields = nil
	// 序列化yaml
	data, err := yaml.Marshal(copyPod)
	if err != nil {
		klog.Warningf("sidecarController failed to marshal pod yaml, pod <%s/%s>", copyPod.Namespace, copyPod.Name)
		return err
	}
	_, err = file.Write(data)
	if err != nil {
		klog.Warningf("sidecarController failed to write pod yaml, pod <%s/%s>", copyPod.Namespace, copyPod.Name)
		return err
	}
	return nil
}

// patchSidecarPostStartHookFailed patch失败annotation
func (ssc *Controller) patchSidecarPostStartHookFailed(pod *corev1.Pod, failedSidecarPath string) error {
	if !ssc.option.EnableDeleteSidecarFailedPod {
		klog.V(4).Infof("sidecarController ignore pod <%s/%s> PostStartHookFailed event ",
			pod.Namespace, pod.Name)
		return nil
	}
	copyPod := pod.DeepCopy()
	if copyPod.Annotations == nil {
		copyPod.Annotations = make(map[string]string)
	}

	if _, ok := copyPod.Annotations[sidecarStartFailedAnnotationKey]; ok {
		return nil
	}

	copyPod.Annotations[sidecarStartFailedAnnotationKey] = failedSidecarPath

	metaData := map[string]map[string]string{
		"annotations": copyPod.Annotations,
	}

	patchData, _ := json.Marshal(&map[string]interface{}{
		"metadata": &metaData,
	})
	var err error
	for i := 0; i < 3; i++ {
		err = ssc.client.Patch(context.TODO(), copyPod, client.RawPatch(types.StrategicMergePatchType, []byte(patchData)))
		klog.V(3).Infof("sidecarController patch pod <%s/%s> PostStartHookFailed , err %+v ", copyPod.Namespace, copyPod.Name, err)
		if err == nil {
			return nil
		}
	}

	return nil
}

// getSidecarContainerNames 获取pod sidecar container names
func getSidecarContainerNames(pod *corev1.Pod) map[string]struct{} {
	result := make(map[string]struct{})
	sidecarContainerName, ok := pod.Annotations[sidecarContainerAnnotationKey]
	if !ok {
		return result
	}
	sidecars := strings.Split(sidecarContainerName, ",")
	for _, sidecar := range sidecars {
		if sidecar == postStartSidecarContainerName {
			continue
		}
		result[sidecar] = struct{}{}
	}
	return result
}

// getSidecarContainerTerminateMsgs 获取pod sidecar container terminate msgs
func getSidecarContainerTerminatedMsg(pod *corev1.Pod) map[string]string {
	result := make(map[string]string)
	for _, status := range pod.Status.ContainerStatuses {
		if status.State.Terminated != nil && status.State.Terminated.Message != "" {
			result[status.Name] = status.State.Terminated.Message
			continue
		}
		if status.LastTerminationState.Terminated != nil && status.LastTerminationState.Terminated.Message != "" {
			result[status.Name] = status.LastTerminationState.Terminated.Message
			continue
		}
	}
	return result
}

func getSuccessedSidecarContainerNames(sidecarMsgs map[string]string) (map[string]struct{}, error) {
	result := make(map[string]struct{})
	if _, ok := sidecarMsgs[postStartSidecarContainerName]; !ok {
		return result, fmt.Errorf("sidecarController failed to get postStart sidecar container terminated msg")
	}
	successedSidecarsStr := sidecarMsgs[postStartSidecarContainerName]
	messages := strings.Split(successedSidecarsStr, "\n")
	var message = ""
	for i := len(messages) - 1; i >= 0; i-- {
		if strings.Contains(messages[i], "start success sidecars") {
			message = messages[i]
			break
		}
	}
	if message == "" {
		return result, nil
	}
	successedSidecarsMsgStr := strings.Split(message, ":")
	successedSidecarsNameStr := successedSidecarsMsgStr[1]
	sucessedSidecarsNameList := strings.Split(successedSidecarsNameStr, " ")
	for _, sidecar := range sucessedSidecarsNameList {
		result[sidecar] = struct{}{}
	}
	return result, nil
}

func getFailedSidecarContainerNames(allSidecarNames map[string]struct{}, successedSidecarNames map[string]struct{}) map[string]struct{} {
	result := make(map[string]struct{})
	for sidecar, _ := range allSidecarNames {
		if _, ok := successedSidecarNames[sidecar]; !ok {
			result[sidecar] = struct{}{}
		}
	}
	return result
}

func getFailedSidecarContainerErrorMessage(failedSidecarNames map[string]struct{}, msg map[string]string) map[string]string {
	result := make(map[string]string)
	for sidecar, _ := range failedSidecarNames {
		if _, ok := msg[sidecar]; ok {
			result[sidecar] = getFailedSidecarContainerErrorMsg(msg[sidecar])
		}
	}
	return result
}

func getFailedSidecarContainerErrorMsg(msg string) string {
	result := ""
	messages := strings.Split(msg, "\n")
	var startFailedMessage = ""
	var checkFailedMessage = ""
	for i := len(messages) - 1; i >= 0; i-- {
		if strings.Contains(messages[i], "sidecar start failed: ") {
			if startFailedMessage == "" {
				startFailedMessage = messages[i]
			}
		}
		if strings.Contains(messages[i], "sidecar check failed: ") {
			if checkFailedMessage == "" {
				checkFailedMessage = messages[i]
			}
		}
	}
	if startFailedMessage != "" {
		return strings.Split(startFailedMessage, "sidecar start failed: ")[1]
	} else if checkFailedMessage != "" {
		return strings.Split(checkFailedMessage, "sidecar check failed: ")[1]
	}
	return result
}

func buildSidecarEventMessage(containerName string, msg string) string {
	if msg != "" {
		return msg
	}
	if strings.Contains(containerName, "nfs") {
		return "bci internal component nfs init failed"
	}
	if strings.Contains(containerName, "pfs") {
		return "bci internal component pfs init failed"
	}
	if strings.Contains(containerName, "log") {
		return "bci internal component bls init failed"
	}
	if strings.Contains(containerName, "kube-proxy") {
		return "bci internal component kubeproxy init failed"
	}
	return "bci internal component init failed"
}
