package sidecar

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/informers"
	k8sfake "k8s.io/client-go/kubernetes/fake"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

func buildPodWithAnnotations(namespace, name string, annotations map[string]string) *corev1.Pod {
	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:         types.UID(fmt.Sprintf("%v-%v", namespace, name)),
			Name:        name,
			Namespace:   namespace,
			Annotations: annotations,
		},
		Spec: corev1.PodSpec{},
	}
}

func buildPodWithAnnotationsAndPostStartHookFailed(namespace, name string, annotations map[string]string) *corev1.Pod {
	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:         types.UID(fmt.Sprintf("%v-%v", namespace, name)),
			Name:        name,
			Namespace:   namespace,
			Annotations: annotations,
		},
		Spec: corev1.PodSpec{},
		Status: corev1.PodStatus{
			ContainerStatuses: []corev1.ContainerStatus{
				{
					// post start sidecar
					Name: "bci-internal-post-start-sidecar",
					State: corev1.ContainerState{
						Terminated: &corev1.ContainerStateTerminated{
							// 退出message 没有成功的sidecar
							Message: "start success sidecars: ttt",
						},
					},
				},
				{
					Name: "bls-sidecar",
					State: corev1.ContainerState{
						Terminated: &corev1.ContainerStateTerminated{
							// 存在启动失败日志 && 检查失败日志
							Message: "sidecar start failed1: ccc\nsidecar check failed: mmm",
						},
					},
				},
			},
		},
	}
}

func buildEventWithPodPostStartHookFailed(podNamespace, podName, sidecarName, eventName string) *corev1.Event {
	return &corev1.Event{
		InvolvedObject: corev1.ObjectReference{
			Kind:       "Pod",
			APIVersion: "v1",
			FieldPath:  "spec.containers{" + sidecarName + "}",
			Namespace:  podNamespace,
			Name:       podName,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      eventName,
			Namespace: podNamespace,
		},
		Message: "PostStartHookFailed",
		Reason:  "FailedPostStartHook",
	}
}

func TestReconcileEvent(t *testing.T) {

	tests := []struct {
		name           string
		pods           []*corev1.Pod
		events         []*corev1.Event
		reconcileParam types.NamespacedName
		res            reconcile.Result
	}{
		{
			name: "pod sidecar执行postStartHook失败, 需要重试",
			pods: []*corev1.Pod{
				buildPodWithAnnotations("ns1", "pod1", map[string]string{sidecarContainerAnnotationKey: "bls-sidecar"}),
			},
			events: []*corev1.Event{
				buildEventWithPodPostStartHookFailed("ns1", "pod1", "bls-sidecar", "event1"),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "ns1",
				Name:      "event1",
			},
			res: reconcile.Result{},
		},
		{
			name: "pod sidecar执行postStartHook失败, 不需要重试",
			pods: []*corev1.Pod{
				buildPodWithAnnotationsAndPostStartHookFailed("ns1", "pod1", map[string]string{sidecarContainerAnnotationKey: "bls-sidecar"}),
			},
			events: []*corev1.Event{
				buildEventWithPodPostStartHookFailed("ns1", "pod1", "bls-sidecar", "event1"),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "ns1",
				Name:      "event1",
			},
			res: reconcile.Result{},
		},
		{
			name: "pod 业务容器执行postStartHook失败",
			pods: []*corev1.Pod{
				buildPodWithAnnotations("ns1", "pod1", map[string]string{sidecarContainerAnnotationKey: "bls-sidecar"}),
			},
			events: []*corev1.Event{
				buildEventWithPodPostStartHookFailed("ns1", "pod1", "workload", "event1"),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "ns1",
				Name:      "event1",
			},
			res: reconcile.Result{},
		},
		{
			name: "pod 无sidecar 容器",
			pods: []*corev1.Pod{
				buildPodWithAnnotations("ns1", "pod1", map[string]string{}),
			},
			events: []*corev1.Event{
				buildEventWithPodPostStartHookFailed("ns1", "pod1", "workload", "event1"),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "ns1",
				Name:      "event1",
			},
			res: reconcile.Result{},
		},
		{
			name: "pod 已经被删除",
			pods: []*corev1.Pod{},
			events: []*corev1.Event{
				buildEventWithPodPostStartHookFailed("ns1", "pod1", "workload", "event1"),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "ns1",
				Name:      "event1",
			},
			res: reconcile.Result{},
		},
		{
			name: "非pod PostStartHook 事件",
			pods: []*corev1.Pod{},
			events: []*corev1.Event{
				func() *corev1.Event {
					event := buildEventWithPodPostStartHookFailed("ns1", "pod1", "workload", "event1")
					event.Reason = "SuccessPostStartHook"
					return event
				}(),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "ns1",
				Name:      "event1",
			},
			res: reconcile.Result{},
		},
		{
			name: "非pod PostStartHook 事件",
			pods: []*corev1.Pod{},
			events: []*corev1.Event{
				func() *corev1.Event {
					event := buildEventWithPodPostStartHookFailed("ns1", "pod1", "workload", "event1")
					event.InvolvedObject.Kind = "Deployment"
					return event
				}(),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "ns1",
				Name:      "event1",
			},
			res: reconcile.Result{},
		},
		{
			name:   "event 未找到",
			pods:   []*corev1.Pod{},
			events: []*corev1.Event{},
			reconcileParam: types.NamespacedName{
				Namespace: "ns1",
				Name:      "event1",
			},
			res: reconcile.Result{},
		},
	}

	for i, test := range tests {
		if i != len(tests)-1 {
			// continue
		}
		t.Run(test.name, func(t *testing.T) {
			k8sclient := k8sfake.NewSimpleClientset()
			client := fake.NewFakeClient()

			informerFactory := informers.NewSharedInformerFactory(k8sclient, 0)
			podInformer := informerFactory.Core().V1().Pods()
			podIndexer := podInformer.Informer().GetIndexer()

			eventInformer := informerFactory.Core().V1().Events()
			eventIndexer := eventInformer.Informer().GetIndexer()

			c := Controller{
				podLister:   podInformer.Lister(),
				eventLister: eventInformer.Lister(),
				client:      client,
				option:      options.NewSidecarOptions(),
			}

			for _, pod := range test.pods {
				err := podIndexer.Add(pod)
				if err != nil {
					fmt.Println(err)
				}
				err = client.Create(context.Background(), pod)
				if err != nil {
					fmt.Println(err)
				}
			}

			for _, event := range test.events {
				err := eventIndexer.Add(event)
				if err != nil {
					fmt.Println(err)
				}
				err = client.Create(context.Background(), event)
				if err != nil {
					fmt.Println(err)
				}
			}

			res, _ := c.Reconcile(context.Background(), reconcile.Request{
				NamespacedName: test.reconcileParam,
			})
			if !reflect.DeepEqual(res, test.res) {
				t.Errorf("sidecarController Reconcile got res %+v ,expect %+v ", res, test.res)
			}
		})
	}

}

func Test_Controller_getSidecarContainerTerminatedMsg(t *testing.T) {
	type args struct {
		pod *corev1.Pod
	}
	tests := []struct {
		name string
		args args
		want map[string]string
	}{
		{
			name: "normal terminated container message",
			args: args{
				pod: &corev1.Pod{
					Status: corev1.PodStatus{
						ContainerStatuses: []corev1.ContainerStatus{
							{
								Name: "sidecar-sidecar-container",
								State: corev1.ContainerState{
									Terminated: &corev1.ContainerStateTerminated{
										Message: "test message",
									},
								},
							},
						},
					},
				},
			},
			want: map[string]string{
				"sidecar-sidecar-container": "test message",
			},
		},
		{
			name: "normal terminated containers message",
			args: args{
				pod: &corev1.Pod{
					Status: corev1.PodStatus{
						ContainerStatuses: []corev1.ContainerStatus{
							{
								Name: "sidecar-sidecar-container",
								State: corev1.ContainerState{
									Terminated: &corev1.ContainerStateTerminated{
										Message: "test message",
									},
								},
							},
							{
								Name: "sidecar-sidecar-container1",
								State: corev1.ContainerState{
									Terminated: &corev1.ContainerStateTerminated{
										Message: "test message1",
									},
								},
							},
							{
								Name:  "sidecar-sidecar-container2",
								State: corev1.ContainerState{},
							},
							{
								Name: "sidecar-sidecar-container2",
								State: corev1.ContainerState{
									Terminated: &corev1.ContainerStateTerminated{},
								},
							},
							{
								Name: "sidecar-sidecar-container2",
								State: corev1.ContainerState{
									Terminated: &corev1.ContainerStateTerminated{
										Message: "",
									},
								},
							},
						},
					},
				},
			},
			want: map[string]string{
				"sidecar-sidecar-container":  "test message",
				"sidecar-sidecar-container1": "test message1",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getSidecarContainerTerminatedMsg(tt.args.pod); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getSidecarContainerTerminatedMsg() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getSuccessedSidecarContainerNames(t *testing.T) {
	type args struct {
		sidecarMsgs map[string]string
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]struct{}
		wantErr bool
	}{
		{
			name: "Should return error when sidecar msg not found",
			args: args{
				sidecarMsgs: map[string]string{},
			},
			want:    map[string]struct{}{},
			wantErr: true,
		},
		{
			name: "Should return success sidecar container terminated msg1",
			args: args{
				sidecarMsgs: map[string]string{
					postStartSidecarContainerName: "sidecar1",
				},
			},
			want:    map[string]struct{}{},
			wantErr: false,
		},
		{
			name: "Should return success sidecar container terminated msg2",
			args: args{
				sidecarMsgs: map[string]string{
					postStartSidecarContainerName: "test\nstart success sidecars:sidecar1 sidecar2 sidecar3",
				},
			},
			want: map[string]struct{}{
				"sidecar1": {},
				"sidecar2": {},
				"sidecar3": {},
			},
			wantErr: false,
		},
		{
			name: "Should return success sidecar container terminated msg3",
			args: args{
				sidecarMsgs: map[string]string{
					postStartSidecarContainerName: "test\nstart success sidecars:sidecar1\nstart success sidecars:sidecar1 sidecar2",
				},
			},
			want: map[string]struct{}{
				"sidecar1": {},
				"sidecar2": {},
			},
			wantErr: false,
		},
		{
			name: "Should return success sidecar container terminated msg4",
			args: args{
				sidecarMsgs: map[string]string{
					postStartSidecarContainerName: "test\nstart success sidecars:sidecar1\nstart success sidecars:sidecar1 sidecar2\nstart success sidecars:sidecar1",
				},
			},
			want: map[string]struct{}{
				"sidecar1": {},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getSuccessedSidecarContainerNames(tt.args.sidecarMsgs)
			if (err != nil) != tt.wantErr {
				t.Errorf("getSuccessedSidecarContainerNames() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getSuccessedSidecarContainerNames() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_Controller_getFailedSidecarContainerNames(t *testing.T) {
	type args struct {
		allSidecarNames       map[string]struct{}
		successedSidecarNames map[string]struct{}
	}
	tests := []struct {
		name string
		args args
		want map[string]struct{}
	}{
		{
			name: "Test_getFailedSidecarContainerNames_1",
			args: args{
				allSidecarNames: map[string]struct{}{
					"sidecar1": {},
					"sidecar2": {},
				},
				successedSidecarNames: map[string]struct{}{},
			},
			want: map[string]struct{}{
				"sidecar1": {},
				"sidecar2": {},
			},
		},
		{
			name: "Test_getFailedSidecarContainerNames_1",
			args: args{
				allSidecarNames: map[string]struct{}{
					"sidecar1": {},
					"sidecar2": {},
				},
				successedSidecarNames: map[string]struct{}{
					"sidecar1": {},
				},
			},
			want: map[string]struct{}{
				"sidecar2": {},
			},
		},
		{
			name: "Test_getFailedSidecarContainerNames_1",
			args: args{
				allSidecarNames: map[string]struct{}{
					"sidecar1": {},
					"sidecar2": {},
				},
				successedSidecarNames: map[string]struct{}{
					"sidecar1": {},
					"sidecar2": {},
				},
			},
			want: map[string]struct{}{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getFailedSidecarContainerNames(tt.args.allSidecarNames, tt.args.successedSidecarNames); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getFailedSidecarContainerNames() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_Controller_getFailedSidecarContainerErrorMsg(t *testing.T) {
	type args struct {
		msg string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "no sidecar message",
			args: args{
				msg: "",
			},
			want: "",
		},
		{
			name: "sidecar message",
			args: args{
				msg: "sidecar start failed:",
			},
			want: "",
		},
		{
			name: "start sidecar message without check sidecar",
			args: args{
				msg: "sidecar start failed: ttt",
			},
			want: "ttt",
		},
		{
			name: "start sidecar message without check sidecar",
			args: args{
				msg: "sidecar start failed: 111\nsidecar start failed: 222\nsidecar start failed: 333",
			},
			want: "333",
		},
		{
			name: "check sidecar message without start sidecar",
			args: args{
				msg: "sidecar check failed: 111\nsidecar check failed: 222\nsidecar check failed: 333",
			},
			want: "333",
		},
		{
			name: "start sidecar message with check sidecar",
			args: args{
				msg: "sidecar start failed: 111\nsidecar check failed: 222",
			},
			want: "111",
		},
		{
			name: "start sidecar message with check sidecar",
			args: args{
				msg: "sidecar start failed: 111\nsidecar check failed: 222\nsidecar check failed: 333",
			},
			want: "111",
		},
		{
			name: "start sidecar message with check sidecar",
			args: args{
				msg: "sidecar start failed: 111\nsidecar start failed: 222\nsidecar check failed: 333\nsidecar check failed: 444",
			},
			want: "222",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getFailedSidecarContainerErrorMsg(tt.args.msg); got != tt.want {
				t.Errorf("getFailedSidecarContainerErrorMsg() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_Controller_getFailedSidecarContainerErrorMessage(t *testing.T) {
	type args struct {
		failedSidecarNames map[string]struct{}
		msg                map[string]string
	}
	tests := []struct {
		name string
		args args
		want map[string]string
	}{
		{
			name: "empty args returns empty map",
			want: map[string]string{},
		},
		{
			name: "test1",
			args: args{
				failedSidecarNames: map[string]struct{}{
					"sidecar1": {},
				},
				msg: map[string]string{
					"sidecar1": "message1",
				},
			},
			want: map[string]string{
				"sidecar1": "",
			},
		},
		{
			name: "test2",
			args: args{
				failedSidecarNames: map[string]struct{}{
					"sidecar1": {},
					"sidecar2": {},
				},
				msg: map[string]string{
					"sidecar1": "message1",
					"sidecar2": "message2",
				},
			},
			want: map[string]string{
				"sidecar1": "",
				"sidecar2": "",
			},
		},
		{
			name: "test2",
			args: args{
				failedSidecarNames: map[string]struct{}{
					"sidecar1": {},
					"sidecar2": {},
				},
				msg: map[string]string{
					"sidecar1": "sidecar start failed: 111",
					"sidecar2": "sidecar start failed: 222",
				},
			},
			want: map[string]string{
				"sidecar1": "111",
				"sidecar2": "222",
			},
		},
		{
			name: "test3",
			args: args{
				failedSidecarNames: map[string]struct{}{
					"sidecar1": {},
					"sidecar2": {},
				},
				msg: map[string]string{
					"sidecar1": "sidecar start failed: 111",
				},
			},
			want: map[string]string{
				"sidecar1": "111",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getFailedSidecarContainerErrorMessage(tt.args.failedSidecarNames, tt.args.msg); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getFailedSidecarContainerErrorMessage() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_Controller_getSidecarContainerNames(t *testing.T) {
	type args struct {
		pod *corev1.Pod
	}
	tests := []struct {
		name string
		args args
		want map[string]struct{}
	}{
		{
			name: "no annotation on pod",
			args: args{
				pod: &corev1.Pod{},
			},
			want: map[string]struct{}{},
		},
		{
			name: "annotation on pod",
			args: args{
				pod: &corev1.Pod{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: map[string]string{
							sidecarContainerAnnotationKey: "foo",
						},
					},
				},
			},
			want: map[string]struct{}{"foo": {}},
		},
		{
			name: "annotation on pod",
			args: args{
				pod: &corev1.Pod{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: map[string]string{
							sidecarContainerAnnotationKey: "test1,test2",
						},
					},
				},
			},
			want: map[string]struct{}{
				"test1": {},
				"test2": {},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getSidecarContainerNames(tt.args.pod); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getSidecarContainerNames() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_Controller_buildSidecarEventMessage(t *testing.T) {
	type args struct {
		containerName string
		msg           string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test with nfs",
			args: args{
				containerName: "bcs-nfs",
				msg:           "",
			},
			want: "bci internal component nfs init failed",
		},
		{
			name: "test with pfs",
			args: args{
				containerName: "bcs-pfs",
				msg:           "",
			},
			want: "bci internal component pfs init failed",
		},
		{
			name: "test with log",
			args: args{
				containerName: "bcs-log",
				msg:           "",
			},
			want: "bci internal component bls init failed",
		},
		{
			name: "test with kube-proxy",
			args: args{
				containerName: "bcs-kube-proxy",
				msg:           "",
			},
			want: "bci internal component kubeproxy init failed",
		},
		{
			name: "test with empty",
			args: args{
				containerName: "",
				msg:           "",
			},
			want: "bci internal component init failed",
		},
		{
			name: "test with not empty",
			args: args{
				containerName: "",
				msg:           "ttt",
			},
			want: "ttt",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := buildSidecarEventMessage(tt.args.containerName, tt.args.msg); got != tt.want {
				t.Errorf("buildSidecarEventMessage() = %v, want %v", got, tt.want)
			}
		})
	}
}
