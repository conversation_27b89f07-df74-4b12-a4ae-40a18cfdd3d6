package monitorsync

import (
	"reflect"
	"sort"
	"strings"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync/bcm"
	prom "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync/prometheus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func Test_syncMonitorOnce(t *testing.T) {
	controller := &Controller{
		promClient: prom.NewClient("", "", "", "", 1, 30),
		nsToPodNameToPodMeta: map[string]map[string]*PodMeta{
			"n1": {
				"p1": {
					shortID:        "p1-short",
					accountID:      "n1-user",
					containerNames: []string{"c1", "c2", ""},
					lastMetrics: map[string]map[string]prom.BciMetricValue{
						"container_cpu_usage_seconds_total": {
							"":   {TimestampSec: **********, Value: 0.025},
							"c1": {TimestampSec: **********, Value: 0.01},
							"c2": {TimestampSec: **********, Value: 0.01},
						},
						"container_network_receive_bytes_total": {
							"":   {TimestampSec: **********, Value: 0.02},
							"c1": {TimestampSec: **********, Value: 0.01},
							"c2": {TimestampSec: **********, Value: 0.01},
						},
					},
				},
			},
			"n2": {
				"p2": {
					shortID:        "p2-short",
					accountID:      "n2-user",
					containerNames: []string{"c1", "c2", ""},
					lastMetrics: map[string]map[string]prom.BciMetricValue{
						"container_cpu_usage_seconds_total": {
							"":   {TimestampSec: **********, Value: 0.025},
							"c1": {TimestampSec: **********, Value: 0.01},
							"c2": {TimestampSec: **********, Value: 0.01},
						},
						"container_network_receive_bytes_total": {
							"":   {TimestampSec: **********, Value: 0.02},
							"c1": {TimestampSec: **********, Value: 0.01},
							"c2": {TimestampSec: **********, Value: 0.01},
						},
					},
				},
			},
		},
		lastSyncTimestamp:             **********,
		bcmPushContainerCountPerBatch: 150,
		bcmPushPodCountPerBatch:       10,
		bcmPushThreadNum:              2,
	}
	gomonkey.ApplyMethod(controller.promClient, "GetMonitorData", GetMonitorData)
	fakeBCMClient := &FakeBCMClient{}
	controller.bcmClient = fakeBCMClient
	gomonkey.ApplyPrivateMethod(controller, "getLastSyncTimestampFromApiserver", getLastSyncTimestampFromApiserver)
	controller.apiServerClient = &FakeAPIServerClient{}

	tests := []struct {
		name          string
		controller    *Controller
		fakeBCMClient *FakeBCMClient
		startTime     int64
		endTime       int64
		response      map[string][]*prom.BciMetric
		wantError     bool
		wantMetrics   map[string][]*bcm.Metric
		wantAggrTask  []*bcm.AggrTaskData
	}{
		{
			name:          "test1",
			controller:    controller,
			fakeBCMClient: fakeBCMClient,
			startTime:     1000,
			endTime:       2000,
			response: map[string][]*prom.BciMetric{
				"container_cpu_usage_seconds_total": {
					{Namespace: "n1", PodName: "p1", ContainerName: "c1",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 0.02}, {TimestampSec: 1666776728, Value: 0.04}}},
					{Namespace: "n1", PodName: "p1", ContainerName: "c2",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 0.03}, {TimestampSec: 1666776728, Value: 0.05}}},
					{Namespace: "n1", PodName: "p1", ContainerName: "POD",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 0.01}, {TimestampSec: 1666776728, Value: 0.01}}},
					{Namespace: "n1", PodName: "p1", ContainerName: "",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776728, Value: 0.12}}},
					{Namespace: "n2", PodName: "p2", ContainerName: "c1",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776728, Value: 0.04}}},
					{Namespace: "n2", PodName: "p2", ContainerName: "c2",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 0.03}, {TimestampSec: 1666776728, Value: 0.05}}},
					{Namespace: "n2", PodName: "p2", ContainerName: "POD",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 0.005}, {TimestampSec: 1666776728, Value: 0.01}}},
					{Namespace: "n2", PodName: "p2", ContainerName: "",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 0.06}, {TimestampSec: 1666776728, Value: 0.12}}},
					{Namespace: "n2", PodName: "p3", ContainerName: "",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 0.06}, {TimestampSec: 1666776728, Value: 0.12}}},
				},
				"container_memory_working_set_bytes": {
					{Namespace: "n1", PodName: "p1", ContainerName: "c1",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776728, Value: 4}}},
					{Namespace: "n1", PodName: "p1", ContainerName: "c2",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 3}, {TimestampSec: 1666776728, Value: 5}}},
					{Namespace: "n1", PodName: "p1", ContainerName: "POD",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 1}, {TimestampSec: 1666776728, Value: 1}}},
					{Namespace: "n1", PodName: "p1", ContainerName: "",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 4}, {TimestampSec: 1666776728, Value: 12}}},
					{Namespace: "n2", PodName: "p2", ContainerName: "c1",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 2}, {TimestampSec: 1666776728, Value: 4}}},
					{Namespace: "n2", PodName: "p2", ContainerName: "c2",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 3}, {TimestampSec: 1666776728, Value: 5}}},
					{Namespace: "n2", PodName: "p2", ContainerName: "POD",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 1}, {TimestampSec: 1666776728, Value: 1}}},
					{Namespace: "n2", PodName: "p2", ContainerName: "",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776728, Value: 12}}},
				},
				"container_network_receive_bytes_total": {
					{Namespace: "n1", PodName: "p1", ContainerName: "POD",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 0.06}, {TimestampSec: 1666776728, Value: 0.12}}},
					{Namespace: "n2", PodName: "p2", ContainerName: "POD",
						Values: []prom.BciMetricValue{{TimestampSec: 1666776668, Value: 0.06}, {TimestampSec: 1666776728, Value: 0.12}}},
				},
			},
			wantError: false,
			wantMetrics: map[string][]*bcm.Metric{
				"n1-user": {
					{
						MetricName: "CpuUsage",
						Dimensions: []*bcm.Dimension{{Name: DimensionPodShortID,
							Value: "p1-short"}, {Name: DimensionContainerName,
							Value: "c1"}},
						Value:     0.5,
						Timestamp: metav1.NewTime((time.Unix(1666776668, 0))),
					},
					{
						MetricName: "CpuUsage",
						Dimensions: []*bcm.Dimension{{Name: DimensionPodShortID,
							Value: "p1-short"}, {Name: DimensionContainerName,
							Value: "c1"}},
						Value:     0.03333333333333333,
						Timestamp: metav1.NewTime((time.Unix(1666776728, 0))),
					},
					{
						MetricName: "CpuUsage",
						Dimensions: []*bcm.Dimension{{Name: DimensionPodShortID,
							Value: "p1-short"}, {Name: DimensionContainerName,
							Value: "c2"}},
						Value:     0.9999999999999999,
						Timestamp: metav1.NewTime((time.Unix(1666776668, 0))),
					},
					{
						MetricName: "CpuUsage",
						Dimensions: []*bcm.Dimension{{Name: DimensionPodShortID,
							Value: "p1-short"}, {Name: DimensionContainerName,
							Value: "c2"}},
						Value:     0.03333333333333334,
						Timestamp: metav1.NewTime((time.Unix(1666776728, 0))),
					},
					{
						MetricName: "MemoryWorkingSetBytes",
						Dimensions: []*bcm.Dimension{{Name: DimensionPodShortID,
							Value: "p1-short"}, {Name: DimensionContainerName,
							Value: "c1"}},
						Value:     4,
						Timestamp: metav1.NewTime((time.Unix(1666776728, 0))),
					},
					{
						MetricName: "MemoryWorkingSetBytes",
						Dimensions: []*bcm.Dimension{{Name: DimensionPodShortID,
							Value: "p1-short"}, {Name: DimensionContainerName,
							Value: "c2"}},
						Value:     3,
						Timestamp: metav1.NewTime((time.Unix(1666776668, 0))),
					},
					{
						MetricName: "MemoryWorkingSetBytes",
						Dimensions: []*bcm.Dimension{{Name: DimensionPodShortID,
							Value: "p1-short"}, {Name: DimensionContainerName,
							Value: "c2"}},
						Value:     5,
						Timestamp: metav1.NewTime((time.Unix(1666776728, 0))),
					},

					{
						MetricName: "NetworkReceiveBits",
						Dimensions: []*bcm.Dimension{{Name: DimensionPodShortID,
							Value: "p1-short"}, {Name: DimensionContainerName,
							Value: "c1"}},
						Value:     0.024999999999999998 * 8,
						Timestamp: metav1.NewTime((time.Unix(1666776668, 0))),
					},
					{
						MetricName: "NetworkReceiveBits",
						Dimensions: []*bcm.Dimension{{Name: DimensionPodShortID,
							Value: "p1-short"}, {Name: DimensionContainerName,
							Value: "c1"}},
						Value:     0.008,
						Timestamp: metav1.NewTime((time.Unix(1666776728, 0))),
					},
					{
						MetricName: "NetworkReceiveBits",
						Dimensions: []*bcm.Dimension{{Name: DimensionPodShortID,
							Value: "p1-short"}, {Name: DimensionContainerName,
							Value: "c2"}},
						Value:     0.024999999999999998 * 8,
						Timestamp: metav1.NewTime((time.Unix(1666776668, 0))),
					},
					{
						MetricName: "NetworkReceiveBits",
						Dimensions: []*bcm.Dimension{{Name: DimensionPodShortID,
							Value: "p1-short"}, {Name: DimensionContainerName,
							Value: "c2"}},
						Value:     0.008,
						Timestamp: metav1.NewTime((time.Unix(1666776728, 0))),
					},
				},
			},
			wantAggrTask: []*bcm.AggrTaskData{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakePromClient.ToBeReturnedError = nil
			fakePromClient.ToBeReturnedResponse = tt.response
			if got := tt.controller.syncMonitorOnce([]string{"test"}, tt.startTime, tt.endTime); (got != nil) != tt.wantError {
				t.Errorf("syncMonitorOnce() = %+v, want %+v", got, tt.wantError)
				return
			}
			if len(tt.wantMetrics["n1-user"]) != len(tt.fakeBCMClient.pushedMetrics["n1-user"]) {
				t.Errorf("syncMonitorOnce() failed, length of metrics is not equal")
				return
			}
			if len(tt.wantAggrTask) != len(tt.fakeBCMClient.pushedAggrTaskData) {
				t.Errorf("syncMonitorOnce() failed, length of aggrTask is not equal")
				return
			}
			wantMetrcis := SortableBcmMetricValues(tt.wantMetrics["n1-user"])
			sort.Sort(wantMetrcis)
			actualValues := SortableBcmMetricValues(tt.fakeBCMClient.pushedMetrics["n1-user"])
			sort.Sort(actualValues)
			for index, wantMetric := range wantMetrcis {
				actualMetric := actualValues[index]
				for dimenssionIdx, dimenssion := range wantMetric.Dimensions {
					if !reflect.DeepEqual(dimenssion, actualMetric.Dimensions[dimenssionIdx]) {
						t.Errorf("syncMonitorOnce() failed, dimenssion of metrics is not equal")
						return
					}
				}
				wantMetric.Dimensions = nil
				actualMetric.Dimensions = nil
				if !reflect.DeepEqual(wantMetric, actualMetric) {
					t.Errorf("syncMonitorOnce() failed, metrics is not equal, want: %+v, actual: %+v", wantMetric, actualMetric)
				}
			}

			/*
				wantPods := SortableBcmAggrTaskDatas(tt.wantAggrTask)
				sort.Sort(wantPods)
				actualPods := SortableBcmAggrTaskDatas(tt.fakeBCMClient.pushedAggrTaskData)
				sort.Sort(actualPods)
				for index, wantPod := range wantPods {
					actualPod := actualPods[index]
					if !reflect.DeepEqual(actualPod.Meta, wantPod.Meta) {
						t.Errorf("syncMonitorOnce() failed, meta of pod is not equal")
						return

					}
					wantPod.Meta = nil
					actualPod.Meta = nil
					if !reflect.DeepEqual(wantPod, actualPod) {
						t.Errorf("syncMonitorOnce() failed, pod is not equal")
					}
				}
			*/
		})
	}
}

type SortableBcmMetricValues []*bcm.Metric

func (s SortableBcmMetricValues) Less(i, j int) bool {
	res := strings.Compare(s[i].MetricName, s[j].MetricName)
	if res < 0 {
		return true
	} else if res > 0 {
		return false
	}
	res = strings.Compare(s[i].Dimensions[0].Value, s[j].Dimensions[0].Value)
	if res < 0 {
		return true
	} else if res > 0 {
		return false
	}
	res = strings.Compare(s[i].Dimensions[1].Value, s[j].Dimensions[1].Value)
	if res < 0 {
		return true
	} else if res > 0 {
		return false
	}
	if s[i].Timestamp.After(s[j].Timestamp.Time) {
		return false
	}
	return true
}
func (s SortableBcmMetricValues) Len() int      { return len(s) }
func (s SortableBcmMetricValues) Swap(i, j int) { s[i], s[j] = s[j], s[i] }

type SortableBcmAggrTaskDatas []*bcm.AggrTaskData

func (s SortableBcmAggrTaskDatas) Less(i, j int) bool {
	res := strings.Compare(s[i].Metric, s[j].Metric)
	if res < 0 {
		return true
	} else if res > 0 {
		return false
	}
	res = strings.Compare(s[i].Tags["userId"], s[j].Tags["userId"])
	if res < 0 {
		return true
	} else if res > 0 {
		return false
	}
	res = strings.Compare(s[i].Tags["Pod"], s[j].Tags["Pod"])
	if res < 0 {
		return true
	} else if res > 0 {
		return false
	}
	if s[i].Timestamp < s[j].Timestamp {
		return true
	}
	return false
}
func (s SortableBcmAggrTaskDatas) Len() int      { return len(s) }
func (s SortableBcmAggrTaskDatas) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
