package monitorsync

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync/bcm"
	prom "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync/prometheus"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	restclient "k8s.io/client-go/rest"
	toolscache "k8s.io/client-go/tools/cache"
	toolscmd "k8s.io/client-go/tools/clientcmd"
)

const (
	DimensionPodShortID          = "PodShortID"
	DimensionContainerName       = "ContainerName"
	LabelAccountID               = "AccountID"
	LabelShortID                 = "podId"
	FieldNameOfLastSyncTimestamp = "LastSyncTimestamp"
)

type BCMMeta struct {
	name   string
	factor int
}

var (
	bciMetricNameToBcm = map[string]BCMMeta{
		// CpuUsage in BCM is percentage, thus factor is 100.
		prom.MetricNameContainerCPUUsageSeconds:       {"CpuUsage", 100},
		prom.MetricNameContainerMemoryWorkingSetBytes: {"MemoryWorkingSetBytes", 1},
		prom.MetricNameContainerFSReadsBytes:          {"FsReadsBytes", 1},
		prom.MetricNameContainerFSReads:               {"FsReads", 1},
		prom.MetricNameContainerFSWritesBytes:         {"FsWritesBytes", 1},
		prom.MetricNameContainerFSWrites:              {"FsWrites", 1},
		// NetworkReceive in BCM is bits, but is bytes in bci, thus factor is 8
		prom.MetricNameContainerNetworkReceiveBytes:   {"NetworkReceiveBits", 8},
		prom.MetricNameContainerNetworkReceivePackets: {"NetworkReceivePackets", 1},
		// NetworkTransmit in BCM is bits, but is bytes in bci, thus factor is 8
		prom.MetricNameContainerNetworkTransmitBytes:   {"NetworkTransmitBits", 8},
		prom.MetricNameContainerNetworkTransmitPackets: {"NetworkTransmitPackets", 1},

		// GPU 相关指标
		// GPU 利用率
		prom.DcgmFiDevGpuUtil:     {"DcgmFiDevGpuUtil", 1},
		prom.DcgmFiDevMemCopyUtil: {"DcgmFiDevMemCopyUtil", 1},
		prom.DcgmFiDevEncUtil:     {"DcgmFiDevEncUtil", 1},
		prom.DcgmFiDevDecUtil:     {"DcgmFiDevDecUtil", 1},

		// 内存
		prom.DcgmFiDevFbFree: {"DcgmFiDevFbFree", 1},
		prom.DcgmFiDevFbUsed: {"DcgmFiDevFbUsed", 1},

		// 剖析
		prom.DcgmFiProfSmActive:      {"DcgmFiProfSmActive", 1},
		prom.DcgmFiProfSmOccupancy:   {"DcgmFiProfSmOccupancy", 1},
		prom.DcgmFiProfPcieTxBytes:   {"DcgmFiProfPcieTxBytes", 1},
		prom.DcgmFiProfPcieRxBytes:   {"DcgmFiProfPcieRxBytes", 1},
		prom.DcgmFiProfNvlinkRxBytes: {"DcgmFiProfNvlinkRxBytes", 1},
		prom.DcgmFiProfNvlinkTxBytes: {"DcgmFiProfNvlinkTxBytes", 1},

		// 频率
		prom.DcgmFiDevSmClock:  {"DcgmFiDevSmClock", 1},
		prom.DcgmFiDevMemClock: {"DcgmFiDevMemClock", 1},

		// 温度&功率
		prom.DcgmFiDevMemoryTemp: {"DcgmFiDevMemoryTemp", 1},
		prom.DcgmFiDevGpuTemp:    {"DcgmFiDevGpuTemp", 1},
		prom.DcgmFiDevPowerUsage: {"DcgmFiDevPowerUsage", 1},
	}
)

// ns -> podName -> metricName -> containerName -> metricValue
type NewestMetricValueMap map[string]map[string]map[string]map[string]prom.BciMetricValue

func recordNewestValue(newestMetricValueMap NewestMetricValueMap, newestValue prom.BciMetricValue, ns, pod, container, metric string) NewestMetricValueMap {
	if _, exist := newestMetricValueMap[ns]; !exist {
		newestMetricValueMap[ns] = make(map[string]map[string]map[string]prom.BciMetricValue)
	}
	if _, exist := newestMetricValueMap[ns][pod]; !exist {
		newestMetricValueMap[ns][pod] = make(map[string]map[string]prom.BciMetricValue)
	}
	if _, exist := newestMetricValueMap[ns][pod][metric]; !exist {
		newestMetricValueMap[ns][pod][metric] = make(map[string]prom.BciMetricValue)
	}
	newestMetricValueMap[ns][pod][metric][container] = newestValue
	return newestMetricValueMap
}

func newBCMMetric(metricName, podShortID, containerName string, sampleTime time.Time, value float64) *bcm.Metric {
	if value < 0 {
		value = 0
	}
	return &bcm.Metric{
		MetricName: bciMetricNameToBcm[metricName].name,
		Dimensions: newDimension(podShortID, containerName),
		Value:      value * float64(bciMetricNameToBcm[metricName].factor),
		Timestamp:  metav1.NewTime(sampleTime),
	}
}

func newAggrTaskData(region, accountID, podShortID, metricName string, dataSources []string, timestamp time.Time, value float64) []*bcm.AggrTaskData {
	result := make([]*bcm.AggrTaskData, 0, len(dataSources))
	if value < 0 {
		value = 0
	}
	for _, dataSource := range dataSources {
		result = append(result, &bcm.AggrTaskData{
			Metric:    bciMetricNameToBcm[metricName].name,
			Timestamp: timestamp.Unix() * 1000,
			ValueType: "double",
			Value:     value * float64(bciMetricNameToBcm[metricName].factor),
			Tags: map[string]string{
				"userId":      accountID,
				"Pod":         podShortID,
				"region":      region,
				"serviceName": bcm.ServiceNameBCI,
			},
			// meta param is pre-defined in bcm
			Meta: &bcm.AggrTaskMeta{
				Cycle:      "60",
				DataSource: dataSource,
				Downstream: "saver,alert",
			},
		})
	}
	return result
}

func toBCMRegion(region string) string {
	if region == "sandbox" {
		return "bj"
	}
	return region
}

func newDimension(podShortID string, containerName string) []*bcm.Dimension {
	v := []*bcm.Dimension{
		{
			Name:  DimensionPodShortID,
			Value: podShortID,
		},
	}
	if containerName != "" {
		v = append(v, &bcm.Dimension{
			Name:  DimensionContainerName,
			Value: containerName,
		})
	}
	return v
}

func (controller *Controller) getPodMeta(metric *prom.BciMetric) (*PodMeta, bool) {
	// nsToPodNameToPodMeta 可能会被并发读写，因此需要加锁
	controller.podMetaRWMutex.RLock()
	defer controller.podMetaRWMutex.RUnlock()
	podNameToPodMeta, exist := controller.nsToPodNameToPodMeta[metric.Namespace]
	if !exist {
		return nil, false
	}
	podMeta, exist := podNameToPodMeta[metric.PodName]
	if !exist {
		return nil, false
	}
	return podMeta, true
}

func parsePod(obj interface{}) (accountID string, shortID string, pod *v1.Pod) {
	pod, ok := obj.(*v1.Pod)
	if !ok {
		return
	}
	podLabels := pod.GetLabels()
	accountID = podLabels[LabelAccountID]
	if accountID == "" {
		// this is not an pod created by user.
		return
	}
	shortID = podLabels[LabelShortID]
	if shortID == "" {
		// this is not an pod created by user.
		return
	}
	return
}

func (controller *Controller) getLastSyncTimestampFromApiserver() (*v1.ConfigMap, int64, error) {
	lister := corev1_listers.NewConfigMapLister(controller.resInformerOfLastSyncTimestamp.(toolscache.SharedIndexInformer).GetIndexer())
	configmap, err := lister.ConfigMaps(controller.resNsOfLastSyncTimestamp).Get(controller.resNameOfLastSyncTimestamp)
	if err != nil {
		return nil, 0, fmt.Errorf("get LastSyncTimestamp failed, error msg is %w", err)
	}
	lastSyncTimestampStrInStore, exist := configmap.Data[FieldNameOfLastSyncTimestamp]
	if !exist {
		return nil, 0, fmt.Errorf("get LastSyncTimestamp failed, LastSyncTimestamp not exist in configmap")
	}
	lastSyncTimestampInStore, err := strconv.ParseInt(lastSyncTimestampStrInStore, 10, 64)
	if err != nil {
		return nil, 0, fmt.Errorf("parse LastSyncTimestampString from configmap failed, error msg is %w", err)
	}
	return configmap, lastSyncTimestampInStore, nil
}

func getKubeconfigs(monitorCceOptions *options.MonitorSyncCceOptions) ([]*restclient.Config, error) {
	cceOptions, err := getCceOptions(monitorCceOptions)
	if err != nil {
		return nil, err
	}
	result := make([]*restclient.Config, 0, len(cceOptions))
	for _, cceOption := range cceOptions {
		cceClient, err := util.NewCceClient(cceOption)
		if err != nil {
			return nil, err
		}
		kubeconfigResp, err := cceClient.GetKubeconfig(cceOption.ClusterID)
		if err != nil {
			return nil, err
		}
		kubeconfig, err := toolscmd.RESTConfigFromKubeConfig([]byte(kubeconfigResp.KubeConfig))
		if err != nil {
			return nil, err
		}
		result = append(result, kubeconfig)

	}

	return result, nil
}

func getCceOptions(monitorCceOptions *options.MonitorSyncCceOptions) ([]*options.CceOptions, error) {
	clusterIDs := splitByComma(monitorCceOptions.ClusterIDs)
	endpoints := splitByComma(monitorCceOptions.CceEndpoints)
	accessKeyIDs := splitByComma(monitorCceOptions.CceAccessKeyIDs)
	accessKeys := splitByComma(monitorCceOptions.CceSecretAccessKeys)
	if len(clusterIDs) != len(endpoints) || len(endpoints) != len(accessKeyIDs) || len(accessKeyIDs) != len(accessKeys) {
		return nil, fmt.Errorf("parse MonitorSyncCceOptions failed, length of clusterIDs/CceEndpoints/CceAccessKeyIDs/CceSecretAccessKeys is not equal")
	}
	result := make([]*options.CceOptions, 0, len(clusterIDs))
	for index, clusterID := range clusterIDs {
		result = append(result, &options.CceOptions{
			CceAccessKeyID:     accessKeyIDs[index],
			CceSecretAccessKey: accessKeys[index],
			CceEndpoint:        endpoints[index],
			ClusterID:          clusterID,
		})
	}
	return result, nil
}

func splitByComma(toBeSplitStr string) []string {
	trimedStr := strings.Trim(toBeSplitStr, " ")
	splits := strings.Split(trimedStr, ",")
	result := make([]string, 0, len(splits))
	for _, split := range splits {
		result = append(result, strings.Trim(split, " "))
	}
	return result
}
