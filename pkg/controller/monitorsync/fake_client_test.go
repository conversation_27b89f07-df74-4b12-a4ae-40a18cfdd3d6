package monitorsync

import (
	"context"
	"sync"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync/bcm"
	prom "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync/prometheus"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type FakePromClient struct {
	ToBeReturnedResponse map[string][]*prom.BciMetric
	ToBeReturnedError    error
}

var fakePromClient = &FakePromClient{}

func GetMonitorData(client prom.Client, clusterIDs []string, startTimestampSec int64, endTimestampSec int64) (map[string][]*prom.BciMetric, error) {
	return fakePromClient.ToBeReturnedResponse, fakePromClient.ToBeReturnedError
}

type FakeBCMClient struct {
	pushedMetrics      map[string][]*bcm.Metric
	pushedAggrTaskData []*bcm.AggrTaskData
	mutex              sync.Mutex
}

func (client *FakeBCMClient) BatchRegisterContainers(ctx context.Context, args []*bcm.RegisterContainerArgs) (err error) {
	return nil
}

func (client *FakeBCMClient) BatchDeleteContainers(ctx context.Context, args []*bcm.DeleteContainerArgs) (err error) {
	return nil
}

func (client *FakeBCMClient) PushMetrics(ctx context.Context, userID string, metrics []*bcm.Metric) (err error) {
	defer client.mutex.Unlock()
	client.mutex.Lock()
	if client.pushedMetrics == nil {
		client.pushedMetrics = make(map[string][]*bcm.Metric)
	}
	client.pushedMetrics[userID] = metrics
	return nil
}

func (client *FakeBCMClient) PushAggrTaskData(ctx context.Context, data []*bcm.AggrTaskData) (err error) {
	defer client.mutex.Unlock()
	client.mutex.Lock()
	client.pushedAggrTaskData = data
	return nil
}

func (client *FakeBCMClient) SetDebug(bool) {}

func (client *FakeBCMClient) DealPushMetricsJob(job bcm.DataPushJob) (err error) {
	containerMonitorDataPushJob := job.(bcm.ContainerMonitorDataPushJob)
	return client.PushMetrics(containerMonitorDataPushJob.Context, containerMonitorDataPushJob.AccountID,
		containerMonitorDataPushJob.Metrics)
}

func (client *FakeBCMClient) DealPushAggrTaskDataJob(job bcm.DataPushJob) (err error) {
	podMonitorDataPushJob := job.(bcm.PodMonitorDataPushJob)
	return client.PushAggrTaskData(podMonitorDataPushJob.Context, podMonitorDataPushJob.Metrics)
}

var fakeLastSyncTimestamp int64

func getLastSyncTimestampFromApiserver(c *Controller) (*v1.ConfigMap, int64, error) {
	return nil, fakeLastSyncTimestamp, nil
}

type FakeAPIServerClient struct{}

func (c *FakeAPIServerClient) Get(ctx context.Context, key client.ObjectKey, obj client.Object) error {
	return nil
}

func (c *FakeAPIServerClient) List(ctx context.Context, list client.ObjectList, opts ...client.ListOption) error {
	return nil
}

func (c *FakeAPIServerClient) Create(ctx context.Context, obj client.Object, opts ...client.CreateOption) error {
	return nil
}

func (c *FakeAPIServerClient) Delete(ctx context.Context, obj client.Object, opts ...client.DeleteOption) error {
	return nil
}

func (c *FakeAPIServerClient) Update(ctx context.Context, obj client.Object, opts ...client.UpdateOption) error {
	return nil
}

func (c *FakeAPIServerClient) Patch(ctx context.Context, obj client.Object, patch client.Patch, opts ...client.PatchOption) error {
	return nil
}

func (c *FakeAPIServerClient) DeleteAllOf(ctx context.Context, obj client.Object, opts ...client.DeleteAllOfOption) error {
	return nil
}

func (c *FakeAPIServerClient) Status() client.StatusWriter {
	return nil
}

func (c *FakeAPIServerClient) Scheme() *runtime.Scheme {
	return nil
}

func (c *FakeAPIServerClient) RESTMapper() meta.RESTMapper {
	return nil
}
