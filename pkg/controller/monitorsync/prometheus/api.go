package prometheus

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
)

type PromMetricValue []interface{}

type PromMetric struct {
	Metric map[string]string `json:"metric"`
	Values []PromMetricValue `json:"values"`
}

type PromRspResult struct {
	ResultType string        `json:"resultType"`
	Result     []*PromMetric `json:"result"`
}

type PromRspBody struct {
	Status    string         `json:"status"`
	IsPartial bool           `json:"isPartial"`
	Data      *PromRspResult `json:"data"`
}

func (c *client) getPromMetrics(clusterIDs []string, metricName string, startTimestampSec, endTimestampSec int64) ([]*PromMetric, error) {
	request, err := c.newRequest(clusterIDs, metricName, startTimestampSec, endTimestampSec)
	if err != nil {
		return nil, fmt.Errorf("get monitor data from cprom failed, error msg is %w", err)
	}
	response, err := c.httpClient.Do(request)
	if err != nil {
		return nil, fmt.Errorf("get monitor data from cprom failed, error msg is %w", err)
	}
	defer response.Body.Close()
	bodyStr, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, fmt.Errorf("read body failed when getting monitor data from cprom, error msg is %w, metricName: %s, startTimestamp: %d, endTimestamp: %d",
			err, metricName, startTimestampSec, endTimestampSec)
	}
	if response.StatusCode != 200 {
		return nil, fmt.Errorf("get monitor data from cprom failed, error code is %d, errResp is %+v, metricName: %s, startTimestamp: %d, endTimestamp: %d",
			response.StatusCode, string(bodyStr), metricName, startTimestampSec, endTimestampSec)
	}
	var bodyResult PromRspBody
	if err := json.Unmarshal(bodyStr, &bodyResult); err != nil {
		return nil, fmt.Errorf("unmarshal body failed when getting monitor data from cprom, error msg is %w", err)
	}
	if bodyResult.Status != "success" {
		return nil, fmt.Errorf("status in body is not success when getting monitor data from cprom, error body is %s", bodyStr)
	}
	return bodyResult.Data.Result, nil
}

func (c *client) newRequest(clusterIDs []string, metricName string, startTimestampSec int64, endTimestampSec int64) (*http.Request, error) {
	url := fmt.Sprintf("%s/%s", c.host, c.uri)
	body := fmt.Sprintf("query=%s&", metricName)
	if len(clusterIDs) == 1 {
		body = body + fmt.Sprintf("cluster_id=%s&", clusterIDs[0])
	} else if len(clusterIDs) > 1 {
		body = body + fmt.Sprintf("cluster_id=~'%s'&", strings.Join(clusterIDs, "|"))
	}
	body = body + fmt.Sprintf("step=%d&", c.queryStepSec)
	body = body + fmt.Sprintf("start=%d&", startTimestampSec)
	body = body + fmt.Sprintf("end=%d&", endTimestampSec)
	req, err := http.NewRequest(http.MethodPost, url, strings.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("new request failed, url is %s, body is %s, error msg is %w", url, body, err)
	}
	req.Header.Add("InstanceId", c.instanceID)
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", c.authToken))
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	return req, nil
}
