package prometheus

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"k8s.io/klog/v2"
)

type MetricType string

const (
	MetricConstDCGM = "DCGM"
)

const (
	MetricTypeCounter MetricType = "COUNTER"
	MetricTypeGauge   MetricType = "GAUGE"
)

type MetricMeta struct {
	MetricType MetricType
	// some metrics only exist in pause container, e.g. network related metrics
	// we should extend these metrics to normal container.
	NeedExtendToNormalContainer bool
	// 对于部分指标而言，pod粒度指标值无法采集，只有container粒度指标，因此在此处通过聚合container粒度指标得到pod粒度指标
	AggrPodMetricByContainerMetric bool
}

// metrics to be pull
const (
	MetricNameContainerCPUUsageSeconds        = "container_cpu_usage_seconds_total"
	MetricNameContainerMemoryWorkingSetBytes  = "container_memory_working_set_bytes"
	MetricNameContainerFSReadsBytes           = "container_fs_reads_bytes_total"
	MetricNameContainerFSReads                = "container_fs_reads_total"
	MetricNameContainerFSWritesBytes          = "container_fs_writes_bytes_total"
	MetricNameContainerFSWrites               = "container_fs_writes_total"
	MetricNameContainerNetworkReceiveBytes    = "container_network_receive_bytes_total"
	MetricNameContainerNetworkReceivePackets  = "container_network_receive_packets_total"
	MetricNameContainerNetworkTransmitBytes   = "container_network_transmit_bytes_total"
	MetricNameContainerNetworkTransmitPackets = "container_network_transmit_packets_total"

	// DCGM 采集的GPU指标
	// DcgmFiDevGpuUtil(Gauge) 表示GPU利用率，即在一个周期时间内（1s或1/6s，根据GPU产品而定），一个或多个核函数处于Active的时间。
	DcgmFiDevGpuUtil = "DCGM_FI_DEV_GPU_UTIL"
	// DcgmFiDevMemCopyUtil(Gauge) 表示内存带宽利用率。
	DcgmFiDevMemCopyUtil = "DCGM_FI_DEV_MEM_COPY_UTIL"
	// DcgmFiDevEncUtil(Gauge) 表示编码器利用率。
	DcgmFiDevEncUtil = "DCGM_FI_DEV_ENC_UTIL"
	// DcgmFiDevDecUtil(Gauge) 表示解码器利用率。
	DcgmFiDevDecUtil = "DCGM_FI_DEV_DEC_UTIL"

	// 内存
	// DcgmFiDevFbFree(Gauge) 表示帧缓存（Framebuffer Memory）剩余数。
	DcgmFiDevFbFree = "DCGM_FI_DEV_FB_FREE"
	// DcgmFiDevFbUsed(Gauge) 表示帧缓存已使用数。
	DcgmFiDevFbUsed = "DCGM_FI_DEV_FB_USED"

	// 剖析
	// DcgmFiProfSmActive(Gauge) 表示在一个时间间隔内，至少一个线程束在一个SM（Streaming Multiprocessor）上处于Active的时间占比。
	DcgmFiProfSmActive = "DCGM_FI_PROF_SM_ACTIVE"
	// DcgmFiProfSmOccupancy(Gauge) 表示在一个时间间隔内，驻留在SM上的线程束与该SM最大可驻留线程束的比例。
	DcgmFiProfSmOccupancy = "DCGM_FI_PROF_SM_OCCUPANCY"
	// DcgmFiProfPcieTxBytes(Counter)  表示通过PCIe总线传输的数据速率，包括协议标头和数据有效负载。
	DcgmFiProfPcieTxBytes = "DCGM_FI_PROF_PCIE_TX_BYTES"
	// DcgmFiProfPcieRxBytes(Counter)  表示通过PCIe总线接收的数据速率，包括协议标头和数据有效负载。
	DcgmFiProfPcieRxBytes = "DCGM_FI_PROF_PCIE_RX_BYTES"
	// DcgmFiProfNvlinkRxBytes(Counter) 表示通过NVLink接收的数据速率，不包括协议标头。
	DcgmFiProfNvlinkRxBytes = "DCGM_FI_PROF_NVLINK_RX_BYTES"
	// DcgmFiProfNvlinkTxBytes(Counter) 表示通过NVLink传输的数据速率，不包括协议标头。
	DcgmFiProfNvlinkTxBytes = "DCGM_FI_PROF_NVLINK_TX_BYTES"

	// 频率
	// DcgmFiDevSmClock(Gauge) 表示SM时钟频率。
	DcgmFiDevSmClock = "DCGM_FI_DEV_SM_CLOCK"
	// DcgmFiDevMemClock(Gauge) 表示内存时钟频率。
	DcgmFiDevMemClock = "DCGM_FI_DEV_MEM_CLOCK"

	// 温度&功率
	// DcgmFiDevMemoryTemp(Gauge) 表示内存温度。
	DcgmFiDevMemoryTemp = "DCGM_FI_DEV_MEMORY_TEMP"
	// DcgmFiDevGpuTemp(Gauge) 表示GPU温度。
	DcgmFiDevGpuTemp = "DCGM_FI_DEV_GPU_TEMP"
	// DcgmFiDevPowerUsage(Gauge) 表示功率。
	DcgmFiDevPowerUsage = "DCGM_FI_DEV_POWER_USAGE"
)

var (
	metricNameToMeta = map[string]*MetricMeta{
		MetricNameContainerCPUUsageSeconds:        {MetricType: MetricTypeCounter, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		MetricNameContainerMemoryWorkingSetBytes:  {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		MetricNameContainerFSReadsBytes:           {MetricType: MetricTypeCounter, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: true},
		MetricNameContainerFSReads:                {MetricType: MetricTypeCounter, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: true},
		MetricNameContainerFSWritesBytes:          {MetricType: MetricTypeCounter, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: true},
		MetricNameContainerFSWrites:               {MetricType: MetricTypeCounter, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: true},
		MetricNameContainerNetworkReceiveBytes:    {MetricType: MetricTypeCounter, NeedExtendToNormalContainer: true, AggrPodMetricByContainerMetric: true},
		MetricNameContainerNetworkReceivePackets:  {MetricType: MetricTypeCounter, NeedExtendToNormalContainer: true, AggrPodMetricByContainerMetric: true},
		MetricNameContainerNetworkTransmitBytes:   {MetricType: MetricTypeCounter, NeedExtendToNormalContainer: true, AggrPodMetricByContainerMetric: true},
		MetricNameContainerNetworkTransmitPackets: {MetricType: MetricTypeCounter, NeedExtendToNormalContainer: true, AggrPodMetricByContainerMetric: true},

		DcgmFiDevGpuUtil:     {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		DcgmFiDevMemCopyUtil: {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		DcgmFiDevEncUtil:     {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		DcgmFiDevDecUtil:     {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},

		DcgmFiDevFbFree: {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		DcgmFiDevFbUsed: {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},

		DcgmFiProfSmActive:      {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		DcgmFiProfSmOccupancy:   {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		DcgmFiProfPcieTxBytes:   {MetricType: MetricTypeCounter, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		DcgmFiProfPcieRxBytes:   {MetricType: MetricTypeCounter, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		DcgmFiProfNvlinkRxBytes: {MetricType: MetricTypeCounter, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		DcgmFiProfNvlinkTxBytes: {MetricType: MetricTypeCounter, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},

		DcgmFiDevSmClock:  {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		DcgmFiDevMemClock: {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},

		DcgmFiDevMemoryTemp: {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		DcgmFiDevGpuTemp:    {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
		DcgmFiDevPowerUsage: {MetricType: MetricTypeGauge, NeedExtendToNormalContainer: false, AggrPodMetricByContainerMetric: false},
	}
)

// reserved word list used to identify belonging of metric
const (
	labelNameNamespace            = "namespace"
	labelNamePodName              = "pod"
	labelNameContainerName        = "container"
	labelNameImageName            = "image"
	PauseContainerImageIdentifier = "pause"
	PauseContainerName            = "POD"

	labelDCGMNameNamespace     = "pod_namespace"
	labelDCGMNamePodName       = "pod_name"
	labelDCGMNameContainerName = "pod_container_name"
)

// the first item is timestamp of value
// the second item is metric value
type BciMetricValue struct {
	TimestampSec int64
	Value        float64
}

type BciMetric struct {
	Namespace     string
	PodName       string
	ContainerName string
	Values        []BciMetricValue
}

var _ Client = &client{}

type Client interface {
	GetMonitorData(clusterIDs []string, startTimestampSec int64, endTimestampSec int64) (map[string][]*BciMetric, error)
}

type client struct {
	httpClient   *http.Client
	host         string
	uri          string
	instanceID   string
	authToken    string
	metricNames  []string
	queryStepSec int
}

func NewClient(host, uri, instanceID, authToken string, reqTimeoutSec, queryStepSec int) *client {
	httpClient := &http.Client{
		Transport:     http.DefaultTransport,
		CheckRedirect: nil,
		Jar:           nil,
		Timeout:       time.Duration(reqTimeoutSec) * time.Second,
	}
	metricNames := make([]string, 0, len(metricNameToMeta))
	for k := range metricNameToMeta {
		metricNames = append(metricNames, k)
	}
	return &client{
		httpClient:   httpClient,
		host:         host,
		uri:          uri,
		instanceID:   instanceID,
		authToken:    authToken,
		metricNames:  metricNames,
		queryStepSec: queryStepSec,
	}
}

func (c *client) GetMonitorDataOfOneMetric(clusterIDs []string, metricName string, startTimestampSec int64, endTimestampSec int64,
	ch chan<- map[string][]*BciMetric) {
	promMetrics, err := c.getPromMetrics(clusterIDs, metricName, startTimestampSec, endTimestampSec)
	if err != nil {
		klog.Errorf("get monitor data from cprom failed, error is %w", err)
		ch <- nil
		return
	}
	nsToPodNameToContNameToValues := make(map[string]map[string]map[string][]BciMetricValue)
	metricCnt := 0
	for _, promMetric := range promMetrics {
		if dropNonePodContainer(promMetric.Metric[labelNamePodName]) && dropNonePodContainer(promMetric.Metric[labelDCGMNamePodName]) {
			continue
		}
		// STEP1.prometheus 格式指标转换为 bci 格式
		bciMetric, err := convertToBciMetric(promMetric)
		if err != nil {
			// metric is invalid, just skip it.
			klog.Warningf("%w", err)
			continue
		}
		ns := bciMetric.Namespace
		podName := bciMetric.PodName
		containerName := bciMetric.ContainerName
		bciMetricValues := bciMetric.Values

		if _, exist := nsToPodNameToContNameToValues[ns]; !exist {
			nsToPodNameToContNameToValues[ns] = make(map[string]map[string][]BciMetricValue)
		}
		if _, exist := nsToPodNameToContNameToValues[ns][podName]; !exist {
			nsToPodNameToContNameToValues[ns][podName] = make(map[string][]BciMetricValue)
		}
		// STEP2. 聚合生成容器粒度指标
		// 同一个容器，同一个指标，同一时间，可能会有多个指标记录，比如blkio和网络相关指标。
		// 因为同一个容器可能挂载多个块设备、网卡设备，每一个块设备、网卡设备都有对应的指标记录。
		// 因此，需要求和聚合它们。
		if values, exist := nsToPodNameToContNameToValues[ns][podName][containerName]; exist {
			if err := sumExtraToBase(values, bciMetricValues); err != nil {
				// TODO：同一个容器，同一个指标，不同的设备采样时间点可能是不一样的，如果从 prom 获取指标的 step 恰好比 prom 的采样间隔小一些，就会导致在部分 step 点，有一些设备存在数据，一些设备不存在数据。
				//       假设从 prom 获取数据的时间段是 0 时刻到 15 时刻，step是5，指标采样间隔是 6，同一个容器有两个设备 A 和 B，A 的原始数据时间序列是 5 -> 11 -> 17，B 的原始数据时间序列是 5.1 -> 11.1 -> 17.1
				//       那么，从 prom 获取导致的数据则是（具体算法参考 prom 的 range query）：A，5 -> 空 -> 15，B，空 -> 10 -> 15
				//       这种场景产生的数据，是没办法 sum 聚合的。只能选择丢弃该条数据。以后如果修复该问题，可以考虑调整 step 大于采样间隔。
				//       目前，在测试中发现，这种不一致的数据，基本是几百分之一，可以暂时忽略。
				klog.V(4).Infof("aggr metrics failed, %w", err)
				continue
			}
		} else {
			nsToPodNameToContNameToValues[ns][podName][containerName] = bciMetricValues
		}
		metricCnt += 1
	}
	// STEP3. 聚合生成Pod粒度指标
	// 有一些指标，比如container_fs_reads_bytes_total，在container粒度可以有效采集指标值，但是在pod粒度无法有效采集，需要从container中聚合得到pod粒度指标值
	if GetMetricMetaByName(metricName).AggrPodMetricByContainerMetric {
		for _, podNameToContNameToValues := range nsToPodNameToContNameToValues {
			for _, contNameToValues := range podNameToContNameToValues {
				var podSandboxValues []BciMetricValue
				for contName, contValues := range contNameToValues {
					if contName == "" {
						continue
					}
					if podSandboxValues == nil {
						podSandboxValues = contValues
					} else {
						if err := sumExtraToBase(podSandboxValues, contValues); err != nil {
							klog.V(4).Infof("aggr metrics failed, %w", err)
						}
					}
				}
				// pod下没有可聚合的容器粒度指标记录
				if podSandboxValues == nil {
					continue
				}
				// 新增一个指标记录，metricCnt 加一
				if _, ok := contNameToValues[""]; !ok {
					metricCnt += 1
				}
				contNameToValues[""] = podSandboxValues
			}
		}
	}
	metrics := make([]*BciMetric, 0, metricCnt)
	for ns, podNameToContNameToValues := range nsToPodNameToContNameToValues {
		for podName, contNameToValues := range podNameToContNameToValues {
			for contName, values := range contNameToValues {
				metrics = append(metrics, &BciMetric{
					Namespace:     ns,
					PodName:       podName,
					ContainerName: contName,
					Values:        values,
				})
			}
		}
	}
	ch <- map[string][]*BciMetric{metricName: metrics}
}

func convertToBciMetric(promMetric *PromMetric) (*BciMetric, error) {
	bciMetricValues, err := convertToBciMetricValues(promMetric.Values)
	if err != nil {
		return nil, fmt.Errorf("prom metric is invalid, it's value is %+v", promMetric)
	}
	namespace := ""
	if value, ok := promMetric.Metric[labelNameNamespace]; ok {
		namespace = value
	}
	if value, ok := promMetric.Metric[labelDCGMNameNamespace]; ok {
		namespace = value
	}

	podName := ""
	if value, ok := promMetric.Metric[labelNamePodName]; ok {
		podName = value
	}
	if value, ok := promMetric.Metric[labelDCGMNamePodName]; ok {
		podName = value
	}

	return &BciMetric{
		Namespace:     namespace,
		PodName:       podName,
		ContainerName: getContainerName(promMetric.Metric),
		Values:        bciMetricValues,
	}, nil
}

func getContainerName(metricLabels map[string]string) string {
	if containerName, ok := metricLabels[labelNameContainerName]; ok {
		return containerName
	}
	if containerName, ok := metricLabels[labelDCGMNameContainerName]; ok {
		return containerName
	}
	if image, ok := metricLabels[labelNameImageName]; ok && strings.Contains(image, PauseContainerImageIdentifier) {
		return PauseContainerName
	}
	return ""
}

func (c *client) GetMonitorData(clusterIDs []string, startTimestampSec int64, endTimestampSec int64) (map[string][]*BciMetric, error) {
	result := make(map[string][]*BciMetric)
	ch := make(chan map[string][]*BciMetric, len(c.metricNames))
	for _, metricName := range c.metricNames {
		go c.GetMonitorDataOfOneMetric(clusterIDs, metricName, startTimestampSec, endTimestampSec, ch)
	}
	for range c.metricNames {
		monitorData := <-ch
		if monitorData == nil {
			return nil, fmt.Errorf("GetMonitorData failed")
		}
		for k, v := range monitorData {
			result[k] = v
		}
	}
	return result, nil
}

// base and extra are prom metric value list.
func sumExtraToBase(base []BciMetricValue, extra []BciMetricValue) error {
	// check length of base and extra must be equal.
	if len(base) != len(extra) {
		return fmt.Errorf("can not sum extra to base, because their lengths are different")
	}
	// check all timestamps of base and extra must be equal.
	for index, extraValue := range extra {
		if extraValue.TimestampSec != base[index].TimestampSec {
			return fmt.Errorf("timestamps of base and extra must be equal")
		}
	}
	for index, extraValue := range extra {
		base[index].Value = base[index].Value + extraValue.Value
	}
	return nil
}

func dropNonePodContainer(podName string) bool {
	return podName == ""
}

func GetMetricMetaByName(metricName string) *MetricMeta {
	return metricNameToMeta[metricName]
}

func convertToBciMetricValues(promMetricValues []PromMetricValue) ([]BciMetricValue, error) {
	bciMetricValues := make([]BciMetricValue, 0, len(promMetricValues))
	for _, promMetricValue := range promMetricValues {
		bciMetricValue, err := convertToBciMetricValue(promMetricValue)
		if err != nil {
			return nil, err
		}
		bciMetricValues = append(bciMetricValues, *bciMetricValue)
	}
	return bciMetricValues, nil
}

func convertToBciMetricValue(promMetricValue PromMetricValue) (*BciMetricValue, error) {
	value, err := strconv.ParseFloat(promMetricValue[1].(string), 64)
	if err != nil {
		return nil, err
	}
	//value := promMetricValue[1].(float64)
	return &BciMetricValue{
		TimestampSec: int64(promMetricValue[0].(float64)),
		Value:        value,
	}, nil
}

// ReverseSortableBciMetricValues lets us to sort BciMetricValues.
type ReverseSortableBciMetricValues []BciMetricValue

func (s ReverseSortableBciMetricValues) Less(i, j int) bool {
	// reverse sort
	return s[i].TimestampSec > s[j].TimestampSec
}
func (s ReverseSortableBciMetricValues) Len() int      { return len(s) }
func (s ReverseSortableBciMetricValues) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
