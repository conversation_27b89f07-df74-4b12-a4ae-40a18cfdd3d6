package prometheus

import (
	"fmt"
	"io"
	"net/http"
	"reflect"
	"sort"
	"strings"
	"sync"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
)

type FakeHTTPClient struct {
	SendedRequest        *http.Request
	ToBeReturnedIndex    int
	ToBeReturnedResponse []*http.Response
	ToBeReturnedError    []error
}

var fakeHTTPClient = &FakeHTTPClient{}

var lock = &sync.RWMutex{}

func Do(client *http.Client, req *http.Request) (resp *http.Response, err error) {
	lock.Lock()
	defer lock.Unlock()
	fakeHTTPClient.SendedRequest = req
	resp = fakeHTTPClient.ToBeReturnedResponse[fakeHTTPClient.ToBeReturnedIndex]
	err = fakeHTTPClient.ToBeReturnedError[fakeHTTPClient.ToBeReturnedIndex]
	fakeHTTPClient.ToBeReturnedIndex += 1
	return
}

func Test_NewClient(t *testing.T) {
	metricNames := make([]string, 0, len(metricNameToMeta))
	for k := range metricNameToMeta {
		metricNames = append(metricNames, k)
	}
	tests := []struct {
		name          string
		host          string
		uri           string
		instanceID    string
		authToken     string
		reqTimeoutSec int
		queryStepSec  int
		want          *client
	}{
		{
			name:          "test1",
			host:          "host",
			uri:           "uri",
			instanceID:    "account",
			authToken:     "token",
			reqTimeoutSec: 1,
			queryStepSec:  30,
			want: &client{
				httpClient:   nil,
				host:         "host",
				uri:          "uri",
				instanceID:   "account",
				authToken:    "token",
				metricNames:  metricNames,
				queryStepSec: 30,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := NewClient(tt.host, tt.uri, tt.instanceID, tt.authToken, tt.reqTimeoutSec, tt.queryStepSec)
			got.httpClient = nil
			sort.Strings(got.metricNames)
			sort.Strings(tt.want.metricNames)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewClient() = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func Test_GetMonitorData(t *testing.T) {
	cli := NewClient("http://host.com", "uri", "account", "token", 1, 2)
	gomonkey.ApplyMethod(cli.httpClient, "Do", Do)
	cli.metricNames = []string{"container_cpu_usage_seconds_total", "container_network_receive_bytes_total"}
	tests := []struct {
		name              string
		promCli           *client
		response          []*http.Response
		err               []error
		startTimestampSec int64
		endTimestampSec   int64
		want              map[string][]*BciMetric
		wantError         bool
	}{
		{
			name:    "test1",
			promCli: cli,
			response: []*http.Response{
				{
					Status:     "200 OK",
					StatusCode: 200,
					Body: io.NopCloser(strings.NewReader(`{
					"status": "success",
					"isPartial": false,
					"data": {
					   "resultType": "matrix",
					   "result": [
						  {
							 "metric": {
								"__name__": "container_cpu_usage_seconds_total",
								"container": "c0",
								"pod": "p0",
								"namespace": "kube-system"
							 },
							 "values":[[**********,"0.02"],[**********,"0.04"]]
						  },
						  {
							"metric": {
							   "__name__": "container_cpu_usage_seconds_total",
							   "container": "c1",
							   "pod": "p0",
							   "namespace": "kube-system"
							},
						    "values":[[**********,"0.03"],[**********,"0.05"]]
						 }
					   ]
					}
				  }`)),
				},
				{
					Status:     "200 OK",
					StatusCode: 200,
					Body: io.NopCloser(strings.NewReader(`{
					"status": "success",
					"isPartial": false,
					"data": {
					   "resultType": "matrix",
					   "result": [
						 {
							"metric": {
							   "__name__": "container_network_receive_bytes_total",
							   "container": "c0",
							   "pod": "p0",
							   "namespace": "kube-system",
							   "device": "d1"
							},
						    "values":[[**********,"0.02"],[**********,"0.04"]]
						 },
						 {
							"metric": {
							   "__name__": "container_network_receive_bytes_total",
							   "container": "c0",
							   "pod": "p0",
							   "namespace": "kube-system",
							   "device": "d2"
							},
						    "values":[[**********,"0.02"],[**********,"0.04"]]
						 },
						 {
							"metric": {
							   "__name__": "container_network_receive_bytes_total",
							   "container": "c0",
							   "pod": "p0",
							   "namespace": "kube-system",
							   "device": "d3"
							},
						    "values":[[**********,"0.02"],[**********,"0.04"]]
						 }
					   ]
					}
				  }`)),
				},
			},
			err:               []error{nil, nil},
			startTimestampSec: int64(10),
			endTimestampSec:   int64(20),
			want: map[string][]*BciMetric{
				"container_cpu_usage_seconds_total": {
					{Namespace: "kube-system", PodName: "p0", ContainerName: "c0",
						Values: []BciMetricValue{{TimestampSec: **********, Value: 0.02}, {TimestampSec: **********, Value: 0.04}}},
					{Namespace: "kube-system", PodName: "p0", ContainerName: "c1",
						Values: []BciMetricValue{{TimestampSec: **********, Value: 0.03}, {TimestampSec: **********, Value: 0.05}}},
				},
				"container_network_receive_bytes_total": {
					{Namespace: "kube-system", PodName: "p0", ContainerName: "c0",
						Values: []BciMetricValue{{TimestampSec: **********, Value: 0.06}, {TimestampSec: **********, Value: 0.12}}},
				},
			},
			wantError: false,
		},
		{
			name:    "test2",
			promCli: cli,
			response: []*http.Response{
				{
					Status:     "404 NotFound",
					StatusCode: 404,
					Body: io.NopCloser(strings.NewReader(`{
					"status": "success",
					"isPartial": false,
					"data": {
					   "resultType": "matrix",
					   "result": [
						 {
							"metric": {
							   "__name__": "container_network_receive_bytes_total",
							   "container": "c0",
							   "pod": "p0",
							   "namespace": "kube-system",
							   "device": "d1"
							},
						    "values":[[**********,"0.02"],[**********,"0.04"]]
						 },
					   ]
					}
				  }`)),
				},
				{
					Status:     "404 NotFound",
					StatusCode: 404,
					Body: io.NopCloser(strings.NewReader(`{
					"status": "success",
					"isPartial": false,
					"data": {
					   "resultType": "matrix",
					   "result": [
						 {
							"metric": {
							   "__name__": "container_network_receive_bytes_total",
							   "container": "c0",
							   "pod": "p0",
							   "namespace": "kube-system",
							   "device": "d1"
							},
						    "values":[[**********,"0.02"],[**********,"0.04"]]
						 },
					   ]
					}
				  }`)),
				},
			},
			err:               []error{nil, nil},
			startTimestampSec: int64(10),
			endTimestampSec:   int64(20),
			want:              nil,
			wantError:         true,
		},
		{
			name:    "test3",
			promCli: cli,
			response: []*http.Response{
				{
					Status:     "200 OK",
					StatusCode: 200,
					Body: io.NopCloser(strings.NewReader(`{
					"status": "success",
					"isPartial": false,
					"data": {
					   "resultType": "matrix",
					   "result": [
						 {
							"metric": {
							   "__name__": "container_network_receive_bytes_total",
							   "container": "c0",
							   "pod": "p0",
							   "namespace": "kube-system",
							   "device": "d1"
							},
						    "values":[[**********,"0.02"],[**********,"0.04"]]
						 },
					   ]
					}
				  }`)),
				},
				{
					Status:     "200 OK",
					StatusCode: 200,
					Body: io.NopCloser(strings.NewReader(`{
					"status": "success",
					"isPartial": false,
					"data": {
					   "resultType": "matrix",
					   "result": [
						 {
							"metric": {
							   "__name__": "container_network_receive_bytes_total",
							   "container": "c0",
							   "pod": "p0",
							   "namespace": "kube-system",
							   "device": "d1"
							},
						    "values":[[**********,"0.02"],[**********,"0.04"]]
						 },
					   ]
					}
				  }`)),
				},
			},
			err:               []error{fmt.Errorf(""), fmt.Errorf("")},
			startTimestampSec: int64(10),
			endTimestampSec:   int64(20),
			want:              nil,
			wantError:         true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			lock.Lock()
			fakeHTTPClient.ToBeReturnedIndex = 0
			fakeHTTPClient.ToBeReturnedResponse = tt.response
			fakeHTTPClient.ToBeReturnedError = tt.err
			lock.Unlock()
			_, err := tt.promCli.GetMonitorData([]string{"test"}, tt.startTimestampSec, tt.endTimestampSec)
			if (err != nil) != tt.wantError {
				t.Errorf("GetMonitorData() failed, got: %+v, want %+v", err, tt.wantError)
				return
			}
		})
	}
}

func TestSort(t *testing.T) {
	t.Run("test1", func(t *testing.T) {
		values := []BciMetricValue{{TimestampSec: 1, Value: 10}, {TimestampSec: 3, Value: 10}, {TimestampSec: 2, Value: 10}}
		reversedValues := ReverseSortableBciMetricValues(values)
		sort.Sort(reversedValues)
		for index, value := range values {
			if value.TimestampSec != int64(3-index) {
				t.Errorf("reverse sort failed, reversedValues is %+v", reversedValues)
			}
		}
	})
}

func Test_getContainerName(t *testing.T) {
	tests := []struct {
		name   string
		labels map[string]string
		want   string
	}{
		{
			name:   "test1",
			labels: map[string]string{"container": "c01"},
			want:   "c01",
		},
		{
			name:   "test2",
			labels: map[string]string{"image": "registry.baidubce.com/cce-public/pause:3.1"},
			want:   "POD",
		},
		{
			name:   "test3",
			labels: map[string]string{},
			want:   "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := getContainerName(tt.labels)
			if !reflect.DeepEqual(tt.want, actual) {
				t.Errorf("getContainerName() failed, want is %+v, actual is %+v", tt.want, actual)
			}
		})
	}
}
