package bcm

import (
	"context"
	"fmt"
	"testing"
	"time"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/bce"
)

var fakeBceClient = &FakeBceClient{
	ToBeReturnedResponse: &bce.Response{},
	ToBeReturnedError:    nil,
}

func SendRequest(bceClient *bce.Client, ctx context.Context, req *bce.Request, option *bce.SignOption) (bceResponse *bce.Response, err error) {
	fakeBceClient.SendedRequest = req
	return fakeBceClient.ToBeReturnedResponse, fakeBceClient.ToBeReturnedError
}

func Test_PushAggrTaskData(t *testing.T) {
	timeNow := time.Now()
	bcmClient := &client{
		Client: &bce.Client{
			Config: &bce.Config{
				APIVersion: "v1",
			},
		},
		aggrTaskEndpoint: "arrg.task.endpoint",
		credential: &bce.Credentials{
			AccessKeyID:     "ak",
			SecretAccessKey: "sk",
		},
	}
	gomonkey.ApplyMethod(bcmClient.Client, "SendRequest", SendRequest)
	tests := []struct {
		name      string
		client    *client
		datas     []*AggrTaskData
		wantError bool
	}{
		{
			name:   "test1",
			client: bcmClient,
			datas: []*AggrTaskData{{
				Metric:    "metric-1",
				Timestamp: timeNow.Unix() * 1000,
				ValueType: "double",
				Value:     float64(1024),
				Tags: map[string]string{
					"userId":      "user-1",
					"Pod":         "pod-1",
					"region":      "bj",
					"serviceName": ServiceNameBCI,
				},
				// meta param is pre-defined in bcm
				Meta: &AggrTaskMeta{
					Cycle:      "60",
					DataSource: "bci",
					Downstream: "saver,alert",
				},
			}},
			wantError: false,
		},
		{
			name: "test1",
			client: &client{
				Client: &bce.Client{
					Config: &bce.Config{
						APIVersion: "v1",
					},
				},
				aggrTaskEndpoint: "arrg.task.endpoint",
				iamClient: &FakeIamClient{
					err: fmt.Errorf(""),
				},
			},
			datas: []*AggrTaskData{{
				Metric:    "metric-1",
				Timestamp: timeNow.Unix() * 1000,
				ValueType: "double",
				Value:     float64(1024),
				Tags: map[string]string{
					"userId":      "user-1",
					"Pod":         "pod-1",
					"region":      "bj",
					"serviceName": ServiceNameBCI,
				},
				// meta param is pre-defined in bcm
				Meta: &AggrTaskMeta{
					Cycle:      "60",
					DataSource: "bci",
					Downstream: "saver,alert",
				},
			}},
			wantError: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.client.PushAggrTaskData(context.TODO(), tt.datas); (got != nil) != tt.wantError {
				t.Errorf("PushAggrTaskData() = %+v, want %+v", got, tt.wantError)
			}
		})
	}
}
