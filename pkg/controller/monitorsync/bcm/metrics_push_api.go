package bcm

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	klog "k8s.io/klog/v2"
)

type Metric struct {
	Region     string       `json:"region,omitempty"`
	MetricName string       `json:"metricName"`
	Dimensions []*Dimension `json:"dimensions"`
	Value      float64      `json:"value"`
	Timestamp  metav1.Time  `json:"timestamp"`
}

type Dimension struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

func (c *client) PushMetrics(ctx context.Context, userID string, metrics []*Metric) (err error) {
	defer func() {
		if err != nil {
			klog.Errorf("[bcmError] PushMetrics err: %v", err)
		}
	}()

	if len(metrics) == 0 {
		return
	}

	postContent, err := json.Marshal(map[string][]*Metric{"metricData": metrics})
	if err != nil {
		return err
	}

	req, err := c.newRequest("POST", c.pushEndpoint, fmt.Sprintf("/json-api/v1/metricdata/%s/%s", userID, c.serviceName), nil, bytes.NewBuffer(postContent))
	if err != nil {
		return
	}

	_, err = c.sendRequest(ctx, req)
	return
}
