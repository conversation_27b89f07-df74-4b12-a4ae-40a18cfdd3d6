package bcm

import (
	"context"
	"io"
	"strings"
	"sync"

	klog "k8s.io/klog/v2"

	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/bce"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/iam"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/util"
)

const (
	ServiceNameBCI         = "BCE_BCI"
	typeContainer          = "Instance"
	typePod                = "Cluster"
	TagPod                 = "Pod"
	DimensionPodShortID    = "PodShortID"
	DimensionContainerName = "ContainerName"
)

// DataPushJob is the interface of ContainerMonitorDataPushJob and PodMonitorDataPushJob
// used as parameter of data push function
type DataPushJob interface{}

// job to push container monitor data to bcm
type ContainerMonitorDataPushJob struct {
	Context   context.Context
	AccountID string
	Metrics   []*Metric
}

// job to push pod monitor data to bcm
type PodMonitorDataPushJob struct {
	Context context.Context
	Metrics []*AggrTaskData
}

type Client interface {
	BatchRegisterContainers(ctx context.Context, args []*RegisterContainerArgs) (err error)
	BatchDeleteContainers(ctx context.Context, args []*DeleteContainerArgs) (err error)
	PushMetrics(ctx context.Context, userID string, metrics []*Metric) (err error)
	PushAggrTaskData(ctx context.Context, data []*AggrTaskData) (err error)
	DealPushMetricsJob(job DataPushJob) (err error)
	DealPushAggrTaskDataJob(job DataPushJob) (err error)
	SetDebug(bool)
}

type client struct {
	*bce.Client
	pushEndpoint     string
	registerEndpoint string
	aggrTaskEndpoint string
	serviceName      string
	serviceID        string
	servicePasswd    string
	iamAccessKey     string
	iamSecretKey     string
	iamClient        iam.Interface
	credential       *bce.Credentials
	mutex            sync.Mutex
}

// Config contains all options for bci.Client.
type Config struct {
	*bce.Config
	iamEndpoint      string
	pushEndpoint     string
	registerEndpoint string
	aggrTaskEndpoint string
	serviceName      string
	serviceID        string
	servicePasswd    string
	iamAccessKey     string
	iamSecretKey     string
}

func (c *client) SetDebug(debug bool) {
	c.iamClient.SetDebug(debug)
	c.Client.SetDebug(debug)
}

func NewConfig(config *bce.Config, iamEndpoint, pushEndpoint, registerEndpoint, aggrTaskEndpoint, serviceName, serviceID, servicePasswd,
	iamAccessKey, iamSecretKey string) *Config {
	return &Config{
		Config:           config,
		iamEndpoint:      iamEndpoint,
		pushEndpoint:     pushEndpoint,
		registerEndpoint: registerEndpoint,
		aggrTaskEndpoint: aggrTaskEndpoint,
		serviceName:      serviceName,
		serviceID:        serviceID,
		servicePasswd:    servicePasswd,
		iamAccessKey:     iamAccessKey,
		iamSecretKey:     iamSecretKey,
	}
}

func NewClient(config *Config) *client {
	bceClient := bce.NewClient(config.Config)
	return &client{
		Client: bceClient,
		iamClient: iam.NewClient(&bce.Config{
			Endpoint: config.iamEndpoint,
		}),
		pushEndpoint:     config.pushEndpoint,
		registerEndpoint: config.registerEndpoint,
		aggrTaskEndpoint: config.aggrTaskEndpoint,
		serviceName:      config.serviceName,
		serviceID:        config.serviceID,
		servicePasswd:    config.servicePasswd,
		iamAccessKey:     config.iamAccessKey,
		iamSecretKey:     config.iamSecretKey,
	}
}

func (c *client) getCredentials(ctx context.Context) error {
	klog.Infof("monitorsync bcm client: iam accessKey is %s, iam secretKey is %s", c.iamAccessKey, c.iamSecretKey)
	c.credential = bce.NewCredentials(c.iamAccessKey, c.iamSecretKey)
	return nil
}

func (c *client) newRequest(method, endpoint, uri string, params map[string]string, body io.Reader) (*bce.Request, error) {
	method = strings.ToUpper(method)
	uri = strings.TrimLeft(uri, "/")

	return bce.NewRequest(method, c.GetURL(endpoint, uri, params), body)
}

func (c *client) sendRequest(ctx context.Context, req *bce.Request) (*bce.Response, error) {
	// get credential will set c.credential, may cause concurrent problem
	c.mutex.Lock()
	if c.credential == nil {
		if err := c.getCredentials(ctx); err != nil {
			c.mutex.Unlock()
			return nil, err
		}
	}
	c.mutex.Unlock()

	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")
	option.Credentials = bce.NewCredentials(c.credential.AccessKeyID, c.credential.SecretAccessKey)
	return c.SendRequest(ctx, req, option)
}

func (c *client) DealPushMetricsJob(job DataPushJob) (err error) {
	containerMonitorDataPushJob := job.(ContainerMonitorDataPushJob)
	return c.PushMetrics(containerMonitorDataPushJob.Context, containerMonitorDataPushJob.AccountID,
		containerMonitorDataPushJob.Metrics)
}

func (c *client) DealPushAggrTaskDataJob(job DataPushJob) (err error) {
	podMonitorDataPushJob := job.(PodMonitorDataPushJob)
	return c.PushAggrTaskData(podMonitorDataPushJob.Context, podMonitorDataPushJob.Metrics)
}

func ToBCMRegion(region string) string {
	if region == "sandbox" {
		return "bj"
	}
	return region
}

func PushDataWorker(f func(DataPushJob) error, jobs <-chan DataPushJob, results chan<- error) {
	for job := range jobs {
		results <- f(job)
	}
}
