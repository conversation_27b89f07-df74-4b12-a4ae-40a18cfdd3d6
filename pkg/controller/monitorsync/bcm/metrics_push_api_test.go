package bcm

import (
	"context"
	"fmt"
	"testing"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/bce"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func Test_PushMetrics(t *testing.T) {
	bcmClient := &client{
		Client: &bce.Client{
			Config: &bce.Config{
				APIVersion: "v1",
			},
		},
		pushEndpoint: "push.endpoint",
		credential: &bce.Credentials{
			AccessKeyID:     "ak",
			SecretAccessKey: "sk",
		},
	}
	gomonkey.ApplyMethod(bcmClient.Client, "SendRequest", SendRequest)
	tests := []struct {
		name      string
		client    *client
		datas     []*Metric
		wantError bool
	}{
		{
			name:   "test1",
			client: bcmClient,
			datas: []*Metric{{
				Region:     "bj",
				MetricName: "cpu_total_usage",
				Dimensions: []*Dimension{{Name: "shortID", Value: "id-1"}},
				Value:      float64(1024),
				Timestamp:  metav1.Now(),
			}},
			wantError: false,
		},
		{
			name: "test2",
			client: &client{
				Client: &bce.Client{
					Config: &bce.Config{
						APIVersion: "v1",
					},
				},
				aggrTaskEndpoint: "arrg.task.endpoint",
				iamClient: &FakeIamClient{
					err: fmt.Errorf(""),
				},
			},
			datas:     make([]*Metric, 0),
			wantError: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.client.PushMetrics(context.TODO(), "user-1", tt.datas); (got != nil) != tt.wantError {
				t.Errorf("PushMetrics() = %+v, want %+v", got, tt.wantError)
			}
		})
	}
}
