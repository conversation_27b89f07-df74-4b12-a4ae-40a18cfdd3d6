package bcm

import (
	"context"
	"reflect"
	"testing"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/bce"
)

func Test_NewRegisterContainerArgs(t *testing.T) {
	tests := []struct {
		name          string
		userID        string
		region        string
		podShortID    string
		containerName string
		want          *RegisterContainerArgs
	}{
		{
			name:          "test1",
			userID:        "zhangsan",
			region:        "bj",
			podShortID:    "pod-1",
			containerName: "container-1",
			want: &RegisterContainerArgs{
				UserID:      "zhangsan",
				Region:      "bj",
				TypeName:    typeContainer,
				Identifiers: NewIdentifiers("pod-1", "container-1"),
			},
		},
		{
			name:          "test2",
			userID:        "lisi",
			region:        "bj",
			podShortID:    "pod-1",
			containerName: "",
			want: &RegisterContainerArgs{
				UserID:      "lisi",
				Region:      "bj",
				TypeName:    typePod,
				Identifiers: NewIdentifiers("pod-1", ""),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewRegisterContainerArgs(tt.userID, tt.region, tt.podShortID, tt.containerName); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewRegisterContainerArgs() = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func Test_BatchRegisterContainers(t *testing.T) {
	bcmClient := &client{
		Client: &bce.Client{
			Config: &bce.Config{
				APIVersion: "v1",
			},
		},
		registerEndpoint: "register.endpoint",
		credential: &bce.Credentials{
			AccessKeyID:     "ak",
			SecretAccessKey: "sk",
		},
	}
	gomonkey.ApplyMethod(bcmClient.Client, "SendRequest", SendRequest)
	tests := []struct {
		name      string
		client    *client
		datas     []*RegisterContainerArgs
		wantError error
	}{
		{
			name:   "test1",
			client: bcmClient,
			datas: []*RegisterContainerArgs{{
				UserID:      "lisi",
				Region:      "bj",
				TypeName:    typePod,
				Identifiers: NewIdentifiers("pod-1", ""),
			}},
			wantError: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.client.BatchRegisterContainers(context.TODO(), tt.datas); got != tt.wantError {
				t.Errorf("BatchRegisterContainers() = %+v, want %+v", got, tt.wantError)
			}
		})
	}
}

func Test_NewDeleteContainerArgs(t *testing.T) {
	tests := []struct {
		name          string
		userID        string
		region        string
		podShortID    string
		containerName string
		want          *DeleteContainerArgs
	}{
		{
			name:          "test1",
			userID:        "zhangsan",
			region:        "bj",
			podShortID:    "pod-1",
			containerName: "container-1",
			want: &DeleteContainerArgs{
				UserID:      "zhangsan",
				Region:      "bj",
				Identifiers: NewIdentifiers("pod-1", "container-1"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewDeleteContainerArgs(tt.userID, tt.region, tt.podShortID, tt.containerName); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewDeleteContainerArgs() = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func Test_BatchDeleteContainers(t *testing.T) {
	bcmClient := &client{
		Client: &bce.Client{
			Config: &bce.Config{
				APIVersion: "v1",
			},
		},
		registerEndpoint: "register.endpoint",
		credential: &bce.Credentials{
			AccessKeyID:     "ak",
			SecretAccessKey: "sk",
		},
	}
	gomonkey.ApplyMethod(bcmClient.Client, "SendRequest", SendRequest)
	tests := []struct {
		name      string
		client    *client
		datas     []*DeleteContainerArgs
		wantError error
	}{
		{
			name:   "test1",
			client: bcmClient,
			datas: []*DeleteContainerArgs{{
				UserID:      "lisi",
				Region:      "bj",
				Identifiers: NewIdentifiers("pod-1", ""),
			}},
			wantError: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.client.BatchDeleteContainers(context.TODO(), tt.datas); got != tt.wantError {
				t.Errorf("BatchDeleteContainers() = %+v, want %+v", got, tt.wantError)
			}
		})
	}
}
