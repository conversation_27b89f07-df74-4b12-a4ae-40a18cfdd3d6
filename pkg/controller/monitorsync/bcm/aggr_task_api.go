package bcm

import (
	"bytes"
	"context"
	"encoding/json"

	klog "k8s.io/klog/v2"
)

type AggrTaskMeta struct {
	Cycle      string `json:"cycle"`
	DataSource string `json:"dataSource"`
	Downstream string `json:"downstream"`
}

type AggrTaskData struct {
	Metric    string            `json:"metric"`
	Timestamp int64             `json:"timestamp"`
	ValueType string            `json:"valueType"`
	Value     float64           `json:"value"`
	Tags      map[string]string `json:"tags"`
	Meta      *AggrTaskMeta     `json:"meta"`
}

func (c *client) PushAggrTaskData(ctx context.Context, data []*AggrTaskData) (err error) {
	defer func() {
		if err != nil {
			klog.Errorf("[bcmError] PushAggrTaskData err: %v", err)
		}
	}()

	if len(data) == 0 {
		return
	}

	postContent, err := json.Marshal(map[string][]*AggrTaskData{"data": data})
	if err != nil {
		return
	}

	req, err := c.newRequest("POST", c.aggrTaskEndpoint, "csm/api/v1/aggregation/callback", nil, bytes.NewBuffer(postContent))
	if err != nil {
		return
	}

	_, err = c.sendRequest(ctx, req)
	return
}
