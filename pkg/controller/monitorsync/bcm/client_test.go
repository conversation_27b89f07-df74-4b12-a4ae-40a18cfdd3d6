package bcm

import (
	"context"
	// "fmt"
	"reflect"
	"testing"

	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/bce"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/iam"
)

type FakeBceClient struct {
	SendedRequest        *bce.Request
	ToBeReturnedResponse *bce.Response
	ToBeReturnedError    error
}

type FakeIamClient struct {
	err error
}

func (c *FakeIamClient) SetDebug(debug bool) {
}

func (c *FakeIamClient) GetToken(ctx context.Context, serviceName string, password string, option *bce.SignOption) (*iam.Token, error) {
	return &iam.Token{}, c.err
}
func (c *FakeIamClient) GetAkSkByToken(ctx context.Context, serviceName string, password string, option *bce.SignOption) (*iam.AccessKey, error) {
	return &iam.AccessKey{}, c.err
}

func Test_NewConfig(t *testing.T) {
	tests := []struct {
		name             string
		bceConfig        *bce.Config
		iamEndpoint      string
		pushEndpoint     string
		registerEndpoint string
		aggrTaskEndpoint string
		serviceName      string
		serviceID        string
		servicePasswd    string
		iamAccessKey     string
		iamSecretKey     string
		want             *Config
	}{
		{
			name:             "test1",
			bceConfig:        nil,
			iamEndpoint:      "iamEndpoint",
			pushEndpoint:     "pushEndpoint",
			registerEndpoint: "registerEndpoint",
			aggrTaskEndpoint: "",
			serviceName:      "",
			serviceID:        "id",
			servicePasswd:    "",
			iamAccessKey:     "",
			iamSecretKey:     "",
			want: &Config{
				Config:           nil,
				iamEndpoint:      "iamEndpoint",
				pushEndpoint:     "pushEndpoint",
				registerEndpoint: "registerEndpoint",
				aggrTaskEndpoint: "",
				serviceName:      "",
				serviceID:        "id",
				servicePasswd:    "",
				iamAccessKey:     "",
				iamSecretKey:     "",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewConfig(tt.bceConfig, tt.iamEndpoint, tt.pushEndpoint, tt.registerEndpoint, tt.aggrTaskEndpoint,
				tt.serviceName, tt.serviceID, tt.servicePasswd, tt.iamAccessKey, tt.iamSecretKey); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewConfig() = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func Test_NewClient(t *testing.T) {
	tests := []struct {
		name   string
		config *Config
		want   *client
	}{
		{
			name: "test1",
			config: &Config{
				Config:           &bce.Config{},
				iamEndpoint:      "iamEndpoint",
				pushEndpoint:     "pushEndpoint",
				registerEndpoint: "registerEndpoint",
				aggrTaskEndpoint: "",
				serviceName:      "",
				serviceID:        "id",
				servicePasswd:    "",
			},
			want: &client{
				Client:           nil,
				iamClient:        nil,
				pushEndpoint:     "pushEndpoint",
				registerEndpoint: "registerEndpoint",
				aggrTaskEndpoint: "",
				serviceName:      "",
				serviceID:        "id",
				servicePasswd:    "",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := NewClient(tt.config)
			got.iamClient = nil
			got.Client = nil
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewClient() = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func Test_getCredentials(t *testing.T) {
	tests := []struct {
		name      string
		client    *client
		wantError bool
	}{
		{
			name:   "test1",
			client: &client{
				// iamClient: &FakeIamClient{
				// 	err: fmt.Errorf("error"),
				// },
			},
			wantError: false,
		},
		{
			name:   "test2",
			client: &client{
				// iamClient: &FakeIamClient{
				// 	err: nil,
				// },
			},
			wantError: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.client.getCredentials(context.TODO()); (got != nil) != tt.wantError {
				t.Errorf("getCredentials() = %+v, want %+v", got, tt.wantError)
			}
		})
	}
}

func Test_SetDebug(t *testing.T) {
	tests := []struct {
		name   string
		client *client
	}{
		{
			name: "test1",
			client: &client{
				iamClient: &FakeIamClient{},
				Client:    &bce.Client{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.client.SetDebug(true)
		})
	}
}

func Test_ToBCMRegion(t *testing.T) {
	tests := []struct {
		name   string
		region string
		want   string
	}{
		{
			name:   "test1",
			region: "sandbox",
			want:   "bj",
		},
		{
			name:   "test2",
			region: "nj",
			want:   "nj",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ToBCMRegion(tt.region); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ToBCMRegion() = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func MockPushJob(job DataPushJob) error {
	return nil
}

func TestPushDataWorker(t *testing.T) {
	jobs := make(chan DataPushJob)
	results := make(chan error)

	go func() {
		jobs <- ContainerMonitorDataPushJob{AccountID: "account1"}
		jobs <- ContainerMonitorDataPushJob{AccountID: "account2"}
		close(jobs)
	}()

	go PushDataWorker(MockPushJob, jobs, results)

	// 验证结果是否符合预期
	err1 := <-results
	if err1 != nil {
		t.Errorf("Expected nil, but got error: %s", err1.Error())
	}

	err2 := <-results
	if err2 != nil {
		t.Errorf("Expected nil, but got error: %s", err2.Error())
	}
}
