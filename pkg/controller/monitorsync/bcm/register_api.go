package bcm

import (
	"bytes"
	"context"
	"encoding/json"

	klog "k8s.io/klog/v2"
)

type RegisterContainerArgs struct {
	UserID      string        `json:"userId"`
	Region      string        `json:"region"`
	ResourceID  string        `json:"resourceId,omitempty"`
	TypeName    string        `json:"typeName"`
	Identifiers []*Identifier `json:"identifiers,omitempty"`
	Properties  []*Property   `json:"properties,omitempty"`
	Tags        []*Tag        `json:"tags,omitempty"`
}

type Identifier struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type Property struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type Tag struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

func NewRegisterContainerArgs(userID, region, podShortID, containerName string) *RegisterContainerArgs {
	if containerName == "" {
		return &RegisterContainerArgs{
			UserID:      userID,
			Region:      region,
			TypeName:    typePod,
			Identifiers: NewIdentifiers(podShortID, ""),
		}
	}
	return &RegisterContainerArgs{
		UserID:      userID,
		Region:      region,
		TypeName:    typeContainer,
		Identifiers: NewIdentifiers(podShortID, containerName),
	}
}

func NewIdentifiers(shortID string, containerName string) []*Identifier {
	v := []*Identifier{
		{
			Name:  DimensionPodShortID,
			Value: shortID,
		},
	}
	if containerName != "" {
		v = append(v, &Identifier{
			Name:  DimensionContainerName,
			Value: containerName,
		})
	}
	return v
}

func NewTags(shortID string) []*Tag {
	return []*Tag{
		{
			Name:  TagPod,
			Value: shortID,
		},
	}
}

func (c *client) BatchRegisterContainers(ctx context.Context, args []*RegisterContainerArgs) (err error) {
	defer func() {
		if err != nil {
			klog.Errorf("[bcmError] BatchRegisterContainers err: %v", err)
		}
	}()
	if len(args) == 0 {
		return
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return
	}

	req, err := c.newRequest("POST", c.registerEndpoint, "mrm-api/v1/services/"+c.serviceName+"/resources/batch/create", nil, bytes.NewBuffer(postContent))
	if err != nil {
		return
	}

	_, err = c.sendRequest(ctx, req)
	return
}

type DeleteContainerArgs struct {
	UserID      string        `json:"userId"`
	Region      string        `json:"region"`
	ResourceID  string        `json:"resourceId,omitempty"`
	Identifiers []*Identifier `json:"identifiers"`
}

func NewDeleteContainerArgs(userID, region, podShortID, containerName string) *DeleteContainerArgs {
	return &DeleteContainerArgs{
		UserID:      userID,
		Region:      region,
		Identifiers: NewIdentifiers(podShortID, containerName),
	}

}

func (c *client) BatchDeleteContainers(ctx context.Context, args []*DeleteContainerArgs) (err error) {
	defer func() {
		if err != nil {
			klog.Errorf("[bcmError] BatchDeleteContainers err: %v", err)
		}
	}()
	if len(args) == 0 {
		return
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return
	}

	req, err := c.newRequest("POST", c.registerEndpoint, "mrm-api/v1/services/"+c.serviceName+"/resources/batch/delete", nil, bytes.NewBuffer(postContent))
	if err != nil {
		return
	}

	_, err = c.sendRequest(ctx, req)
	return
}
