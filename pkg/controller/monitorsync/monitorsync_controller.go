package monitorsync

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	options "icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync/bcm"
	prom "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync/prometheus"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime/inject"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/bce"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	toolscache "k8s.io/client-go/tools/cache"
	klog "k8s.io/klog/v2"
	controllercache "sigs.k8s.io/controller-runtime/pkg/cache"
	controllerclient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/manager"
)

const (
	ControllerName = "monitorsync"
)

type PodMeta struct {
	shortID        string
	accountID      string
	containerNames []string
	// metricName -> containerName -> value
	lastMetrics map[string]map[string]prom.BciMetricValue
}

var _ inject.StartFuncInjector = &Controller{}

type Controller struct {
	// global information
	enabled                        bool
	region                         string
	clusterIDs                     []string
	syncIntervalSec                int64
	resNsOfLastSyncTimestamp       string
	resNameOfLastSyncTimestamp     string
	resInformerOfLastSyncTimestamp controllercache.Informer
	apiServerClient                controllerclient.Client

	// watched cce cluster informations
	cceCaches       []controllercache.Cache
	ccePodInformers []controllercache.Informer

	// bcm and prom informations
	bcmClient                     bcm.Client
	bcmRegister                   Registrar
	promClient                    prom.Client
	bcmAggrTaskDataSources        []string
	bcmPushContainerCountPerBatch int
	bcmPushPodCountPerBatch       int
	bcmPushThreadNum              int

	// dynamically changing information
	nsToPodNameToPodMeta map[string]map[string]*PodMeta
	podMetaRWMutex       sync.RWMutex
	lastSyncTimestamp    int64
}

func NewBcmClient(options *options.ServerRunOptions) bcm.Client {
	bcmOptions := options.MonitorSyncOptions.BcmOptions
	bcmClient := bcm.NewClient(bcm.NewConfig(&bce.Config{
		Timeout:     60 * time.Second,
		RetryPolicy: bce.NewDefaultRetryPolicy(0, 0),
		Protocol:    "http",
	}, bcmOptions.IamEndpoint, bcmOptions.BcmPushEndpoint, bcmOptions.BcmRegisterEndpoint, bcmOptions.BcmAggrTaskEndpoint,
		bcm.ServiceNameBCI, bcmOptions.BciServiceID, bcmOptions.BciServicePasswd, options.BciIamAccessKey, options.BciIamSecretKey))
	bcmClient.SetDebug(bcmOptions.Debug)
	return bcmClient
}

// NewController returns a new *Controller.
func New(option *options.ServerRunOptions, mgr manager.Manager) (interface{}, error) {
	if !option.MonitorSyncOptions.Enabled {
		return &Controller{enabled: false}, nil
	}
	resInformerOfLastSyncTimestamp, err := mgr.GetCache().GetInformer(context.TODO(), &corev1.ConfigMap{})
	if err != nil {
		return nil, fmt.Errorf("get informer of lastSyncTimestamp failed, error is %w", err)
	}

	kubeconfigs, err := getKubeconfigs(&option.MonitorSyncOptions.CceOptions)
	if err != nil {
		return nil, fmt.Errorf("get kubeconfigs of cce clusters failed, error is %w", err)
	}
	cceCaches := make([]controllercache.Cache, 0, len(kubeconfigs))
	ccePodInformers := make([]controllercache.Informer, 0, len(kubeconfigs))
	for _, cceKubeconfig := range kubeconfigs {
		klog.Infof("kubeconfig is %+v", cceKubeconfig)
		cache, err := controllercache.New(cceKubeconfig, controllercache.Options{})
		if err != nil {
			return nil, fmt.Errorf("get cache of cce failed, error is %w", err)
		}
		podinformer, err := cache.GetInformer(context.TODO(), &corev1.Pod{})
		if err != nil {
			return nil, fmt.Errorf("get pod informer of cce clusters failed, error is %w", err)
		}
		cceCaches = append(cceCaches, cache)
		ccePodInformers = append(ccePodInformers, podinformer)
	}

	promOptions := option.MonitorSyncOptions.PromOptions
	promClient := prom.NewClient(promOptions.Host, promOptions.URI, promOptions.InstanceID, promOptions.AuthToken, promOptions.ReqTimeoutSec,
		promOptions.QueryStepSec)
	bcmClient := NewBcmClient(option)
	bcmRegister := NewRegistrar(bcmClient, option.MonitorSyncOptions.Region)
	c := &Controller{
		enabled:                        true,
		region:                         option.MonitorSyncOptions.Region,
		syncIntervalSec:                option.MonitorSyncOptions.SyncIntervalSec,
		resNsOfLastSyncTimestamp:       option.MonitorSyncOptions.NsOfLastSyncTimestamp,
		resNameOfLastSyncTimestamp:     option.MonitorSyncOptions.ResNameOfLastSyncTimestamp,
		bcmPushContainerCountPerBatch:  option.MonitorSyncOptions.BcmOptions.BcmPushContainerCountPerBatch,
		bcmPushPodCountPerBatch:        option.MonitorSyncOptions.BcmOptions.BcmPushPodCountPerBatch,
		bcmPushThreadNum:               option.MonitorSyncOptions.BcmOptions.BcmPushThreadNum,
		resInformerOfLastSyncTimestamp: resInformerOfLastSyncTimestamp,
		apiServerClient:                mgr.GetClient(),
		cceCaches:                      cceCaches,
		ccePodInformers:                ccePodInformers,
		bcmClient:                      bcmClient,
		bcmRegister:                    bcmRegister,
		promClient:                     promClient,
		bcmAggrTaskDataSources:         splitByComma(option.MonitorSyncOptions.BcmOptions.BcmAggrTaskDataSources),
		podMetaRWMutex:                 sync.RWMutex{},
		clusterIDs:                     strings.Split(option.MonitorSyncOptions.CceOptions.ClusterIDs, ","),
	}

	for _, podInformer := range ccePodInformers {
		podInformer.AddEventHandler(
			toolscache.ResourceEventHandlerFuncs{
				AddFunc:    c.registerMonitorObj,
				DeleteFunc: c.unRegisterMonitorObj,
			})
	}
	return c, nil
}

func (controller *Controller) Start(stopCh <-chan struct{}) error {
	defer utilruntime.HandleCrash()
	// if controller is not enabled, just skip start
	if !controller.enabled {
		klog.V(2).Info("monitor-sync controller is not enabled, just skip startting.")
		return nil
	}

	klog.V(2).Info("start monitor-sync controller")

	// step1. start bcm register
	controller.bcmRegister.Start(stopCh)

	// step2. format nsToPodNameToPodMeta
	controller.nsToPodNameToPodMeta = make(map[string]map[string]*PodMeta)

	// step3. Wait for the caches to be synced before starting worker
	for _, cache := range controller.cceCaches {
		go startCache(cache)
	}
	klog.Info("Waiting for informer caches to sync")
	for _, podInformer := range controller.ccePodInformers {
		if ok := toolscache.WaitForCacheSync(stopCh, podInformer.HasSynced); !ok {
			return fmt.Errorf("failed to wait for caches to sync")
		}
	}
	if ok := toolscache.WaitForCacheSync(stopCh, controller.resInformerOfLastSyncTimestamp.HasSynced); !ok {
		return fmt.Errorf("failed to wait for caches to sync")
	}
	klog.Info("informer caches has synced")

	// step4. initialize nsToPodNameToPodMeta
	if err := controller.initNsToPodNameToPodMeta(); err != nil {
		return fmt.Errorf("failed to initialize nsToPodNameToPodMeta, error is %w", err)
	}

	// step5. initialize lastSyncTimestamp
	_, lastSyncTimestamp, err := controller.getLastSyncTimestampFromApiserver()
	if err != nil {
		return fmt.Errorf("failed to initialize lastSyncTimestamp, error is %w", err)
	}
	controller.lastSyncTimestamp = lastSyncTimestamp

	// step6. start sync monitor
	go wait.Until(controller.syncMonitor, time.Duration(controller.syncIntervalSec)*time.Second, stopCh)
	klog.V(2).Info("Started monitor sync controller")
	<-stopCh
	klog.V(2).Info("Shutting down workers")
	return nil
}

func startCache(cache controllercache.Cache) {
	err := cache.Start(context.TODO())
	if err != nil {
		klog.Errorf("start cache failed, err is %w", err)
	}
}

func (controller *Controller) initNsToPodNameToPodMeta() error {
	for _, podInformer := range controller.ccePodInformers {
		podLister := corev1_listers.NewPodLister(podInformer.(toolscache.SharedIndexInformer).GetIndexer())
		pods, err := podLister.List(labels.Everything())
		if err != nil {
			return err
		}
		for _, pod := range pods {
			accountID, shortID, pod := parsePod(pod)
			if accountID == "" || shortID == "" || pod == nil {
				// this is not an pod created by user.
				continue
			}
			controller.addPodToMemory(accountID, shortID, pod)
		}
	}
	return nil
}

func (controller *Controller) syncMonitor() {
	startUnixSec := time.Now().Local().Unix()
	klog.V(2).Infof("start sync monitor. timestamp is %+v", time.Now().Local())
	for {
		nowUnixSec := time.Now().Local().Unix()
		klog.V(2).Infof("start sync monitor once. now is %+v", time.Now().Local())
		// 晚1分钟同步监控数据；如果实时同步，部分数据无法从 Cprom 获取，因为这些数据可能还没被采集。
		// cprom 采集周期是30秒，保险起见，晚1分钟同步监控数据。
		nowUnixSec = nowUnixSec - 60
		if nowUnixSec-controller.lastSyncTimestamp < controller.syncIntervalSec {
			break
		}
		err := controller.syncMonitorOnce(controller.clusterIDs, controller.lastSyncTimestamp, controller.lastSyncTimestamp+controller.syncIntervalSec)
		if err != nil {
			klog.Errorf("syncMonitorOnce failed, error is %+v", err)
			break
		}
	}
	endUnixSec := time.Now().Local().Unix()
	klog.V(2).Infof("end sync monitor. cost time is %ds", endUnixSec-startUnixSec)
}
