package monitorsync

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	bcm "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync/bcm"
	prom "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync/prometheus"
	"k8s.io/apimachinery/pkg/types"
	klog "k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	PauseContainerName         = "POD"
	BciInternalContainerPrefix = "bci-internal-"
)

func (controller *Controller) syncMonitorOnce(clusterIDs []string, startTimestamp, endTimestamp int64) error {
	klog.V(2).Infof("start to get metrics from prom")
	nameToMetrics, err := controller.promClient.GetMonitorData(clusterIDs, startTimestamp, endTimestamp)
	if err != nil {
		return err
	}
	klog.V(2).Infof("end to get metrics from prom")
	for k, v := range nameToMetrics {
		klog.V(4).Infof("got metrics from prom, metric name: %s, count: %d", k, len(v))
	}
	// as of this time, the existence of metric in nameToMetrics is shown as follows
	//                    PodSandbox        "POD"(Pause Container)         {normal containers}
	//  cpu(counter)           Y                  Y                              Y
	//  mem(gauge)             Y                  Y                              Y
	//  fs(counter)            Y                  Y                              Y
	//  network(counter)       N                  Y                              N

	// step1. generate netwrok metrics for Podsandbox and normal containers.
	//        remove network metrics of Pause container.
	klog.V(2).Infof("start to process bci metrics")
	for metricName, metrics := range nameToMetrics {
		metricMeta := prom.GetMetricMetaByName(metricName)
		finalMetrics := metrics
		if metricMeta.NeedExtendToNormalContainer {
			finalMetrics = controller.genContainersMetricsByPauseContainer(metrics)
		}
		nameToMetrics[metricName] = finalMetrics
	}
	// as of this time, the existence of metric in nameToMetrics is shown as follows
	//                    PodSandbox        "POD"(Pause Container)         {normal containers}
	//  cpu(counter)           Y                  Y                              Y
	//  mem(gauge)             Y                  Y                              Y
	//  fs(counter)            Y                  Y                              Y
	//  network(counter)       Y                  N                              Y

	// step2. remove cpu/mem/fs metrics of Pause container
	//        convert counter metric to gauge metric.
	//        gen BCMMetric for container and pod.
	//        record newest metric values and newest timestamp
	var newestValue prom.BciMetricValue
	newestMetrics := make(NewestMetricValueMap)
	accountToBCMMetrics := make(map[string][]*bcm.Metric)
	podAggrDataList := make([]*bcm.AggrTaskData, 0)
	for metricName, metrics := range nameToMetrics {
		metricMeta := prom.GetMetricMetaByName(metricName)
		for _, metric := range metrics {
			// remove metrics of Pause container
			if metric.ContainerName == PauseContainerName {
				continue
			}
			// remove metrics of bci internal container
			if strings.HasPrefix(metric.ContainerName, BciInternalContainerPrefix) {
				continue
			}
			// pod not exist, just ignore it.
			podMeta, exist := controller.getPodMeta(metric)
			if !exist {
				continue
			}
			finalMetric := metric
			// convert counter metric to gauge metric.
			if metricMeta.MetricType == prom.MetricTypeCounter {
				finalMetric, newestValue, err = controller.convertCounterMetricToGaugeMetric(metricName, metric.ContainerName, metric, podMeta)
				if err != nil {
					klog.Errorf("convert counter metric to gauge metric failed, error msg is %v", err)
					continue
				}
				// record counter metric
				newestMetrics = recordNewestValue(newestMetrics, newestValue, metric.Namespace, metric.PodName, metric.ContainerName, metricName)
				// there is no last value for a counter metric, thus we can not compute it's gauge value.
				if finalMetric == nil {
					continue
				}
			}
			// convert prom metric to bcm metric
			if finalMetric.ContainerName == "" {
				for _, bciMetricValue := range finalMetric.Values {
					timestamp := time.Unix(bciMetricValue.TimestampSec, 0)
					podAggrDataList = append(podAggrDataList, newAggrTaskData(toBCMRegion(controller.region), podMeta.accountID,
						podMeta.shortID, metricName, controller.bcmAggrTaskDataSources, timestamp, bciMetricValue.Value)...)
				}
			} else {
				containerMetrics := make([]*bcm.Metric, 0, len(finalMetric.Values))
				for _, bciMetricValue := range finalMetric.Values {
					timestamp := time.Unix(bciMetricValue.TimestampSec, 0)
					containerMetrics = append(containerMetrics,
						newBCMMetric(metricName, podMeta.shortID, finalMetric.ContainerName, timestamp, bciMetricValue.Value))
				}
				if _, exist := accountToBCMMetrics[podMeta.accountID]; exist {
					accountToBCMMetrics[podMeta.accountID] = append(accountToBCMMetrics[podMeta.accountID], containerMetrics...)
				} else {
					accountToBCMMetrics[podMeta.accountID] = containerMetrics
				}
			}
		}
	}
	klog.V(2).Infof("end to process bci metrics")
	for k, v := range accountToBCMMetrics {
		klog.V(4).Infof("convert metrics to bcm, accountID: %s, count: %d", k, len(v))
	}
	// as of this time, the existence of metric in nameToMetrics is shown as follows
	//                    PodSandbox        "POD"(Pause Container)         {normal containers}
	//  cpu(gauge)             Y                  N                              Y
	//  mem(gauge)             Y                  N                              Y
	//  fs(gauge)              Y                  N                              Y
	//  network(gauge)         Y                  N                              Y

	// step3. push monitor data to BCM.
	//        update newest metrics and persist lastSyncTimestamp.
	hasError := controller.pushMonitorToBCM(accountToBCMMetrics, podAggrDataList)
	if !hasError {
		klog.V(2).Infof("start to sync LastSyncTimestamp %d", endTimestamp)
		err := controller.updateLastSyncTimestamp(endTimestamp)
		if err != nil {
			return err
		}
		controller.updateNewestValue(newestMetrics)
		klog.V(2).Infof("LastSyncTimestamp %d has synced.", endTimestamp)
	}
	return nil
}

// some metrics(e.g. network related metrics) exist only in pause container and they should be same between all containers belonging to one pod.
// thus, we set all containers belonging to one pod with pause container's metric value
func (controller *Controller) genContainersMetricsByPauseContainer(metrics []*prom.BciMetric) []*prom.BciMetric {
	finalMetricCount := 0
	generatedMetricsList := make([][]*prom.BciMetric, 0, len(metrics))
	for _, metric := range metrics {
		// pod not exist, just ignore it.
		podMeta, exist := controller.getPodMeta(metric)
		if !exist {
			continue
		}
		generatedMetrics := make([]*prom.BciMetric, 0, len(podMeta.containerNames))
		for _, containerName := range podMeta.containerNames {
			generatedMetrics = append(generatedMetrics, &prom.BciMetric{
				Namespace:     metric.Namespace,
				PodName:       metric.PodName,
				ContainerName: containerName,
				Values:        metric.Values})
			finalMetricCount += 1
		}
		generatedMetricsList = append(generatedMetricsList, generatedMetrics)
	}
	finalMetrics := make([]*prom.BciMetric, 0, finalMetricCount)
	for _, generatedMetrics := range generatedMetricsList {
		finalMetrics = append(finalMetrics, generatedMetrics...)
	}
	return finalMetrics
}

func (controller *Controller) convertCounterMetricToGaugeMetric(metricName, containerName string, metric *prom.BciMetric,
	podMeta *PodMeta) (*prom.BciMetric, prom.BciMetricValue, error) {
	samples := make([]prom.BciMetricValue, 0, len(metric.Values))
	samples = append(samples, metric.Values...)
	if containerNameToMetric, exist := podMeta.lastMetrics[metricName]; exist {
		if lastMetric, exist := containerNameToMetric[containerName]; exist {
			samples = append(samples, lastMetric)
		}
	}
	sampleCount := len(samples)
	if sampleCount == 0 {
		return nil, prom.BciMetricValue{}, fmt.Errorf("metric value is null in ns: %s, pod: %s, container: %s", metric.Namespace,
			metric.PodName, metric.ContainerName)
	}
	if sampleCount == 1 {
		return nil, samples[0], nil
	}
	result := make([]prom.BciMetricValue, 0, sampleCount-1)
	reverseSortedSamples := prom.ReverseSortableBciMetricValues(samples)
	sort.Sort(reverseSortedSamples)
	// gauge_value = (new_counter_value - old_counter_value) / (new_counter_timestamp - old_counter_timestamp)
	// timestamp of gauge value is new_counter_timestamp
	for i := 0; i < sampleCount-1; i++ {
		interval := reverseSortedSamples[i].TimestampSec - reverseSortedSamples[i+1].TimestampSec
		if interval == 0 {
			continue
		}
		newGaugeValue := (reverseSortedSamples[i].Value - reverseSortedSamples[i+1].Value) / float64(interval)
		result = append(result, prom.BciMetricValue{TimestampSec: reverseSortedSamples[i].TimestampSec, Value: newGaugeValue})
	}
	return &prom.BciMetric{
		Namespace:     metric.Namespace,
		PodName:       metric.PodName,
		ContainerName: metric.ContainerName,
		Values:        result,
	}, reverseSortedSamples[0], nil
}

func (controller *Controller) pushMonitorToBCM(containerMonitorData map[string][]*bcm.Metric, podMonitorData []*bcm.AggrTaskData) bool {
	klog.V(2).Infof("start to push bcm monitor data, account count is %d, pod data count is %d", len(containerMonitorData), len(podMonitorData))
	errOccurs := false

	// step1. push container monitor data to BCM.
	jobNum := 0
	// calculate task num
	for _, metrics := range containerMonitorData {
		round := (len(metrics) + controller.bcmPushContainerCountPerBatch - 1) /
			controller.bcmPushContainerCountPerBatch
		jobNum = jobNum + round
	}
	pushJobsChan := make(chan bcm.DataPushJob, jobNum)
	results := make(chan error, jobNum)
	var wg sync.WaitGroup
	// create worker according to thread num
	for i := 0; i < controller.bcmPushThreadNum; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			bcm.PushDataWorker(controller.bcmClient.DealPushMetricsJob, pushJobsChan, results)
		}()
	}

	// loop container monitor data to create job, feed them to pushJobsChan
	for accountID, metrics := range containerMonitorData {
		metricsCount := len(metrics)
		klog.V(2).Infof("start to push container metrics, account id is %s, metrics count is %d", accountID, metricsCount)
		round := (len(metrics) + controller.bcmPushContainerCountPerBatch - 1) /
			controller.bcmPushContainerCountPerBatch
		for i := 0; i < round; i++ {
			start := i * controller.bcmPushContainerCountPerBatch
			end := start + controller.bcmPushContainerCountPerBatch
			if end > metricsCount {
				end = metricsCount
			}
			bcmContainerMonitorDataPushJob := bcm.ContainerMonitorDataPushJob{
				Context:   context.TODO(),
				AccountID: accountID,
				Metrics:   metrics[start:end],
			}
			pushJobsChan <- bcmContainerMonitorDataPushJob
		}
	}
	// notify worker no more job
	close(pushJobsChan)
	// read result
	for i := 0; i < jobNum; i++ {
		if err := <-results; err != nil {
			klog.Errorf("fail to push bcm metrics: %v", err)
			errOccurs = true
		}
	}

	// step 2. push pod monitor data to BCM.
	podMetricsCount := len(podMonitorData)
	podRound := (podMetricsCount + controller.bcmPushPodCountPerBatch - 1) / controller.bcmPushPodCountPerBatch
	aggrJobsChan := make(chan bcm.DataPushJob, podRound)
	// create worker according to thread num
	for i := 0; i < controller.bcmPushThreadNum; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			bcm.PushDataWorker(controller.bcmClient.DealPushAggrTaskDataJob, aggrJobsChan, results)
		}()
	}

	// loop pod monitor data to create job, feed them to bcmPodMonitorDataPushJob
	for i := 0; i < podRound; i++ {
		start := i * controller.bcmPushPodCountPerBatch
		end := start + controller.bcmPushPodCountPerBatch
		if end > podMetricsCount {
			end = podMetricsCount
		}
		bcmPodMonitorDataPushJob := bcm.PodMonitorDataPushJob{
			Context: context.TODO(),
			Metrics: podMonitorData[start:end],
		}
		aggrJobsChan <- bcmPodMonitorDataPushJob
	}
	close(aggrJobsChan)
	for i := 0; i < podRound; i++ {
		if err := <-results; err != nil {
			klog.Errorf("fail to push aggr task data: %v", err)
			errOccurs = true
		}
	}

	// wait for all worker done
	wg.Wait()
	close(results)
	klog.V(2).Infof("end to push bcm monitor data.")

	return errOccurs
}

func (controller *Controller) updateLastSyncTimestamp(timestamp int64) error {
	// step1. update lastSyncTimestamp in configmap
	configmap, lastSyncTimestampInStore, err := controller.getLastSyncTimestampFromApiserver()
	if err != nil {
		return err
	}
	var patchErr error
	if lastSyncTimestampInStore != timestamp {
		patchData, _ := json.Marshal(&map[string]map[string]string{
			"data": {FieldNameOfLastSyncTimestamp: fmt.Sprintf("%d", timestamp)},
		})
		for i := 0; i < 3; i++ {
			patchErr = controller.apiServerClient.Patch(context.TODO(), configmap, client.RawPatch(types.StrategicMergePatchType, []byte(patchData)))
			if patchErr == nil {
				break
			}
			klog.Errorf("patch lastSyncTimestamp to configmap failed, err is %+v", patchErr)
		}
	}
	if patchErr != nil {
		return patchErr
	}

	// step2. update it in memory
	controller.lastSyncTimestamp = int64(timestamp)
	return nil
}

func (controller *Controller) updateNewestValue(newestMetricValueMap NewestMetricValueMap) {
	// nsToPodNameToPodMeta 可能会被并发读写，因此需要加锁
	controller.podMetaRWMutex.Lock()
	defer controller.podMetaRWMutex.Unlock()
	for ns, podMetrics := range newestMetricValueMap {
		for pod, nameToMetrics := range podMetrics {
			for metricName, containerMetric := range nameToMetrics {
				for container, value := range containerMetric {
					if _, ok := controller.nsToPodNameToPodMeta[ns][pod]; !ok {
						continue
					}

					lastMetrics := controller.nsToPodNameToPodMeta[ns][pod].lastMetrics
					if _, exist := lastMetrics[metricName]; !exist {
						lastMetrics[metricName] = make(map[string]prom.BciMetricValue)
					}
					lastMetrics[metricName][container] = value
				}
			}
		}
	}
}
