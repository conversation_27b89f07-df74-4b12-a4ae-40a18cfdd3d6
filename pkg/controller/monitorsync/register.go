package monitorsync

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync/bcm"
	prom "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync/prometheus"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/bce"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/api/core/v1"
	klog "k8s.io/klog/v2"
)

const (
	unknown = iota
	registered
	unregistered
)

type Registrar interface {
	Start(stopCh <-chan struct{})
	Add(pod *corev1.Pod, accountID, shortID string)
	Remove(pod *corev1.Pod, accountID, shortID string)
	Notify()
}

type registrar struct {
	sync.Mutex
	region       string
	bcmClient    bcm.Client
	pods         map[string]*registerPod
	notification chan struct{}
}

type registerPod struct {
	pod       *corev1.Pod
	shortID   string
	accountID string
	desire    int
	actual    int
}

func NewRegistrar(bcmClient bcm.Client, region string) Registrar {
	return &registrar{
		region:       region,
		bcmClient:    bcmClient,
		pods:         make(map[string]*registerPod),
		notification: make(chan struct{}, 1),
	}
}

func (r *registrar) Start(stopCh <-chan struct{}) {
	go func() {
		for {
			select {
			case <-stopCh:
				klog.Info("bcm registrar stopped")
				return
			case <-r.notification:
				r.reconcile()
				<-time.After(time.Second * 5) // cooling down (may replace with back off)
			}
		}
	}()
	klog.Info("bcm registrar starts")
}

func isAlreadyRegistered(err error) bool {
	var bcerror *bce.Error
	if errors.As(err, &bcerror) {
		return bcerror.Code == "ParameterValueException" && strings.Contains(bcerror.Message, "already exists")
	}
	return false
}

func (r *registrar) reconcile() {
	var todos []*registerPod

	r.Lock()
	for _, pod := range r.pods {
		if pod.desire != pod.actual {
			todos = append(todos, pod)
		}
	}
	r.Unlock()

	errOccurs := false
	toGC := make([]string, 0)

	for _, todo := range todos {
		switch todo.desire {
		case registered:
			klog.V(4).Infof("try to register containers of pod %s to bcm", todo.pod.UID)
			if err := r.registerToBCM(todo.pod, todo.accountID, todo.shortID); err != nil && !isAlreadyRegistered(err) {
				klog.Errorf("fail to register containers of pod %s to bcm, err is %w", todo.pod.UID, err)
				errOccurs = true
				continue
			}
			klog.V(2).Infof("successfully register containers of pod %s to bcm", todo.pod.UID)
			todo.actual = registered

		case unregistered:
			klog.V(4).Infof("try to remove containers of pod %s from bcm", todo.pod.UID)
			if err := r.removeFromBCM(todo.pod, todo.accountID, todo.shortID); err != nil {
				klog.Errorf("fail to remove containers of pod %s from bcm", todo.pod.UID)
				errOccurs = true
				continue
			}
			klog.V(2).Infof("successfully remove containers of pod %s from bcm", todo.pod.UID)
			todo.actual = unregistered
			toGC = append(toGC, string(todo.pod.GetUID()))
		}
	}

	if errOccurs {
		r.Notify()
	}

	if len(toGC) > 0 {
		r.Lock()
		for _, uid := range toGC {
			delete(r.pods, uid)
		}
		r.Unlock()
	}
}

func (r *registrar) Notify() {
	select {
	case r.notification <- struct{}{}:
	default:
	}
}

func (r *registrar) Add(pod *corev1.Pod, accountID, shortID string) {
	if pod == nil || accountID == "" {
		klog.Warning("invalid args to register")
		return
	}
	uid := string(pod.GetUID())

	r.Lock()
	defer r.Unlock()

	// NOTE: containers in pod are not supported to add or remove, so no need to update.
	if v, ok := r.pods[uid]; ok {
		v.desire = registered
		return
	}
	r.pods[uid] = &registerPod{
		pod:       pod,
		accountID: accountID,
		shortID:   shortID,
		desire:    registered,
		actual:    unknown,
	}
}

func (r *registrar) Remove(pod *corev1.Pod, accountID, shortID string) {
	if pod == nil {
		klog.Warning("nil pod to remove")
		return
	}
	podUID := string(pod.GetUID())

	r.Lock()
	defer r.Unlock()
	if v, ok := r.pods[podUID]; ok {
		v.desire = unregistered
		return
	}

	r.pods[podUID] = &registerPod{
		pod:       pod,
		shortID:   shortID,
		accountID: accountID,
		desire:    unregistered,
		actual:    unknown,
	}
}

func (r *registrar) registerToBCM(pod *corev1.Pod, accountID, shortID string) error {
	if pod == nil {
		return fmt.Errorf("pod cannot be nil")
	}

	var containers []*bcm.RegisterContainerArgs
	// object for pod
	containers = append(containers, bcm.NewRegisterContainerArgs(accountID, bcm.ToBCMRegion(r.region),
		shortID, ""))
	for _, c := range pod.Spec.Containers {
		// remove bci internal container
		if strings.HasPrefix(c.Name, BciInternalContainerPrefix) {
			continue
		}
		containers = append(containers, bcm.NewRegisterContainerArgs(accountID, bcm.ToBCMRegion(r.region),
			shortID, c.Name))
	}

	if err := r.bcmClient.BatchRegisterContainers(context.TODO(), containers); err != nil {
		return err
	}
	return nil
}

func (r *registrar) removeFromBCM(pod *corev1.Pod, accountID, shortID string) error {
	if pod == nil {
		return fmt.Errorf("pod cannot be nil")
	}

	var containers []*bcm.DeleteContainerArgs
	// object for pod
	containers = append(containers, bcm.NewDeleteContainerArgs(accountID, bcm.ToBCMRegion(r.region),
		shortID, ""))
	for _, c := range pod.Spec.Containers {
		containers = append(containers, bcm.NewDeleteContainerArgs(accountID, bcm.ToBCMRegion(r.region),
			shortID, c.Name))
	}

	if err := r.bcmClient.BatchDeleteContainers(context.TODO(), containers); err != nil {
		return err
	}
	return nil
}

func (controller *Controller) registerMonitorObj(obj interface{}) {
	accountID, shortID, pod := parsePod(obj)
	if accountID == "" || shortID == "" || pod == nil {
		// this is not an pod created by user, just skip it.
		return
	}

	// add pod to bcm
	controller.bcmRegister.Add(pod, accountID, shortID)
	controller.bcmRegister.Notify()

	// add pod to memory
	controller.addPodToMemory(accountID, shortID, pod)
}

func (controller *Controller) unRegisterMonitorObj(obj interface{}) {
	accountID, shortID, pod := parsePod(obj)
	if accountID == "" || shortID == "" || pod == nil {
		// this is not an pod created by user, just skip it.
		return
	}

	// remove pod from bcm
	controller.bcmRegister.Remove(pod, accountID, shortID)
	controller.bcmRegister.Notify()

	// remove pod from memory
	// nsToPodNameToPodMeta 可能会被并发读写，因此需要加锁
	controller.podMetaRWMutex.Lock()
	defer controller.podMetaRWMutex.Unlock()
	if _, exist := controller.nsToPodNameToPodMeta[pod.Namespace]; exist {
		delete(controller.nsToPodNameToPodMeta[pod.Namespace], pod.Name)
	}
}

func (controller *Controller) addPodToMemory(accountID, shortID string, pod *v1.Pod) {
	// nsToPodNameToPodMeta 可能会被并发读写，因此需要加锁
	controller.podMetaRWMutex.Lock()
	defer controller.podMetaRWMutex.Unlock()
	if _, exist := controller.nsToPodNameToPodMeta[pod.Namespace]; !exist {
		controller.nsToPodNameToPodMeta[pod.Namespace] = make(map[string]*PodMeta)
	}
	containerNames := make([]string, 0, len(pod.Spec.Containers)+1)
	// "" means it's pod
	containerNames = append(containerNames, "")
	for _, container := range pod.Spec.Containers {
		containerNames = append(containerNames, container.Name)
	}
	controller.nsToPodNameToPodMeta[pod.Namespace][pod.Name] = &PodMeta{
		shortID:        shortID,
		accountID:      accountID,
		containerNames: containerNames,
		lastMetrics:    make(map[string]map[string]prom.BciMetricValue),
	}
}
