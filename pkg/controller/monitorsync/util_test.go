package monitorsync

import (
	"reflect"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/monitorsync/bcm"
)

func Test_newAggrTaskData(t *testing.T) {
	timeNow := time.Now()
	tests := []struct {
		name        string
		region      string
		userID      string
		podShortID  string
		metricName  string
		dataSources []string
		timeStamp   time.Time
		value       float64
		want        *bcm.AggrTaskData
	}{
		{
			name:        "test1",
			region:      "bj",
			userID:      "user-1",
			podShortID:  "pod-1",
			metricName:  "container_cpu_usage_seconds_total",
			dataSources: append(make([]string, 0), "bci"),
			timeStamp:   timeNow,
			value:       float64(1024),
			want: &bcm.AggrTaskData{
				Metric:    "CpuUsage",
				Timestamp: timeNow.Unix() * 1000,
				ValueType: "double",
				Value:     float64(102400),
				Tags: map[string]string{
					"userId":      "user-1",
					"Pod":         "pod-1",
					"region":      "bj",
					"serviceName": bcm.ServiceNameBCI,
				},
				// meta param is pre-defined in bcm
				Meta: &bcm.AggrTaskMeta{
					Cycle:      "60",
					DataSource: "bci",
					Downstream: "saver,alert",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := newAggrTaskData(tt.region, tt.userID, tt.podShortID, tt.metricName, tt.dataSources, tt.timeStamp, tt.value); !reflect.DeepEqual(got[0], tt.want) {
				t.Errorf("newPodAggrTaskArgs() = %+v, want %+v", got[0], tt.want)
			}
		})
	}
}
