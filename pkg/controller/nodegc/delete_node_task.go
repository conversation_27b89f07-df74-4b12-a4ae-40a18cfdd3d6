package nodegc

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// 删除buffer以外的node
func (c *Controller) deleteNodeTask() error {
	igConfigMap, err := c.cmLister.ConfigMaps(entity.InstanceGroupCmNamespace).Get(entity.InstanceGroupCmName)
	if err != nil {
		klog.Errorf("nodeGcController get igCm err %+v ", err)
		return err
	}
	data := igConfigMap.Data[entity.InstanceGropuCmDataKey]
	igList := make([]entity.InstanceGroupCm, 0)
	if err = json.Unmarshal([]byte(data), &igList); err != nil {
		klog.Error("nodeGcController instanceGroupCm data %+v Unmarshal err %+v ", data, err)
		return err
	}

	wg := &sync.WaitGroup{}
	wg.Add(len(igList))
	for _, instanceGroup := range igList {
		go func(ig entity.InstanceGroupCm) {
			defer wg.Done()
			// 竞价实例节点组不处理
			if ig.ChargingType != "" && ig.ChargingType == entity.BiddingString {
				klog.V(5).Infof("nodeGcController InstanceGroupId %+v is bid ignore ", ig.InstanceGroupId)
				return
			}
			if err := c.deleteInstanceGroupExtraNodes(ig.InstanceGroupId, ig.Buffer); err != nil {
				klog.Errorf("nodeGcController deleteInstanceGroupextraNodes InstanceGroupId %+v err %+v ", ig.InstanceGroupId, err)
			}
		}(instanceGroup)
	}
	wg.Wait()
	return nil
}

func (c *Controller) deleteBidNodeTask() error {
	selector := labels.SelectorFromSet(labels.Set(map[string]string{entity.NodeLabelChargingTypeKey: string(bccapi.PaymentTimingBidding)}))
	nodes, err := c.nodeLister.List(selector)
	if err != nil {
		klog.Error("get bid node err %+v ", err)
		return err
	}
	for _, node := range nodes {
		if node.Labels[entity.NodeStateLabelKey] == string(entity.NodeStateEmpty) || node.Labels[entity.NodeStateLabelKey] == string(entity.NodeStateInit) {
			nodeStateTime, _ := strconv.Atoi(node.Labels[entity.NodeStateTimeLabelKey])
			if time.Now().Unix()-int64(nodeStateTime) > InitNodeCreateTimeOut {
				if runtime.RunCustomizeNodeGCHook(runtime.DeleteNodeGC, node) {
					resp, err := c.cceClient.DeleteInstances([]string{node.Labels[entity.NodeLabelCCEInstanceIDKey]})
					if err != nil {
						return err
					}
					klog.V(3).Infof("delete bid node name: %+v, cce instance id: %+v, resp %+v , err %+v ",
						node.Name, node.Labels[entity.NodeLabelCCEInstanceIDKey], resp, err)
				}
			}
		}
	}

	return nil
}

func (c *Controller) deleteInstanceGroupExtraNodes(instanceGroupID string, buffer int) error {
	klog.V(4).Infof("nodeGcController delete instanceGroupID %+v start ", instanceGroupID)

	selector := labels.SelectorFromSet(labels.Set(map[string]string{entity.InstanceGroupKey: instanceGroupID}))
	nodes, err := c.nodeLister.List(selector)
	if err != nil {
		klog.Error("nodeGcController nodeLister list instanceGroup %+v node  err %+v ", instanceGroupID, err)
		return err
	}

	notReadyStateNodes := make([]*corev1.Node, 0)
	coolStateNodes := make([]*corev1.Node, 0)
	gcFailNodes := make([]*corev1.Node, 0)
	initStateNodes := make([]*corev1.Node, 0)
	customizeDeleteNodes := make([]*corev1.Node, 0)

	for _, node := range nodes {
		nodeInfo := entity.NewNodeInfo(node)
		if nodeInfo.NodeNotReady() && c.notReadyNodeCanRemove(nodeInfo) {
			notReadyStateNodes = append(notReadyStateNodes, node)
			continue
		}

		if runtime.RunCanDeleteNodeCustomizeFilter(node) {
			customizeDeleteNodes = append(customizeDeleteNodes, node)
			continue
		}

		if nodeInfo.CurrentNodeState() == entity.NodeStateCool {
			canDelete, err := c.coolStateNodeExpired(nodeInfo)
			if err != nil {
				continue
			}
			// cool状态时间超过CoolStateHoldTime
			if !canDelete {
				continue
			}
			gcState := node.Labels[entity.NodeGcStateKey]
			if gcState == entity.NodeGcStateFail.String() {
				gcFailNodes = append(gcFailNodes, node)
				continue
			}
			if gcState == entity.NodeGcStateInit.String() {
				// 潮汐场景，几百台node 回收eni，耗时较长，此处添加判断，只能尝试删除gc success 的node
				klog.V(4).Infof("nodeGcController deleteInstanceGroupExtraNodes cool node %s gc state init skip try delete",
					node.Name)
				continue
			}
			// gc success
			klog.V(5).Infof("nodeGcController deleteInstanceGroupExtraNodes cool node %s gc state success try delete",
				node.Name)
			coolStateNodes = append(coolStateNodes, node)
		}
		if nodeInfo.CurrentNodeState() == entity.NodeStateInit && !nodeInfo.NodeLocked() {
			initStateNodes = append(initStateNodes, node)
		}
	}

	// 不需要删除
	if len(gcFailNodes) == 0 && len(notReadyStateNodes) == 0 &&
		len(coolStateNodes)+len(initStateNodes) <= buffer && len(customizeDeleteNodes) == 0 {
		klog.V(4).Infof("nodeGcController instanceGroup %+v no node delete ", instanceGroupID)
		return nil
	}

	// 先删除notReadyNode
	if len(notReadyStateNodes) != 0 && c.options.NodeOptions.EnableDeleteNotReadyNodes {
		for _, notReadyStateNode := range notReadyStateNodes {
			klog.V(3).Infof("nodeGcController instanceGroup %+v attempt delete notReady nodes %s ", instanceGroupID, notReadyStateNode.Name)
		}
		if err = c.deleteCceNodes(runtime.DeleteNodeGC, instanceGroupID, notReadyStateNodes); err != nil {
			return err
		}
	}

	// 删除自定义删除node 返回的node列表
	if len(customizeDeleteNodes) != 0 && c.options.NodeOptions.EnableAutoDeleteCustomizeFilterNode {
		for _, customizeDeleteNode := range customizeDeleteNodes {
			klog.V(3).Infof("nodeGcController instanceGroup %+v attempt delete customizeDeleteNode nodes %s ", instanceGroupID, customizeDeleteNode.Name)
		}

		finallyDeleteCustomizeNodes := c.deleteNodeCheck(customizeDeleteNodes)

		if err = c.deleteCceNodes(runtime.GCRetrySuccessNotDeleteNode, instanceGroupID, finallyDeleteCustomizeNodes); err != nil {
			return err
		}
	}

	// 需要删除的node个数
	deleteNodeCount := len(coolStateNodes) + len(initStateNodes) - buffer
	deleteNodes := make([]*corev1.Node, 0)

	// 打印日志，gc 时将buffer 数打印出来
	klog.V(3).Infof("nodeGcController instanceGroup %s cool node count %+v init node count  %+v buffer %+v ",
		instanceGroupID, len(coolStateNodes), len(initStateNodes), buffer)
	var deleteNode *corev1.Node

	for deleteNodeCount > 0 {
		// 先删除cool状态node
		if len(coolStateNodes) > 0 {
			deleteNode, coolStateNodes = coolStateNodes[len(coolStateNodes)-1], coolStateNodes[:len(coolStateNodes)-1]
		} else {
			// 在删除init状态node
			deleteNode, initStateNodes = initStateNodes[len(initStateNodes)-1], initStateNodes[:len(initStateNodes)-1]
		}
		deleteNodes = append(deleteNodes, deleteNode)
		deleteNodeCount--
	}

	// 此处删除gc失败的node
	if len(gcFailNodes) != 0 && c.options.NodeOptions.EnableAutoDeleteGCFailedNode {
		for _, gcFailNode := range gcFailNodes {
			klog.V(3).Infof("nodeGcController instanceGroup %+v delete GCFail node %s ", instanceGroupID, gcFailNode.Name)
		}

		deleteNodes = append(deleteNodes, gcFailNodes...)
	}

	for _, deleteNode := range deleteNodes {
		klog.V(4).Infof("nodeGcController instanceGroupId %+v attempt delete node %s ", instanceGroupID, deleteNode.Name)
	}
	// 关闭自动删除buffer以外node 开关
	if len(deleteNodes) == 0 || !c.options.NodeOptions.EnableAutoDeleteExceedBufferNode {
		return nil
	}

	finallyDeleteNodes := c.deleteNodeCheck(deleteNodes)

	return c.deleteCceNodes(runtime.DeleteNodeGC, instanceGroupID, finallyDeleteNodes)
}

// 删除node前check，防止误删用户已经调度的pod
func (c *Controller) deleteNodeCheck(deleteNodes []*corev1.Node) []*corev1.Node {
	wg := &sync.WaitGroup{}
	wg.Add(len(deleteNodes))
	// 并发执行
	for _, deleteNode := range deleteNodes {
		go func(node *corev1.Node) {
			defer wg.Done()
			if c.canDeleteNode(node) {
				klog.V(4).Infof("nodeGcController node %s canDeleteNode true ", node.Name)
				c.cordonNode(node)
			}
		}(deleteNode)
	}
	wg.Wait()
	// sleep一下，后续做double check
	time.Sleep(c.deleteNodeSleepTime)

	// 最终删除的node
	finallyDeleteNodes := make([]*corev1.Node, 0)

	lock := &sync.Mutex{}

	wg = &sync.WaitGroup{}
	wg.Add(len(deleteNodes))
	for _, deleteNode := range deleteNodes {
		go func(node *corev1.Node) {
			defer wg.Done()
			if !c.canDeleteNode(node) {
				// 解除node屏蔽
				klog.V(4).Infof("nodeGcController node %s canDeleteNode false ", node.Name)
				c.uncordonNode(node)
				return
			}
			lock.Lock()
			finallyDeleteNodes = append(finallyDeleteNodes, node)
			lock.Unlock()
		}(deleteNode)
	}

	wg.Wait()
	return finallyDeleteNodes
}

// 屏蔽node，删除node前需要屏蔽，防止新调度用户pod上去
func (c *Controller) cordonNode(node *corev1.Node) error {
	// 从informer中获取最新node
	node, _ = c.nodeLister.Get(node.Name)
	if node == nil {
		return nil
	}
	// 节点已经被屏蔽了
	if node.Spec.Unschedulable {
		return nil
	}

	copyNode := node.DeepCopy()
	// 屏蔽节点
	specData := map[string]bool{ // 屏蔽
		"unschedulable": true,
	}
	nodeUpdateEle := map[string]interface{}{
		"spec": &specData,
	}
	patchData, _ := json.Marshal(&nodeUpdateEle)

	var err error
	for i := 0; i < 3; i++ {
		err = c.client.Patch(context.TODO(), copyNode, client.RawPatch(types.StrategicMergePatchType, []byte(patchData)))
		klog.V(3).Infof("nodeGcController cordon node %+v err %+v ", node.Name, err)
		if err == nil {
			return nil
		}
	}
	return err
}

// node解除屏蔽
func (c *Controller) uncordonNode(node *corev1.Node) error {
	// 从informer中获取最新node
	node, _ = c.nodeLister.Get(node.Name)
	if node == nil {
		return nil
	}
	// 节点已经被解除屏蔽了
	if !node.Spec.Unschedulable {
		return nil
	}

	copyNode := node.DeepCopy()
	// 解除屏蔽节点
	specData := map[string]bool{ // 解除屏蔽
		"unschedulable": false,
	}
	nodeUpdateEle := map[string]interface{}{
		"spec": &specData,
	}
	patchData, _ := json.Marshal(&nodeUpdateEle)

	var err error
	for i := 0; i < 3; i++ {
		err = c.client.Patch(context.TODO(), copyNode, client.RawPatch(types.StrategicMergePatchType, []byte(patchData)))
		klog.V(3).Infof("nodeGcController uncordon node %+v err %+v ", node.Name, err)
		if err == nil {
			return nil
		}
	}
	return err
}

// 是否能够删除node
func (c *Controller) canDeleteNode(node *corev1.Node) bool {
	// 从informer中获取最新node
	node, err := c.nodeLister.Get(node.Name)
	if err != nil {
		return false
	}

	// 判断node上是否存在用户pod
	// 因为删除node 不是高频操作，此处从informer中list
	allPods, err := c.podLister.List(labels.Everything())
	if err != nil {
		return false
	}

	nodeInfo := entity.NewNodeInfo(node)
	for _, pod := range allPods {
		// 过滤非本机pod
		if pod.Spec.NodeName == node.Name {
			nodeInfo.AddOrUpdatePod(pod)
		}
	}

	// 是空闲机器 & 没有被用户锁定
	return nodeInfo.EmptyNode() && !nodeInfo.NodeLocked()
}

func (c *Controller) deleteCceNodes(nodeGCSource runtime.NodeGCSource, instanceGroup string, deleteNodes []*corev1.Node) error {
	if len(deleteNodes) == 0 {
		klog.V(4).Infof("nodeGcController instanceGroup %s deleteNodes empty ignore ", instanceGroup)
		return nil
	}

	finalDeleteNodes := make([]*corev1.Node, 0)
	// 执行自定义CustomizeNodeGCHook hook
	for _, deleteNode := range deleteNodes {
		if runtime.RunCustomizeNodeGCHook(nodeGCSource, deleteNode) {
			finalDeleteNodes = append(finalDeleteNodes, deleteNode)
			continue
		}
		// deleteNodes node列表已经被cordon了，此处需要uncordon
		_ = c.uncordonNode(deleteNode)
	}

	cceInstances := make([]string, 0)
	k8sNodes := make([]string, 0)
	for _, deleteNode := range finalDeleteNodes {
		// 状态机controller把instanceID写到node Annotation中，此处复用
		if cceInstance, ok := deleteNode.Labels[entity.NodeLabelCCEInstanceIDKey]; ok {
			cceInstances = append(cceInstances, cceInstance)
			k8sNodes = append(k8sNodes, deleteNode.Name)
			continue
		}
		klog.Warningf("nodeGcController instanceGroup %s node %s cceInstance is empty ", instanceGroup, deleteNode.Name)
	}

	if len(cceInstances) == 0 {
		klog.V(4).Info("nodeGcController instanceGroup %s cceInstances is empty ", instanceGroup)
		return nil
	}

	resp, err := c.cceClient.RemoveInstanceGroupNodes(instanceGroup, cceInstances)
	klog.V(3).Infof("nodeGcController deleteCceNodes k8sNodeNames %+v cceInstances %+v ,resp %+v , err %+v ", k8sNodes, cceInstances, resp, err)

	return err
}

func (c *Controller) coolStateNodeExpired(nodeInfo *entity.NodeInfo) (delete bool, err error) {

	startCoolTime := nodeInfo.Node.Labels[entity.StartCoolTimeLabelKey]
	unixPodExitTime, err := strconv.ParseInt(startCoolTime, 10, 64)
	if err != nil {
		err := fmt.Errorf("nodeGcController node %s parse startCoolTime %+v err %+v ", nodeInfo.Name, startCoolTime, err)
		klog.Error(err)
		return false, err
	}

	covertTime := time.Unix(unixPodExitTime, 0)
	// 过了cool 状态最大保持时间，并且没有被用户锁定
	if time.Now().After(covertTime.Add(c.options.NodeOptions.CoolStateHoldTime)) && !nodeInfo.NodeLocked() {
		return true, nil
	}
	return false, nil
}

func (c *Controller) notReadyNodeCanRemove(nodeInfo *entity.NodeInfo) bool {

	nodeCreateTime := nodeInfo.Node.CreationTimestamp.Time
	duration, _ := time.ParseDuration("10m")
	// node初始化时是notReady，此时不能删除
	if !time.Now().After(nodeCreateTime.Add(duration)) {
		return false
	}

	notReadyStartTime, ok := nodeInfo.Node.Labels[entity.NotReadyStartTimeKey]
	if !ok {
		return false
	}

	unixNotReadyStartTime, err := strconv.ParseInt(notReadyStartTime, 10, 64)
	if err != nil {
		klog.Errorf("nodeGcController notReady node %s parse notReadyStartTime %+v err %+v ",
			nodeInfo.Name, notReadyStartTime, err)
		return false
	}
	covertTime := time.Unix(unixNotReadyStartTime, 0)
	if time.Now().After(covertTime.Add(c.options.NodeOptions.NotReadyStateHoldTime)) {
		return true
	}
	return false
}
