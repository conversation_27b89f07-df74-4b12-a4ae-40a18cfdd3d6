package nodegc

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/informers"
	k8sfake "k8s.io/client-go/kubernetes/fake"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

func buildNodeWithLabels(name string, labels map[string]string, runtimeVersion string) *corev1.Node {
	return &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name:   name,
			Labels: labels,
		},
		Status: corev1.NodeStatus{
			NodeInfo: corev1.NodeSystemInfo{
				ContainerRuntimeVersion: runtimeVersion,
			},
		},
	}
}

func buildJobSuccessPod(name, jobName string) *corev1.Pod {

	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       types.UID(fmt.Sprintf("%v-%v", "kube-system", name)),
			Name:      name,
			Namespace: "kube-system",
			Labels: map[string]string{
				"job-name": jobName,
			},
		},
		Spec: corev1.PodSpec{},
		Status: corev1.PodStatus{
			Phase: corev1.PodSucceeded,
		},
	}
}

func buildJob(name string, jobConditions []batchv1.JobCondition) *batchv1.Job {
	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: jobNamespace,
			Name:      name,
			Labels: map[string]string{
				// "node":       node.Name,
				controlledBy: controlledByValue,
			},
		},
		Status: batchv1.JobStatus{
			Conditions: jobConditions,
		},
	}
	return job
}

func TestReconcileNodeGc(t *testing.T) {

	tests := []struct {
		name           string
		pods           []*corev1.Pod
		nodes          []*corev1.Node
		jobs           []*batchv1.Job
		reconcileParam types.NamespacedName
		res            reconcile.Result
	}{
		{
			name: "node 不存在，job存在，需要删除job",
			pods: []*corev1.Pod{
				buildJobSuccessPod("node1"+jobSuffix+"pod", "node1"+jobSuffix),
			},
			jobs: []*batchv1.Job{
				buildJob("node1"+jobSuffix, nil),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "kube-system",
				Name:      "node1" + jobSuffix,
			},
		},
		{
			name: "node 状态为cool，未创建job,runtime为docker",
			nodes: []*corev1.Node{
				buildNodeWithLabels("node1", map[string]string{
					entity.NodeStateLabelKey: string(entity.NodeStateCool),
				}, "docker://1.2.1"),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "kube-system",
				Name:      "node1",
			},
		},
		{
			name: "node 状态为cool，已创建job",
			nodes: []*corev1.Node{
				buildNodeWithLabels("node1", map[string]string{
					entity.NodeStateLabelKey: string(entity.NodeStateCool),
				}, "docker://1.2.1"),
			},
			jobs: []*batchv1.Job{
				buildJob("node1"+jobSuffix, nil),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "kube-system",
				Name:      "node1",
			},
		},
		{
			name: "node 状态为cool，未创建job,runtime为containerd",
			nodes: []*corev1.Node{
				buildNodeWithLabels("node1", map[string]string{
					entity.NodeStateLabelKey: string(entity.NodeStateCool),
				}, "docker://1.2.1"),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "kube-system",
				Name:      "node1",
			},
		},
		{
			name: "node 状态为cool，job gc成功",
			nodes: []*corev1.Node{
				buildNodeWithLabels("node1", map[string]string{
					entity.NodeStateLabelKey: string(entity.NodeStateCool),
				}, "docker://1.2.1"),
			},
			jobs: []*batchv1.Job{
				buildJob("node1"+jobSuffix, []batchv1.JobCondition{
					{Type: batchv1.JobComplete},
				}),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "kube-system",
				Name:      "node1" + jobSuffix,
			},
		},
		{
			name: "node 状态为cool，job gc失败",
			nodes: []*corev1.Node{
				buildNodeWithLabels("node1", map[string]string{
					entity.NodeStateLabelKey: string(entity.NodeStateCool),
				}, "docker://1.2.1"),
			},
			jobs: []*batchv1.Job{
				buildJob("node1"+jobSuffix, []batchv1.JobCondition{
					{Type: batchv1.JobFailed},
				}),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "kube-system",
				Name:      "node1" + jobSuffix,
			},
		},
		{
			name: "node 为竞价实例，并且状态为not ready，node gc成功",
			nodes: []*corev1.Node{
				buildNodeWithLabels("node1", map[string]string{
					entity.NodeLabelChargingTypeKey: string(bccapi.PaymentTimingBidding),
					entity.NodeStateLabelKey:        string(entity.NodeStateNotReady),
				}, "docker://1.2.1"),
			},
			jobs: []*batchv1.Job{
				buildJob("node1"+jobSuffix, []batchv1.JobCondition{
					{Type: batchv1.JobComplete},
				}),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "kube-system",
				Name:      "node1" + jobSuffix,
			},
		},
		{
			name: "node 为竞价实例，并且状态为init，node gc成功",
			nodes: []*corev1.Node{
				buildNodeWithLabels("node1", map[string]string{
					entity.NodeLabelChargingTypeKey: string(bccapi.PaymentTimingBidding),
					entity.NodeStateLabelKey:        string(entity.NodeStateInit),
				}, "docker://1.2.1"),
			},
			jobs: []*batchv1.Job{
				buildJob("node1"+jobSuffix, []batchv1.JobCondition{
					{Type: batchv1.JobComplete},
				}),
			},
			reconcileParam: types.NamespacedName{
				Namespace: "kube-system",
				Name:      "node1" + jobSuffix,
			},
		},
	}

	for i, test := range tests {
		if i != len(tests)-1 {
			// continue
		}
		t.Run(test.name, func(t *testing.T) {

			k8sclient := k8sfake.NewSimpleClientset()
			client := fake.NewFakeClient()

			informerFactory := informers.NewSharedInformerFactory(k8sclient, 0)
			podInformer := informerFactory.Core().V1().Pods()
			nodeInformer := informerFactory.Core().V1().Nodes()
			jobInformer := informerFactory.Batch().V1().Jobs()

			podIndexer := podInformer.Informer().GetIndexer()
			nodeIndexer := nodeInformer.Informer().GetIndexer()
			jobIndexer := jobInformer.Informer().GetIndexer()

			c := Controller{
				podLister:  podInformer.Lister(),
				nodeLister: nodeInformer.Lister(),
				jobLister:  jobInformer.Lister(),
				client:     client,
				options:    options.NewServerRunOptions(),
			}

			for _, job := range test.jobs {
				jobIndexer.Add(job)
				client.Create(context.Background(), job)
				c.shouldHandleJob(job)
			}

			for _, pod := range test.pods {
				podIndexer.Add(pod)
				client.Create(context.Background(), pod)
			}

			for _, node := range test.nodes {
				nodeIndexer.Add(node)
				client.Create(context.Background(), node)
				c.shouldHandleNode(node)
			}

			result, _ := c.Reconcile(context.Background(), reconcile.Request{
				NamespacedName: test.reconcileParam,
			})
			fmt.Println(result)

			if !reflect.DeepEqual(result, test.res) {
				t.Errorf("nodeGcController Reconcile got res %+v ,expect %+v ", result, test.res)
			}
		})
	}
}
