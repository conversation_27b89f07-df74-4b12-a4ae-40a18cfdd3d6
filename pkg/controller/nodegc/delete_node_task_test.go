package nodegc

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"testing"
	"time"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	ccev2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	"github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8s_type "k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/informers"
	k8sfake "k8s.io/client-go/kubernetes/fake"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

type fakeCceClient struct {
	hasErr        bool
	instanceIDMap map[string][]string
}

func newfakeCceClient(hasErr bool, instanceIDMap map[string][]string) util.CceClient {

	return &fakeCceClient{
		hasErr:        hasErr,
		instanceIDMap: instanceIDMap,
	}
}

func (fake *fakeCceClient) ChangeInstanceGroupAutoscalerConfig(enableAutoscaler bool, instanceGroupID string, maxReplicas int) (
	*ccev2.UpdateInstanceGroupClusterAutoscalerSpecResponse, error) {
	res := &ccev2.UpdateInstanceGroupClusterAutoscalerSpecResponse{
		CommonResponse: ccev2.CommonResponse{
			RequestID: "reqId",
		},
	}
	return res, nil
}

func (fake *fakeCceClient) GetInstanceGroupNodes(instanceGroupID string) (*ccev2.ListInstancesByInstanceGroupIDResponse, error) {
	instanceIDS, ok := fake.instanceIDMap[instanceGroupID]

	if !ok {
		return nil, errors.New("empty")
	}
	resp := &ccev2.ListInstancesByInstanceGroupIDResponse{
		CommonResponse: ccev2.CommonResponse{
			RequestID: "reqID",
		},
		Page: ccev2.ListInstancesByInstanceGroupIDPage{
			List: []*ccev2.Instance{},
		},
	}

	for _, instance := range instanceIDS {
		resp.Page.List = append(resp.Page.List, &ccev2.Instance{
			Spec: &types.InstanceSpec{
				CCEInstanceID: instance,
				Labels: types.InstanceLabels{
					entity.PatchNodeName: instance,
				},
			},
			Status: &ccev2.InstanceStatus{},
		})
	}
	return resp, nil
}

func (fake *fakeCceClient) GetInstanceGroupDetail(instanceGroupID string) (*ccev2.GetInstanceGroupResponse, error) {
	return nil, nil
}

func (fake *fakeCceClient) RemoveInstanceGroupNodes(instanceGroupID string, removeInstances []string) (*ccev2.CreateTaskResp, error) {
	res := &ccev2.CreateTaskResp{
		CommonResponse: ccev2.CommonResponse{
			RequestID: "reqId",
		},
	}
	if fake.hasErr {
		return nil, errors.New("err")
	}
	fmt.Printf("cceClient remove instance %+v \n", removeInstances)
	return res, nil
}

func (fake *fakeCceClient) GetInstanceGroupList() (*ccev2.ListInstanceGroupResponse, error) {
	if fake.hasErr {
		return nil, errors.New("err")
	}
	res := &ccev2.ListInstanceGroupResponse{
		CommonResponse: ccev2.CommonResponse{
			RequestID: "reqID",
		},
		Page: ccev2.ListInstanceGroupPage{
			List: []*ccev2.InstanceGroup{},
		},
	}
	return res, nil
}

func (fake *fakeCceClient) GetKubeconfig(clusterID string) (*ccev2.GetKubeConfigResponse, error) {
	return nil, nil
}

func (fake *fakeCceClient) GetInstance(instanceID string) (resp *ccev2.GetInstanceResponse, err error) {
	return nil, nil
}

func (fake *fakeCceClient) CreateInstances(instanceSet []*ccev2.InstanceSet) (resp *ccev2.CreateInstancesResponse, err error) {
	return nil, nil
}

func (fake *fakeCceClient) DeleteInstances(removeCCEInstanceIds []string) (resp *ccev2.DeleteInstancesResponse, err error) {
	return nil, nil
}

func (fake *fakeCceClient) InstanceGroupTasks(instanceGroupID string, pageNo, pageSize int) (resp *ccev2.ListTaskResp, err error) {
	return nil, nil
}

func buildBciRunningPod(name, nodeName, ig string) *corev1.Pod {

	return &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:       k8s_type.UID(fmt.Sprintf("%v-%v", "kube-system", name)),
			Name:      name,
			Namespace: "kube-system",
		},
		Spec: corev1.PodSpec{
			NodeName: nodeName,
			NodeSelector: map[string]string{
				entity.InstanceGroupKey: ig,
				entity.TenantLockKey:    "user1",
			},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
		},
	}
}

func TestDeleteNodeTask(t *testing.T) {
	tests := []struct {
		name             string
		pods             []*corev1.Pod
		nodes            []*corev1.Node
		cmInstanceGroup  []entity.InstanceGroupCm
		cceInstanceIDMap map[string][]string
		// 处于cool 状态node，重新调度上pod
		coolNodeReRunPodName string
		testControllerStart  bool
	}{
		{
			name: "节点为init状态node",
			nodes: []*corev1.Node{
				buildNodeWithLabels("n1", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateInit), entity.InstanceGroupKey: "2c4G"}, "docker"),
				buildNodeWithLabels("n2", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateInit), entity.InstanceGroupKey: "2c4G"}, "docker"),
				buildNodeWithLabels("n3", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateInit), entity.InstanceGroupKey: "2c4G"}, "docker"),
			},
			cmInstanceGroup: []entity.InstanceGroupCm{
				{
					InstanceGroupName: "2c4G",
					InstanceGroupId:   "2c4G",
					Buffer:            1,
				},
			},
			cceInstanceIDMap: map[string][]string{
				"2c4G": {
					"n1", "n2", "n3",
				},
			},
		},
		{
			name: "节点为init & cool 状态，node可被回收，gcfail node,notReady node ",
			nodes: []*corev1.Node{
				buildNodeWithLabels("n1", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateInit), entity.InstanceGroupKey: "2c4G"}, "docker"),
				// cool 状态node，没有entity.StartCoolTimeLabelKey 标签
				buildNodeWithLabels("n2", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateCool), entity.InstanceGroupKey: "2c4G"}, "docker"),
				func() *corev1.Node {
					node := buildNodeWithLabels("n3", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateCool), entity.InstanceGroupKey: "2c4G"}, "docker")
					d, _ := time.ParseDuration("-6m")
					t := time.Now().Add(d).Unix()
					node.Labels[entity.StartCoolTimeLabelKey] = strconv.FormatInt(t, 10)
					return node
				}(),
				func() *corev1.Node {
					// gc fail node
					node := buildNodeWithLabels("n4", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateCool), entity.InstanceGroupKey: "2c4G"}, "docker")
					d, _ := time.ParseDuration("-6m")
					t := time.Now().Add(d).Unix()
					node.Labels[entity.StartCoolTimeLabelKey] = strconv.FormatInt(t, 10)
					node.Labels[entity.NodeGcStateKey] = entity.NodeGcStateFail.String()
					return node
				}(),
				func() *corev1.Node {
					// not ready node
					node := buildNodeWithLabels("n5", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateNotReady), entity.InstanceGroupKey: "2c4G"}, "docker")
					node.Status.Conditions = []corev1.NodeCondition{
						{
							Type:   corev1.NodeReady,
							Status: corev1.ConditionFalse,
						},
					}
					d, _ := time.ParseDuration("-1h")
					t := time.Now().Add(d)
					node.CreationTimestamp.Time = t
					return node
				}(),
			},
			cmInstanceGroup: []entity.InstanceGroupCm{
				{
					InstanceGroupName: "2c4G",
					InstanceGroupId:   "2c4G",
					Buffer:            1,
				},
			},
			cceInstanceIDMap: map[string][]string{
				"2c4G": {
					"n1", "n2", "n3", "n4", "n5",
				},
			},
		},
		{
			name: "无可删除节点",
			nodes: []*corev1.Node{
				buildNodeWithLabels("n1", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateRunPod), entity.InstanceGroupKey: "2c4G"}, "docker"),
				buildNodeWithLabels("n2", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateRunPod), entity.InstanceGroupKey: "2c4G"}, "docker"),
				buildNodeWithLabels("n3", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateRunPod), entity.InstanceGroupKey: "2c4G"}, "docker"),
			},
			cmInstanceGroup: []entity.InstanceGroupCm{
				{
					InstanceGroupName: "2c4G",
					InstanceGroupId:   "2c4G",
					Buffer:            1,
				},
			},
			cceInstanceIDMap: map[string][]string{
				"2c4G": {
					"n1", "n2", "n3",
				},
			},
			testControllerStart: true,
		},
		{
			name: "删除过程中重新调度pod",
			nodes: []*corev1.Node{
				buildNodeWithLabels("n1", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateRunPod), entity.InstanceGroupKey: "2c4G"}, "docker"),
				func() *corev1.Node {
					node := buildNodeWithLabels("n2", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateCool), entity.InstanceGroupKey: "2c4G"}, "docker")
					d, _ := time.ParseDuration("-6m")
					t := time.Now().Add(d).Unix()
					node.Labels[entity.StartCoolTimeLabelKey] = strconv.FormatInt(t, 10)
					return node
				}(),
			},
			cmInstanceGroup: []entity.InstanceGroupCm{
				{
					InstanceGroupName: "2c4G",
					InstanceGroupId:   "2c4G",
					Buffer:            0,
				},
			},
			cceInstanceIDMap: map[string][]string{
				"2c4G": {
					"n1", "n2",
				},
			},
			coolNodeReRunPodName: "n2",
		},
		{
			name: "notReady node 删除",
			nodes: []*corev1.Node{
				func() *corev1.Node {
					node := buildNodeWithLabels("n1", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateNotReady), entity.InstanceGroupKey: "2c4G"}, "docker")
					d, _ := time.ParseDuration("-6m")
					t := time.Now().Add(d).Unix()
					node.Labels[entity.NotReadyStartTimeKey] = strconv.FormatInt(t, 10)

					node.Status.Conditions = append(node.Status.Conditions, corev1.NodeCondition{
						Type:   "Ready",
						Status: "False",
					})
					return node
				}(),
			},
			cmInstanceGroup: []entity.InstanceGroupCm{
				{
					InstanceGroupName: "2c4G",
					InstanceGroupId:   "2c4G",
					Buffer:            0,
				},
			},
			cceInstanceIDMap: map[string][]string{
				"2c4G": {
					"n1", "n2",
				},
			},
		},
	}

	for i, test := range tests {
		if i != len(tests)-1 {
			continue
		}
		t.Run(test.name, func(t *testing.T) {

			k8sclient := k8sfake.NewSimpleClientset()
			client := fake.NewFakeClient()

			informerFactory := informers.NewSharedInformerFactory(k8sclient, 0)
			podInformer := informerFactory.Core().V1().Pods()
			nodeInformer := informerFactory.Core().V1().Nodes()
			cmInformer := informerFactory.Core().V1().ConfigMaps()
			podIndexer := podInformer.Informer().GetIndexer()
			nodeIndexer := nodeInformer.Informer().GetIndexer()
			cmIndexer := cmInformer.Informer().GetIndexer()

			cceClient := newfakeCceClient(false, test.cceInstanceIDMap)
			opt := options.NewServerRunOptions()
			opt.NodeOptions.DeleteNodeTaskInterval = 1 * time.Second
			c := Controller{
				options:             opt,
				nodeLister:          nodeInformer.Lister(),
				podLister:           podInformer.Lister(),
				cmLister:            cmInformer.Lister(),
				client:              client,
				cceClient:           cceClient,
				deleteNodeSleepTime: 2 * time.Second,
			}

			for _, pod := range test.pods {
				podIndexer.Add(pod)
				client.Create(context.Background(), pod)
			}

			for _, node := range test.nodes {
				nodeIndexer.Add(node)
				client.Create(context.Background(), node)
			}

			bytes, _ := json.Marshal(test.cmInstanceGroup)
			cm := &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: entity.InstanceGroupCmNamespace,
					Name:      entity.InstanceGroupCmName,
				},
				Data: map[string]string{
					entity.InstanceGropuCmDataKey: string(bytes),
				},
			}
			cmIndexer.Add(cm)
			client.Create(context.Background(), cm)

			if test.coolNodeReRunPodName != "" {
				go func() {
					time.Sleep(1 * time.Second)
					pod := buildBciRunningPod("pod1", test.coolNodeReRunPodName, "2c4G")
					client.Create(context.Background(), pod)
					podIndexer.Add(pod)
					n, _, _ := nodeIndexer.GetByKey(test.coolNodeReRunPodName)
					node := n.(*corev1.Node)
					copyNode := node.DeepCopy()
					copyNode.Spec.Unschedulable = true
					// nodeIndexer.Add(copyNode)
					nodeIndexer.Update(copyNode)
				}()
			}

			err := c.deleteNodeTask()
			if err != nil {
				t.Errorf("deleteNodeTask err %+v ", err)
				return
			}
			if test.testControllerStart {
				stopChan := make(chan struct{})
				c.Start(stopChan)
				go func() {
					time.Sleep(2 * time.Second)
					close(stopChan)
				}()
				time.Sleep(5 * time.Second)
			}

		})
	}
}

func TestDeleteBidNodeTask(t *testing.T) {
	tests := []struct {
		name             string
		pods             []*corev1.Pod
		nodes            []*corev1.Node
		cmInstanceGroup  []entity.InstanceGroupCm
		cceInstanceIDMap map[string][]string
		// 处于cool 状态node，重新调度上pod
		coolNodeReRunPodName string
		testControllerStart  bool
	}{
		{
			name: "节点为init状态node",
			nodes: []*corev1.Node{
				buildNodeWithLabels("n1", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateInit), entity.InstanceGroupKey: "2c4G",
					entity.NodeLabelChargingTypeKey: string(bccapi.PaymentTimingBidding), entity.NodeStateTimeLabelKey: "1670399537"}, "docker"),
				buildNodeWithLabels("n2", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateInit), entity.InstanceGroupKey: "2c4G",
					entity.NodeLabelChargingTypeKey: string(bccapi.PaymentTimingBidding), entity.NodeStateTimeLabelKey: "1670399537"}, "docker"),
				buildNodeWithLabels("n3", map[string]string{entity.NodeStateLabelKey: string(entity.NodeStateInit), entity.InstanceGroupKey: "2c4G",
					entity.NodeLabelChargingTypeKey: string(bccapi.PaymentTimingBidding), entity.NodeStateTimeLabelKey: "1670399537"}, "docker"),
			},
			cmInstanceGroup: []entity.InstanceGroupCm{
				{
					InstanceGroupName: "2c4G",
					InstanceGroupId:   "2c4G",
					Buffer:            1,
				},
			},
			cceInstanceIDMap: map[string][]string{
				"2c4G": {
					"n1", "n2", "n3",
				},
			},
		},
	}

	for i, test := range tests {
		if i != len(tests)-1 {
			// continue
		}
		t.Run(test.name, func(t *testing.T) {

			k8sclient := k8sfake.NewSimpleClientset()
			client := fake.NewFakeClient()

			informerFactory := informers.NewSharedInformerFactory(k8sclient, 0)
			podInformer := informerFactory.Core().V1().Pods()
			nodeInformer := informerFactory.Core().V1().Nodes()
			cmInformer := informerFactory.Core().V1().ConfigMaps()
			podIndexer := podInformer.Informer().GetIndexer()
			nodeIndexer := nodeInformer.Informer().GetIndexer()
			cmIndexer := cmInformer.Informer().GetIndexer()

			cceClient := newfakeCceClient(false, test.cceInstanceIDMap)
			opt := options.NewServerRunOptions()
			opt.NodeOptions.DeleteNodeTaskInterval = 1 * time.Second
			c := Controller{
				options:             opt,
				nodeLister:          nodeInformer.Lister(),
				podLister:           podInformer.Lister(),
				cmLister:            cmInformer.Lister(),
				client:              client,
				cceClient:           cceClient,
				deleteNodeSleepTime: 2 * time.Second,
			}

			for _, pod := range test.pods {
				err := podIndexer.Add(pod)
				if err != nil {
					t.Errorf("deleteNodeTask err %+v ", err)
					return
				}
				client.Create(context.Background(), pod)
			}

			for _, node := range test.nodes {
				err := nodeIndexer.Add(node)
				if err != nil {
					t.Errorf("deleteNodeTask err %+v ", err)
					return
				}
				client.Create(context.Background(), node)
			}

			bytes, _ := json.Marshal(test.cmInstanceGroup)
			cm := &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: entity.InstanceGroupCmNamespace,
					Name:      entity.InstanceGroupCmName,
				},
				Data: map[string]string{
					entity.InstanceGropuCmDataKey: string(bytes),
				},
			}
			err := cmIndexer.Add(cm)
			if err != nil {
				t.Errorf("deleteNodeTask err %+v ", err)
				return
			}
			client.Create(context.Background(), cm)

			if test.coolNodeReRunPodName != "" {
				go func() {
					time.Sleep(1 * time.Second)
					pod := buildBciRunningPod("pod1", test.coolNodeReRunPodName, "2c4G")
					client.Create(context.Background(), pod)
					err := podIndexer.Add(pod)
					if err != nil {
						t.Errorf("deleteNodeTask err %+v ", err)
						return
					}
					n, _, _ := nodeIndexer.GetByKey(test.coolNodeReRunPodName)
					node := n.(*corev1.Node)
					copyNode := node.DeepCopy()
					copyNode.Spec.Unschedulable = true
					// nodeIndexer.Add(copyNode)
					err = nodeIndexer.Update(copyNode)
					if err != nil {
						t.Errorf("deleteNodeTask err %+v ", err)
						return
					}
				}()
			}

			err = c.deleteBidNodeTask()
			if err != nil {
				t.Errorf("deleteNodeTask err %+v ", err)
				return
			}
			if test.testControllerStart {
				stopChan := make(chan struct{})
				err := c.Start(stopChan)
				if err != nil {
					t.Errorf("deleteNodeTask err %+v ", err)
					return
				}
				go func() {
					time.Sleep(2 * time.Second)
					close(stopChan)
				}()
				time.Sleep(5 * time.Second)
			}

		})
	}
}
