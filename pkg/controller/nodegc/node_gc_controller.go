package nodegc

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/prometheus/client_golang/prometheus"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime/inject"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	batchv1_listers "k8s.io/client-go/listers/batch/v1"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	toolscache "k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/metrics"

	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"sigs.k8s.io/controller-runtime/pkg/source"
)

const (
	jobSuffix             = "-gc-job"
	jobNamespace          = "kube-system"
	controlledBy          = "controlled-by"
	controlledByValue     = "bci-resource-controller"
	NodeCreateTimeOut     = "10m"
	InitNodeCreateTimeOut = 180
)

var _ reconcile.Reconciler = &Controller{}
var _ inject.StartFuncInjector = &Controller{}

var (
	// 节点组锁node数量打点统计
	nodeGCFailCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "node_gc_fail_total",
			Help:      "The Node GC Fail Total Count .",
		},
		[]string{},
	)
)

func init() {
	metrics.Registry.MustRegister(nodeGCFailCounter)
}

// New 初始化controller，此处无法写单测
func New(option *options.ServerRunOptions, mgr manager.Manager) (interface{}, error) {
	cache := mgr.GetCache()
	nodeInformer, err := cache.GetInformer(context.TODO(), &corev1.Node{})
	if err != nil {
		klog.Errorf("nodeGcController get nodeInformer err %+v ", err)
		return nil, err
	}
	jobInformer, err := cache.GetInformer(context.TODO(), &batchv1.Job{})
	if err != nil {
		klog.Errorf("nodeGcController get jobInformer err %+v ", err)
		return nil, err
	}

	cmInformer, err := cache.GetInformer(context.TODO(), &corev1.ConfigMap{})
	if err != nil {
		klog.Errorf("nodeGcController get cmInformer err %+v ", err)
		return nil, err
	}
	podInformer, err := cache.GetInformer(context.TODO(), &corev1.Pod{})
	if err != nil {
		klog.Errorf("nodeController get podInformer err %+v ", err)
		return nil, err
	}

	cceClient, err := util.NewCceClient(option.CceOptions)
	if err != nil {
		klog.Errorf("nodeGcController NewCceClient err %+v ", err)
		return nil, err
	}

	nodeLister := corev1_listers.NewNodeLister(nodeInformer.(toolscache.SharedIndexInformer).GetIndexer())
	jobLister := batchv1_listers.NewJobLister(jobInformer.(toolscache.SharedIndexInformer).GetIndexer())
	cmLister := corev1_listers.NewConfigMapLister(cmInformer.(toolscache.SharedIndexInformer).GetIndexer())
	podLister := corev1_listers.NewPodLister(podInformer.(toolscache.SharedIndexInformer).GetIndexer())
	c := &Controller{
		options:             option,
		jobLister:           jobLister,
		nodeLister:          nodeLister,
		podLister:           podLister,
		cmLister:            cmLister,
		client:              mgr.GetClient(),
		cceClient:           cceClient,
		deleteNodeSleepTime: 10 * time.Second,
	}

	controller, err := controller.New("node-gc-controller", mgr, controller.Options{
		Reconciler:              c,
		MaxConcurrentReconciles: option.NodeOptions.NodeGCControllerWorkers,
		RateLimiter:             util.DefaultControllerRateLimiter(option),
	})

	controller.Watch(source.Kind(mgr.GetCache(), &corev1.Node{}), &handler.EnqueueRequestForObject{}, predicate.Funcs{
		CreateFunc: func(ce event.CreateEvent) bool {
			return c.shouldHandleNode(ce.Object.(*corev1.Node))
		},
		UpdateFunc: func(ue event.UpdateEvent) bool {
			return c.shouldHandleNode(ue.ObjectNew.(*corev1.Node))
		},
	})

	controller.Watch(source.Kind(mgr.GetCache(), &batchv1.Job{}), &handler.EnqueueRequestForObject{}, predicate.Funcs{
		CreateFunc: func(ce event.CreateEvent) bool {
			return c.shouldHandleJob(ce.Object.(*batchv1.Job))
		},
		UpdateFunc: func(ue event.UpdateEvent) bool {
			return c.shouldHandleJob(ue.ObjectNew.(*batchv1.Job))
		},
	})

	if err != nil {
		klog.Errorf("New nodeGcController err %+v ", err)
		return nil, err
	}
	return c, nil
}

// Controller nodeGc & 缩容逻辑 相关实现
type Controller struct {
	options *options.ServerRunOptions
	// informer lister
	jobLister  batchv1_listers.JobLister
	nodeLister corev1_listers.NodeLister
	cmLister   corev1_listers.ConfigMapLister
	podLister  corev1_listers.PodLister

	// 与k8s apiserver 交互的client
	client client.Client
	// 与cce交互的client
	cceClient           util.CceClient
	deleteNodeSleepTime time.Duration
}

// Reconcile nodeGc 逻辑实现
func (c *Controller) Reconcile(_ context.Context, request reconcile.Request) (res reconcile.Result, retErr error) {
	// 获取node
	nodeName := nodeNameFn(request.Name)
	// 处理node逻辑
	node, err := c.nodeLister.Get(nodeName)
	if errors.IsNotFound(err) {
		klog.Infof("nodeGcController node %+v has been deleted", request.Name)
		// 删除对应的job
		err = c.deleteJobIfExist(jobNameFn(nodeName))
		return reconcile.Result{}, err
	}
	if err != nil {
		klog.Errorf("nodeGcController nodeLister get node %s err %+v ", request.Name, err)
		return reconcile.Result{}, err
	}

	isJobChange := strings.Contains(request.Name, jobSuffix)
	if isJobChange {
		job, err := c.getGcJob(request.Name)
		if job == nil {
			// 被删除或get err
			return reconcile.Result{}, err
		}

		// sync job 逻辑
		jobSuccess := false
		jobFail := false

		for _, jobCondition := range job.Status.Conditions {
			if jobCondition.Type == batchv1.JobComplete {
				jobSuccess = true
				break
			}
			if jobCondition.Type == batchv1.JobFailed {
				jobFail = true
				break
			}
		}

		if jobSuccess {
			err = c.changeNodeGcState(node, entity.NodeGcStateSuccess)
		}
		if jobFail {
			// GC 失败打点
			nodeGCFailCounter.WithLabelValues().Inc()
			err = c.changeNodeGcState(node, entity.NodeGcStateFail)
		}
		return reconcile.Result{}, err
	}

	if !c.shouldHandleNode(node) {
		klog.V(3).Infof("nodeGcController node %s state is not cool ,ignore ", request.Name)
		return reconcile.Result{}, nil
	}

	// 判断是否为竞价实例，如果是，调用cce接口主动释放instance
	labels := node.Labels
	if len(labels) != 0 && labels[entity.NodeLabelChargingTypeKey] == string(bccapi.PaymentTimingBidding) {
		if runtime.RunCustomizeNodeGCHook(runtime.StateMachineGC, node) {
			resp, err := c.cceClient.DeleteInstances([]string{labels[entity.NodeLabelCCEInstanceIDKey]})
			if err != nil {
				return reconcile.Result{}, err
			}
			klog.V(3).Infof("nodeGcController delete instance id %+v ,resp %+v , err %+v ", labels[entity.NodeLabelCCEInstanceIDKey], resp, err)
			return reconcile.Result{}, nil
		}
	}

	jobName := jobNameFn(request.Name)
	job, err := c.getGcJob(jobName)

	if job == nil && err == nil {
		startGCTime := time.Now()
		// 执行自定义gc逻辑,此处串行处理，耗时较长。
		klog.V(3).Infof("nodeGcController node %s RunCustomizeNodeGCHook start", node.Name)
		defer func() {
			cost := time.Now().Sub(startGCTime)
			klog.V(3).Infof("nodeGcController node %s RunCustomizeNodeGCHook cost %+v ", node.Name, cost)
		}()
		if !runtime.RunCustomizeNodeGCHook(runtime.StateMachineGC, node) {
			klog.V(3).Infof("nodeGcController node %s RunCustomizeNodeGCHook not success ", node.Name)
			// GC 失败打点
			nodeGCFailCounter.WithLabelValues().Inc()
			err = c.changeNodeGcState(node, entity.NodeGcStateFail)
			return reconcile.Result{}, nil
		}
		// sleep 一段时间，给informer时间同步
		time.Sleep(800 * time.Millisecond)
		// 创建job
		err = c.createGcJob(node, jobName)
		if err != nil {
			return reconcile.Result{}, err
		}
	} else if err != nil {
		return reconcile.Result{}, err
	} else if job != nil {
		// job 已创建
		return reconcile.Result{}, nil
	}

	// patch node label
	err = c.changeNodeGcState(node, entity.NodeGcStateInit)
	return reconcile.Result{}, err
}

// 从informer 中获取job
func (c *Controller) getGcJob(jobName string) (*batchv1.Job, error) {
	job, err := c.jobLister.Jobs(jobNamespace).Get(jobName)
	if errors.IsNotFound(err) {
		// job被删除
		return nil, nil
	}
	if err != nil {
		klog.Errorf("nodeGcController jobLister get job %s err %+v ", jobName, err)
	}
	return job, err
}

// 创建job
func (c *Controller) createGcJob(node *corev1.Node, jobName string) error {
	// 运行命令
	cmd := buildGcCommand(node.Status.NodeInfo.ContainerRuntimeVersion)
	// 挂载sock路径
	mountPath := c.buildMountPath(node.Status.NodeInfo.ContainerRuntimeVersion)
	// 最大重试次数
	var defaultBackoffLimit int32 = 3
	var oneInt int32 = 1

	// containerResource := map[corev1.ResourceName]resource.Quantity{
	// 	corev1.ResourceCPU:    resource.MustParse("1m"),
	// 	corev1.ResourceMemory: resource.MustParse("1M"),
	// }

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: jobNamespace,
			Name:      jobName,
			Labels: map[string]string{
				"node":       node.Name,
				controlledBy: controlledByValue,
			},
		},
		Spec: batchv1.JobSpec{
			BackoffLimit: &defaultBackoffLimit,
			Parallelism:  &oneInt,
			Completions:  &oneInt,
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Name: "gc-job",
				},
				Spec: corev1.PodSpec{
					// 不重启容器
					RestartPolicy: corev1.RestartPolicyNever,
					// 直接调度到node节点上
					NodeName: node.Name,
					// 适配2.1ENI，使用主机网络
					HostNetwork: true,
					Volumes: []corev1.Volume{
						{
							Name: "runtime-socket",
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: mountPath,
								},
							},
						},
					},
					Containers: []corev1.Container{
						{
							Name:            "job",
							Image:           c.options.NodeOptions.NodeGcImage,
							ImagePullPolicy: corev1.PullIfNotPresent,
							// Resources: corev1.ResourceRequirements{
							// 	// 只设置request
							// 	Requests: containerResource,
							// },
							Command: []string{"sh"},
							Args:    []string{"-c", cmd},
							// 挂载var目录
							VolumeMounts: []corev1.VolumeMount{
								{
									MountPath: mountPath,
									Name:      "runtime-socket",
								},
							},
						},
					},
				},
			},
		},
	}

	err := c.client.Create(context.Background(), job)
	klog.V(3).Infof("nodeGcController node %+v create gc job err %+v ", node.Name, err)
	return err
}

// 删除job
func (c *Controller) deleteJobIfExist(jobName string) error {
	job, err := c.jobLister.Jobs(jobNamespace).Get(jobName)
	if errors.IsNotFound(err) {
		return nil
	}
	if err != nil {
		klog.Errorf("nodeGcController nodeLister get job %s err %+v ", jobName, err)
		return err
	}
	// 删除gc成功的pod
	selector := labels.SelectorFromSet(labels.Set(map[string]string{"job-name": job.Name}))
	jobPods, err := c.podLister.List(selector)
	if err != nil {
		klog.Errorf("nodeGcController nodeLister get job pods %s err %+v ", jobName, err)
		return err
	}
	for _, jobPod := range jobPods {
		if jobPod.Status.Phase == corev1.PodSucceeded {
			klog.V(4).Infof("nodeGcController delete job %+v Succeeded pod %+v ", jobName, jobPod.Name)
			c.client.Delete(context.Background(), jobPod)
		}
	}
	err = c.client.Delete(context.Background(), job)
	klog.V(3).Infof("nodeGcController delete job %+v err %+v ", job.Name, err)

	return err
}

// 只处理状态机为cool的node，并且未进行gc 或gc 失败
func (c *Controller) shouldHandleNode(node *corev1.Node) bool {
	// node := nodeObject.(*corev1.Node)
	if len(node.Labels) == 0 || node.Labels[entity.NodeStateLabelKey] == "" {
		return false
	}
	// 判断bidNode是否为not ready状态
	if isNotReady := c.BidNodeIsNotReady(node); isNotReady {
		return isNotReady
	}

	isCool := node.Labels[entity.NodeStateLabelKey] == entity.NodeStateCool.String()
	// 判断是否gc过，gc过则不再处理
	gcSuccess := node.Labels[entity.NodeGcStateKey] == entity.NodeGcStateSuccess.String()
	// 判断是否有lock标签
	_, ok := node.Labels[entity.TenantLockKey]
	return isCool && !gcSuccess && !ok
}

// BidNodeIsNotReady 判断是否为not ready
func (c *Controller) BidNodeIsNotReady(node *corev1.Node) bool {
	// 节点notReady，返回true
	nodeCreateTime := node.CreationTimestamp.Time
	duration, _ := time.ParseDuration(NodeCreateTimeOut)

	if entity.K8sNodeIsBid(node) {
		if node.Labels[entity.NodeStateLabelKey] == entity.NodeStateNotReady.String() &&
			time.Now().After(nodeCreateTime.Add(duration)) {
			return true
		}
		// 竞价实例为空或者init，时间超过3m，就触发回收
		if node.Labels[entity.NodeStateLabelKey] == string(entity.NodeStateEmpty) || node.Labels[entity.NodeStateLabelKey] == string(entity.NodeStateInit) {
			nodeStateTime, _ := strconv.Atoi(node.Labels[entity.NodeStateTimeLabelKey])
			if time.Now().Unix()-int64(nodeStateTime) > InitNodeCreateTimeOut {
				return true
			}
		}
	}
	return false
}

// shouldHandleJob 是否需要处理job
func (c *Controller) shouldHandleJob(job *batchv1.Job) bool {
	if len(job.Labels) == 0 {
		return false
	}
	containsJobSuffix := strings.Contains(job.Name, jobSuffix)

	if job.Labels[controlledBy] == controlledByValue && containsJobSuffix {
		return true
	}
	return false
}

// changeNodeGcState patch node label，修改nodeGc 状态
func (c *Controller) changeNodeGcState(node *corev1.Node, gcStatus entity.NodeGcState) error {
	klog.V(3).Infof("nodeGcController changeNodeGcState node %+v gcStatus %+v ", node.Name, gcStatus)

	nodeName := node.Name
	// 获取最新node，从informer，防止Annotation被覆盖
	node, err := c.nodeLister.Get(node.Name)
	if err != nil {
		klog.Errorf("nodeGcController changeNodeGcState get node %s from informer err %+v ", nodeName, err)
		return err
	}
	copyNode := node.DeepCopy()
	if len(copyNode.Labels) == 0 {
		copyNode.Labels = map[string]string{}
	}
	needPatch := false
	if copyNode.Labels[entity.NodeGcStateKey] != gcStatus.String() {
		needPatch = true
	}
	if gcStatus == entity.NodeGcStateSuccess || gcStatus == entity.NodeGcStateFail {
		// 成功或失败，删除job
		if err := c.deleteJobIfExist(jobNameFn(node.Name)); err != nil {
			return err
		}
	}

	if !needPatch {
		return nil
	}

	copyNode.Labels[entity.NodeGcStateKey] = gcStatus.String()

	metaData := map[string]map[string]string{
		"labels": copyNode.Labels,
	}
	patchData, _ := json.Marshal(&map[string]interface{}{
		"metadata": &metaData,
	})

	for i := 0; i < 3; i++ {
		err = c.client.Patch(context.TODO(), copyNode, client.RawPatch(types.StrategicMergePatchType, []byte(patchData)))
		if err == nil {
			return nil
		}
		klog.Errorf("nodeGcController patch node %+v gcState %+v , err %+v ", node.Name, gcStatus, err)
	}
	return err
}

func buildGcCommand(runtimeVersion string) string {
	// docker 还是containerd
	isDocker := strings.Contains(runtimeVersion, "docker")
	if isDocker {
		return "docker rm `docker ps -a|grep Exited|awk '{print $1}'` && docker image prune -a -f"
	}
	// containerd
	return "crictl ps -a | grep Exit | awk '{print $1}' && crictl rmi --prune"
}

func (c *Controller) buildMountPath(runtimeVersion string) string {
	// docker 还是containerd
	isDocker := strings.Contains(runtimeVersion, "docker")
	if isDocker {
		return c.options.NodeOptions.DockerSockPath
	}
	// containerd
	return c.options.NodeOptions.ContainerdSockPath
}

// 根据nodeName 构建jobName
func jobNameFn(nodeName string) string {
	return nodeName + jobSuffix
}

// 获取nodeName
func nodeNameFn(requestName string) string {
	if !strings.Contains(requestName, jobSuffix) {
		return requestName
	}

	var nodeName string
	splits := strings.Split(requestName, jobSuffix)
	if len(splits) == 2 {
		nodeName = splits[0]
	}
	return nodeName
}

// Start 开始回收节点定时任务
func (c *Controller) Start(stopChan <-chan struct{}) error {
	go func() {
		ticker := time.NewTicker(c.options.NodeOptions.DeleteNodeTaskInterval)
		time.Sleep(c.options.NodeOptions.DeleteNodeTaskInterval)

		for {
			select {
			case <-stopChan:
				return
			case <-ticker.C:
				c.deleteNodeTask()
				err := c.deleteBidNodeTask()
				if err != nil {
					klog.Errorf("delete bid node err %+v ", err)
				}
			}
		}
	}()
	return nil
}
