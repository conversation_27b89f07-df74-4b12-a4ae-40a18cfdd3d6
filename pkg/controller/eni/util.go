package eni

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"sort"
	"strings"
	"time"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/client"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	"k8s.io/klog/v2"
)

func parseAccountID(waitEniKey string) string {
	// 配置组成 : bci-wait-eni-%s
	splits := strings.Split(waitEniKey, podWaitEniPrefix)
	if len(splits) != 2 {
		return ""
	}
	return splits[1]
}

func parsePodNamespaceAndName(value string) (namespace, name string) {
	// 配置组成 :  "%s/%s", podNameSpace, podName
	splits := strings.Split(value, "/")
	if len(splits) != 2 {
		return
	}
	return splits[0], splits[1]
}

func buildAccountPodInfo(accountKey string, podInfo string) (*entity.WaitEniPodInfo, error) {
	accountID := parseAccountID(accountKey)
	if accountID == "" {
		return nil, fmt.Errorf("parse account ID empty")
	}
	namespace, name := parsePodNamespaceAndName(podInfo)
	if name == "" || namespace == "" {
		return nil, fmt.Errorf("parse pod info empty")
	}
	klog.V(4).Infof("eniController pod <%s/%s> try create eni ", namespace, name)
	return &entity.WaitEniPodInfo{
		AccountID:    accountID,
		PodNamespace: namespace,
		PodName:      name,
	}, nil
}

func getAllWaitEniPodList(node *corev1.Node, all bool) []*entity.WaitEniPodInfo {
	result := make([]*entity.WaitEniPodInfo, 0)

	for k, v := range node.Annotations {
		if !strings.Contains(k, podWaitEniPrefix) {
			continue
		}
		podInfo, err := buildAccountPodInfo(k, v)
		if err != nil {
			klog.Errorf("eniController parse wait eni pod k : %s v : %s err %+v ", k, v, err)
			continue
		}
		result = append(result, podInfo)
		if !all {
			break
		}
	}
	return result
}

func getNodeProviderID(node *corev1.Node) (string, error) {
	providerID := node.Spec.ProviderID

	err := fmt.Errorf("eniController node %s get ProviderID not right : %s ", node.Name, providerID)

	if !strings.HasPrefix(providerID, cceProviderIDPrefix) {
		klog.Error(err)
		return "", err
	}

	splits := strings.Split(providerID, cceProviderIDPrefix)
	if len(splits) != 2 {
		klog.Error(err)
		return "", err
	}
	return splits[1], nil
}

func buildAttachSuccessEniList(node *corev1.Node) []*entity.EniInfo {
	attachSuccessENIList := make([]*entity.EniInfo, 0)

	for k, v := range node.Annotations {
		if !strings.HasPrefix(k, nodeBindEniInfo) {
			continue
		}
		eniInfo := &entity.EniInfo{}
		if err := json.Unmarshal([]byte(v), eniInfo); err != nil {
			continue
		}
		if eniInfo.AttachSuccess {
			attachSuccessENIList = append(attachSuccessENIList, eniInfo)
		}
	}
	return attachSuccessENIList
}

func getEniInfo(node *corev1.Node, eniID string, accountID string) (*entity.EniInfo, error) {
	eniJSON, ok := node.Annotations[buildEniAnnotationKey(eniID)]
	if !ok {
		// 信息丢失，则忽略处理
		klog.Warningf("eniController node %s nodeBindEniInfo eni %s account %s info empty ",
			node.Name, eniID, accountID)
		return nil, fmt.Errorf("eni info empty")
	}
	eniInfo := &entity.EniInfo{}
	if err := json.Unmarshal([]byte(eniJSON), eniInfo); err != nil {
		klog.Errorf("eniController node %s eni %s account %s info %s  Unmarshal err %+v ",
			node.Name, eniID, accountID, eniJSON, err)
		return nil, err
	}
	return eniInfo, nil
}

// vpc 错误码说明: https://cloud.baidu.com/doc/VPC/s/sjwvyuhe7

func isENINotFoundErr(err error) bool {
	if err == nil {
		return false
	}
	//  Error Message: "The specified object is not found or resource do not exist.",
	// Error Code: "NoSuchObject", Status Code: 404, Request Id: "c923ebe6-2d0c-4013-b7d4-cf5b58b97b08"
	if strings.Contains(err.Error(), "NoSuchObject") ||
		strings.Contains(err.Error(), "resource do not exist") ||
		strings.Contains(err.Error(), "Status Code: 404") {
		return true
	}
	return false
}

func isENIRatelimited(err error) bool {
	if err == nil {
		return false
	}

	// Error Message: "There are too many connections. The host is {host}",
	// Error Code: "RateLimit", Status Code: 421, Request Id: "48e8dbee-7919-4ad9-81ab-4204ff5eb1e7"
	if strings.Contains(err.Error(), "RateLimit") || strings.Contains(err.Error(), "Status Code: 421") {
		return true
	}
	return false
}

func isQualifyFailed(err error) bool {
	if err == nil {
		return false
	}

	// Error Message: "The user's real name authentication verification failed.",
	// Error Code: "QualifyFailed", Status Code: 403, Request Id: "78bce3ba-c4a0-4994-b6dc-e3c6287e2cac"
	if strings.Contains(err.Error(), "QualifyFailed") {
		return true
	}
	return false
}

func isENISubnetHasNoMoreIPErr(err error) bool {
	if err == nil {
		return false
	}

	//  "The subnet has no more Ip to allocate",
	// Error Code: "SubnetHasNoMoreIpException", Status Code: 400, Request Id: "f16a2a8d-64ae-4ab0-bbe7-79d5623675d9"
	if strings.Contains(err.Error(), "SubnetHasNoMoreIpException") || strings.Contains(err.Error(), "subnet has no more Ip to allocate") {
		return true
	}
	return false
}

func readENIPrivateIPConfigFromFile(filePath string) map[string]*entity.ENIPrivateIPConfig {
	if filePath == "" {
		panic("eni PrivateIPConfig path is empty")
	}

	bytes, err := os.ReadFile(filePath)
	if err != nil {
		klog.Errorf("eniController read ENIPrivateIPConfig path %s err %+v ", filePath, err)
		panic(err)
	}

	configs := make([]*entity.ENIPrivateIPConfig, 0)
	err = json.Unmarshal(bytes, &configs)
	if err != nil {
		klog.Errorf("eniController Unmarshal ENIPrivateIPConfig %s err %+v ", string(bytes), err)
		panic(err)
	}

	result := make(map[string]*entity.ENIPrivateIPConfig)
	for _, config := range configs {
		if config.EniMaxPrivateIPCount <= 0 {
			panic("EniMaxPrivateIPCount should > 0 ")
		}
		result[config.NodeResourceType] = config
	}

	return result
}

// computeWaitSameEniPodListOnNode 计算等待相同eni 配置的pod数量
func computeWaitSameEniPodListOnNode(waitEniPodInfo *entity.WaitEniPodInfo,
	node *corev1.Node, podLister corev1_listers.PodLister) ([]*corev1.Pod, error) {

	waitPrivateIPPodList := make([]*corev1.Pod, 0)
	allPods, err := podLister.List(labels.Everything())
	if err != nil {
		klog.Errorf("eniController computeWaitSameEniPodListOnNode List All pods err %+v ", err)
		return waitPrivateIPPodList, err
	}

	for _, pod := range allPods {
		if pod.Spec.NodeName != node.Name {
			continue
		}
		// 判断是否是 pending
		if pod.Status.Phase != corev1.PodPending {
			continue
		}
		// 过滤已经分配ip的pod
		if pod.Status.PodIP != "" {
			continue
		}
		if _, ok := pod.Annotations[bciPodIPAnnotationKey]; ok {
			continue
		}

		if isSameEniConfig(waitEniPodInfo.SubnetID, waitEniPodInfo.SecurityGroupIDList, pod) {
			waitPrivateIPPodList = append(waitPrivateIPPodList, pod)
		}
	}

	return waitPrivateIPPodList, nil
}

func isSameEniConfig(eniSubnetID string, eniSecurityGroupIDList []string, pod *corev1.Pod) bool {
	// 判断是否eni 创建失败
	if _, ok := pod.Annotations[createEniFailed]; ok {
		return false
	}
	// 获取pod vpc、subnet、安全组相关信息，目前按单个获取
	subnetID, ok := pod.Annotations[podSubnetID]
	if !ok || subnetID == "" {
		return false
	}

	// 按 , 分隔开
	securityGroupIDs, ok := pod.Annotations[podSecurityGroupIDs]
	if !ok || securityGroupIDs == "" {
		return false
	}
	securityGroupIDList := strings.Split(securityGroupIDs, ",")
	// 排序
	sort.Strings(securityGroupIDList)

	if eniSubnetID == subnetID && reflect.DeepEqual(eniSecurityGroupIDList, securityGroupIDList) {
		return true
	}

	return false
}

// 计算eni下pod 使用的辅助ip列表
func computePodAllocatedPrivateIPList(eniInfo *entity.EniInfo,
	node *corev1.Node,
	podLister corev1_listers.PodLister) ([]string, error) {

	allocatedPrivateIPList := make([]string, 0)
	allPods, err := podLister.List(labels.Everything())
	if err != nil {
		klog.Errorf("eniController computeEniAllocatedPrivateIPList List All pods err %+v ", err)
		return allocatedPrivateIPList, err
	}

	for _, pod := range allPods {
		if pod.Spec.NodeName != node.Name {
			continue
		}
		// 判断是否是running 或 pending
		if pod.Status.Phase != corev1.PodPending && pod.Status.Phase != corev1.PodRunning {
			continue
		}

		podIP := pod.Status.PodIP
		if podIP == "" {
			podIPAnnotation, ok := pod.Annotations[bciPodIPAnnotationKey]
			if !ok {
				continue
			}
			podIP = podIPAnnotation
		}

		if isSameEniConfig(eniInfo.SubnetID, eniInfo.SecurityGroupIDs, pod) {
			allocatedPrivateIPList = append(allocatedPrivateIPList, podIP)
			// pod 创建时间戳
			// pod.CreationTimestamp.Time.Unix()
		}
	}
	return allocatedPrivateIPList, nil
}

func computePodAllocatedPrivateIPv6List(eniInfo *entity.EniInfo,
	node *corev1.Node,
	podLister corev1_listers.PodLister) ([]string, error) {

	allocatedPrivateIPv6List := make([]string, 0)
	allPods, err := podLister.List(labels.Everything())
	if err != nil {
		klog.Errorf("eniController computeEniAllocatedPrivateIPv6List List All pods err %+v ", err)
		return allocatedPrivateIPv6List, err
	}

	for _, pod := range allPods {
		if pod.Spec.NodeName != node.Name {
			continue
		}
		// 判断是否是running 或 pending
		if pod.Status.Phase != corev1.PodPending && pod.Status.Phase != corev1.PodRunning {
			continue
		}

		podIPv6Annotation, _ := pod.Annotations[bciPodIPv6AnnotationKey]
		if podIPv6Annotation == "" {
			continue
		}

		if isSameEniConfig(eniInfo.SubnetID, eniInfo.SecurityGroupIDs, pod) {
			allocatedPrivateIPv6List = append(allocatedPrivateIPv6List, podIPv6Annotation)
			// pod 创建时间戳
			// pod.CreationTimestamp.Time.Unix()
		}
	}
	return allocatedPrivateIPv6List, nil
}

func buildEniAnnotationKey(eniID string) string {
	return nodeBindEniInfo + eniID
}

func isNormalSercurytyGroup(securityGroupIDs []string) bool {
	return allStartWithPrifix(securityGroupIDs, "g-")
}

func isEnterpriseSercurytyGroup(securityGroupIDs []string) bool {
	return allStartWithPrifix(securityGroupIDs, "esg-")
}

func allStartWithPrifix(strs []string, prefix string) bool {
	for _, str := range strs {
		if !strings.HasPrefix(str, prefix) {
			return false
		}
	}
	return true
}

func splitSliceByBatchsize(slices []string, batchsize int) [][]string {

	result := make([][]string, 0)

	if len(slices) == 0 || batchsize <= 0 {
		return result
	}

	min := func(x, y int) int {
		if x < y {
			return x
		}
		return y
	}

	loopSize := 0
	if len(slices)%batchsize == 0 {
		loopSize = len(slices) / batchsize
	} else {
		loopSize = (len(slices) / batchsize) + 1
	}

	// 0,1,2,3,4,5
	for i := 0; i < loopSize; i++ {
		result = append(result, slices[i*batchsize:min((i+1)*batchsize, len(slices))])
	}

	return result
}

func (c *Controller) getTidalTimeFromCloudBridge(accountID string) (param util.CloudBridgeTidalParam, err error) {

	req := &client.QuotaRequest{
		UserType:   client.UserTypeAccountID,
		UserValue:  accountID,
		QuotaTypes: []client.QuotaType{tidalQuotaType},
	}

	var resp *client.QuotaResponse
	// 最多重试3次
	for i := 0; i < 3; i++ {
		resp, err = c.cloudBridgeClient.GetUserQuotas(context.Background(), req,
			c.stsClient.NewSignOption(context.Background(), accountID))
		if err == nil {
			break
		}
		time.Sleep(1 * time.Second)
	}
	if err != nil {
		// 云桥获取配置失败，使用默认节点组配置
		klog.Errorf("eniController getTidalTimeFromCloudBridge get accountID %s cloudBridge tidalParam err %+v", accountID, err)
		_ = c.hiClient.SendMarkDownMessage(context.Background(),
			"**云桥获取潮汐参数失败**\nregion:"+c.networkOption.Region+"\naccountID:"+accountID+"\n错误信息:"+err.Error())
		return param, err
	}

	tidalParamStr, ok := resp.QuotaType2Quota[tidalQuotaType]
	if !ok {
		return param, fmt.Errorf("tidalParam not found in response %+v", resp)
	}
	klog.V(4).Infof("eniController getTidalTimeFromCloudBridge accountID %s tidalParamStr %s ", accountID, tidalParamStr)

	err = json.Unmarshal([]byte(tidalParamStr), &param)
	if err != nil {
		// 云桥解析配置失败，使用默认节点组配置
		err := fmt.Errorf("eniController getTidalTimeFromCloudBridge get accountID %s Unmarshal tidalParam %s err %w", accountID, tidalParamStr, err)
		klog.Error(err)
		_ = c.hiClient.SendMarkDownMessage(context.Background(),
			"**解析云桥潮汐参数失败**\nregion:"+c.networkOption.Region+"\naccountID:"+accountID+"\n错误信息:"+err.Error())
		return param, err
	}
	return
}
