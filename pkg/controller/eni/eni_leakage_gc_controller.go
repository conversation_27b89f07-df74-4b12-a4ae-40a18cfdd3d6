package eni

import (
	"context"
	"encoding/json"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/client"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog/v2"
)

var (
	// instanceGroup维度各状态机节点数量监控
	bidNodeLeakageEniCountGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Subsystem: "bci",
			Name:      "bid_node_leakage_eni_count",
			Help:      "The bid node leakage eni count.",
		},
		[]string{},
	)
)

const (
	bidNodeRunningEniCmName           = "bidding-running-node-eni"
	bciNodeLeakageEniCmName           = "bidding-leakage-node-eni"
	bidNodeEniCmNamespace             = "kube-system"
	bidEniDataKey                     = "config"
	autoDeletePodLabelKey             = "bci_internal_AutoDeletePod"
	podRecycleReasonAnnotationKey     = "bci_internal_PodRecycleReason"
	controllerAutoDeleteRecycleReason = "controllerAutoDelete"
	tidalQuotaType                    = "tidalParam"
)

var (
	// 存放正在eni gc的node列表
	processEniGcBidNodeMap  = make(map[string]struct{})
	processEniGcBidNodeLock = &sync.RWMutex{}
)

var (
	// 节假日列表
	globalHolidays *client.HolidayList = nil
	// 调休日列表
	globalTimeoffDay *client.HolidayList = nil
)

type BidNodeEvent struct {
	NodeName string
	// 驱逐pod 等待时间
	EvictPodWaitSecond int
}

// 删除pod & 清理eni worker
type BidNodeEniGCWorker struct {
	BidNodeEventChan chan BidNodeEvent
	Controller       *Controller
}

func (c *Controller) startAllBidNodeEventWorker() {
	for i := 0; i < c.networkOption.SyncBidNodeEventWorkerCount; i++ {
		bidWorker := &BidNodeEniGCWorker{
			BidNodeEventChan: c.bidNodeEventChan,
			Controller:       c,
		}
		go func(worker *BidNodeEniGCWorker) {
			worker.run()
		}(bidWorker)
	}
}

func (w *BidNodeEniGCWorker) run() {
	klog.V(4).Infof("eniController BidNodeEniGCWorker start run ")
	for {
		select {
		case event := <-w.BidNodeEventChan:
			klog.V(3).Infof("eniController BidNodeEniGCWorker receive bid node event %+v ", event)
			// 标示正在处理的node列表
			processEniGcBidNodeLock.Lock()
			processEniGcBidNodeMap[event.NodeName] = struct{}{}
			processEniGcBidNodeLock.Unlock()

			// 此处重启，有可能会导致丢失事件，从而造成eni泄漏，重新获取到主的controller会尝试重新list bcc event 处理
			if event.EvictPodWaitSecond > 0 {
				// 启动一个timer定时任务执行
				time.AfterFunc(time.Duration(event.EvictPodWaitSecond), func() {
					w.doDeleteBidPodAndDeleteNodeEni(event)
				})
				break
			}
			w.doDeleteBidPodAndDeleteNodeEni(event)
		}
	}
}

func (w *BidNodeEniGCWorker) doDeleteBidPodAndDeleteNodeEni(event BidNodeEvent) {

	defer func() {
		processEniGcBidNodeLock.Lock()
		delete(processEniGcBidNodeMap, event.NodeName)
		processEniGcBidNodeLock.Unlock()
	}()

	node, err := w.Controller.nodeLister.Get(event.NodeName)
	if err != nil {
		klog.Errorf("eniController BidNodeEniGCWorker get node %s err %+v ", event.NodeName, err)
		return
	}

	// list node 上所有bci pod
	allPods, err := w.Controller.podLister.List(labels.Everything())

	nodeInfo := entity.NewNodeInfo(node)

	for _, pod := range allPods {
		if pod.Spec.NodeName != event.NodeName {
			continue
		}
		nodeInfo.AddOrUpdatePod(pod)
	}

	bciPods := nodeInfo.GetBciPendingOrRunningPods()

	// 校验是否所有pod都可自动删除,bci 控制面提交pod 时设置
	for _, pod := range bciPods {
		if _, ok := pod.Annotations[autoDeletePodLabelKey]; !ok {
			// 打日志，防止调度器问题，正常pod 调度到潮汐或竞价节点组上
			klog.Errorf("eniController doDeleteBidPodAndDeleteNodeEni on node %s find not auto delete pod %s ",
				node.Name, pod.Name)
		}
	}

	// 更新潮汐pod Annotation,告知bci 控制面,bci 控制面watch到pod 有此Annotation,更新resourceRecycleReason=CONTROLLER_AUTO_DELETE
	for _, bciPod := range bciPods {
		klog.V(4).Infof("eniController doDeleteBidPodAndDeleteNodeEni patch pod %s podRecycleReasonAnnotationKey Annotation ",
			bciPod.Name)
		patchValue := entity.BuildUpdatePatchValue(podRecycleReasonAnnotationKey, controllerAutoDeleteRecycleReason)
		_ = util.PatchPodAnnotation(w.Controller.client, bciPod, []entity.PatchValue{patchValue})
	}
	// 给bci 控制面informer 同步时间
	time.Sleep(3 * time.Second)

	// 删除bci pod
	for _, bciPod := range bciPods {
		err = w.Controller.client.Delete(context.Background(), bciPod)
		klog.V(2).Infof("eniController doDeleteBidPodAndDeleteNodeEni on node %s tidalNode type %+v delete bci bid pod %s err %+v ",
			node.Name, nodeInfo.GetBiddingNodeType(), bciPod.Name, err)
	}

	// 删除eni，eni 删除失败,定时任务重试
	err = w.Controller.DoCustomizeNodeGC(runtime.DeleteBidNodeGC, node)
	if err != nil {
		klog.Errorf("eniController doDeleteBidPodAndDeleteNodeEni node %s err %+v ", node.Name, err)
		return
	}

	// 删除node上所有eni列表，从 bid eni cm中
	allNodeEnis := getAllNodeEnis(node)
	deleteNodeEniList := make([]entity.BidNodeEniInfo, 0)
	providerID, _ := getNodeProviderID(node)
	for _, nodeEni := range allNodeEnis {
		deleteNodeEniList = append(deleteNodeEniList, entity.BuildBidNodeEniInfo(nodeEni, providerID, node.Name))
	}

	w.Controller.UpdateBidRunningEniConfigMapWithRetry(deleteNodeEniList, true)
	return
}

func (c *Controller) gcLeakageBciNode() {
	allBciNodes, err := c.bciNodeLister.List(labels.Everything())
	if err != nil {
		klog.Errorf("eniController gcLeakageBciNode list all bcinode err %+v", err)
		return
	}

	for _, bciNode := range allBciNodes {
		_, err := c.nodeLister.Get(bciNode.Name)
		if err == nil {
			continue
		}

		if !errors.IsNotFound(err) {
			klog.Warningf("eniController gcLeakageBciNode get node %s err %+v ", bciNode.Name, err)
			continue
		}

		// 已经删除过了，当新创建node上node agent 起来后，发现有delete时间戳，会删除Finalizers，随后crd被删除
		if bciNode.DeletionTimestamp != nil {
			continue
		}
		// node 已经不在了
		err = c.client.Delete(context.Background(), bciNode)
		klog.V(3).Infof("eniController gcLeakageBciNode find leakage crd %s delete it err %+v ",
			bciNode.Name, err)

	}
}

func (c *Controller) listNodesByLabels(nodeLabels map[string]string) []*corev1.Node {
	selector := labels.SelectorFromSet(labels.Set(nodeLabels))

	// 筛选所有竞价实例node
	nodes, err := c.nodeLister.List(selector)
	if err != nil {
		klog.Errorf("eniController listNodesByLabels list nodes err %+v ", err)
		return nil
	}
	return nodes
}

// gc 竞价实例node eni 回收
func (c *Controller) gcNodeEniByBccEvent() {

	allNodes := make([]*corev1.Node, 0)
	// 竞价实例node
	bidNodeLabels := map[string]string{
		entity.NodeLabelBiddingNodeType: entity.NodeLabelBiddingNodeTypeBidding,
	}
	bidNodes := c.listNodesByLabels(bidNodeLabels)

	if len(bidNodes) > 0 {
		allNodes = append(allNodes, bidNodes...)
	}

	// 潮汐实例node
	tidalNodeLabels := map[string]string{
		entity.NodeLabelBiddingNodeType: entity.NodeLabelBiddingNodeTypeTidal,
	}

	tidalNodes := c.listNodesByLabels(tidalNodeLabels)
	if len(tidalNodes) > 0 {
		allNodes = append(allNodes, tidalNodes...)
	}
	if len(allNodes) == 0 {
		return
	}

	providerIDs := make([]string, 0)
	addCount := 0
	// key: providerID , value : nodeName
	providerIDMap := make(map[string]string)

	// 分页，bcc list event 事件，一次默认最多100条
	for _, node := range allNodes {
		providerID, err := getNodeProviderID(node)
		if err != nil {
			continue
		}
		providerIDMap[providerID] = node.Name
		providerIDs = append(providerIDs, providerID)
		addCount++
		if addCount == c.networkOption.BidBccEventPageSize {
			c.sendBidNodeEvent(providerIDs, providerIDMap)
			addCount = 0
			providerIDs = make([]string, 0)
		}
	}

	if len(providerIDs) > 0 {
		c.sendBidNodeEvent(providerIDs, providerIDMap)
	}
}

func (c *Controller) sendBidNodeEvent(instanceUUIDs []string, providerIDMap map[string]string) {
	request := &client.GetBidEventsRequest{
		InstanceUUIDs: instanceUUIDs,
	}

	resp, err := c.bidEventClient.GetBidEvents(context.Background(), request, c.buildSignOptionFn(c.networkOption.ResourceAccountID))
	if err != nil {
		klog.Errorf("eniController gcBiddingNodeEni GetBidEvents instanceUUIDs %+v err %+v ", instanceUUIDs, err)
		return
	}
	klog.V(4).Infof("eniController gcBiddingNodeEni GetBidEvents params %+v resp %+v ", instanceUUIDs, resp)

	if len(resp.BidEvents) == 0 {
		return
	}
	for _, event := range resp.BidEvents {
		nodeName := providerIDMap[event.InstanceID]
		node, err := c.nodeLister.Get(nodeName)
		if err != nil {
			continue
		}
		// 发送event消息
		c.sendTidalOrBiddingReleaseEvent(node)
		c.doSendBidNodeEvent(nodeName, c.networkOption.EvictBidNodePodWaitSecond)
	}
	return
}

func (c *Controller) gcTidalNodeEniWhenGcTimeUp() {
	// 先筛选出潮汐的节点组
	igMap, _, err := util.GetInstanceGroupCm(c.cmLister)
	if err != nil {
		klog.Errorf("eniController gcTidalNodeEniWhenGcTimeUp GetInstanceGroupCm err %+v ", err)
		return
	}

	for _, ig := range igMap {
		// 普通用户节点组通过设置matchType调度潮汐pod
		// 有可能普通用户节点组通过设置matchType=tidal 来调度潮汐pod，此处也是到时间后就删除node 上pod+eni
		if !strings.Contains(ig.MatchType, entity.NodeLabelBiddingNodeTypeTidal) {
			continue
		}
		c.sendTidalNodeGCEvent(ig)
	}
}

func (c *Controller) sendTidalNodeGCEvent(ig entity.InstanceGroupCm) {

	labels := map[string]string{
		entity.InstanceGroupKey: ig.InstanceGroupId,
	}

	klog.V(4).Infof("eniController sendTidalNodeGCEvent ig %s start ", ig.InstanceGroupId)

	nodes := c.listNodesByLabels(labels)
	if len(nodes) == 0 {
		klog.V(4).Infof("eniController sendTidalNodeGCEvent instanceGroupID %s node empty ", ig.InstanceGroupId)
		return
	}

	// 按用户锁定node分组
	// TODO 待适配bci 3.0
	accountMap := make(map[string][]*corev1.Node)
	for _, node := range nodes {
		nodeInfo := entity.NewNodeInfo(node)
		accountID := nodeInfo.NodeLockedAccount()
		if accountID == "" {
			continue
		}
		if _, ok := accountMap[accountID]; !ok {
			accountMap[accountID] = make([]*corev1.Node, 0)
		}
		accountMap[accountID] = append(accountMap[accountID], node)
	}

	for accountID, nodes := range accountMap {
		tidalParam, err := c.getTidalTimeFromCloudBridge(accountID)
		if err != nil {
			// 从云桥获取配置失败，则按节点组配置，节点组配置会配置一个比较大的时间段
			tidalParam.TidalStartSubmitTime = ig.Params.TidalNodeStartRunTime
			tidalParam.TidalEndSubmitTime = ig.Params.TidalNodeEndRunTime
			tidalParam.TidalStartGCTime = ig.Params.TidalNodeStartGCTime
		}
		tidalTime := util.BuildCurrentTidalTime(tidalParam, globalHolidays, globalTimeoffDay)

		if !util.InTidalRunTime(tidalTime) {
			// 不在潮汐时间段内
			for _, node := range nodes {
				c.doSendBidNodeEvent(node.Name, 0)
			}
			continue
		}
		// 在潮汐时间段内
		now := time.Now()
		klog.V(4).Infof("eniController tidal instanceGroup %s NodeStartRunTime %s NodeStartGCTime %s current time %+v:%+v ",
			ig.InstanceGroupId, ig.Params.TidalNodeStartRunTime, ig.Params.TidalNodeStartGCTime, now.Hour(), now.Minute())
		if util.ShouldReportTidalReleasedEvent(tidalTime) {
			// 到达发送event时间
			klog.V(4).Infof("eniController tidal instanceGroup %s accountID %s should send release event ",
				ig.InstanceGroupId, accountID)
			for _, node := range nodes {
				c.sendTidalOrBiddingReleaseEvent(node)
			}
		}
	}
	return
}

func (c *Controller) doSendBidNodeEvent(nodeName string, evictPodWaitSecond int) {
	// 判断是否有worker正在处理，如有先暂时忽略掉，防止把chan 打满
	processEniGcBidNodeLock.RLock()
	defer processEniGcBidNodeLock.RUnlock()

	if _, ok := processEniGcBidNodeMap[nodeName]; ok {
		return
	}

	select {
	case c.bidNodeEventChan <- BidNodeEvent{
		NodeName:           nodeName,
		EvictPodWaitSecond: evictPodWaitSecond,
	}:
	default:
		klog.Errorf("eniController sendTidalNodeGCEvent node %s send bidNodeEventChan full ", nodeName)
	}
}

// 定期统计竞价+潮汐node上运行的eni信息
func (c *Controller) UpdateBidRunningEniConfigMapWithRetry(changeBidNodeEniList []entity.BidNodeEniInfo, isDelete bool) {
	c.doUpdateBidEniConfigmapWithRetry(changeBidNodeEniList, isDelete, bidNodeRunningEniCmName)
}

func (c *Controller) UpdateBidLeakageEniConfigMapWithRetry(changeBidNodeEniList []entity.BidNodeEniInfo, isDelete bool) {
	c.doUpdateBidEniConfigmapWithRetry(changeBidNodeEniList, isDelete, bciNodeLeakageEniCmName)
}

func (c *Controller) doUpdateBidEniConfigmapWithRetry(
	changeBidNodeEniList []entity.BidNodeEniInfo, isDelete bool, cmName string) {
	if len(changeBidNodeEniList) == 0 {
		return
	}

	// 创建cm 函数
	createBidNodeEniCmFn := func(initEniList []entity.BidNodeEniInfo) error {
		bytes, _ := json.MarshalIndent(changeBidNodeEniList, "", "    ")
		initCm := &corev1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Namespace: bidNodeEniCmNamespace,
				Name:      cmName,
			},
			Data: map[string]string{
				bidEniDataKey: string(bytes),
			},
		}

		err := c.client.Create(context.Background(), initCm)
		klog.V(3).Infof("eniController createBidNodeEniCmFn cmName %s eni params %+v err %+v", cmName, string(bytes), err)
		return err
	}

	retryBackoff := wait.Backoff{
		Steps:    10,
		Duration: 10 * time.Millisecond,
		Factor:   5.0,
		Jitter:   0.1,
	}

	_ = retry.RetryOnConflict(retryBackoff, func() error {
		// 从informer中获取最新的
		cm, err := c.cmLister.ConfigMaps(bidNodeEniCmNamespace).Get(cmName)
		if errors.IsNotFound(err) {
			if !isDelete {
				// 初始化创建
				return createBidNodeEniCmFn(changeBidNodeEniList)
			}
			return nil
		}
		if err != nil {
			return err
		}

		updateEniList := make([]entity.BidNodeEniInfo, 0)
		existEniList := make([]entity.BidNodeEniInfo, 0)
		existEniStr := cm.Data[bidEniDataKey]
		if err = json.Unmarshal([]byte(existEniStr), &existEniList); err != nil {
			klog.Errorf("eniController doUpdateBidEniConfigmapWithRetry Unmarshal cmName %s  eni data %s err %+v ",
				cmName, existEniStr, err)
			return err
		}

		if isDelete {
			// 删除eni 配置
			changeEniIDMap := make(map[string]struct{})
			for _, bidNodeEni := range changeBidNodeEniList {
				changeEniIDMap[bidNodeEni.EniID] = struct{}{}
			}

			for _, existEni := range existEniList {
				if _, ok := changeEniIDMap[existEni.EniID]; ok {
					continue
				}
				updateEniList = append(updateEniList, existEni)
			}
		} else {
			// 添加eni配置
			existEniMap := make(map[string]entity.BidNodeEniInfo)
			for _, existEni := range existEniList {
				existEniMap[existEni.EniID] = existEni
			}
			updateEniList = append(updateEniList, existEniList...)

			// 添加eni 配置,去重
			for _, addEni := range changeBidNodeEniList {
				if _, ok := existEniMap[addEni.EniID]; ok {
					continue
				}
				updateEniList = append(updateEniList, addEni)
			}
		}

		if reflect.DeepEqual(updateEniList, existEniList) {
			// 配置没有变更
			return nil
		}
		// 序列化，更新
		bytes, _ := json.MarshalIndent(updateEniList, "", "    ")
		copyCm := cm.DeepCopy()
		copyCm.Data[bidEniDataKey] = string(bytes)

		err = c.client.Update(context.Background(), copyCm)
		klog.V(3).Infof("eniController doUpdateBidEniConfigmapWithRetry cmName %s eni data %s err %+v ", cmName, string(bytes), err)
		return err
	})
}

// 遍历running的bid eni列表，如泄漏，则往leakage 的cm 中添加
func (c *Controller) syncRunningBidNodeEniCm() {
	runningBidEniCm, err := c.cmLister.ConfigMaps(bidNodeEniCmNamespace).Get(bidNodeRunningEniCmName)
	if err != nil {
		return
	}

	runningEniList := make([]entity.BidNodeEniInfo, 0)
	eniData := runningBidEniCm.Data[bidEniDataKey]
	if err = json.Unmarshal([]byte(eniData), &runningEniList); err != nil {
		klog.Errorf("eniController syncRunningBidNodeEni Unmarshal running bidEni %s err %+v ", eniData, err)
		return
	}

	// 需要从running eni 列表中删除的列表
	needDeleteBidEniList := make([]entity.BidNodeEniInfo, 0)
	// 泄漏的eni列表
	leakageBidEniList := make([]entity.BidNodeEniInfo, 0)

	for _, runningEni := range runningEniList {
		eniDeleted := c.eniDeleted(runningEni.EniID, runningEni.AccountID)
		if c.nodeIsRunning(runningEni.ProviderID, runningEni.NodeName) && !eniDeleted {
			continue
		}
		needDeleteBidEniList = append(needDeleteBidEniList, runningEni)
		if !eniDeleted {
			klog.Warningf("eniController syncLeakageBidNodeEni find leakage eni %s on node %s ",
				runningEni.EniID, runningEni.NodeName+":"+runningEni.ProviderID)
			leakageBidEniList = append(leakageBidEniList, runningEni)
		}
	}

	// eni 已经非running了，此时从running的cm中删掉
	c.UpdateBidRunningEniConfigMapWithRetry(needDeleteBidEniList, true)
	// 泄漏的eni 报警
	c.UpdateBidLeakageEniConfigMapWithRetry(leakageBidEniList, false)
}

// 刷新泄漏的eni列表，判断是否还存在，如不存在则从列表中删除
func (c *Controller) syncLeakageBidNodeEniCm() {
	leakageBidEniCm, err := c.cmLister.ConfigMaps(bidNodeEniCmNamespace).Get(bciNodeLeakageEniCmName)
	if err != nil {
		return
	}

	leakageEniList := make([]entity.BidNodeEniInfo, 0)
	eniData := leakageBidEniCm.Data[bidEniDataKey]
	if err = json.Unmarshal([]byte(eniData), &leakageEniList); err != nil {
		klog.Errorf("eniController syncLeakageBidNodeEniCm Unmarshal leakage bidEni %s err %+v ", eniData, err)
		return
	}

	// 监控打点
	bidNodeLeakageEniCountGauge.WithLabelValues().Set(float64(len(leakageEniList)))
	if len(leakageEniList) > 0 {
		// eni 泄漏，hi 报警
		_ = c.hiClient.SendMarkDownMessage(context.Background(),
			"**潮汐node存在eni泄漏**\nregion: "+c.networkOption.Region+"\n泄漏个数: "+
				strconv.Itoa(len(leakageEniList))+"\n")
	}

	deleteSuccessEniList := make([]entity.BidNodeEniInfo, 0)
	for _, leakageEni := range leakageEniList {
		if c.eniDeleted(leakageEni.EniID, leakageEni.AccountID) {
			deleteSuccessEniList = append(deleteSuccessEniList, leakageEni)
		}
	}

	c.UpdateBidLeakageEniConfigMapWithRetry(deleteSuccessEniList, true)
}

func (c *Controller) nodeIsRunning(providerID string, nodeName string) bool {
	node, err := c.nodeLister.Get(nodeName)
	if err != nil {
		if errors.IsNotFound(err) {
			klog.V(4).Infof("eniController nodeIsRunning node name %s providerID %s is not found in informer ", nodeName, providerID)
			return false
		}
		klog.Errorf("eniController nodeIsRunning get node %s err %+v ", nodeName, err)
		return true
	}

	currentProviderID, err := getNodeProviderID(node)
	if err != nil {
		return true
	}
	// node 被重新创建，ip被复用
	if currentProviderID != providerID {
		klog.V(4).Info("eniController node %s old providerID %s current providerID %s , is deleted ",
			node.Name, currentProviderID, providerID)
		return false
	}
	return true
}

// 定时刷新节假日信息
func (c *Controller) refreshHolidayAndTimeOffDay() {
	holiday, timeoffDay, err := c.apiGoClient.BuildHolidayAndTimeOffDayMap()
	if err != nil {
		klog.Errorf("eniController refreshHolidayAndTimeOffDay err %+v ", err)
		_ = c.hiClient.SendMarkDownMessage(context.Background(),
			"**潮汐刷新节假日信息失败**\nregion:"+c.networkOption.Region+"\n错误信息:"+err.Error())
		return
	}
	globalHolidays = holiday
	globalTimeoffDay = timeoffDay

	holidayBytes, _ := json.Marshal(holiday)
	timeoffDayBytes, _ := json.Marshal(timeoffDay)
	klog.V(4).Infof("eniController refreshHolidayAndTimeOffDay holiday %s timeoffDay %s ",
		string(holidayBytes), string(timeoffDayBytes))
}

func (c *Controller) sendTidalOrBiddingReleaseEvent(node *corev1.Node) {
	nodeInfo := entity.NewNodeInfo(node)
	nodeType := nodeInfo.GetBiddingNodeType()

	// list node 上所有bci pod
	allPods, err := c.podLister.List(labels.Everything())
	if err != nil {
		klog.Errorf("eniController list all pod err %+v ", err)
		return
	}

	for _, pod := range allPods {
		if pod.Spec.NodeName != node.Name {
			continue
		}
		nodeInfo.AddOrUpdatePod(pod)
	}

	bciPods := nodeInfo.GetBciPendingOrRunningPods()
	for _, pod := range bciPods {
		if nodeType == entity.NodeLabelBiddingNodeTypeBidding {
			c.eventRecord.Eventf(pod, "Warning", "BiddingPodToBeReleased", "Bidding BCI pod will be released in 5 minutes.")
		} else {
			c.eventRecord.Eventf(pod, "Warning", "TidalPodToBeReleased", "Tidal BCI pod will be released in 10 minutes.")
		}
	}
}

// ===================================以下为潮汐debug接口===================================

func (c *Controller) tidalDateDebugHandler(w http.ResponseWriter, r *http.Request) {

	// curl localhost:10000/tidal/debug?expectTime=2023-06-25%2018:01\&accountID=2e1be1eb99e946c3a543ec5a4eaa7d39
	// time 格式 2023-06-12 12:30
	var parseTime time.Time
	var err error = nil
	expectTime := r.URL.Query().Get("expectTime")
	if expectTime != "" {
		parseTime, err = time.ParseInLocation("2006-01-02 15:04", expectTime, util.LocalLocation)
		if err != nil {
			_, _ = w.Write([]byte("expectTime params parse err :" + err.Error()))
			return
		}
	} else {
		parseTime = time.Now().In(util.LocalLocation)
	}

	accountID := r.URL.Query().Get("accountID")
	if accountID == "" {
		accountID = c.networkOption.ResourceAccountID
	}

	tidalParam, err := c.getTidalTimeFromCloudBridge(accountID)
	if err != nil {
		_, _ = w.Write([]byte("get account clould bridge err " + err.Error()))
		return
	}
	tidalTime := util.TidalTime{
		CurrentYear:         parseTime.Year(),
		CurrentMonth:        int(parseTime.Month()),
		CurrentDay:          parseTime.Day(),
		CurrentHour:         parseTime.Hour(),
		CurrentMinute:       parseTime.Minute(),
		TidalNightStartTime: tidalParam.TidalStartSubmitTime,
		TidalNightEndTime:   tidalParam.TidalEndSubmitTime,
		TidalNightGCTime:    tidalParam.TidalStartGCTime,
		Holidays:            globalHolidays,
		TimeoffDay:          globalTimeoffDay,
	}
	bytes, _ := json.MarshalIndent(tidalTime, "", "    ")
	result := util.InTidalRunTime(tidalTime)
	_, _ = w.Write([]byte("tidalTime : " + string(bytes) + "\n"))
	_, _ = w.Write([]byte("result :" + strconv.FormatBool(result) + "\n"))

}

// ===================================潮汐debug接口结束===================================

func (c *Controller) statisticsTidalPendingPodCount() {
	allPods, err := c.podLister.List(labels.Everything())
	if err != nil {
		klog.Errorf("eniController list all pod err %+v ", err)
		return
	}

	tidalPendingPodCount := make(map[string]int)
	for _, pod := range allPods {
		matchType := pod.Annotations[entity.BciMatchTypeKey]
		if matchType != entity.MatchTypeTidal {
			continue
		}
		if pod.Status.Phase != corev1.PodPending {
			continue
		}
		if pod.Spec.NodeName != "" {
			continue
		}
		account := pod.Annotations[entity.BciAccountKey]
		tidalPendingPodCount[account]++
	}

	if len(tidalPendingPodCount) == 0 {
		return
	}

	bytes, _ := json.Marshal(tidalPendingPodCount)
	msg := "***潮汐Pending Pod数量***\nregion: " + c.networkOption.Region + "\ncount:" + string(bytes) + "\n"
	_ = c.hiClient.SendMarkDownMessage(context.Background(), msg)
}
