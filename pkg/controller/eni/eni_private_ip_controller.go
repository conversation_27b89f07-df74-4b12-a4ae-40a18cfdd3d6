package eni

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	node_eni "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/klog/v2"
)

type eniPrivateIPCountType string

const (
	eniPrivateIPCountMax    eniPrivateIPCountType = "max"
	eniPrivateIPCountBuffer eniPrivateIPCountType = "buffer"

	// node agent 分配ip成功后，把分配成功的ip patch到pod Annotation上
	bciPodIPAnnotationKey string = "bci_internal_PodIP"

	bciPodIPv6AnnotationKey string = "bci_internal_PodIPv6"

	// node 是否开启eni 辅助ip 动态gc 功能
	nodeEnableEniPrivateIPGC = "kubernetes.io/enable-eni-privateip-gc"

	// eni 辅助ip buffer 数量,node label上设置
	nodeEniPrivateIPBuffer = "kubernetes.io/eni-privateip-buffer"
)

// eniPrivateIPWorker负责辅助ip 动态创建和删除
type eniPrivateIPWorker struct {
	NodeName  string
	EniID     string
	AccountID string
	// userID:EniID
	WaitReleaseIPMapKey    string
	StopChan               chan struct{}
	Closed                 bool
	Controller             *Controller
	PrivateIPBufferCount   int // IPv4 buffer数
	PrivateIPMaxCount      int // IPv4 max数
	PrivateIPv6BufferCount int // IPv6 buffer数
	PrivateIPv6MaxCount    int // IPv6 max数
	// 当eni 删除时，worker对应的定时任务不允许执行
	EniTaskDisable bool
	// eni 是否attach 成功
	EniAttachSuccess bool
	// 当前空闲buffer数, IPv4
	CurrentBufferIPCount int
	// 当前空闲buffer数, IPv6
	CurrentBufferIPv6Count int
	// 突发pod 申请ip请求
	EmergencyAllocateIPChan chan struct{}
	// bcinode status 更新通知chan
	BciNodeStatusUpdateChan chan struct{}
	// 上次紧急申请IP时间
	LastDealEmergencyAllocateIPTime time.Time
}

func (w *eniPrivateIPWorker) sendEmergencyAllocateIPRequest() {
	if w.Closed {
		return
	}

	select {
	case w.EmergencyAllocateIPChan <- struct{}{}:
		// klog.Warningf("eniPrivateIPWorker eniID %s on node %s dealEmergencyAllocateIPRequest failed , retry ",
		// 	w.EniID, w.NodeName)
	default:
		klog.Warningf("eniPrivateIPWorker eniID %s on node %s EmergencyAllocateIPChan chan full , ignore ",
			w.EniID, w.NodeName)
	}
}

func (w *eniPrivateIPWorker) start() {
	bufferIPTicker := time.NewTicker(30 * time.Second)

	// 正在detach + delete 过程中，worker 定时任务应该停止
	shouldProcessWorkerTask := func() bool {
		if w.EniTaskDisable {
			klog.Warningf("eniPrivateIPWorker eniID %s on node %s EniTaskDisable skip task ",
				w.EniID, w.NodeName)
			return false
		}
		if w.Controller.networkOption.EnableAllNodePrivateIPGC {
			return true
		}

		eniInfo, node, err := w.getNodeEniInfo()
		if err != nil {
			return false
		}
		// eni 还没有attach 成功，忽略定时任务
		if !eniInfo.AttachSuccess {
			klog.Warningf("eniPrivateIPWorker eniID %s on node %s not attach sucess, skip task ", w.EniID, w.NodeName)
			return false
		}

		// node 未开启eni 辅助ip 动态gc
		if _, ok := node.Labels[nodeEnableEniPrivateIPGC]; !ok {
			return false
		}
		return true
	}

	removeEmergencyRequest := func() {
		// 删除podWaitPrivatePrefix Annotation key
		node, err := w.Controller.nodeLister.Get(w.NodeName)
		if err == nil {
			_ = util.PatchNodeAnnotation(w.Controller.client, node, []entity.PatchValue{
				entity.BuildDeletePatchValue(podWaitPrivatePrefix + w.EniID),
			})
		}
	}

	go func() {
		for {
			select {
			case _ = <-w.EmergencyAllocateIPChan:
				if !shouldProcessWorkerTask() {
					break
				}

				// 紧急分配ip请求
				err := w.dealEmergencyAllocateIPRequest()
				if err != nil {
					klog.Errorf("eniController eniPrivateIPWorker eniID %s on node %s dealEmergencyAllocateIPRequest err %+v ",
						w.EniID, w.NodeName, err)
					break
				}
				removeEmergencyRequest()
			case _ = <-bufferIPTicker.C:
				if !shouldProcessWorkerTask() {
					break
				}
				// 辅助ip 动态创建&gc
				if len(w.EmergencyAllocateIPChan) > 0 {
					break
				}
				// 判断有 podWaitPrivatePrefix Annotation,则不执行
				eniInfo, node, err := w.getNodeEniInfo()
				if err != nil {
					break
				}
				// 该eni 需要紧急申请辅助ip
				if _, ok := node.Annotations[podWaitPrivatePrefix+w.EniID]; ok {
					w.sendEmergencyAllocateIPRequest()
					break
				}
				err = w.syncEniBuffer()
				if err != nil {
					klog.Errorf("eniController eniPrivateIPWorker eniID %s on node %s syncEniBuffer err %+v ",
						w.EniID, w.NodeName, err)
					break
				}
				// 更新eni buffer 辅助ip数量
				eniInfo.CurrentBuffer = w.CurrentBufferIPCount
				bytes, _ := json.Marshal(eniInfo)
				patch := entity.PatchValue{
					Key:   buildEniAnnotationKey(w.EniID),
					Type:  entity.PatchTypeUpdate,
					Value: string(bytes),
				}
				_ = util.PatchNodeAnnotation(w.Controller.client, node, []entity.PatchValue{patch})
			case _ = <-w.BciNodeStatusUpdateChan:
				if !shouldProcessWorkerTask() {
					break
				}
				// 处理crd status更新
				err := w.syncBciNodeStatus()
				if err != nil {
					klog.Errorf("eniController eniPrivateIPWorker eniID %s on node %s syncBciNodeStatus err %+v ",
						w.EniID, w.NodeName, err)
				}
			case _, ok := <-w.StopChan:
				if !ok {
					klog.V(3).Infof("eniController eniPrivateIPWorker %s exit ", w.EniID)
					removeEmergencyRequest()
					return
				}
			}
		}
	}()
}

func (w *eniPrivateIPWorker) syncBciNodeStatus() error {

	bcinode, err := w.Controller.bciNodeLister.Get(w.NodeName)
	if err != nil {
		klog.Errorf("eniPrivateIPWorker computeENIFreePrivateIPList get node %s bciNode cr err %+v ", w.NodeName, err)
		return err
	}

	if len(bcinode.Status.WaitReleaseIP) == 0 || len(bcinode.Status.WaitReleaseIP[w.WaitReleaseIPMapKey]) == 0 {
		return nil
	}

	klog.V(4).Infof("eniPrivateIPWorker eniID %s syncBciNodeStatus on node %s start ", w.EniID, w.NodeName)
	ipReleaseStatus := bcinode.Status.WaitReleaseIP[w.WaitReleaseIPMapKey]

	// crd status 中标示可被删除
	readyReleaseIPList := make([]string, 0)
	// crd status中标示不可被删除
	doNotReleaseIPList := make([]string, 0)
	// 最终可被删除的ip列表
	finalReadyReleaseList := make([]string, 0)

	// 只处理可被释放和不可被释放的ip
	for ip, status := range ipReleaseStatus {
		if status == node_eni.IPAMReadyForRelease {
			readyReleaseIPList = append(readyReleaseIPList, ip)
		} else if status == node_eni.IPAMDoNotRelease {
			doNotReleaseIPList = append(doNotReleaseIPList, ip)
		}
	}

	klog.V(3).Infof("eniPrivateIPWorker eniID %s on node %s syncBciNodeStatus readyReleaseIPList %+v doNotReleaseIPList %+v",
		w.EniID, w.NodeName, readyReleaseIPList, doNotReleaseIPList)

	// 调用iaas接口删除ip
	if len(readyReleaseIPList) > 0 {
		// 释放ip前，check下要释放的ip是否在free列表中
		_, freeIPs, _ := w.computeENIAllocatedAndFreePrivateIPList()

		// 计算IPv6空闲列表
		var freeIPv6s []string
		_, freeIPv6s, _ = w.computeENIAllocatedAndFreePrivateIPv6List()

		freeIPMap := make(map[string]struct{})
		for _, freeIP := range freeIPs {
			freeIPMap[freeIP] = struct{}{}
		}
		for _, freeIPv6 := range freeIPv6s {
			freeIPMap[freeIPv6] = struct{}{}
		}

		for _, readyReleaseIP := range readyReleaseIPList {
			if _, ok := freeIPMap[readyReleaseIP]; ok {
				finalReadyReleaseList = append(finalReadyReleaseList, readyReleaseIP)
			} else {
				klog.Warningf("eniPrivateIPWorker eniID %s on node %s readyReleaseIP %+v in crd status but not in freeIP list ,ignore delete",
					w.EniID, w.NodeName, readyReleaseIP)
				doNotReleaseIPList = append(doNotReleaseIPList, readyReleaseIP)
			}
		}

		err = w.doBatchDeletePrivateIP(finalReadyReleaseList)
		if err != nil {
			klog.Errorf("eniPrivateIPWorker eniID %s on node %s doBatchDeletePrivateIP finalReadyReleaseList %+v err %+v ",
				w.EniID, w.NodeName, finalReadyReleaseList, err)
			return err
		}
		// time.Sleep(1 * time.Second)
	}

	return w.Controller.updateBciNodeStatusWithRetry(w.NodeName, func(bciNode *node_eni.BciNode) bool {
		var changed bool = false
		eniIPStatus := bciNode.Status.WaitReleaseIP[w.WaitReleaseIPMapKey]
		// 不可被删除的ip列表从crd status中删除
		if len(doNotReleaseIPList) > 0 && len(eniIPStatus) > 0 {
			changed = true
			for _, doNotReleaseIP := range doNotReleaseIPList {
				delete(eniIPStatus, doNotReleaseIP)
			}
		}

		// 已经释放的从crd status中删除
		if len(finalReadyReleaseList) > 0 && len(eniIPStatus) > 0 {
			changed = true
			for _, finalReadyReleaseIP := range finalReadyReleaseList {
				delete(eniIPStatus, finalReadyReleaseIP)
			}
		}

		enis, ok := bciNode.Spec.EniMultiIP.Pool[w.AccountID]
		if !ok {
			return changed
		}

		// spec 中不存在了，在status 中还存在，添加diff 校验（同时考虑 IPv4 与 IPv6）
		if eniAllocatedIP, ok := enis[w.EniID]; ok {
			for ip := range eniIPStatus {
				if _, ok := eniAllocatedIP.PrivateIPAddresses[ip]; ok {
					continue
				}
				if _, ok := eniAllocatedIP.PrivateIPv6Addresses[ip]; ok {
					continue
				}
				delete(eniIPStatus, ip)
			}
		}
		return changed
	})
}

func (w *eniPrivateIPWorker) dealEmergencyAllocateIPRequest() error {
	klog.V(4).Infof("eniPrivateIPWorker eniID %s dealEmergencyAllocateIPRequest on node %s start ", w.EniID, w.NodeName)

	// 查找紧急申请ip的pod 列表
	eniInfo, node, err := w.getNodeEniInfo()
	if err != nil {
		return err
	}

	// 单次处理周期为3s,防止频繁申请IP
	duration, _ := time.ParseDuration("-2s")
	if !time.Now().Add(duration).After(w.LastDealEmergencyAllocateIPTime) {
		klog.V(4).Infof("eniPrivateIPWorker eniID %s dealEmergencyAllocateIPRequest on node %s in last Time period ",
			w.EniID, w.NodeName)
		return nil
	}
	// 更新处理Emergency事件时间戳
	w.LastDealEmergencyAllocateIPTime = time.Now()

	waitInfo := &entity.WaitEniPodInfo{
		SubnetID:            eniInfo.SubnetID,
		SecurityGroupIDList: eniInfo.SecurityGroupIDs,
	}

	// 计算正在等待申请ip的pod列表
	waitENIPodList, err := computeWaitSameEniPodListOnNode(waitInfo, node, w.Controller.podLister)
	klog.V(3).Infof("eniPrivateIPWorker computeWaitSameEniPodCountOnNode eniID %s on node %s waitENIPodCount %+v err %+v ",
		w.EniID, w.NodeName, len(waitENIPodList), err)
	if err != nil {
		return err
	}
	if len(waitENIPodList) == 0 {
		return nil
	}

	var waitENIPodWithIPv6List int
	for _, waitPod := range waitENIPodList {
		if waitPod.Annotations[entity.BciEnableIPv6AnnotationKey] == "true" {
			waitENIPodWithIPv6List++
		}
	}

	// 删除bcinode crd status 中eni 对应的WaitReleaseIP信息
	err = w.removeENIWaitReleaseIP(node.Name)
	if err != nil {
		return err
	}

	allocatedIPList, freeIps, err := w.computeENIAllocatedAndFreePrivateIPList()

	if err != nil {
		klog.Errorf("eniPrivateIPWorker computeENIAllocatedAndFreePrivateIPList eniID %s on node %s err %+v ",
			eniInfo.EniID, w.NodeName, err)
		return err
	}

	var allocatedIPv6List, freeIPv6s []string
	if waitENIPodWithIPv6List > 0 {
		allocatedIPv6List, freeIPv6s, err = w.computeENIAllocatedAndFreePrivateIPv6List()
		if err != nil {
			klog.Errorf("eniPrivateIPWorker computeENIAllocatedAndFreePrivateIPv6List eniID %s on node %s err %+v ",
				eniInfo.EniID, w.NodeName, err)
			return err
		}
	}
	// freeip 数够分配, 这里只以IPv4数量做判断
	if len(freeIps) > len(waitENIPodList) {
		klog.V(3).Infof("eniPrivateIPWorker dealEmergencyAllocateIPRequest eniID %s on node %s freeIP count %+v waitENIPodCount %+v freeIPv6 "+
			"count %+v waitENIPv6odCount %+v ,ignore ",
			eniInfo.EniID, w.NodeName, len(freeIps), len(waitENIPodList), len(freeIPv6s), waitENIPodWithIPv6List)
		return nil
	}

	// 计算要调用iaas接口申请的辅助ip数
	if len(allocatedIPList) > w.PrivateIPMaxCount || len(allocatedIPv6List) > w.PrivateIPv6MaxCount {
		err = fmt.Errorf("eniPrivateIPWorker eniID %s on node %s allocatedIPList len %+v > eniMaxPrivateIPCount %+v "+
			"allocatedIPv6List len %+v > eniMaxPrivateIPv6Count %+v",
			eniInfo.EniID, w.NodeName, len(allocatedIPList), w.PrivateIPMaxCount, len(allocatedIPv6List), w.PrivateIPv6MaxCount)
		klog.Error(err)
		return err
	}

	maxAssignableIPCount := w.PrivateIPMaxCount - len(allocatedIPList)
	maxAssignableIPv6Count := w.PrivateIPv6MaxCount - len(allocatedIPv6List)
	if maxAssignableIPCount == 0 {
		err := fmt.Errorf("eniPrivateIPWorker dealEmergencyAllocateIPRequest eniID %s on node %s maxPrivateIPCount is equal allocatedIP count",
			w.EniID, w.NodeName)
		klog.Error(err)
		return err
	}
	if maxAssignableIPv6Count == 0 && waitENIPodWithIPv6List > 0 {
		err := fmt.Errorf("eniPrivateIPWorker dealEmergencyAllocateIPRequest eniID %s on node %s maxPrivateIPv6Count is equal allocatedIPv6 count",
			w.EniID, w.NodeName)
		klog.Error(err)
		return err
	}

	needAllocateIPCount := len(waitENIPodList) - len(freeIps)

	if needAllocateIPCount > maxAssignableIPCount {
		needAllocateIPCount = maxAssignableIPCount
	}
	needAllocateIPv6Count := waitENIPodWithIPv6List - len(freeIPv6s)
	if needAllocateIPv6Count > maxAssignableIPv6Count {
		needAllocateIPv6Count = maxAssignableIPv6Count
	}

	// 确保分配数量不为负数
	if needAllocateIPCount < 0 {
		needAllocateIPCount = 0
	}
	if needAllocateIPv6Count < 0 {
		needAllocateIPv6Count = 0
	}

	klog.V(3).Infof("eniPrivateIPWorker dealEmergencyAllocateIPRequest eniID %s on node %s "+
		"allocation summary: need IPv4=%d, need IPv6=%d, max IPv4=%d, max IPv6=%d",
		eniInfo.EniID, w.NodeName, needAllocateIPCount, needAllocateIPv6Count,
		maxAssignableIPCount, maxAssignableIPv6Count)
	err = w.doBatchAddEniPrivateIPWithIPv6(needAllocateIPCount, needAllocateIPv6Count)
	if err != nil {
		klog.Errorf("eniPrivateIPWorker dealEmergencyAllocateIPRequest doBatchAddEniPrivateIPWithIPv6 eniID %s on node %s err %+v ",
			eniInfo.EniID, w.NodeName, err)

		if isENIRatelimited(err) || isQualifyFailed(err) {
			// eni 被限流 || 因为token过期实名认证失败，后续重试
			// iam token 过期问题,目前在sdk内提前5s过期,在gztest仍存在token 过期导致失败问题
			klog.Errorf("eniPrivateIPWorker dealEmergencyAllocateIPRequest doBatchAddEniPrivateIPWithIPv6 eniID %s on node %s Ratelimited or QualifyFailed retry ",
				eniInfo.EniID, w.NodeName)
			return err
		}
		// 分配IP失败,patch Annotation 告知bci 控制面
		for _, pod := range waitENIPodList {
			dynamicIPFailCounter.WithLabelValues().Inc()
			if pod.Annotations[entity.BciEnableIPv6AnnotationKey] == "true" {
				dynamicIPv6FailCounter.WithLabelValues().Inc()
			}
			klog.Errorf("eniPrivateIPWorker dealEmergencyAllocateIPRequest eni %s on node %s pod %s allocate ip err %+v ",
				eniInfo.EniID, w.NodeName, pod.Name, err)
			w.Controller.patchPodAnnotationWhenAllocateIPFailed(pod.Namespace, pod.Name, err)
		}
	}
	return err
}

// 定期扩容或gc eni 辅助ip
func (w *eniPrivateIPWorker) syncEniBuffer() error {
	// IPv4
	allocatedIPList, freeIPs, err := w.computeENIAllocatedAndFreePrivateIPList()
	if err != nil {
		klog.Errorf("eniPrivateIPWorker syncEniBuffer eniID %s on node %s computeENIAllocatedAndFreePrivateIPList err %+v",
			w.EniID, w.NodeName, err)
		return err
	}
	// IPv6
	allocatedIPv6List, freeIPv6s, err := w.computeENIAllocatedAndFreePrivateIPv6List()
	if err != nil {
		klog.Errorf("eniPrivateIPWorker syncEniBuffer eniID %s on node %s computeENIAllocatedAndFreePrivateIPv6List err %+v",
			w.EniID, w.NodeName, err)
		return err
	}
	w.CurrentBufferIPCount = len(freeIPs)
	w.CurrentBufferIPv6Count = len(freeIPv6s)

	// 需要删除的IPv4辅助ip数
	needDeleteIPCount := -1
	// 需要删除的IPv6辅助ip数
	needDeleteIPv6Count := -1
	// 需要删除IPv4辅助ip
	if len(allocatedIPList) > w.PrivateIPMaxCount {
		// 已经申请的ip大于eni 最大可分配ip数，需要找到buffer的ip释放掉
		needDeleteIPCount = len(allocatedIPList) - w.PrivateIPMaxCount
		if needDeleteIPCount > len(freeIPs) {
			err = fmt.Errorf("eniPrivateIPWorker eniID %s on node %s allocatedIP %+v maxPrivateIPCount %+v freeIpCount %+v is a bug ",
				w.EniID, w.NodeName, len(allocatedIPList), w.PrivateIPMaxCount, len(freeIPs))
			klog.Error(err)
		}
	} else if len(freeIPs) > w.PrivateIPBufferCount {
		needDeleteIPCount = len(freeIPs) - w.PrivateIPBufferCount
	}

	// 需要删除IPv6辅助ip
	if len(allocatedIPv6List) > w.PrivateIPv6MaxCount {
		// 已经申请的ip大于eni 最大可分配ip数，需要找到buffer的ip释放掉
		needDeleteIPv6Count = len(allocatedIPv6List) - w.PrivateIPv6MaxCount
		if needDeleteIPv6Count > len(freeIPv6s) {
			err = fmt.Errorf("eniPrivateIPWorker eniID %s on node %s allocatedIPv6 %+v maxPrivateIPv6Count %+v freeIpv6Count %+v is a bug ",
				w.EniID, w.NodeName, len(allocatedIPv6List), w.PrivateIPv6MaxCount, len(freeIPv6s))
			klog.Error(err)
		}
	} else if len(freeIPv6s) > w.PrivateIPv6BufferCount {
		needDeleteIPv6Count = len(freeIPv6s) - w.PrivateIPv6BufferCount
	}

	// 需要删除多余空闲ip
	tryDeleteIPList := make([]string, 0)
	// 添加需要删除的IPv4辅助ips
	if needDeleteIPCount > 0 {
		for _, freeIP := range freeIPs {
			tryDeleteIPList = append(tryDeleteIPList, freeIP)
			needDeleteIPCount--
			if needDeleteIPCount == 0 {
				break
			}
		}
	}
	// 添加需要删除的IPv6辅助ips
	if needDeleteIPv6Count > 0 {
		for _, freeIPv6 := range freeIPv6s {
			tryDeleteIPList = append(tryDeleteIPList, freeIPv6)
			needDeleteIPv6Count--
			if needDeleteIPv6Count == 0 {
				break
			}
		}
	}
	if len(tryDeleteIPList) > 0 {
		return w.callNaToReleaseEniPrivateIP(tryDeleteIPList)
	}

	// buffer 数和freeIP 数相同
	if len(freeIPs) == w.PrivateIPBufferCount && len(freeIPv6s) == w.PrivateIPv6BufferCount {
		klog.V(5).Infof("eniPrivateIPWorker eniID %s on node %s buffer count %+v IPv6Buffer count %+v ignore syncEniBuffer",
			w.EniID, w.NodeName, len(freeIPs), len(freeIPv6s))
		return nil
	}

	// 需要新增IPv4/IPv6辅助ip
	var needAddIPCount, needAddIPv6Count int
	if len(freeIPs) < w.PrivateIPBufferCount {
		needAddIPCount = w.PrivateIPBufferCount - len(freeIPs)
		if needAddIPCount+len(allocatedIPList) > w.PrivateIPMaxCount {
			needAddIPCount = w.PrivateIPMaxCount - len(allocatedIPList)
		}
	}
	// 需要新增IPv6辅助ip
	if len(freeIPv6s) < w.PrivateIPv6BufferCount {
		needAddIPv6Count = w.PrivateIPv6BufferCount - len(freeIPv6s)
		if needAddIPv6Count+len(allocatedIPv6List) > w.PrivateIPv6MaxCount {
			needAddIPv6Count = w.PrivateIPv6MaxCount - len(allocatedIPv6List)
		}
	}
	if needAddIPCount > 0 || needAddIPv6Count > 0 {
		return w.doBatchAddEniPrivateIPWithIPv6(needAddIPCount, needAddIPv6Count)
	}
	return nil
}

// 计算辅助ip列表和空闲ip列表
func (w *eniPrivateIPWorker) computeENIAllocatedAndFreePrivateIPList() ([]string, []string, error) {
	freeIps := make([]string, 0) // IPv4
	eniInfo, node, err := w.getNodeEniInfo()
	if err != nil {
		return []string{}, freeIps, err
	}

	// 计算已经使用的辅助ip列表
	usedPrivateIPList, err := computePodAllocatedPrivateIPList(eniInfo, node, w.Controller.podLister)
	klog.V(3).Infof("eniPrivateIPWorker computePodAllocatedPrivateIPList eniID %s on node %s usedPrivateIPList %+v err %+v ",
		w.EniID, w.NodeName, usedPrivateIPList, err)

	if err != nil {
		return []string{}, freeIps, err
	}

	// 计算空闲的辅助ip数
	allocatedIPList, err := w.computeENIAllocatedPrivateIPList(eniInfo)
	klog.V(3).Infof("eniPrivateIPWorker computeENIAllocatedPrivateIPList eniID %s on node %s allocatedIPList %+v err %+v ",
		w.EniID, w.NodeName, allocatedIPList, err)

	if err != nil {
		return allocatedIPList, freeIps, err
	}

	freeIps = util.SliceSubtract(allocatedIPList, usedPrivateIPList)

	klog.V(3).Infof("eniPrivateIPWorker eniID %s on node %s freeIps %+v err %+v ", w.EniID, w.NodeName, freeIps, err)
	return allocatedIPList, freeIps, nil
}

func (w *eniPrivateIPWorker) computeENIAllocatedAndFreePrivateIPv6List() ([]string, []string, error) {
	freeIPv6s := make([]string, 0)
	eniInfo, node, err := w.getNodeEniInfo()
	if err != nil {
		return []string{}, freeIPv6s, err
	}
	if !eniInfo.EnableIPv6 {
		return []string{}, freeIPv6s, nil
	}

	// 计算已经使用的辅助ip列表
	usedPrivateIPv6List, err := computePodAllocatedPrivateIPv6List(eniInfo, node, w.Controller.podLister)
	klog.V(3).Infof("eniPrivateIPWorker computePodAllocatedPrivateIPv6List eniID %s on node %s usedPrivateIPv6List %+v err %+v ",
		w.EniID, w.NodeName, usedPrivateIPv6List, err)

	if err != nil {
		return []string{}, freeIPv6s, err
	}

	// 计算空闲的辅助ip数
	allocatedIPv6List, err := w.computeENIAllocatedPrivateIPv6List(eniInfo)
	klog.V(3).Infof("eniPrivateIPWorker computeENIAllocatedPrivateIPv6List eniID %s on node %s allocatedIPv6List %+v err %+v ",
		w.EniID, w.NodeName, allocatedIPv6List, err)

	if err != nil {
		return allocatedIPv6List, freeIPv6s, err
	}

	freeIPv6s = util.SliceSubtract(allocatedIPv6List, usedPrivateIPv6List)

	klog.V(3).Infof("eniPrivateIPWorker eniID %s on node %s freeIPv6s %+v err %+v ", w.EniID, w.NodeName, freeIPv6s, err)
	return allocatedIPv6List, freeIPv6s, nil
}

// 更新crd status，询问node agent是否可释放ip
func (w *eniPrivateIPWorker) callNaToReleaseEniPrivateIP(releaseIPList []string) error {
	klog.V(3).Infof("eniPrivateIPWorker eniID %s on node %s callNaToReleaseEniPrivateIP %+v ", w.EniID, w.NodeName, releaseIPList)

	err := w.Controller.updateBciNodeStatusWithRetry(w.NodeName, func(bciNode *node_eni.BciNode) bool {
		var changed bool = false
		if bciNode.Status.WaitReleaseIP == nil {
			changed = true
			bciNode.Status.WaitReleaseIP = make(map[string]map[string]node_eni.IPReleaseStatus)
		}
		_, ok := bciNode.Status.WaitReleaseIP[w.WaitReleaseIPMapKey]
		if !ok {
			changed = true
			bciNode.Status.WaitReleaseIP[w.WaitReleaseIPMapKey] = make(map[string]node_eni.IPReleaseStatus)
		}

		for _, ip := range releaseIPList {
			if _, ok := bciNode.Status.WaitReleaseIP[w.WaitReleaseIPMapKey][ip]; ok {
				continue
			}
			changed = true
			bciNode.Status.WaitReleaseIP[w.WaitReleaseIPMapKey][ip] = node_eni.IPAMMarkForRelease
		}
		return changed
	})
	if err != nil {
		klog.Errorf("eniPrivateIPWorker eniID %s on node %s callNaToReleaseEniPrivateIP %+v err %+v ",
			w.EniID, w.NodeName, releaseIPList, err)
	}

	return err
}

// doBatchDeletePrivateIP 批量删除辅助IP（支持IPv4和IPv6混合删除）
// 基于百度云VPC接口规范，不需要isIpv6参数，系统自动识别IPv4和IPv6地址
func (w *eniPrivateIPWorker) doBatchDeletePrivateIP(deleteIPList []string) error {
	klog.V(4).Infof("eniPrivateIPWorker eniID %s on node %s BatchDeletePrivateIP %+v start ",
		w.EniID, w.NodeName, deleteIPList)
	if len(deleteIPList) == 0 {
		return nil
	}
	eniInfo, node, err := w.getNodeEniInfo()
	if err != nil {
		return err
	}

	// iaas 接口批量接口删除,ip batch size 最大为10
	// 支持IPv4和IPv6混合删除，系统会自动识别IP类型
	splitByBatchSize := splitSliceByBatchsize(deleteIPList, w.Controller.networkOption.DynamicChangePrivateIPBatchsize)

	for _, ipList := range splitByBatchSize {
		// 设置限流规则
		w.Controller.changeEniPrivateIPRatelimit(eniInfo.AccountID, eniPrivateIPBatchDeleteMethod, eniInfo.EniID, node, len(ipList))

		deleteArgs := &eni.BatchPrivateIPArgs{
			EniID:              eniInfo.EniID,
			PrivateIPAddresses: ipList, // 混合IPv4和IPv6地址，系统自动识别
		}

		// 调用接口err,有可能超时,此处需要去查询下eni 状态,更新crd
		err = w.Controller.eniClient.BatchDeletePrivateIP(context.Background(), deleteArgs, w.Controller.buildSignOptionFn(eniInfo.AccountID))

		if isENIRatelimited(err) {
			for i := 0; i < w.Controller.networkOption.EniChangeRatelimitLoopRetryCount*2; i++ {
				// 被限流，重试
				klog.Errorf("eniPrivateIPWorker eni %s on node %s BatchDeletePrivateIP count %+v ratelimited try again",
					eniInfo.EniID, w.NodeName, len(ipList))
				// sleep 1s - 3s 之间
				time.Sleep(time.Duration(rand.Intn(2000)+1000) * time.Millisecond)
				w.Controller.changeEniPrivateIPRatelimit(eniInfo.AccountID, eniPrivateIPBatchDeleteMethod, eniInfo.EniID, node, len(ipList))
				err = w.Controller.eniClient.BatchDeletePrivateIP(context.Background(), deleteArgs, w.Controller.buildSignOptionFn(eniInfo.AccountID))
				if err == nil {
					break
				}
			}
		}

		if err != nil {
			klog.Errorf("eniPrivateIPWorker eniID %s on node %s BatchDeletePrivateIP %+v err %+v ",
				w.EniID, w.NodeName, ipList, err)
			dynamicIPFailCounter.WithLabelValues().Inc()
			return fmt.Errorf("failed to delete private IPs: %w", err)
		}

		klog.V(3).Infof("eniPrivateIPWorker eni %s on node %s BatchDeletePrivateIP success, deleted IPs: %+v",
			eniInfo.EniID, w.NodeName, ipList)
		err = w.updateBciNodeCRDSpecWhenPrivateIPChanged(eniInfo, node)
		if err != nil {
			return err
		}
	}

	return nil
}

func (w *eniPrivateIPWorker) doBatchAddEniPrivateIP(needAllocateIPCount int) error {
	eniInfo, node, err := w.getNodeEniInfo()
	if err != nil {
		return err
	}

	addPrivateIPAddress := make([]string, 0)

	for i := 0; i < needAllocateIPCount; i++ {
		addPrivateIPAddress = append(addPrivateIPAddress, "")
	}

	splitByBatchsize := splitSliceByBatchsize(addPrivateIPAddress, w.Controller.networkOption.DynamicChangePrivateIPBatchsize)
	for _, ipList := range splitByBatchsize {
		addArgs := &eni.BatchPrivateIPArgs{
			EniID:              eniInfo.EniID,
			PrivateIPAddresses: ipList,
			// PrivateIPAddressCount: needAllocateIPCount,
		}

		// 设置限流规则
		w.Controller.changeEniPrivateIPRatelimit(eniInfo.AccountID, eniPrivateIPBatchAddMethod, eniInfo.EniID, node, needAllocateIPCount)

		result, err := w.Controller.eniClient.BatchAddPrivateIP(context.Background(), addArgs, w.Controller.buildSignOptionFn(eniInfo.AccountID))

		klog.V(3).Infof("eniPrivateIPWorker eni %s on node %s BatchAddPrivateIP count %+v result %+v err %+v ",
			eniInfo.EniID, w.NodeName, needAllocateIPCount, result, err)

		// 调用超时 或 iaas 接口返回错误、或 子网没有足够ip了，等待后续重试
		if isENIRatelimited(err) {
			for i := 0; i < w.Controller.networkOption.EniChangeRatelimitLoopRetryCount*2; i++ {
				// 被限流，重试
				klog.Errorf("eniPrivateIPWorker eni %s on node %s BatchAddPrivateIP count %+v ratelimited try again",
					eniInfo.EniID, w.NodeName, needAllocateIPCount)
				// sleep 1s - 3s 之间
				time.Sleep(time.Duration(rand.Intn(2000)+1000) * time.Millisecond)
				// 限流
				w.Controller.changeEniPrivateIPRatelimit(eniInfo.AccountID, eniPrivateIPBatchAddMethod, eniInfo.EniID, node, needAllocateIPCount)
				result, err = w.Controller.eniClient.BatchAddPrivateIP(context.Background(), addArgs, w.Controller.buildSignOptionFn(eniInfo.AccountID))
				if err == nil {
					break
				}
			}
		}
		if err != nil {
			return err
		}

		err = w.updateBciNodeCRDSpecWhenPrivateIPChanged(eniInfo, node)
		if err != nil {
			return err
		}
	}
	return nil
}

// doBatchAddEniPrivateIPWithIPv6 批量添加IPv4和IPv6辅助IP（支持双栈）
func (w *eniPrivateIPWorker) doBatchAddEniPrivateIPWithIPv6(needAllocateIPv4Count, needAllocateIPv6Count int) error {
	eniInfo, node, err := w.getNodeEniInfo()
	if err != nil {
		return err
	}

	// 批量添加IPv4辅助IP（使用isIpv6=false）
	if needAllocateIPv4Count > 0 {
		addIPv4Address := make([]string, 0)
		for i := 0; i < needAllocateIPv4Count; i++ {
			addIPv4Address = append(addIPv4Address, "")
		}

		splitByBatchsize := splitSliceByBatchsize(addIPv4Address, w.Controller.networkOption.DynamicChangePrivateIPBatchsize)
		for _, ipList := range splitByBatchsize {
			// 使用isIpv6=false参数调用BatchAddPrivateIP
			addArgs := &eni.BatchPrivateIPArgs{
				EniID:              eniInfo.EniID,
				PrivateIPAddresses: ipList,
				IsIpv6:             false, // 明确指定为IPv4
			}

			w.Controller.changeEniPrivateIPRatelimit(eniInfo.AccountID, eniPrivateIPBatchAddMethod, eniInfo.EniID, node, needAllocateIPv4Count)

			result, err := w.Controller.eniClient.BatchAddPrivateIP(context.Background(), addArgs, w.Controller.buildSignOptionFn(eniInfo.AccountID))

			klog.V(3).Infof("eniPrivateIPWorker eni %s on node %s BatchAddPrivateIP IPv4 count %+v result %+v err %+v",
				eniInfo.EniID, w.NodeName, needAllocateIPv4Count, result, err)

			if isENIRatelimited(err) {
				for i := 0; i < w.Controller.networkOption.EniChangeRatelimitLoopRetryCount*2; i++ {
					klog.Errorf("eniPrivateIPWorker eni %s on node %s BatchAddPrivateIP IPv4 count %+v ratelimited try again",
						eniInfo.EniID, w.NodeName, needAllocateIPv4Count)
					time.Sleep(time.Duration(rand.Intn(2000)+1000) * time.Millisecond)
					w.Controller.changeEniPrivateIPRatelimit(eniInfo.AccountID, eniPrivateIPBatchAddMethod, eniInfo.EniID, node, needAllocateIPv4Count)
					result, err = w.Controller.eniClient.BatchAddPrivateIP(context.Background(), addArgs, w.Controller.buildSignOptionFn(eniInfo.AccountID))
					if err == nil {
						break
					}
				}
			}

			if err != nil {
				return fmt.Errorf("failed to add IPv4 private IPs: %w", err)
			}
		}
	}

	// 批量添加IPv6辅助IP（使用isIpv6=true）
	if needAllocateIPv6Count > 0 {
		addIPv6Address := make([]string, 0)
		for i := 0; i < needAllocateIPv6Count; i++ {
			addIPv6Address = append(addIPv6Address, "")
		}

		splitByBatchsize := splitSliceByBatchsize(addIPv6Address, w.Controller.networkOption.DynamicChangePrivateIPBatchsize)
		for _, ipList := range splitByBatchsize {
			// 使用isIpv6=true参数调用BatchAddPrivateIP
			addArgs := &eni.BatchPrivateIPArgs{
				EniID:              eniInfo.EniID,
				PrivateIPAddresses: ipList,
				IsIpv6:             true, // 明确指定为IPv6
			}

			w.Controller.changeEniPrivateIPRatelimit(eniInfo.AccountID, eniPrivateIPBatchAddMethod, eniInfo.EniID, node, needAllocateIPv6Count)

			result, err := w.Controller.eniClient.BatchAddPrivateIP(context.Background(), addArgs, w.Controller.buildSignOptionFn(eniInfo.AccountID))

			klog.V(3).Infof("eniPrivateIPWorker eni %s on node %s BatchAddPrivateIP IPv6 count %+v result %+v err %+v",
				eniInfo.EniID, w.NodeName, needAllocateIPv6Count, result, err)

			if isENIRatelimited(err) {
				for i := 0; i < w.Controller.networkOption.EniChangeRatelimitLoopRetryCount*2; i++ {
					klog.Errorf("eniPrivateIPWorker eni %s on node %s BatchAddPrivateIP IPv6 count %+v ratelimited try again",
						eniInfo.EniID, w.NodeName, needAllocateIPv6Count)
					time.Sleep(time.Duration(rand.Intn(2000)+1000) * time.Millisecond)
					w.Controller.changeEniPrivateIPRatelimit(eniInfo.AccountID, eniPrivateIPBatchAddMethod, eniInfo.EniID, node, needAllocateIPv6Count)
					result, err = w.Controller.eniClient.BatchAddPrivateIP(context.Background(), addArgs, w.Controller.buildSignOptionFn(eniInfo.AccountID))
					if err == nil {
						break
					}
				}
			}

			if err != nil {
				return fmt.Errorf("failed to add IPv6 private IPs: %w", err)
			}
		}
	}

	return w.updateBciNodeCRDSpecWhenPrivateIPChanged(eniInfo, node)
}

func (w *eniPrivateIPWorker) updateBciNodeCRDSpecWhenPrivateIPChanged(eniInfo *entity.EniInfo, node *corev1.Node) error {
	// 加锁
	lock := w.Controller.getNodeLock(node.Name)
	lock.Lock()
	defer lock.Unlock()

	var statResp *eni.StatENIResponse
	var err error
	// 调用成功，更新crd
	for i := 0; i < 3; i++ {
		statResp, err = w.Controller.statEniWithRatelimit(eniInfo.EniID, eniInfo.AccountID)
		if err != nil {
			klog.Errorf("eniPrivateIPWorker update bcinode crd query eni %s on node %s status err %+v ", eniInfo.EniID, w.NodeName, err)
			time.Sleep(1 * time.Second)
			continue
		}

		bytes, _ := json.Marshal(statResp.PrivateIPSet)
		klog.V(3).Infof("eniPrivateIPWorker eniID %s on node %s try update bcinode crd spec new privateIP %+v ",
			eniInfo.EniID, node.Name, string(bytes))

		queryTask := &eniAttachTask{
			AccountID: eniInfo.AccountID,
			EniID:     eniInfo.EniID,
			NodeName:  w.NodeName,
		}
		// 实现定时任务定期查询iaas 接口，更新crd spec
		err = w.Controller.createOrUpdateNodeEniCr(statResp, queryTask, node)
		if err == nil {
			return nil
		}
		time.Sleep(1 * time.Second)
	}
	return err
}

// computeENIAllocatedPrivateIPList 计算eni已申请的辅助ip列表
func (w *eniPrivateIPWorker) computeENIAllocatedPrivateIPList(eniInfo *entity.EniInfo) ([]string, error) {

	eniAllocatedPrivateIP := make([]string, 0)
	resp, err := w.Controller.statEniWithRatelimit(eniInfo.EniID, eniInfo.AccountID)
	// 优先调用iaas 接口拿到所有已分配的ip
	if err == nil {
		for _, ip := range resp.PrivateIPSet {
			if ip.Primary {
				continue
			}
			eniAllocatedPrivateIP = append(eniAllocatedPrivateIP, ip.PrivateIPAddress)
		}

		return eniAllocatedPrivateIP, nil
	}

	klog.Errorf("eniPrivateIPWorker computeENIFreePrivateIPList statEni %s on node %s err use bcinode crd err %+v",
		eniInfo.EniID, w.NodeName, err)

	// 从crd中获取
	bcinode, err := w.Controller.bciNodeLister.Get(w.NodeName)
	if err != nil {
		klog.Errorf("eniPrivateIPWorker computeENIFreePrivateIPList get node %s bciNode cr err %+v ", w.NodeName, err)
		return []string{}, err
	}
	allUserENIs, ok := bcinode.Spec.EniMultiIP.Pool[eniInfo.AccountID]
	if !ok {
		klog.Warningf("eniPrivateIPWorker computeENIFreePrivateIPList get user %s bcinode crd eni is empty ", eniInfo.AccountID)
		return []string{}, nil
	}
	eniIPs, ok := allUserENIs[eniInfo.EniID]
	if !ok {
		klog.Warningf("eniPrivateIPWorker computeENIFreePrivateIPList get user %s eni %s from bcinode crd is empty ",
			eniInfo.AccountID, eniInfo.EniID)
		return []string{}, nil
	}
	for ip := range eniIPs.PrivateIPAddresses {
		eniAllocatedPrivateIP = append(eniAllocatedPrivateIP, ip)
	}

	return eniAllocatedPrivateIP, nil
}

func (w *eniPrivateIPWorker) computeENIAllocatedPrivateIPv6List(eniInfo *entity.EniInfo) ([]string, error) {

	eniAllocatedPrivateIPv6 := make([]string, 0)
	resp, err := w.Controller.statEniWithRatelimit(eniInfo.EniID, eniInfo.AccountID)
	// 优先调用iaas 接口拿到所有已分配的ipv6
	if err == nil {
		for _, ip := range resp.IPv6PrivateIPSet {
			if ip.Primary {
				continue
			}
			eniAllocatedPrivateIPv6 = append(eniAllocatedPrivateIPv6, ip.PrivateIPAddress)
		}

		return eniAllocatedPrivateIPv6, nil
	}

	klog.Errorf("eniPrivateIPWorker computeENIFreePrivateIPList statEni %s on node %s err use bcinode crd err %+v",
		eniInfo.EniID, w.NodeName, err)

	// 从crd中获取
	bcinode, err := w.Controller.bciNodeLister.Get(w.NodeName)
	if err != nil {
		klog.Errorf("eniPrivateIPWorker computeENIFreePrivateIPList get node %s bciNode cr err %+v ", w.NodeName, err)
		return []string{}, err
	}
	allUserENIs, ok := bcinode.Spec.EniMultiIP.Pool[eniInfo.AccountID]
	if !ok {
		klog.Warningf("eniPrivateIPWorker computeENIFreePrivateIPList get user %s bcinode crd eni is empty ", eniInfo.AccountID)
		return []string{}, nil
	}
	eniIPs, ok := allUserENIs[eniInfo.EniID]
	if !ok {
		klog.Warningf("eniPrivateIPWorker computeENIFreePrivateIPList get user %s eni %s from bcinode crd is empty ",
			eniInfo.AccountID, eniInfo.EniID)
		return []string{}, nil
	}
	for ip := range eniIPs.PrivateIPv6Addresses {
		eniAllocatedPrivateIPv6 = append(eniAllocatedPrivateIPv6, ip)
	}

	return eniAllocatedPrivateIPv6, nil
}

func (w *eniPrivateIPWorker) removeENIWaitReleaseIP(bciNodeName string) error {
	bcinode, err := w.Controller.bciNodeLister.Get(bciNodeName)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		klog.Errorf("eniPrivateIPWorker computeENIFreePrivateIPList get node %s bciNode cr err %+v ", w.NodeName, err)
		return err
	}

	if len(bcinode.Status.WaitReleaseIP) == 0 {
		return nil
	}

	_, ok := bcinode.Status.WaitReleaseIP[w.WaitReleaseIPMapKey]
	if !ok {
		return nil
	}

	// 更新bcinode crd status，去掉WaitReleaseIP信息
	err = w.Controller.updateBciNodeStatusWithRetry(bcinode.Name, func(bciNode *node_eni.BciNode) bool {
		delete(bciNode.Status.WaitReleaseIP, w.WaitReleaseIPMapKey)
		return true
	})
	if err != nil {
		klog.Errorf("eniPrivateIPWorker updateBciNodeStatusWithRetry remove eni %s on node %s WaitReleaseIP err %+v ",
			w.WaitReleaseIPMapKey, w.NodeName, err)
		return err
	}

	return nil
}

func (w *eniPrivateIPWorker) getNodeEniInfo() (*entity.EniInfo, *corev1.Node, error) {
	result := &entity.EniInfo{}
	node, err := w.Controller.nodeLister.Get(w.NodeName)

	if err != nil {
		klog.Errorf("eniController eniPrivateIPWorker get node %s from informer err %+v ", w.NodeName, err)
		return result, node, err
	}

	jsonValue, ok := node.Annotations[buildEniAnnotationKey(w.EniID)]
	if !ok {
		err = fmt.Errorf("eniController eniPrivateIPWorker node %s not have eniID %s info ", w.NodeName, w.EniID)
		klog.Warning(err)
		return result, node, err
	}
	err = json.Unmarshal([]byte(jsonValue), result)
	if err != nil {
		klog.Errorf("eniController eniPrivateIPWorker on node %s  Unmarshal eni %s config %s err  ",
			w.NodeName, w.EniID, jsonValue)
		return result, node, err
	}
	return result, node, nil
}

// computeEniPrivateIPInitCount 初始化eni 辅助ip 数量
func (c *Controller) computeEniPrivateIPInitCount(podInfo *entity.WaitEniPodInfo, node *corev1.Node) int {
	// 主要用于灰度阶段，按节点组维度开启eni 辅助ip动态gc
	if _, ok := node.Labels[nodeEnableEniPrivateIPGC]; !ok {
		if !c.networkOption.EnableAllNodePrivateIPGC {
			return c.computeEniPrivateIPCountByNodeResource(node, eniPrivateIPCountMax)
		}
	}

	// 开启eni 辅助ip 动态gc 功能
	eniBufferPrivateIPCount := c.computeEniPrivateIPCountByNodeResource(node, eniPrivateIPCountBuffer)

	waitENIPodList, err := computeWaitSameEniPodListOnNode(podInfo, node, c.podLister)
	if err != nil {
		return eniBufferPrivateIPCount
	}

	klog.V(3).Infof("eniController computeWaitEniPodCountOnNode on node %s subnetID %s SecurityGroupIDStr %s wait pod count %+v ",
		node.Name, podInfo.SubnetID, podInfo.SecurityGroupIDStr, len(waitENIPodList))

	if len(waitENIPodList) > eniBufferPrivateIPCount {
		return len(waitENIPodList)
	}
	return eniBufferPrivateIPCount
}

// computeEniPrivateIPv6InitCount 初始化eni IPv6辅助ip数量
func (c *Controller) computeEniPrivateIPv6InitCount(podInfo *entity.WaitEniPodInfo, node *corev1.Node) int {
	// 主要用于灰度阶段，按节点组维度开启eni 辅助ip动态gc
	if _, ok := node.Labels[nodeEnableEniPrivateIPGC]; !ok {
		if !c.networkOption.EnableAllNodePrivateIPGC {
			return c.computeEniPrivateIPv6CountByNodeResource(node, eniPrivateIPCountMax)
		}
	}

	// 开启eni 辅助ip 动态gc 功能
	eniBufferPrivateIPv6Count := c.computeEniPrivateIPv6CountByNodeResource(node, eniPrivateIPCountBuffer)

	// 注意：这里暂时计算所有等待的pod数量，后续可以优化为只计算需要IPv6的pod数量
	waitENIPodList, err := computeWaitSameEniPodListOnNode(podInfo, node, c.podLister)
	if err != nil {
		return eniBufferPrivateIPv6Count
	}

	klog.V(3).Infof("eniController computeEniPrivateIPv6InitCount on node %s subnetID %s SecurityGroupIDStr %s wait pod count %+v ",
		node.Name, podInfo.SubnetID, podInfo.SecurityGroupIDStr, len(waitENIPodList))

	if len(waitENIPodList) > eniBufferPrivateIPv6Count {
		return len(waitENIPodList)
	}
	return eniBufferPrivateIPv6Count
}

func (c *Controller) computeEniPrivateIPv6CountByNodeResource(node *corev1.Node, countType eniPrivateIPCountType) int {

	// 计算IPv6辅助IP buffer数, 复用IPv4辅助IP buffer数
	if countType == eniPrivateIPCountBuffer {
		var bufferIntVal int = c.networkOption.DefaultENIBufferPrivateIPCount
		buffer, ok := node.Labels[nodeEniPrivateIPBuffer]
		if ok {
			if atoi, err := strconv.Atoi(buffer); err == nil {
				bufferIntVal = atoi
			}
			// 处理配置错误的场景
			if bufferIntVal <= 0 {
				bufferIntVal = c.networkOption.DefaultENIBufferPrivateIPCount
			}
			if bufferIntVal > c.networkOption.DefaultENIPrivateIPCount {
				bufferIntVal = c.networkOption.DefaultENIPrivateIPCount
			}
		}
		return bufferIntVal
	}

	// 计算IPv6辅助IP 最大数
	nodeResourceType, ok := node.Labels[nodeResourceTypeLabelKey]
	if !ok {
		klog.Warningf("eniController for node %s computeEniPrivateIPv6CountByNodeResource not found nodeResourceTypeLabel use default ", node.Name)
		return c.networkOption.DefaultENIPrivateIPCount
	}
	config, ok := c.eniPrivateIPConfig[nodeResourceType]
	if !ok {
		klog.Warningf("eniController for node %s computeEniPrivateIPv6CountByNodeResource nodeResourceType %s in eniPrivateIPConfig not found use default ",
			nodeResourceType, node.Name)
		return c.networkOption.DefaultENIPrivateIPCount
	}

	// 注意: 对于IPv6，这里需要单独配置EniMaxPrivateIPv6Count
	// 目前先使用相同的配置，后续可以扩展为独立配置
	return config.EniMaxPrivateIPv6Count
}

func (c *Controller) computeEniPrivateIPCountByNodeResource(node *corev1.Node, countType eniPrivateIPCountType) int {

	// 计算辅助IP buffer数
	if countType == eniPrivateIPCountBuffer {
		var bufferIntVal int = c.networkOption.DefaultENIBufferPrivateIPCount
		buffer, ok := node.Labels[nodeEniPrivateIPBuffer]
		if ok {
			if atoi, err := strconv.Atoi(buffer); err == nil {
				bufferIntVal = atoi
			}
			// 处理配置错误的场景
			if bufferIntVal <= 0 {
				bufferIntVal = c.networkOption.DefaultENIBufferPrivateIPCount
			}
			if bufferIntVal > c.networkOption.DefaultENIPrivateIPCount {
				bufferIntVal = c.networkOption.DefaultENIPrivateIPCount
			}
		}
		return bufferIntVal
	}

	// 计算辅助IP 最大数
	nodeResourceType, ok := node.Labels[nodeResourceTypeLabelKey]
	if !ok {
		klog.Warningf("eniController for node %s computeEniPrivateIPCountByNodeResource not found nodeResourceTypeLabel use default ", node.Name)
		return c.networkOption.DefaultENIPrivateIPCount
	}
	config, ok := c.eniPrivateIPConfig[nodeResourceType]
	if !ok {
		klog.Warningf("eniController for node %s computeEniPrivateIPCountByNodeResource nodeResourceType %s in eniPrivateIPConfig not found use default ",
			nodeResourceType, node.Name)
		return c.networkOption.DefaultENIPrivateIPCount
	}

	return config.EniMaxPrivateIPCount
}

// computeEniPrivateIPCurrentCount 运行过程中eni 上辅助ip数量，主要用于eni 辅助ip限流
// 在attach + detach阶段调用
func (c *Controller) computeEniPrivateIPCurrentCount(eniID string, accountID string, node *corev1.Node) int {

	if eniID == "" {
		return c.computeEniPrivateIPCountByNodeResource(node, eniPrivateIPCountBuffer)
	}

	// 调用iaas接口获取当前eni上有多少辅助ip
	resp, err := c.statEniWithRatelimit(eniID, accountID)
	if err != nil {
		// 此处读取失败，按buffer返回
		klog.Warningf("eniController computeEniPrivateIPCurrentCount stat eni %s err %+v ", eniID, err)
		return c.computeEniPrivateIPCountByNodeResource(node, eniPrivateIPCountBuffer)
	}
	// 去除eni 主ip数
	return len(resp.PrivateIPSet) - 1
}
