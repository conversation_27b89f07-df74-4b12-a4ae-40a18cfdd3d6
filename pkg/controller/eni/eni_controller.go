package eni

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"golang.org/x/time/rate"
	bci_node_crd "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/iam"
	bcinode_listers "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/generated/listers/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/sts"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	bci_client "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/client"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	bci_runtime "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	toolscache "k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/record"
	"k8s.io/client-go/util/workqueue"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/event"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"sigs.k8s.io/controller-runtime/pkg/source"
)

var (
	createENIFailCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "eni_create_fail_total",
			Help:      "The ENI Create Fail Total Count .",
		},
		[]string{},
	)

	dynamicIPFailCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "eni_dynamic_ip_fail_total",
			Help:      "The ENI Dynamic IP Fail Total Count .",
		},
		[]string{},
	)

	dynamicIPv6FailCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "eni_dynamic_ipv6_fail_total",
			Help:      "The ENI Dynamic IPv6 Fail Total Count .",
		},
		[]string{},
	)

	attachENIFailCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "eni_attach_fail_total",
			Help:      "The ENI Attach Fail Total Count .",
		},
		[]string{},
	)
)

func init() {
	metrics.Registry.MustRegister(createENIFailCounter)
	metrics.Registry.MustRegister(attachENIFailCounter)
	metrics.Registry.MustRegister(deleteENIFailCounter)
	metrics.Registry.MustRegister(detachENIFailCounter)
	metrics.Registry.MustRegister(bidNodeLeakageEniCountGauge)
	metrics.Registry.MustRegister(dynamicIPFailCounter)
}

const (
	// node agent patch node Annotation，表示pod 需要eni 网卡
	podWaitEniPrefix = "bci-wait-eni-"
	// node agent patch node Annotation，标示此eni 需要申请辅助ip
	podWaitPrivatePrefix = "bci-wait-private-ip-"
	// pod Annotation 子网、安全组信息
	podSubnetID         = "cross-vpc-eni.cce.io/subnetID"
	podSecurityGroupIDs = "cross-vpc-eni.cce.io/securityGroupIDs"
	podVpcCIDR          = "cross-vpc-eni.cce.io/vpcCidr"
	// controller patch eni 信息
	nodeBindEniInfo = "bci-node-eni-info-"
	// 是否申请辅助ip
	eniPrivateIP = "bci_internal_eniPrivateIp"
	// 迁移eni pod，告知控制面
	migrateWaitEniPod = "bci_internal_migrateEniPod"
	// 创建eni失败
	createEniFailed = "bci_internal_createEniFailed"
	// 标识node 类型，机器入池或节点组打label
	nodeResourceTypeLabelKey = "kubernetes.io/node-resource-type"
)

const (
	cceProviderIDPrefix           = "cce://"
	eniCreateMethod               = "Create"
	eniAttachMethod               = "Attach"
	eniDetachMethod               = "Detach"
	eniDeleteMethod               = "Delete"
	eniPrivateIPBatchAddMethod    = "PrivateIPBatchAdd"
	eniPrivateIPBatchDeleteMethod = "PrivateIPBatchDelete"
)

var _ reconcile.Reconciler = &Controller{}

type eniAttachTask struct {
	AccountID string
	EniID     string
	VpcCIDR   string
	NodeName  string
	// 是否是create 完成后创建，区分定时任务
	AfterCreate bool
	// 重试次数
	RetryCount int
}

// Controller eni controller实现
type Controller struct {
	// sync.RWMutex
	// 与k8s apiserver 交互的client
	client            client.Client
	stsClient         *sts.Client
	eniClient         *eni.Client
	bidEventClient    *bci_client.BidEventClient
	hiClient          util.Alerter
	cloudBridgeClient *bci_client.CloudBridgeClient
	apiGoClient       *bci_client.APIGoClient

	nodeLister    corev1_listers.NodeLister
	podLister     corev1_listers.PodLister
	cmLister      corev1_listers.ConfigMapLister
	bciNodeLister bcinode_listers.BciNodeLister
	networkOption *options.NetworkOptions

	eniAttachStatusQueryChan chan eniAttachTask
	bidNodeEventChan         chan BidNodeEvent

	buildSignOptionFn func(accountID string) *bce.SignOption
	// key : nodeName
	attachNodeWorker       map[string]*attachWorker
	attachNodeWorkerRWLock sync.RWMutex
	// create eni node lock
	nodeLock                  map[string]*sync.Mutex
	allNodeRWLock             sync.RWMutex
	changeEniLimiter          *rate.Limiter // region维度eni 修改接口限速qps
	changeEniPrivateIPLimiter *rate.Limiter // region维度eni 辅助ip限速qps
	readEniLimiter            *rate.Limiter // region维度eni 读接口限速qps
	// eni 辅助ip与机型对应信息，当辅助ip buffer修改时，此map被覆盖，不需要加锁
	eniPrivateIPConfig map[string]*entity.ENIPrivateIPConfig
	// key: eniID
	privateIPWorker   map[string]*eniPrivateIPWorker
	privateIPRWWorker sync.RWMutex
	// 当潮汐pod即将被释放时打印event告知用户
	eventRecord record.EventRecorder
	// node上正在创建和attach的eni flag
	nodeCreatingOrAttachEniFlag map[string]bool
}

// New 创建eni controller，此处无法实现单测
func New(option *options.ServerRunOptions, mgr manager.Manager) (interface{}, error) {
	cache := mgr.GetCache()
	podInformer, err := cache.GetInformer(context.TODO(), &corev1.Pod{})
	if err != nil {
		klog.Errorf("eniController get podInformer err %+v ", err)
		return nil, err
	}

	nodeInformer, err := cache.GetInformer(context.TODO(), &corev1.Node{})
	if err != nil {
		klog.Errorf("eniController get nodeInformer err %+v ", err)
		return nil, err
	}

	eniInformer, err := cache.GetInformer(context.Background(), &bci_node_crd.BciNode{})
	if err != nil {
		klog.Errorf("eniController get eniInformer err %+v ", err)
		return nil, err
	}

	cmInformer, err := cache.GetInformer(context.Background(), &corev1.ConfigMap{})
	if err != nil {
		klog.Errorf("eniController get cmInformer err %+v ", err)
		return nil, err
	}

	podLister := corev1_listers.NewPodLister(podInformer.(toolscache.SharedIndexInformer).GetIndexer())
	nodeLister := corev1_listers.NewNodeLister(nodeInformer.(toolscache.SharedIndexInformer).GetIndexer())
	eniLister := bcinode_listers.NewBciNodeLister(eniInformer.(toolscache.SharedIndexInformer).GetIndexer())
	cmLister := corev1_listers.NewConfigMapLister(cmInformer.(toolscache.SharedIndexInformer).GetIndexer())

	// 初始化sts client
	c := &Controller{
		client:                      mgr.GetClient(),
		nodeLister:                  nodeLister,
		podLister:                   podLister,
		cmLister:                    cmLister,
		bciNodeLister:               eniLister,
		networkOption:               option.NetworkOptions,
		attachNodeWorker:            map[string]*attachWorker{},
		nodeLock:                    make(map[string]*sync.Mutex),
		eniPrivateIPConfig:          readENIPrivateIPConfigFromFile(option.NetworkOptions.ENIPrivateIPConfigFilePath),
		bidNodeEventChan:            make(chan BidNodeEvent, 1000),
		privateIPWorker:             map[string]*eniPrivateIPWorker{},
		eventRecord:                 mgr.GetEventRecorderFor("eni-controller"),
		nodeCreatingOrAttachEniFlag: map[string]bool{},
	}
	// 初始化eni限流器
	c.initEniReatlimiter(option)
	// 初始化eni client
	c.initStsClient()
	// 初始化hi client，当存在eni 泄漏时发送hi消息
	hiClient := util.NewHiClient(option.InstanceGroupOptions.HiClientTokenURL)
	c.hiClient = hiClient
	// 初始化apiGo client，获取日历信息
	c.apiGoClient = bci_client.NewAPIGoCliect(option.APIGoAppKey, option.APIGoSecretKey)

	c.buildSignOptionFn = func(accountID string) *bce.SignOption {
		return c.stsClient.NewSignOptionWithResourceHeader(context.Background(), accountID,
			c.networkOption.EniHexKey, c.networkOption.ResourceAccountID, "bci")
	}
	controller, err := controller.New("eni-controller", mgr, controller.Options{
		Reconciler:              c,
		MaxConcurrentReconciles: option.NetworkOptions.EniWorkerCount,
		RateLimiter:             util.DefaultControllerRateLimiter(option),
	})

	if err != nil {
		klog.Errorf("New eniController err %+v ", err)
		return nil, err
	}

	err = controller.Watch(source.Kind(mgr.GetCache(), &corev1.Node{}), &handler.EnqueueRequestForObject{}, predicate.Funcs{
		CreateFunc: func(ce event.CreateEvent) bool {
			node := ce.Object.(*corev1.Node)
			podInfo := getAllWaitEniPodList(node, false)
			return len(podInfo) > 0
		},
		UpdateFunc: func(ue event.UpdateEvent) bool {
			node := ue.ObjectNew.(*corev1.Node)
			podInfo := getAllWaitEniPodList(node, false)
			// 发送na 动态申请ip 请求
			c.dealNodeUpdateForPrivateWorker(node)
			return len(podInfo) > 0
		},
		DeleteFunc: func(de event.DeleteEvent) bool {
			// gc 时删除eni
			node := de.Object.(*corev1.Node)
			klog.V(3).Infof("eniController node %s delete close attach worker ", node.Name)
			c.closeNodeAttachWorker(node.Name)
			return false
		},
	})
	if err != nil {
		klog.Errorf("New eniController watch err %+v ", err)
		return nil, err
	}

	// watch bciNode crd 变化，Reconcile 函数只处理node变化,因此事件都返false
	err = controller.Watch(source.Kind(mgr.GetCache(), &bci_node_crd.BciNode{}), &handler.EnqueueRequestForObject{}, predicate.Funcs{
		CreateFunc: func(ce event.CreateEvent) bool {
			bciNode := ce.Object.(*bci_node_crd.BciNode)
			// 刷新bciNode crd status
			c.sendBciNodeCRDUpdateEvent(bciNode.Name)
			return false
		},
		UpdateFunc: func(ue event.UpdateEvent) bool {
			bciNode := ue.ObjectNew.(*bci_node_crd.BciNode)
			//  单机初始化失败，pod做迁移。
			if len(bciNode.Status.EniMultiIP.NotReady) != 0 {
				klog.V(3).Infof("eniController node %s bciNodeCr notReady eniID %+v ",
					bciNode.Name, bciNode.Status.EniMultiIP.NotReady)
				node, err := c.nodeLister.Get(bciNode.Name)
				if err == nil {
					c.disableUseEniPodScheduleAndMigrateFailedPods(entity.PodEniInitFailed, node)
				}
			}
			// 刷新bciNode crd status
			c.sendBciNodeCRDUpdateEvent(bciNode.Name)
			return false
		},
		DeleteFunc: func(de event.DeleteEvent) bool {
			return false
		},
		GenericFunc: func(ge event.GenericEvent) bool {
			return false
		},
	})
	if err != nil {
		klog.Errorf("New eniController watch err %+v ", err)
		return nil, err
	}

	// 注册delete node hook
	bci_runtime.RegisterCustomizeDeleteNodeFilter(c)
	bci_runtime.RegisterCustomizeNodeGCHook(c)

	return c, nil
}

func (c *Controller) initStsClient() {
	stsClient := sts.NewClient(context.Background(), &bce.Config{
		Endpoint: sts.Endpoints[c.networkOption.Region],
		Checksum: true,
		Timeout:  60 * time.Second,
		Region:   c.networkOption.Region,
	}, &bce.Config{
		Endpoint: iam.Endpoints[c.networkOption.Region],
		Checksum: true,
		Timeout:  60 * time.Second,
		Region:   c.networkOption.Region,
	}, c.networkOption.IamRoleName, c.networkOption.IamUserName, c.networkOption.IamConsolePassword)

	bceConfig := &bce.Config{
		Checksum: true,
		Timeout:  60 * time.Second,
		Endpoint: eni.Endpoint[c.networkOption.Region],
	}

	// bcc.bj.qasandbox.baidu-int.com
	if c.networkOption.Region == "sandbox" {
		bceConfig.Endpoint = "bcc.bj.qasandbox.baidu-int.com"
	}
	eniClient := eni.NewClient(eni.NewConfig(bceConfig))

	c.stsClient = stsClient
	c.eniClient = eniClient

	bccClient := bcc.NewClient(bceConfig)
	bidEventClient := bci_client.BidEventClient{
		BccClient: bccClient,
	}
	c.bidEventClient = &bidEventClient

	cloudBridgeConfig := &bce.Config{
		Checksum: true,
		Timeout:  60 * time.Second,
		Endpoint: bci_client.CloudBridgeEndpoints[c.networkOption.Region],
	}
	c.cloudBridgeClient = bci_client.NewCloudBridgeClient(cloudBridgeConfig)
}

func (c *Controller) initEniReatlimiter(option *options.ServerRunOptions) {
	// 创建限流器，参考文档 https://www.cyhone.com/articles/usage-of-golang-rate/
	// 所有用户共享此限流
	// iaas eni接口有总体限速，所以用户修改eni限速100qps作业，eni 读接口200qps
	changeEniLimit := rate.Every(time.Duration(1000/option.NetworkOptions.ChangeEniQPS) * time.Millisecond)
	if option.NetworkOptions.ChangeEniQPS > 1000 {
		changeEniLimit = rate.Every(time.Duration(1000000/option.NetworkOptions.ChangeEniQPS) * time.Microsecond)
	}
	c.changeEniLimiter = rate.NewLimiter(changeEniLimit, 3)

	changeEniPrivateIPLimit := rate.Every(time.Duration(1000/option.NetworkOptions.ChangeEniPrivateIPQPS) * time.Millisecond)
	if option.NetworkOptions.ChangeEniPrivateIPQPS > 1000 {
		changeEniPrivateIPLimit = rate.Every(time.Duration(1000000/option.NetworkOptions.ChangeEniPrivateIPQPS) * time.Microsecond)
	}
	c.changeEniPrivateIPLimiter = rate.NewLimiter(changeEniPrivateIPLimit, option.NetworkOptions.ChangeEniPrivateIPQPS)

	readEniLimit := rate.Every(time.Duration(1000/option.NetworkOptions.ReadEniQPS) * time.Millisecond)
	if option.NetworkOptions.ReadEniQPS > 1000 {
		readEniLimit = rate.Every(time.Duration(1000000/option.NetworkOptions.ReadEniQPS) * time.Microsecond)
	}
	c.readEniLimiter = rate.NewLimiter(readEniLimit, 3)
}

func (c *Controller) Reconcile(ctx context.Context, request reconcile.Request) (res reconcile.Result, retErr error) {

	node, err := c.nodeLister.Get(request.Name)
	if err != nil {
		klog.Errorf("eniController nodeLister get node %s err %+v ", request.Name, err)
		return
	}

	pods := getAllWaitEniPodList(node, true)
	if len(pods) == 0 {
		return
	}

	// 校验node eni 是否create or attach 失败
	if checkNodeDisableEniSchedule(node) {
		klog.V(3).Infof("eniController node %s has disabled eni schedule, skip Reconcile ", node.Name)
		return
	}

	// 按照node 维度lock
	nodeLock := c.getNodeLock(node.Name)
	nodeLock.Lock()
	defer nodeLock.Unlock()

	// 判断node eni 是否正在创建或者attach
	eniCreatingOrAttaching := c.getNodeEniCreatingOrAttachEniFlag(node.Name)
	if eniCreatingOrAttaching {
		klog.V(3).Infof("eniController node %s is creating or attaching eni, skip reconcile ", node.Name)
		return
	}

	for _, pod := range pods {
		err := c.createEniForPod(pod, node)
		klog.V(3).Infof("eniController createEniForPod for pod %s/%s on node %s err %+v",
			pod.PodNamespace, pod.PodName, node.Name, err)
	}
	return
}

func (c *Controller) getNodeEniCreatingOrAttachEniFlag(nodeName string) bool {
	flag, ok := c.nodeCreatingOrAttachEniFlag[nodeName]
	if !ok {
		klog.V(3).Infof("eniController getNodeEniCreatingOrAttachEniFlag for node %s return false", nodeName)
		return false
	}
	klog.V(3).Infof("eniController getNodeEniCreatingOrAttachEniFlag for node %s return %v", nodeName, flag)
	return flag
}

func (c *Controller) setNodeEniCreatingOrAttachEniFlag(nodeName string) {
	klog.V(3).Infof("eniController setNodeEniCreatingOrAttachEniFlag for node %s", nodeName)
	c.nodeCreatingOrAttachEniFlag[nodeName] = true
}

func (c *Controller) deleteNodeEniCreatingOrAttachEniFlag(nodeName string) {
	klog.V(3).Infof("eniController deleteNodeEniCreatingOrAttachEniFlag for node %s", nodeName)
	if _, ok := c.nodeCreatingOrAttachEniFlag[nodeName]; !ok {
		return
	}
	delete(c.nodeCreatingOrAttachEniFlag, nodeName)
}

func (c *Controller) setNodeEniCreatingOrAttachEniFlagWithLock(nodeName string) {
	klog.V(3).Infof("eniController setNodeEniCreatingOrAttachEniFlagWithLock for node %s", nodeName)
	// 加锁
	lock := c.getNodeLock(nodeName)
	lock.Lock()
	defer lock.Unlock()
	c.nodeCreatingOrAttachEniFlag[nodeName] = true
}

func (c *Controller) deleteNodeEniCreatingOrAttachEniFlagWithLock(nodeName string) {
	klog.V(3).Infof("eniController deleteNodeEniCreatingOrAttachEniFlagWithLock for node %s", nodeName)
	// 加锁
	lock := c.getNodeLock(nodeName)
	lock.Lock()
	defer lock.Unlock()
	if _, ok := c.nodeCreatingOrAttachEniFlag[nodeName]; !ok {
		return
	}
	delete(c.nodeCreatingOrAttachEniFlag, nodeName)
}

func (c *Controller) disableNodeWhenBciNodeEniStatusNotReady(nodeName string) bool {
	bciNodeCr, err := c.bciNodeLister.Get(nodeName)
	if err != nil {
		klog.V(4).Infof("eniController node %s get bciNodeCr err %+v ", nodeName, err)
		return false
	}

	if len(bciNodeCr.Status.EniMultiIP.NotReady) == 0 {
		return false
	}

	node, err := c.nodeLister.Get(nodeName)
	if err != nil {
		klog.V(4).Infof("eniController nodeLister %s get node err %+v ", nodeName, err)
		return false
	}
	// node agent 单机失败，屏蔽此node
	klog.V(3).Infof("eniController node %s bciNodeCr notReady eniIds %+v ",
		nodeName, bciNodeCr.Status.EniMultiIP.NotReady)
	patch := entity.PatchValue{
		Key:   string(entity.PodEniInitFailed),
		Value: "1",
		Type:  entity.PatchTypeUpdate,
	}
	_ = util.PatchNodeAnnotation(c.client, node, []entity.PatchValue{patch})
	return true
}

func checkNodeDisableEniSchedule(node *corev1.Node) bool {
	if _, ok := node.Annotations[string(entity.EniAttachFailed)]; ok {
		return true
	}

	if _, ok := node.Annotations[string(entity.PodEniInitFailed)]; ok {
		return true
	}

	if _, ok := node.Annotations[string(entity.EniDeleteFailed)]; ok {
		return true
	}

	return false
}

// 禁止node 调度 & 触发迁移pod
func (c *Controller) disableUseEniPodScheduleAndMigrateFailedPods(disableType entity.DisableEniPodScheduleType, node *corev1.Node) {
	patchNode := entity.PatchValue{
		Key:   string(disableType),
		Value: "1",
		Type:  entity.PatchTypeUpdate,
	}

	err := util.PatchNodeAnnotation(c.client, node, []entity.PatchValue{patchNode})
	if err != nil {
		klog.Errorf("eniController node %s disable eni pod scheduler err %+v ", node.Name, err)
		return
	}

	waitEniPods := c.listWaitEniPendingPod(node)
	// 触发迁移pod
	// TODO 控制面暂不支持pod迁移，后续支持后在修改
	patchPod := entity.PatchValue{
		// Key:   migrateWaitEniPod,
		Key:   createEniFailed,
		Value: "1",
		Type:  entity.PatchTypeUpdate,
	}
	for _, waitEniPod := range waitEniPods {
		err := util.PatchPodAnnotation(c.client, waitEniPod, []entity.PatchValue{patchPod})
		klog.V(3).Infof("eniController node %s migrate wait eni pod <%s/%s> err %+v ", node.Name, waitEniPod.Namespace, waitEniPod.Name, err)
	}
	return
}

// list node 上等待eni 的pod
func (c *Controller) listWaitEniPendingPod(node *corev1.Node) []*corev1.Pod {
	waitEniPods := make([]*corev1.Pod, 0)

	allPods, err := c.podLister.List(labels.Everything())
	if err != nil {
		klog.Errorf("eniController list node %s wait eniPods err %+v", node.Name, err)
		return waitEniPods
	}

	for _, pod := range allPods {
		// 过滤非本机pod
		if pod.Spec.NodeName != node.Name {
			continue
		}
		if _, ok := pod.Annotations[eniPrivateIP]; !ok {
			continue
		}

		if pod.Status.Phase == corev1.PodPending {
			waitEniPods = append(waitEniPods, pod)
		}
	}
	return waitEniPods
}

// 为pod 创建eni
func (c *Controller) createEniForPod(podInfo *entity.WaitEniPodInfo, node *corev1.Node) error {

	accountID := podInfo.PodNamespace
	pod, err := c.podLister.Pods(accountID).Get(podInfo.PodName)

	if err != nil {
		if errors.IsNotFound(err) {
			klog.V(4).Infof("eniController get pod <%s/%s> not found, ignore ", accountID, podInfo.PodName)
			return nil
		}
		klog.Errorf("eniController podLister get pod <%s/%s> err %+v ", accountID, podInfo.PodName, err)
		return err
	}

	// 判断是否eni 创建失败
	if _, ok := pod.Annotations[createEniFailed]; ok {
		klog.V(4).Infof("eniController pod <%s/%s> Annotation has create eni failed msg, ignore ", pod.Namespace, pod.Name)
		return nil
	}
	// 获取pod vpc、subnet、安全组相关信息，目前按单个获取
	subnetID, ok := pod.Annotations[podSubnetID]
	if !ok || subnetID == "" {
		klog.Errorf("eniController pod <%s/%s> subnetID is empty , ignore ", accountID, podInfo.PodName)
		return nil
	}

	// 按 , 分隔开
	securityGroupIDs, ok := pod.Annotations[podSecurityGroupIDs]
	if !ok || securityGroupIDs == "" {
		klog.Errorf("eniController pod <%s/%s> securityGroupIDs is empty , ignore ", accountID, podInfo.PodName)
		return nil
	}
	securityGroupIDList := strings.Split(securityGroupIDs, ",")
	// 排序
	sort.Strings(securityGroupIDList)

	enableIPv6, ok := pod.Annotations[bciPodIPv6AnnotationKey]
	if ok && enableIPv6 == "true" {
		podInfo.EnableIPv6 = true
	}
	podInfo.SubnetID = subnetID
	podInfo.SecurityGroupIDList = securityGroupIDList
	podInfo.SecurityGroupIDStr = securityGroupIDs

	// 获取pod vpc cidr
	vpcCIDR, ok := pod.Annotations[podVpcCIDR]
	if !ok || vpcCIDR == "" {
		klog.Errorf("eniController pod <%s/%s> vpcCIDR is empty , ignore ", accountID, podInfo.PodName)
		return nil
	}

	klog.V(3).Infof("eniController accountID: %s subnetID: %s securityGroupIDs: %s vpcCIDR: %s try createEni ",
		accountID, subnetID, securityGroupIDs, vpcCIDR)

	eniCreated, eniAttachSuccess := c.hasAttachEniForNode(podInfo, node)

	if eniCreated {
		// 删除node bci-wait-eni-%s Annotation
		if eniAttachSuccess {
			deleteWaitPodKey := podWaitEniPrefix + podInfo.AccountID
			klog.V(3).Infof("eniController node %s delete waitEniPod %s ", node.Name, deleteWaitPodKey)
			deletePatch := entity.PatchValue{
				Key:  deleteWaitPodKey,
				Type: entity.PatchTypeDelete,
			}
			_ = util.PatchNodeAnnotation(c.client, node, []entity.PatchValue{deletePatch})
			c.deleteNodeEniCreatingOrAttachEniFlag(node.Name)
		}
		return nil
	}

	c.setNodeEniCreatingOrAttachEniFlag(node.Name)
	eniID, err := c.doCreateEni(node, podInfo, subnetID, securityGroupIDList)
	if err != nil {
		c.deleteNodeEniCreatingOrAttachEniFlag(node.Name)
		return err
	}

	klog.V(3).Infof("eniController for pod <%s/%s> on node %s create eni %s ",
		podInfo.PodNamespace, podInfo.PodName, node.Name, eniID)

	eniInfo := entity.EniInfo{
		AccountID:             accountID,
		SubnetID:              subnetID,
		SecurityGroupIDs:      securityGroupIDList,
		EniID:                 eniID,
		VpcCIDR:               vpcCIDR,
		AttachInterfaceCalled: false,
		AttachTime:            time.Now().Unix(),
		AttachSuccess:         false,
		EnableIPv6:            podInfo.EnableIPv6,
	}

	bytes, _ := json.Marshal(eniInfo)

	// patch node Annotation eni info
	patchValue := entity.PatchValue{
		// Key:   nodeBindEniInfo + eniID,
		Key:   buildEniAnnotationKey(eniID),
		Value: string(bytes),
		Type:  entity.PatchTypeUpdate,
	}

	err = util.PatchNodeAnnotation(c.client, node, []entity.PatchValue{patchValue})

	attachTask := &eniAttachTask{
		AccountID:   accountID,
		EniID:       eniID,
		VpcCIDR:     vpcCIDR,
		NodeName:    node.Name,
		AfterCreate: true,
		RetryCount:  0,
	}

	attachWorker := c.getNodeAttachWorker(node.Name)
	attachWorker.sendAttachTask(attachTask)
	return err
}

func (c *Controller) hasAttachEniForNode(podInfo *entity.WaitEniPodInfo,
	node *corev1.Node) (eniCreated bool, eniAttachSuccess bool) {
	// 从client中获取最新node 信息，informer 同步有时间延时(并发场景下)
	clientNode := &corev1.Node{}
	err := c.client.Get(context.Background(), client.ObjectKey{
		Name: node.GetName(),
	}, clientNode)

	// 从informer中获取最新的
	if err != nil {
		nodeName := node.Name
		node, err = c.nodeLister.Get(node.Name)
		if node == nil || err != nil {
			// node 找不到，认为已经创建和attach成功了
			klog.Errorf("eniController hasAttachEniForNode get node %s from nodeLister err %+v ", nodeName, err)
			return true, true
		}
	} else {
		node = clientNode.DeepCopy()
	}
	// 排序
	sort.Strings(podInfo.SecurityGroupIDList)
	for k, v := range node.Annotations {
		if !strings.HasPrefix(k, nodeBindEniInfo) {
			continue
		}
		eniInfo := &entity.EniInfo{}
		_ = json.Unmarshal([]byte(v), eniInfo)
		// 排序
		sort.Strings(eniInfo.SecurityGroupIDs)

		if eniInfo.AccountID == podInfo.PodNamespace && eniInfo.SubnetID == podInfo.SubnetID &&
			reflect.DeepEqual(eniInfo.SecurityGroupIDs, podInfo.SecurityGroupIDList) {
			// 已经创建过eni了
			klog.V(4).Infof("eniController has create eni %s ,pod <%s/%s> ", eniInfo.EniID, podInfo.PodNamespace, podInfo.PodName)
			return true, eniInfo.AttachSuccess
		}
	}
	return false, false
}

func (c *Controller) doCreateEni(node *corev1.Node, podInfo *entity.WaitEniPodInfo, subnetID string, securityGroupIDList []string) (eniID string, err error) {
	// 创建eni & attach 到node上
	clusterID := node.Labels["cluster-id"]
	providerID, _ := getNodeProviderID(node)

	eniName := entity.CreateNameForENI(clusterID, providerID, node.Name)

	createArgs := eni.CreateENIArgs{
		Name:        eniName,
		Description: "created by bci-resource-controller",
		SubnetID:    subnetID,
		PrivateIPSet: []*eni.PrivateIP{
			{
				// 主ip
				Primary: true,
			},
		},
	}

	//securityGroupType normal or enterprise
	isNormal := isNormalSercurytyGroup(securityGroupIDList)
	isEnterprise := isEnterpriseSercurytyGroup(securityGroupIDList)

	if isNormal {
		createArgs.SecurityGroupIDs = securityGroupIDList
	} else if isEnterprise {
		createArgs.EnterpriseSecurityGroupIds = securityGroupIDList
	} else {
		klog.Errorf("eniController for pod %s/%s create eni securitygroup not support %+v",
			podInfo.AccountID, podInfo.PodName, securityGroupIDList)
		return "", fmt.Errorf("eniController pod <%s/%s> security group %+v type not support", podInfo.PodNamespace, podInfo.PodName,
			securityGroupIDList)
	}

	// 检查是否需要IPv6支持
	enableIPv6Key := node.Labels[entity.BCIEnableIPv6Key]
	enableIPv6 := enableIPv6Key == "true"

	initPrivateIPCount := c.computeEniPrivateIPInitCount(podInfo, node)
	var initPrivateIPv6Count int
	if enableIPv6 {
		initPrivateIPv6Count = c.computeEniPrivateIPv6InitCount(podInfo, node)
	}
	klog.V(4).Infof("eniController for node %s create eni initPrivateIPCount %+v enableIPv6 %+v initPrivateIPv6Count %+v",
		node.Name, initPrivateIPCount, enableIPv6, initPrivateIPv6Count)

	// 创建IPv4辅助IP（现有逻辑保持不变）
	for i := 0; i < initPrivateIPCount; i++ {
		createArgs.PrivateIPSet = append(createArgs.PrivateIPSet,
			&eni.PrivateIP{
				Primary:          false,
				PrivateIPAddress: "", // 辅助ip 设置空会自动创建
			},
		)
	}

	// 如果启用IPv6，创建IPv6辅助IP
	if enableIPv6 {
		createArgs.IPv6PrivateIPSet = make([]*eni.PrivateIP, 0)
		for i := 0; i < initPrivateIPv6Count; i++ {
			createArgs.IPv6PrivateIPSet = append(createArgs.IPv6PrivateIPSet,
				&eni.PrivateIP{
					Primary:          false,
					PrivateIPAddress: "", // IPv6辅助ip 设置空会自动创建
				},
			)
		}
	}

out:
	for i := 0; i < 3; i++ {

		c.changeEniRatelimit(podInfo.PodNamespace, eniCreateMethod, "", node)
		eniID, err = c.eniClient.CreateENI(context.Background(), &createArgs, c.buildSignOptionFn(podInfo.PodNamespace))
		if err == nil {
			break
		}
		// 被限流
		if isENIRatelimited(err) {
			klog.Errorf("eniController for pod %s/%s create eni params %+v ratelimited try again",
				podInfo.AccountID, podInfo.PodName, createArgs)
			// 在重试2次
			for j := 0; j < c.networkOption.EniChangeRatelimitLoopRetryCount; j++ {
				// sleep 2s - 3s 之间
				time.Sleep(time.Duration(rand.Intn(1000)+2000) * time.Millisecond)
				c.changeEniRatelimit(podInfo.PodNamespace, eniCreateMethod, "", node)
				eniID, err = c.eniClient.CreateENI(context.Background(), &createArgs, c.buildSignOptionFn(podInfo.PodNamespace))
				if err == nil {
					break out
				}
			}
			continue
		}
		time.Sleep(500 * time.Millisecond)
	}

	if err != nil {
		// 展示信息优化，bci 控制面读取errMsg信息
		klog.Errorf("eniController for account %s subnetID %s create eni err %+v ",
			podInfo.PodNamespace, subnetID, err)
		createENIFailCounter.WithLabelValues().Inc()

		c.patchPodAnnotationWhenAllocateIPFailed(podInfo.PodNamespace, podInfo.PodName, err)
	}
	return
}

func (c *Controller) patchPodAnnotationWhenAllocateIPFailed(namespace, name string, err error) {
	errMsg := ""
	if isENISubnetHasNoMoreIPErr(err) {
		errMsg = "The vpc subnet has no more Ip to allocate."
	} else {
		errMsg = "Allocate vpc subnet ip err."
	}

	klog.Errorf("eniController for account %s pod %s allocate IP err %+v ", namespace, name, err)

	// patch eni create failed msg
	if pod, err := c.podLister.Pods(namespace).Get(name); err == nil {
		patch := entity.PatchValue{
			Key:   createEniFailed,
			Value: errMsg,
			Type:  entity.PatchTypeUpdate,
		}
		_ = util.PatchPodAnnotation(c.client, pod, []entity.PatchValue{patch})
	}
}

// TODO 后续此函数去掉
// 后续iaas版本会识别BCI，不再为BCI的辅助IP分配floatingip，这样就不用特别限速了，预计4月中旬发版.
func (c *Controller) changeEniPrivateIPRatelimit(account string, method string, eniID string, node *corev1.Node, ipCount int) {
	c.changeEniRatelimit(account, method, eniID, node)
	if ipCount > c.networkOption.ChangeEniPrivateIPQPS {
		ipCount = c.networkOption.ChangeEniPrivateIPQPS
	}
	start := time.Now()
	if err := c.changeEniPrivateIPLimiter.WaitN(context.Background(), ipCount); err != nil {
		klog.Errorf("eniController for account %s PrivateIP ratelimit method %s ip count %+v err %+v ",
			account, method, ipCount, err)
	}
	klog.V(3).Infof("eniController for account %s PrivateIP ratelimit method %s ip count %+v  start time %+v ,wait time %+v ",
		account, method, ipCount, start, time.Now().Sub(start))
}

func (c *Controller) changeEniRatelimit(account string, method string, eniID string, node *corev1.Node) {
	// TODO 暂时不按用户来区分限流，按总体限流
	start := time.Now()
	// 限流，此处wait 不会超时
	_ = c.changeEniLimiter.Wait(context.Background())
	klog.V(3).Infof("eniController for account %s method %s call iaas eni start time %+v ,wait time %+v ",
		account, method, start, time.Now().Sub(start))

	if method != eniAttachMethod && method != eniDeleteMethod {
		return
	}
	currentPrivateIPCount := c.computeEniPrivateIPCurrentCount(eniID, account, node)
	// eni 辅助ip维度限流，需要添加eni 主ip
	currentPrivateIPCount = currentPrivateIPCount + 1

	// 默认qps 30，eni按机型配置，辅助ip 大于ChangeEniPrivateIPQPS，按 ChangeEniPrivateIPQPS计算，防止WaitN函数报错
	if currentPrivateIPCount > c.networkOption.ChangeEniPrivateIPQPS {
		currentPrivateIPCount = c.networkOption.ChangeEniPrivateIPQPS
	}
	start = time.Now()
	if err := c.changeEniPrivateIPLimiter.WaitN(context.Background(), currentPrivateIPCount); err != nil {
		klog.Errorf("eniController for account %s PrivateIP ratelimit method %s ip count %+v err %+v ",
			account, method, currentPrivateIPCount, err)
	}
	klog.V(3).Infof("eniController for account %s PrivateIP ratelimit method %s ip count %+v  start time %+v ,wait time %+v ",
		account, method, currentPrivateIPCount, start, time.Now().Sub(start))
}

func (c *Controller) statEniWithRatelimit(eniID string, accountID string) (*eni.StatENIResponse, error) {
	var resp *eni.StatENIResponse
	var err error
	for i := 0; i < c.networkOption.EniChangeRatelimitLoopRetryCount; i++ {
		start := time.Now()
		_ = c.readEniLimiter.Wait(context.Background())
		klog.V(3).Infof("eniController for account %s call iaas read eni stat start time %+v ,wait time %+v ",
			accountID, start, time.Now().Sub(start))
		resp, err = c.eniClient.StatENI(context.Background(), eniID, c.buildSignOptionFn(accountID))
		if isENIRatelimited(err) {
			time.Sleep(1 * time.Second)
			continue
		}
		return resp, err
	}
	return resp, err
}

// Start 启动eni controller
func (c *Controller) Start(stopChan <-chan struct{}) error {
	c.refreshHolidayAndTimeOffDay()
	// 定时任务定期刷新eni状态
	go func() {
		ticker := time.NewTicker(c.networkOption.EniAttachTaskInterval)
		for {
			select {
			case <-ticker.C:
				c.syncNodeEniAttachStatus()
				c.gcLeakageBciNode()
				c.refreshHolidayAndTimeOffDay()
			case <-stopChan:
				return
			}
		}
	}()

	// 定时刷新竞价+潮汐node 定时任务
	go func() {
		ticker := time.NewTicker(c.networkOption.SyncBidNodeTaskInterval)
		for {
			select {
			case <-ticker.C:
				c.gcNodeEniByBccEvent()
				c.gcTidalNodeEniWhenGcTimeUp()
			case <-stopChan:
				return
			}
		}
	}()

	// 定时刷新竞价+潮汐 eni cm
	go func() {
		ticker := time.NewTicker(c.networkOption.SyncBidNodeTaskInterval)
		for {
			select {
			case <-ticker.C:
				c.syncRunningBidNodeEniCm()
				c.syncLeakageBidNodeEniCm()
			case <-stopChan:
				return
			}
		}
	}()

	go func() {
		ticker := time.NewTicker(c.networkOption.StatisticsPendingTidalTaskInterval)
		c.statisticsTidalPendingPodCount()
		for {
			select {
			case <-ticker.C:
				c.statisticsTidalPendingPodCount()
			case <-stopChan:
				return
			}
		}
	}()

	c.startAllBidNodeEventWorker()

	// 开启潮汐debug端口
	mux := http.NewServeMux()
	mux.HandleFunc("/tidal/debug", c.tidalDateDebugHandler)
	server := &http.Server{
		Addr:         "127.0.0.1:" + strconv.Itoa(c.networkOption.TidalDebugPort),
		WriteTimeout: time.Second * 3, //设置3秒的写超时
		Handler:      mux,
	}

	err := server.ListenAndServe()
	if err != nil {
		klog.Errorf("start tidal debug server err %+v", err)
	}
	return nil
}

func (c *Controller) syncNodeEniAttachStatus() {
	allNodes, err := c.nodeLister.List(labels.Everything())
	if err != nil {
		return
	}

	// 当前集群eni 列表
	allNodeEniMap := make(map[string]struct{})
	lock := &sync.Mutex{}

	syncOneNode := func(index int) {
		node := allNodes[index]
		if _, ok := node.Annotations[reBuildBciNodeCrTaskDisable]; ok {
			// node gc，忽略rebuild crd task
			return
		}
		if checkNodeDisableEniSchedule(node) {
			return
		}
		for k, v := range node.Annotations {
			if !strings.HasPrefix(k, nodeBindEniInfo) {
				continue
			}
			eniInfo := &entity.EniInfo{}
			if err := json.Unmarshal([]byte(v), eniInfo); err != nil {
				continue
			}

			lock.Lock()
			allNodeEniMap[eniInfo.EniID] = struct{}{}
			lock.Unlock()

			eniQuery := &eniAttachTask{
				AccountID: eniInfo.AccountID,
				EniID:     eniInfo.EniID,
				VpcCIDR:   eniInfo.VpcCIDR,
				NodeName:  node.Name,
			}

			time.AfterFunc(time.Duration(rand.Intn(3000)*int(time.Millisecond)), func() {
				// 查询接口打散
				attachWorker := c.getNodeAttachWorker(node.Name)
				attachWorker.sendAttachTask(eniQuery)
			})

		}
	}

	workqueue.ParallelizeUntil(context.Background(), 10, len(allNodes), syncOneNode)

	// 拷贝一份privateIPWorkerMap，防止map 并发读写panic
	c.privateIPRWWorker.RLock()
	privateIPWorkerCopyMap := make(map[string]struct{})
	for privateIPWorkerEni := range c.privateIPWorker {
		privateIPWorkerCopyMap[privateIPWorkerEni] = struct{}{}
	}
	c.privateIPRWWorker.RUnlock()

	// 删除已经退出的eni private ip worker
	for eni := range privateIPWorkerCopyMap {
		if _, ok := allNodeEniMap[eni]; ok {
			continue
		}
		c.closeEniPrivateIPWorker(eni)
	}
}

func (c *Controller) getNodeAttachWorker(nodeName string) *attachWorker {
	c.attachNodeWorkerRWLock.RLock()
	worker, ok := c.attachNodeWorker[nodeName]
	if ok {
		c.attachNodeWorkerRWLock.RUnlock()
		return worker
	}
	c.attachNodeWorkerRWLock.RUnlock()

	c.attachNodeWorkerRWLock.Lock()
	worker, ok = c.attachNodeWorker[nodeName]
	if ok {
		c.attachNodeWorkerRWLock.Unlock()
		return worker
	}
	worker = &attachWorker{
		NodeName:   nodeName,
		AttachChan: make(chan *eniAttachTask, 100),
		Controller: c,
		Closed:     false,
		StopChan:   make(chan struct{}),
	}
	c.attachNodeWorker[nodeName] = worker
	worker.start()
	c.attachNodeWorkerRWLock.Unlock()
	return worker
}

func (c *Controller) closeNodeAttachWorker(nodeName string) {
	c.attachNodeWorkerRWLock.Lock()
	defer c.attachNodeWorkerRWLock.Unlock()
	worker, ok := c.attachNodeWorker[nodeName]
	if !ok {
		return
	}
	worker.Closed = true
	close(worker.StopChan)
	delete(c.attachNodeWorker, nodeName)
	delete(c.nodeCreatingOrAttachEniFlag, nodeName)
}

func (c *Controller) getNodeLock(nodeName string) *sync.Mutex {
	c.allNodeRWLock.RLock()
	lock, ok := c.nodeLock[nodeName]
	if ok {
		c.allNodeRWLock.RUnlock()
		return lock
	}
	c.allNodeRWLock.RUnlock()

	c.allNodeRWLock.Lock()
	lock, ok = c.nodeLock[nodeName]
	if ok {
		c.allNodeRWLock.Unlock()
		return lock
	}
	lock = &sync.Mutex{}
	c.nodeLock[nodeName] = lock
	c.allNodeRWLock.Unlock()
	return lock
}

func (c *Controller) getEniPrivateIPWorker(eniID string, accountID string, node *corev1.Node) *eniPrivateIPWorker {
	c.privateIPRWWorker.RLock()
	worker, ok := c.privateIPWorker[eniID]
	if ok {
		c.privateIPRWWorker.RUnlock()
		return worker
	}
	c.privateIPRWWorker.RUnlock()

	c.privateIPRWWorker.Lock()
	worker, ok = c.privateIPWorker[eniID]
	if ok {
		c.privateIPRWWorker.Unlock()
		return worker
	}

	worker = &eniPrivateIPWorker{
		NodeName:                        node.Name,
		EniID:                           eniID,
		AccountID:                       accountID,
		WaitReleaseIPMapKey:             accountID + ":" + eniID,
		StopChan:                        make(chan struct{}),
		EmergencyAllocateIPChan:         make(chan struct{}, 100),
		BciNodeStatusUpdateChan:         make(chan struct{}, 100),
		Closed:                          false,
		Controller:                      c,
		PrivateIPBufferCount:            c.computeEniPrivateIPCountByNodeResource(node, eniPrivateIPCountBuffer),
		PrivateIPMaxCount:               c.computeEniPrivateIPCountByNodeResource(node, eniPrivateIPCountMax),
		LastDealEmergencyAllocateIPTime: time.Now(),
	}
	c.privateIPWorker[eniID] = worker
	worker.start()
	c.privateIPRWWorker.Unlock()
	return worker
}

func (c *Controller) closeEniPrivateIPWorker(eniID string) {
	klog.V(4).Infof("eniController close privateIPRWWorker eniID %s ", eniID)
	c.privateIPRWWorker.Lock()
	defer c.privateIPRWWorker.Unlock()
	worker, ok := c.privateIPWorker[eniID]
	if !ok {
		return
	}

	if !worker.Closed {
		close(worker.StopChan)
		worker.Closed = true
	}
	delete(c.privateIPWorker, eniID)
}

func (c *Controller) sendBciNodeCRDUpdateEvent(bciNodeName string) {
	node, err := c.nodeLister.Get(bciNodeName)
	if err != nil {
		return
	}

	eniList := getAllNodeEnis(node)

	for _, eni := range eniList {
		eniWorker := c.getEniPrivateIPWorker(eni.EniID, eni.AccountID, node)
		select {
		case eniWorker.BciNodeStatusUpdateChan <- struct{}{}:
		default:
			klog.Warningf("eniController eniID %s BciNodeStatusUpdateChan if full ", eni.EniID)
		}
	}
}

func (c *Controller) dealNodeUpdateForPrivateWorker(node *corev1.Node) {
	eniUserMap := make(map[string]string)
	// 获取所有等待辅助ip 动态申请的eni 列表
	for k, v := range node.Annotations {
		if !strings.Contains(k, podWaitPrivatePrefix) {
			continue
		}
		splits := strings.Split(k, podWaitPrivatePrefix)
		if len(splits) != 2 {
			continue
		}
		eniUserMap[splits[1]] = v
	}

	for eniID, userAccount := range eniUserMap {
		eniPrivateIPWorker := c.getEniPrivateIPWorker(eniID, userAccount, node)
		// 动态更新eni 辅助ip buffer
		eniPrivateIPWorker.PrivateIPBufferCount = c.computeEniPrivateIPCountByNodeResource(node, eniPrivateIPCountBuffer)
		eniPrivateIPWorker.sendEmergencyAllocateIPRequest()
	}

	// 动态更新eni 辅助ip buffer
	allEniList := getAllNodeEnis(node)
	for _, eni := range allEniList {
		eniPrivateIPWorker := c.getEniPrivateIPWorker(eni.EniID, eni.AccountID, node)
		currentBuffer := c.computeEniPrivateIPCountByNodeResource(node, eniPrivateIPCountBuffer)
		if eniPrivateIPWorker.PrivateIPBufferCount != currentBuffer {
			klog.V(4).Infof("eniController eniID %s PrivateIPBufferCount change old %+v current %+v ",
				eni.EniID, eniPrivateIPWorker.PrivateIPBufferCount, currentBuffer)
			eniPrivateIPWorker.PrivateIPBufferCount = currentBuffer
		}

		currentMaxPrivateCount := c.computeEniPrivateIPCountByNodeResource(node, eniPrivateIPCountMax)
		if eniPrivateIPWorker.PrivateIPMaxCount != currentMaxPrivateCount {
			klog.V(4).Infof("eniController eniID %s PrivateIPMaxCount change old %+v current %+v ",
				eni.EniID, eniPrivateIPWorker.PrivateIPMaxCount, currentMaxPrivateCount)
			eniPrivateIPWorker.PrivateIPMaxCount = currentMaxPrivateCount
		}
	}
}
