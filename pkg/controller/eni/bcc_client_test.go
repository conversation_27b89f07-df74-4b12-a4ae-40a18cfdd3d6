package eni

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/sts"
)

type BidClient struct {
	BccClient *bcc.Client
}

type GetBidEventsRequest struct {
	InstanceUUIDs []string `json:"instanceUuids"`
}

type GetBidEventsResponse struct {
	BidEvents []BidEvent `json:"events"`
}

type BidEvent struct {
	InstanceID   string         `json:"instanceId"`
	InstanceUUID string         `json:"instanceUuid"`
	EventTime    time.Time      `json:"eventTime"`
	Action       BidEventAction `json:"action"`
}

type BidEventAction string

const (
	// GetBidEvents 接口限制一次查询最多 100 事件
	GetBidEventsLimit = 100

	BidEventActionDelete BidEventAction = "delete"
)

func (c *BidClient) GetBidEvents(ctx context.Context, getReq *GetBidEventsRequest, option *bce.SignOption) (*GetBidEventsResponse, error) {
	params := map[string]string{
		"clientToken": c.BccClient.GenerateClientToken(),
	}

	if getReq == nil {
		return nil, fmt.Errorf("args is nil")
	}

	postContent, err := json.Marshal(getReq)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.BccClient.GetURL("api/logical/bcc/v1/instance/bid/events", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.BccClient.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(GetBidEventsResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func TestBCCBidEvent(t *testing.T) {

	if true {
		return
	}
	region := "bd"
	iamRoleName := "BceServiceRole_bci"
	iamUserName := "bci"
	iamPassword := "IreriJJDE4XS8AsFBHdkXkBiBxEqWnrK"
	// accountID := "eca97e148cb74e9683d7b7240829d1ff"
	accountID := "b9ca12ddf6064c5eab7961d32496d564"

	eniHexKey := "WG8ugAEcU726q90B"
	stsClient := sts.NewClient(context.Background(), &bce.Config{
		Endpoint: sts.Endpoints[region],
		Checksum: true,
		Timeout:  60 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: iam.Endpoints[region],
		Checksum: true,
		Timeout:  60 * time.Second,
		Region:   region,
	}, iamRoleName, iamUserName, iamPassword)

	buildSignOptionFn := func(accountID string) *bce.SignOption {
		return stsClient.NewSignOptionWithResourceHeader(context.Background(), accountID,
			eniHexKey, accountID, "bci")
	}

	bceConfig := &bce.Config{
		Checksum: true,
		Timeout:  60 * time.Second,
		Endpoint: eni.Endpoint[region],
	}
	client := bcc.NewClient(bceConfig)

	bidClient := &BidClient{
		BccClient: client,
	}

	// nodeID := "i-k0O0B8IN"
	req := &GetBidEventsRequest{
		// "i-OwRW7rby"
		InstanceUUIDs: []string{},
	}
	resp, err := bidClient.GetBidEvents(context.Background(), req, buildSignOptionFn(accountID))
	if err != nil {
		fmt.Println(err)
	}
	bytes, err := json.Marshal(resp)
	if err != nil {
		panic(err)
	}
	fmt.Println(resp)
	fmt.Println(string(bytes))
}
