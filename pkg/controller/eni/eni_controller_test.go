package eni

import (
	// "context"
	// "encoding/json"
	// "fmt"
	"context"
	"fmt"
	"math/rand"
	"sync"
	"testing"
	"time"

	"golang.org/x/time/rate"
	// "time"
	// "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	// "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/iam"
	// "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/sts"
)

func TestLimiter(t *testing.T) {
	// 使用文档： https://www.cyhone.com/articles/usage-of-golang-rate/
	qps := 20
	limit := rate.Every(time.Duration(1000/qps) * time.Millisecond)
	if qps > 1000 {
		limit = rate.Every(time.Duration(1000000/qps) * time.Microsecond)
	}

	var LockPushLimit = rate.NewLimiter(limit, 3)
	var g sync.WaitGroup
	fmt.Println(time.Now())
	for a := 0; a < 10; a++ {
		g.Add(1)
		go func() {
			for b := 0; b < 10; b++ {
				_ = LockPushLimit.Wait(context.Background())
			}
			g.Done()
		}()
	}
	g.Wait()
	fmt.Println(time.Now())
}

func TestLimiterBatchRequest(t *testing.T) {
	qps := 30
	limit := rate.Every(time.Duration(1000/qps) * time.Millisecond)
	if qps > 1000 {
		limit = rate.Every(time.Duration(1000000/qps) * time.Microsecond)
	}

	var rateLimiter = rate.NewLimiter(limit, qps)
	var g sync.WaitGroup
	fmt.Println(time.Now())

	for a := 0; a < 10; a++ {
		g.Add(1)
		go func() {
			for b := 0; b < 1; b++ {
				start := time.Now()
				err := rateLimiter.WaitN(context.Background(), 10)
				if err != nil {
					panic(err)
				}
				fmt.Printf("cost %+v \n", time.Now().Sub(start))
			}
			g.Done()
		}()
	}
	g.Wait()
	fmt.Println(time.Now())
}

func TestRandom(t *testing.T) {
	fmt.Println(rand.Intn(100))

out:
	for i := 0; i < 3; i++ {
		// if i ==1 {
		// 	break
		// }
		fmt.Println(i, "i 值打印")

		for j := 0; j < 2; j++ {
			fmt.Println("   ", j, "j 值打印")
			if i == 2 {
				break out
			}
		}
	}
}

func TestStsClient(t *testing.T) {
	// return

	// ss := []int{1, 2, 3, 4, 5, 6}
	// fmt.Println(ss[:3])
	
	// region := "gz"
	// roleName := "BceServiceRole_bci"
	// iamUserName := "bci"
	// iamConsolePassword := "IreriJJDE4XS8AsFBHdkXkBiBxEqWnrK"

	// stsClient := sts.NewClient(context.Background(), &bce.Config{
	// 	Endpoint: sts.Endpoints[region],
	// 	Checksum: true,
	// 	Timeout:  10 * time.Second,
	// 	Region:   region,
	// }, &bce.Config{
	// 	Endpoint: iam.Endpoints[region],
	// 	Checksum: true,
	// 	Timeout:  10 * time.Second,
	// 	Region:   region,
	// }, roleName, iamUserName, iamConsolePassword)

	// resp, err := stsClient.GetCredential(context.Background(), "2e1be1eb99e946c3a543ec5a4eaa7d39")
	// if err != nil {
	// 	fmt.Println(err)
	// 	return
	// }

	// fmt.Println(resp.Expiration)
	// fmt.Println(resp.SessionToken)
	// bytes, _ := json.Marshal(resp)
	// fmt.Println(string(bytes))
}
