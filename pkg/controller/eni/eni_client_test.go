package eni

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"strconv"

	"github.com/gorilla/mux"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eni"
	bci_util "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/util"
)

var (
	eniClient      *eni.Client
	testHTTPServer *httptest.Server
)

const statENITemplate = `
{
    "zoneName":"zoneA",
    "createdTime":"2019-04-12 18:53:07.0",
    "description":"",
    "instanceId":"i-Z2iJfB90",
    "eniId":"%s",
    "privateIpSet":[
        {
            "publicIpAddress":"**************",
            "primary":true,
            "privateIpAddress":"*************"
        }
    ],
    "macAddress":"fa:16:3e:c0:e4:3d",
    "name":"test",
    "securityGroupIds":[
        "g-SY5smEG9"
    ],
    "status":"available",
    "subnetId":"sbn-053m53r01z3h",
    "vpcId":"vpc-7hueyu2089wf"
}
`

var eniPrivateMap = make(map[string][]string)

const (
	eni1IP          = "eni1IP"
	eni3IP          = "eni3IP"
	eni5IP          = "eni5IP"
	defaultNodeName = "defaultNodeName"
)

func setEniPrivateIPMap() {
	eniPrivateMap[eni1IP] = []string{"***********"}
	eniPrivateMap[eni3IP] = []string{"***********", "***********", "***********"}
	eniPrivateMap[eni5IP] = []string{"***********", "***********", "***********", "***********", "***********"}
}

func handleStatENI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	vars := mux.Vars(r)
	eniID, ok := vars["eniId"]
	if !ok {
		_, _ = w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
	}

	if eniID == "" || eniID == "invalid-eni" {
		w.WriteHeader(http.StatusBadRequest)
		_, _ = w.Write([]byte(`{"privateIpSet":[],"securityGroupIds":[]}`))
		return
	}

	resp := eni.StatENIResponse{}
	_ = json.Unmarshal([]byte(statENITemplate), &resp)

	privateIPs, ok := eniPrivateMap[eniID]
	if !ok {
		w.WriteHeader(http.StatusBadRequest)
		_, _ = w.Write([]byte(`{"privateIpSet":[],"securityGroupIds":[]}`))
		return
	}

	for _, ip := range privateIPs {
		resp.PrivateIPSet = append(resp.PrivateIPSet, &eni.PrivateIP{
			Primary:          false,
			PrivateIPAddress: ip,
		})
	}

	resp.ENIID = eniID
	bytes, _ := json.Marshal(resp)
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write(bytes)
}

func setupTestEnv(handler http.Handler) {
	credentials := &bce.Credentials{
		AccessKeyID:     "b275875821484bc7a3ee0bdae74c3ef1",
		SecretAccessKey: "xxx",
	}

	var bceConfig = &bce.Config{
		Credentials: credentials,
		Checksum:    true,
	}
	var eniConfig = eni.NewConfig(bceConfig)
	eniClient = eni.NewClient(eniConfig)
	eniClient.SetDebug(true)

	testHTTPServer = httptest.NewServer(handler)
	eniClient.Endpoint = testHTTPServer.URL
}

const defaultIP = "192.168.2."

func handleAddPrivateIP(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	vars := mux.Vars(r)
	eniID, ok := vars["eniId"]
	if !ok {
		_, _ = w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	if eniID == "ratelimit" {
		w.WriteHeader(http.StatusBadRequest)
		_, _ = w.Write([]byte(fmt.Sprintf(`{"Code":"RateLimit","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	if eniID == "" || eniID == "invalid-eni" {
		w.WriteHeader(http.StatusBadRequest)
		_, _ = w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	bodyContent, _ := ioutil.ReadAll(r.Body)
	defer r.Body.Close()

	addPrivateIPArgs := &eni.BatchPrivateIPArgs{}
	err := json.Unmarshal(bodyContent, addPrivateIPArgs)
	if err != nil {
		_, _ = w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"AddPrivateIPArgs is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	resp := make([]string, 0)

	for i := range addPrivateIPArgs.PrivateIPAddresses {
		resp = append(resp, defaultIP+strconv.Itoa(i))
	}
	result := eni.BatchAddPrivateIPResult{
		PrivateIPAddresses: resp,
	}

	eniPrivateMap[eniID] = append(eniPrivateMap[eniID], resp...)
	bytes, _ := json.Marshal(result)
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write(bytes)
}

func tearDownTestEnv() {
	testHTTPServer.Close()
}

func handleDeletePrivateIP(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	vars := mux.Vars(r)
	eniID, ok := vars["eniId"]
	if !ok {
		_, _ = w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	if eniID == "ratelimit" {
		w.WriteHeader(http.StatusBadRequest)
		_, _ = w.Write([]byte(fmt.Sprintf(`{"Code":"RateLimit","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	if eniID == "" || eniID == "invalid-eni" {
		w.WriteHeader(http.StatusBadRequest)
		_, _ = w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	bodyContent, _ := ioutil.ReadAll(r.Body)
	defer r.Body.Close()

	deletePrivateIPArgs := &eni.BatchPrivateIPArgs{}
	err := json.Unmarshal(bodyContent, deletePrivateIPArgs)
	if err != nil {
		_, _ = w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"AddPrivateIPArgs is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	ips := eniPrivateMap[eniID]
	result := bci_util.SliceSubtract(ips, deletePrivateIPArgs.PrivateIPAddresses)
	eniPrivateMap[eniID] = result

	w.WriteHeader(http.StatusOK)
}

func newENIHandler() http.Handler {
	r := mux.NewRouter()

	// r.HandleFunc("/v1/eni", handleListENIs).Methods("GET")
	r.HandleFunc("/v1/eni/{eniId}", handleStatENI).Methods("GET")
	// r.HandleFunc("/v1/eni", handleCreateENI).Methods("POST")
	// r.HandleFunc("/v1/eni/{eniId}", handleDeleteENI).Methods("DELETE")
	// r.HandleFunc("/v1/eni/{eniId}", handleENIPutAction).Methods("PUT")
	r.HandleFunc("/v1/eni/{eniId}/privateIp/batchAdd", handleAddPrivateIP).Methods("POST")
	r.HandleFunc("/v1/eni/{eniId}/privateIp/batchDel", handleDeletePrivateIP).Methods("POST")

	return r
}

func handleENIPutAction(w http.ResponseWriter, r *http.Request) {
	query := r.URL.Query()

	if _, ok := query["attach"]; ok {
		// handleAttachAndDetachENI(w, r)
		return
	}

	if _, ok := query["detach"]; ok {
		// handleAttachAndDetachENI(w, r)
		return
	}

	if _, ok := query["bindSg"]; ok {
		// handleUpdateSecurityGroup(w, r)
		return
	}

	w.WriteHeader(http.StatusBadRequest)
	_, _ = w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"handler is invalid,"RequestID":%s}`, util.GetRequestID())))
}
