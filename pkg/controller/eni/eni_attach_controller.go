package eni

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"
)

type attachWorker struct {
	NodeName   string
	Attach<PERSON>han chan *eniAttachTask
	Controller *Controller
	Closed     bool
	Stop<PERSON>han   chan struct{}
}

func (worker *attachWorker) sendAttachTask(task *eniAttachTask) {
	if worker.Closed {
		klog.V(4).Infof("eniController node %s attach worker is closed ", worker.NodeName)
		return
	}
	select {
	case worker.AttachChan <- task:
		return
	default:
		klog.V(3).Infof("eniController node %s attach worker At<PERSON><PERSON><PERSON><PERSON> is full ,ignore attach task ", worker.NodeName)
	}
}

// iaas 接口单机attch 要串行
func (worker *attachWorker) start() {
	go func() {
		for {
			select {
			case <-worker.StopChan:
				klog.V(3).Infof("eniController node %s attachWorker exit ", worker.NodeName)
				return
			// eni attach 单node 串行
			case attach := <-worker.AttachChan:

				node, err := worker.Controller.nodeLister.Get(worker.NodeName)
				if err != nil {
					break
				}

				query, retry, err := worker.Controller.attachEniIfPossible(node, attach)
				if retry {
					klog.Warningf("eniController node %s attach eni %s retry count %+v ", node.Name, attach.EniID, attach.RetryCount)
					// 300ms 后重试
					time.AfterFunc(300*time.Millisecond, func() {
						worker.sendAttachTask(attach)
					})
				}
				if err != nil {
					// attach 失败，退出worker，屏蔽node
					klog.Errorf("eniController node %s attach eni %s err %+v ", node.Name, attach.EniID, err)
					worker.Controller.deleteNodeEniCreatingOrAttachEniFlagWithLock(node.Name)
					return
				}
				if !query {
					break
				}
				for {
					attachSuccess, err := worker.Controller.createOrUpdateBciNodeCRDByEniAttachStatus(attach, node)
					if err != nil {
						// attach 查询失败，退出worker，屏蔽node
						klog.Errorf("eniController node %s query eni %s attach status err %+v ", node.Name, attach.EniID, err)
						worker.Controller.deleteNodeEniCreatingOrAttachEniFlagWithLock(node.Name)
						return
					}
					if attachSuccess {
						worker.Controller.deleteNodeEniCreatingOrAttachEniFlagWithLock(node.Name)
						break
					}
					time.Sleep(1 * time.Second)
				}
			}
		}
	}()
}

func (c *Controller) hasSameConfigurationEniInfo(node *corev1.Node, eniInfo *entity.EniInfo) bool {

	allEniList := getAllNodeEnis(node)
	// 相同网络配置的eni
	var sameEniInfo *entity.EniInfo

	for _, otherEni := range allEniList {
		if eniInfo.HasSameNetworkConfiguration(otherEni) {
			sameEniInfo = otherEni
			break
		}
	}

	if sameEniInfo == nil || !sameEniInfo.AttachInterfaceCalled {
		return false
	}

	klog.V(3).Infof("eniController for node %s eniInfo %+v has same config eni %+v ", node.Name, eniInfo, sameEniInfo)

	// 删除重复创建的eni
	if !eniInfo.AttachInterfaceCalled {

		err := c.doDeleteEni(eniInfo, node)
		klog.V(3).Infof("eniController for node %s for eniID %s delete same network config eniID %s err %+v ",
			node.Name, sameEniInfo.EniID, eniInfo.EniID, err)
		if err == nil {
			// 删除成功，删除node Annotation eni info
			deletePatch := entity.PatchValue{
				Key:  buildEniAnnotationKey(eniInfo.EniID),
				Type: entity.PatchTypeDelete,
			}
			if err = util.PatchNodeAnnotation(c.client, node, []entity.PatchValue{deletePatch}); err != nil {
				klog.Errorf("eniController for node %s delete same eniID %s patch node err %+v ", node.Name, eniInfo.EniID, err)
			}
		}
	}
	return true
}

func (c *Controller) attachEniIfPossible(node *corev1.Node, attachTask *eniAttachTask) (query bool, retry bool, err error) {
	eniInfo, err := getEniInfo(node, attachTask.EniID, attachTask.AccountID)
	if err != nil {
		// 有可能是网络延时，node 最新信息没有同步到informer中，因此此处重试。
		if attachTask.AfterCreate && attachTask.RetryCount <= 3 {
			attachTask.RetryCount++
			return false, true, nil
		}
		return false, false, nil
	}
	if eniInfo.AttachInterfaceCalled || eniInfo.AttachSuccess {
		return true, false, nil
	}

	if c.hasSameConfigurationEniInfo(node, eniInfo) {
		return false, false, nil
	}

	// 发起attach请求
	if err := c.doAttachEniToNode(node, attachTask.EniID, attachTask.AccountID); err != nil {
		c.disableUseEniPodScheduleAndMigrateFailedPods(entity.EniAttachFailed, node)
		return false, false, err
	}

	eniInfo.AttachInterfaceCalled = true
	bytes, _ := json.Marshal(eniInfo)
	updatePatch := entity.PatchValue{
		// Key:   nodeBindEniInfo + attachTask.EniID,
		Key:   buildEniAnnotationKey(attachTask.EniID),
		Value: string(bytes),
		Type:  entity.PatchTypeUpdate,
	}
	if err = util.PatchNodeAnnotation(c.client, node, []entity.PatchValue{updatePatch}); err != nil {
		klog.Errorf("eniController node %s attach eni %s patch node err %+v ", node.Name, eniInfo.EniID, err)
	}
	return true, false, nil
}

func (c *Controller) eniStatusAttachingOrInuse(eniID string, accountID string) bool {
	resp, err := c.statEniWithRatelimit(eniID, accountID)
	if err != nil {
		klog.Errorf("eniController for account %s query eni %s status err %+v ", accountID, eniID, err)
		return false
	}

	if resp.Status == string(eni.ENIStatusAttaching) || resp.Status == string(eni.ENIStatusInuse) {
		return true
	}
	return false
}

func (c *Controller) doAttachEniToNode(node *corev1.Node, eniID string, accountID string) error {
	providerID, err := getNodeProviderID(node)
	if err != nil {
		return err
	}

	attachArgs := &eni.AttachENIArgs{
		ENIID:      eniID,
		InstanceID: providerID,
	}

out:
	for i := 0; i < 3; i++ {
		// attach 前查询一下eni 状态，防止由于进程重启导致状态没有保存，重复attach 失败
		if c.eniStatusAttachingOrInuse(eniID, accountID) {
			klog.Warningf("eniController eniID %s on node %s doAttachEniToNode status is attaching or inuse, ignore attach ",
				node.Name, eniID)
			return nil
		}
		// 限流
		c.changeEniRatelimit(accountID, eniAttachMethod, eniID, node)

		klog.V(3).Infof("eniController attach accountID %s eni %s for node %s ", accountID, eniID, node.Name)
		err = c.eniClient.AttachENI(context.Background(), attachArgs, c.buildSignOptionFn(accountID))
		if err == nil {
			break
		}

		if isENIRatelimited(err) {
			klog.Errorf("eniController attach eni params %+v ratelimited try again", attachArgs)
			// 在重试2次
			for j := 0; j < c.networkOption.EniChangeRatelimitLoopRetryCount; j++ {
				// sleep 2s - 3s 之间
				time.Sleep(time.Duration(rand.Intn(1000)+2000) * time.Millisecond)
				// 限流
				c.changeEniRatelimit(accountID, eniAttachMethod, eniID, node)

				err = c.eniClient.AttachENI(context.Background(), attachArgs, c.buildSignOptionFn(accountID))
				if err == nil {
					break out
				}
			}
			continue
		}
		time.Sleep(500 * time.Millisecond)
	}
	if err != nil {
		klog.Errorf("eniController for account %s attach eni %s err %+v ", accountID, eniID, err)
		attachENIFailCounter.WithLabelValues().Inc()
	}
	return err
}

func (c *Controller) createOrUpdateBciNodeCRDByEniAttachStatus(queryTask *eniAttachTask, node *corev1.Node) (attachSuccess bool, err error) {
	// 加锁
	lock := c.getNodeLock(node.Name)
	lock.Lock()
	defer lock.Unlock()

	// 从informer获取最新node
	node, err = c.nodeLister.Get(node.Name)
	if err != nil {
		return true, nil
	}
	eniInfo, err := getEniInfo(node, queryTask.EniID, queryTask.AccountID)
	if err != nil {
		// 信息丢失，则忽略处理
		return true, nil
	}

	if c.attachQueryTimeout(eniInfo, node) {
		attachENIFailCounter.WithLabelValues().Inc()
		return false, fmt.Errorf("attach query timeout ")
	}

	resp, success, err := c.eniAttachSuccess(queryTask.EniID, queryTask.AccountID)
	// eni 已经被删除
	if isENINotFoundErr(err) {
		klog.Infof("eniController query eni %s found 404 on node %s delete patch ",
			queryTask.EniID, node.Name)
		// eni 已经不存在，删除eni信息
		deletePatch := entity.PatchValue{
			// Key:  nodeBindEniInfo + queryTask.EniID,
			Key:  buildEniAnnotationKey(queryTask.EniID),
			Type: entity.PatchTypeDelete,
		}
		_ = util.PatchNodeAnnotation(c.client, node, []entity.PatchValue{deletePatch})
		return true, nil
	}

	if !success {
		klog.V(3).Infof("eniController query eni %s on node %s accountID %s attach not success ,retry ",
			queryTask.EniID, node.Name, queryTask.AccountID)
		return false, nil
	}

	// 创建或更新crd
	err = c.createOrUpdateNodeEniCr(resp, queryTask, node)
	if err != nil {
		klog.Errorf("eniController for node %s createOrUpdateNodeEniCr err %+v ", node.Name, err)
		return false, nil
	}

	// 更新eni 状态为success
	eniInfo.AttachSuccess = true
	bytes, _ := json.Marshal(eniInfo)

	updatePatch := entity.PatchValue{
		// Key:   nodeBindEniInfo + queryTask.EniID,
		Key:   buildEniAnnotationKey(queryTask.EniID),
		Value: string(bytes),
		Type:  entity.PatchTypeUpdate,
	}
	_ = util.PatchNodeAnnotation(c.client, node, []entity.PatchValue{updatePatch})

	// 更新竞价+潮汐node eni running cm信息
	nodeInfo := entity.NewNodeInfo(node)
	if nodeInfo.GetBiddingNodeType() != "" {
		providerID, _ := getNodeProviderID(node)
		bidEniInfo := entity.BuildBidNodeEniInfo(eniInfo, providerID, node.Name)
		c.UpdateBidRunningEniConfigMapWithRetry([]entity.BidNodeEniInfo{bidEniInfo}, false)
	}
	return true, nil
}

func (c *Controller) attachQueryTimeout(eniInfo *entity.EniInfo, node *corev1.Node) bool {
	if eniInfo.AttachSuccess {
		return false
	}
	// 默认2分钟不在重试
	covertTime := time.Unix(eniInfo.AttachTime, 0)
	if time.Now().After(covertTime.Add(c.networkOption.EniAttachQueryTimeout)) {
		klog.V(3).Infof("eniController for account %s eniID %s query timeout , disable node schedule eni pod ",
			eniInfo.AccountID, eniInfo.EniID)
		c.disableUseEniPodScheduleAndMigrateFailedPods(entity.EniAttachFailed, node)
		return true
	}
	return false
}

func (c *Controller) eniAttachSuccess(eniID string, accountID string) (*eni.StatENIResponse, bool, error) {

	resp, err := c.statEniWithRatelimit(eniID, accountID)
	if err != nil {
		klog.Warningf("eniController for account %s query eni %s status err %+v ", accountID, eniID, err)
		return nil, false, err
	}

	if resp.Status == string(eni.ENIStatusInuse) {
		return resp, true, nil
	}
	return resp, false, nil
}
