package eni

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/util"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/klog/v2"
)

var (
	deleteENIFailCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "eni_delete_fail_total",
			Help:      "The ENI Delete Fail Total Count .",
		},
		[]string{},
	)

	detachENIFailCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "eni_detach_fail_total",
			Help:      "The ENI Detach Fail Total Count .",
		},
		[]string{},
	)
)

const (
	reBuildBciNodeCrTaskDisable = "bci-node-eni-rebuild-task-disable"
)

// DoCustomizeNodeGC 执行node gc操作，gc node attach的eni
func (c *Controller) DoCustomizeNodeGC(source runtime.NodeGCSource, node *corev1.Node) error {

	hasAttachOrInitErr := func(node *corev1.Node) bool {
		// eni删除gc成功，看是否还有其它eni 错误，如没有其它错误，则不删除node
		_, attachFailed := node.Annotations[string(entity.EniAttachFailed)]
		_, initFailed := node.Annotations[string(entity.PodEniInitFailed)]
		if attachFailed || initFailed {
			klog.V(3).Infof("eniController node %s eni gc source %+v attachFailed %+v initFailed %+v can delete node ",
				node.Name, source, attachFailed, initFailed)
			return true
		}
		return false
	}

	lock := c.getNodeLock(node.Name)
	lock.Lock()
	defer lock.Unlock()

	// 删除node 上挂载的eni
	allNodeEnis := getAllNodeEnis(node)
	if len(allNodeEnis) == 0 {
		// 删除机器上的bcinode crd，如不存在eni
		_ = c.deleteBciNodeCRD(node.Name)
		if source == runtime.GCRetrySuccessNotDeleteNode {
			if hasAttachOrInitErr(node) {
				// 可以删除node
				return nil
			}
			return fmt.Errorf("node %s enigc retry success , do not delete node", node.Name)
		}
		return nil
	}

	klog.V(3).Infof("eniController DoCustomizeNodeGC node %s NodeGCSource %s start ", node.Name, source)

	// 因为此处会删除bciNode crd，patch 禁止syncBciNodeCrd task 重建crd
	disableRebuildBciNodeTaskPatch := entity.PatchValue{
		Key:   reBuildBciNodeCrTaskDisable,
		Value: "1",
		Type:  entity.PatchTypeUpdate,
	}
	_ = util.PatchNodeAnnotation(c.client, node, []entity.PatchValue{disableRebuildBciNodeTaskPatch})

	deleteFailedEniList := make([]string, 0)

	// detach 只能串行调用，不然会报
	// Error Message: "The vm status is not allowed to operate", Error Code: "VmStatusException", Status Code: 403
	for _, eniInfo := range allNodeEnis {
		// 停止eni private worker 定时任务 + 申请紧急申请辅助ip任务
		eniPrivateWorker := c.getEniPrivateIPWorker(eniInfo.EniID, eniInfo.AccountID, node)
		eniPrivateWorker.EniTaskDisable = true

		err := c.detachAndDeleteEni(eniInfo, node)
		if err != nil {
			deleteFailedEniList = append(deleteFailedEniList, eniInfo.EniID)
			continue
		}
		// 删除eni信息
		patch := entity.PatchValue{
			// Key:  nodeBindEniInfo + eniInfo.EniID,
			Key:  buildEniAnnotationKey(eniInfo.EniID),
			Type: entity.PatchTypeDelete,
		}
		_ = util.PatchNodeAnnotation(c.client, node, []entity.PatchValue{patch})
	}

	// 删除机器上的bcinode crd，如不存在eni
	_ = c.deleteBciNodeCRD(node.Name)

	if len(deleteFailedEniList) != 0 {
		bytes, _ := json.Marshal(deleteFailedEniList)
		klog.Errorf("eniController node %s  delete failed eniList %s ", node.Name, string(bytes))
		patch := entity.PatchValue{
			Key:   string(entity.EniDeleteFailed),
			Value: string(bytes),
			Type:  entity.PatchTypeUpdate,
		}
		_ = util.PatchNodeAnnotation(c.client, node, []entity.PatchValue{patch})
		return fmt.Errorf("node has delete failed eni")
	}

	klog.V(3).Infof("eniController node %s StateMachineGC remove reBuildBciNodeCrTaskDisable ", node.Name)
	disableRebuildBciNodeTaskPatchRemove := entity.PatchValue{
		Key:  reBuildBciNodeCrTaskDisable,
		Type: entity.PatchTypeDelete,
	}

	removeFailedEni := entity.PatchValue{
		Key:  string(entity.EniDeleteFailed),
		Type: entity.PatchTypeDelete,
	}
	_ = util.PatchNodeAnnotation(c.client, node,
		[]entity.PatchValue{disableRebuildBciNodeTaskPatchRemove, removeFailedEni})

	if source == runtime.GCRetrySuccessNotDeleteNode {
		if hasAttachOrInitErr(node) {
			// 可以删除node
			return nil
		}
		return fmt.Errorf("node %s enigc retry success , do not delete node", node.Name)
	}
	return nil
}

// TODO 当多租户在一个node上时，某个eni上pod 全部退出了，剩下的eni pod 未退出，不会删除完全退出的eni，待优化
// CanDeleteNode 是否能够删除node
func (c *Controller) CanDeleteNode(node *corev1.Node) bool {
	// 创建eni失败，并且是空节点
	_, createFailed := node.Annotations[string(entity.EniAttachFailed)]
	_, initFailed := node.Annotations[string(entity.PodEniInitFailed)]
	_, deleteFailed := node.Annotations[string(entity.EniDeleteFailed)]

	// 如果有eni相关错误，并且node 上无用户pod，允许删除
	if !createFailed && !initFailed && !deleteFailed {
		return false
	}

	klog.Infof("eniController CanDeleteNode node %s createFailed %+v initFailed %+v deleteFailed %+v ",
		node.Name, createFailed, initFailed, deleteFailed)

	nodeInfo := entity.NewNodeInfo(node)
	allPods, _ := c.podLister.List(labels.Everything())

	for _, pod := range allPods {
		if pod.Spec.NodeName == node.Name {
			nodeInfo.AddOrUpdatePod(pod)
		}
	}
	emptyNode := nodeInfo.EmptyNode()
	klog.V(4).Infof("eniController CanDeleteNode node %s has disable schedule , empty node %+v ", node.Name, emptyNode)
	// 只有emptyNode 可以删除
	return emptyNode
}

func (c *Controller) detachAndDeleteEni(eniInfo *entity.EniInfo, node *corev1.Node) error {
	status, err := c.queryEniStatus(eniInfo.EniID, eniInfo.AccountID)
	// resp, attachSuccess, err := c.eniAttachSuccess(eniInfo.EniID, eniInfo.AccountID)
	// eni 已经被删除了
	if isENINotFoundErr(err) {
		klog.Infof("eniController detachAndDeleteEni eni %s on node %s has deleted ", eniInfo.EniID, node.Name)
		return c.deleteBciNodeCRD(node.Name)
	}

	// attach到node上
	if status == string(eni.ENIStatusInuse) {
		queryDetachStatus, err := c.doDetachEni(eniInfo, node)

		if err != nil {
			klog.Errorf("eniController for node %s DetachEni eni %s err %+v ", node.Name, eniInfo.EniID, err)
			detachENIFailCounter.WithLabelValues().Inc()
			return err
		}
		if queryDetachStatus {
			err = c.waitDetachEniSuccess(eniInfo)
			if err != nil {
				return err
			}
		}
	} else if status == string(eni.ENIStatusDetaching) {
		// 进入此处逻辑，说明之前detach 等待超时，进入gc node 重试逻辑
		err = fmt.Errorf("eniController detachAndDeleteEni for node %s eni %s is Detaching wait detach success,retry it",
			node.Name, eniInfo.EniID)
		klog.Error(err)
		return err
	} else if status == string(eni.ENIStatusAttaching) {
		// 说明attach 失败，进入gc node 逻辑，等待attach 成功后，在进行detach
		err = fmt.Errorf("eniController detachAndDeleteEni for node %s eni %s is Attaching wait attach success,retry it",
			node.Name, eniInfo.EniID)
		klog.Error(err)
		return err
	}

	// 删除eni,eni 状态变为 available
	err = c.doDeleteEni(eniInfo, node)
	if err != nil {
		deleteENIFailCounter.WithLabelValues().Inc()
		return err
	}

	return c.deleteBciNodeCRD(node.Name)
}

func (c *Controller) deleteBciNodeCRD(nodeName string) error {
	// 删除bciNode cr
	bciNodeCr, err := c.bciNodeLister.Get(nodeName)

	if err != nil {
		klog.V(4).Infof("eniController node %s detachAndDeleteEni get bciNodeCr err %+v ", nodeName, err)
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	err = c.client.Delete(context.Background(), bciNodeCr)
	if err != nil {
		klog.Warningf("eniController node %s detachAndDeleteEni delete bciNodeCr err %+v ", nodeName, err)
	}
	return err
}

func (c *Controller) doDeleteEni(eniInfo *entity.EniInfo, node *corev1.Node) error {
	var err error

	for i := 0; i < 3; i++ {
		// 限流
		c.changeEniRatelimit(eniInfo.AccountID, eniDeleteMethod, eniInfo.EniID, node)

		err = c.eniClient.DeleteENI(context.Background(), eniInfo.EniID, c.buildSignOptionFn(eniInfo.AccountID))
		// err = c.eniClient.DeleteENI(context.Background(), eniInfo.EniID, c.buildSignOptionFn(eniInfo.AccountID))
		klog.V(3).Infof("eniController for node %s DeleteENI eni %s accountID %s err %+v ", node.Name, eniInfo.EniID, eniInfo.AccountID, err)
		if err == nil {
			return nil
		}
		if isENIRatelimited(err) {
			klog.Errorf("eniController delete eni params %+v ratelimited try again", eniInfo.EniID)
			// 在重试2次
			for j := 0; j < c.networkOption.EniChangeRatelimitLoopRetryCount; j++ {
				// sleep 2s - 3s 之间
				time.Sleep(time.Duration(rand.Intn(1000)+2000) * time.Millisecond)
				// 限流
				c.changeEniRatelimit(eniInfo.AccountID, eniDeleteMethod, eniInfo.EniID, node)

				err = c.eniClient.DeleteENI(context.Background(), eniInfo.EniID, c.buildSignOptionFn(eniInfo.AccountID))
				if err == nil {
					return nil
				}
			}
			continue
		}
		// 有可能接口调用超时，接口调用超时iaas 返回err :
		// Error Message: "The param eniId is invalid", Error Code: "EniIdException", Status Code: 400, Request Id: "b1b1a72b-e1ea-49bb-966d-593ed9a3b6da"
		if c.eniDeleted(eniInfo.EniID, eniInfo.AccountID) {
			return nil
		}
		time.Sleep(1 * time.Second)
	}
	return err
}

func (c *Controller) doDetachEni(eniInfo *entity.EniInfo, node *corev1.Node) (needQueryDetachStatus bool, err error) {
	// var err error
out:
	for i := 0; i < 3; i++ {
		instanceID, _ := getNodeProviderID(node)
		detachArgs := &eni.DetachENIArgs{
			ENIID:      eniInfo.EniID,
			InstanceID: instanceID,
		}
		// 限流
		c.changeEniRatelimit(eniInfo.AccountID, eniDetachMethod, eniInfo.EniID, node)

		err = c.eniClient.DetachENI(context.Background(), detachArgs, c.buildSignOptionFn(eniInfo.AccountID))
		klog.V(3).Infof("eniController for node %s DetachEni eni %s accountID %s err %+v ", node.Name, eniInfo.EniID, eniInfo.AccountID, err)

		// err1 := c.eniClient.DetachENI(context.Background(), detachArgs, c.buildSignOptionFn(eniInfo.AccountID))
		// klog.V(3).Infof("eniController for node %s DetachEni eni again %s accountID %s err %+v ", node.Name, eniInfo.EniID, eniInfo.AccountID, err1)

		if err == nil {
			return true, nil
		}
		if isENIRatelimited(err) {
			klog.Errorf("eniController detach eni params %+v ratelimited try again", detachArgs)
			// 在重试2次
			for j := 0; j < c.networkOption.EniChangeRatelimitLoopRetryCount; j++ {
				// sleep 2s - 3s 之间
				time.Sleep(time.Duration(rand.Intn(1000)+2000) * time.Millisecond)
				// 限流
				c.changeEniRatelimit(eniInfo.AccountID, eniDetachMethod, eniInfo.EniID, node)

				err = c.eniClient.DetachENI(context.Background(), detachArgs, c.buildSignOptionFn(eniInfo.AccountID))
				if err == nil {
					break out
				}
			}
			continue
		}

		// detach 超时后，再次发起detach，返回报错,查询eni 状态接口
		// err Error Message: "The eni status is not allowed to operate", Error Code: "EniStatusException", Status Code: 403
		eniStatus, err := c.queryEniStatus(eniInfo.EniID, eniInfo.AccountID)
		if err != nil {
			time.Sleep(1 * time.Second)
			klog.V(3).Infof("eniController for node %s DetachEni eni %s accountID %s query is Detaching err %+v ", node.Name, eniInfo.EniID, eniInfo.AccountID, err)
			continue
		}
		if eniStatus == string(eni.ENIStatusDetaching) {
			klog.V(3).Infof("eniController for node %s DetachEni eni %s accountID %s is Detaching ", node.Name, eniInfo.EniID, eniInfo.AccountID)
			return true, nil
		}
		if eniStatus == string(eni.ENIStatusAvailable) {
			klog.V(3).Infof("eniController for node %s DetachEni eni %s accountID %s is Available ", node.Name, eniInfo.EniID, eniInfo.AccountID)
			return false, nil
		}
		time.Sleep(1 * time.Second)
	}
	return true, err
}

// 时间一般 20 - 30 s
func (c *Controller) waitDetachEniSuccess(eniInfo *entity.EniInfo) error {
	retryCount := 0
	for {
		status, _ := c.queryEniStatus(eniInfo.EniID, eniInfo.AccountID)
		if status == string(eni.ENIStatusAvailable) {
			return nil
		}
		klog.V(3).Infof("eniController query eni %s accountID %s Detach not success ,retry ",
			eniInfo.EniID, eniInfo.AccountID)
		retryCount++
		time.Sleep(1 * time.Second)
		if retryCount >= c.networkOption.EniDetachQueryMaxCount {
			err := fmt.Errorf("eniController for account %s waitDetachEni %s timeout ", eniInfo.AccountID, eniInfo.EniID)
			klog.Error(err)
			return err
		}
	}
}

func getAllNodeEnis(node *corev1.Node) []*entity.EniInfo {
	result := make([]*entity.EniInfo, 0)

	for k, v := range node.Annotations {
		if !strings.HasPrefix(k, nodeBindEniInfo) {
			continue
		}
		eni := &entity.EniInfo{}
		err := json.Unmarshal([]byte(v), eni)
		if err == nil {
			result = append(result, eni)
		}
	}

	return result
}

func (c *Controller) eniDeleted(eniID string, accountID string) bool {
	_, err := c.statEniWithRatelimit(eniID, accountID)
	if isENINotFoundErr(err) {
		klog.Infof("eniController detachAndDeleteEni eni %s is deleted ", eniID)
		return true
	}
	return false
}

func (c *Controller) queryEniStatus(eniID string, accountID string) (string, error) {
	resp, err := c.statEniWithRatelimit(eniID, accountID)

	if err != nil {
		return "", err
	}

	return resp.Status, nil
}
