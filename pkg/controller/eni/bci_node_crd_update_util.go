package eni

import (
	"context"
	"reflect"

	node_eni "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eni"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog/v2"
)

// 工具类，更新bci node crd + status

func (c *Controller) createOrUpdateNodeEniCr(eniResp *eni.StatENIResponse, queryTask *eniAttachTask, node *corev1.Node) error {

	nodeEni, err := c.bciNodeLister.Get(queryTask.NodeName)
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建cr
			nodeEni = c.newBciNodeEniCr(node)
			configureNodeEni(eniResp, node, queryTask, nodeEni)

			klog.V(3).Infof("eniController for node %s create nodeEniCr %+v ", node.Name, nodeEni)
			if err = c.client.Create(context.Background(), nodeEni); err != nil {
				klog.Errorf("eniController for node %s create nodeEniCr err %+v ", node.Name, err)
			}
			return nil
		}
		klog.Errorf("eniController for node %s get nodeEniCr err %+v ", node.Name, err)
		return err

	}

	return retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		// 获取最新值
		nodeEni, err := c.bciNodeLister.Get(queryTask.NodeName)
		if err != nil {
			if errors.IsNotFound(err) {
				return nil
			}
			return err
		}
		copyNodeENI := nodeEni.DeepCopy()
		// 更新cr
		changed := configureNodeEni(eniResp, node, queryTask, copyNodeENI)
		if !changed {
			return nil
		}
		klog.V(3).Infof("eniController for node %s update nodeEniCr %+v ", node.Name, copyNodeENI)
		err = c.client.Update(context.Background(), copyNodeENI)
		return err
	})

}

// 更新bcinode crd status，更新失败重试
func (c *Controller) updateBciNodeStatusWithRetry(bciNodeName string, updateFunc func(bciNode *node_eni.BciNode) bool) error {

	err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		// 获取最新值
		nodeEni, err := c.bciNodeLister.Get(bciNodeName)
		if err != nil {
			if errors.IsNotFound(err) {
				return nil
			}
			return err
		}
		copyNodeENI := nodeEni.DeepCopy()

		change := updateFunc(copyNodeENI)
		if !change {
			klog.V(4).Infof("eniController updateBciNodeStatusWithRetry bcinode %s not change ", bciNodeName)
			return nil
		}

		klog.V(3).Infof("eniController updateBciNodeStatusWithRetry bcinode %s status %+v ", bciNodeName, copyNodeENI.Status)
		return c.client.Status().Update(context.Background(), copyNodeENI)
	})

	if err != nil {
		klog.Errorf("eniController updateBciNodeStatusWithRetry bcinode %s err %+v ", bciNodeName, err)
	}
	return err
}

func (c *Controller) newBciNodeEniCr(node *corev1.Node) *node_eni.BciNode {
	instanceType := node.Labels["beta.kubernetes.io/instance-type"]

	bciNode := &node_eni.BciNode{
		ObjectMeta: metav1.ObjectMeta{
			Name:        node.Name,
			Labels:      map[string]string{},
			Annotations: map[string]string{},
			Finalizers:  []string{"kubernetes"},
		},
		Spec: node_eni.BciNodeSpec{
			InstanceID:   node.Name,
			InstanceType: instanceType,
			EniMultiIP: node_eni.EniMultiIPSpec{
				Pool:              make(node_eni.AllocationMap),
				MinAllocate:       c.networkOption.DefaultENIPrivateIPCount,
				MaxAllocate:       c.networkOption.DefaultENIPrivateIPCount,
				PreAllocate:       c.networkOption.DefaultENIPrivateIPCount,
				MaxAboveWatermark: c.networkOption.DefaultENIPrivateIPCount,
			},
		},
		Status: node_eni.BciNodeStatus{},
	}

	return bciNode

}

func configureNodeEni(eniResp *eni.StatENIResponse, node *corev1.Node, queryTask *eniAttachTask, bciNode *node_eni.BciNode) (changed bool) {
	// 构建主ip 和 辅助ip
	var primaryIPAddress = ""
	privateIPMap := make(map[string]node_eni.AllocationIP)

	for _, privateIP := range eniResp.PrivateIPSet {
		if privateIP.Primary {
			primaryIPAddress = privateIP.PrivateIPAddress
			continue
		}
		privateIPMap[privateIP.PrivateIPAddress] = node_eni.AllocationIP{
			UserID: queryTask.AccountID,
			EniID:  queryTask.EniID,
		}
	}

	// 构建 IPv6 辅助ip
	privateIPv6Map := make(map[string]node_eni.AllocationIP)
	for _, privateIPv6 := range eniResp.IPv6PrivateIPSet {
		if privateIPv6.Primary {
			// IPv6 通常不作为主 IP，但为了安全起见还是检查
			continue
		}
		privateIPv6Map[privateIPv6.PrivateIPAddress] = node_eni.AllocationIP{
			UserID: queryTask.AccountID,
			EniID:  queryTask.EniID,
		}
	}

	if bciNode.Spec.EniMultiIP.Pool == nil {
		bciNode.Spec.EniMultiIP.Pool = make(node_eni.AllocationMap)
	}

	if bciNode.Spec.EniMultiIP.Pool[queryTask.AccountID] == nil {
		bciNode.Spec.EniMultiIP.Pool[queryTask.AccountID] = make(node_eni.UserAllocationEnis)
	}

	eniAllocation, ok := bciNode.Spec.EniMultiIP.Pool[queryTask.AccountID][queryTask.EniID]

	if !ok {
		changed = true
		allocationEni := &node_eni.AllocationEni{
			UserID:               queryTask.AccountID,
			EniID:                queryTask.EniID,
			MacAddress:           eniResp.MacAddress,
			SubnetID:             eniResp.SubnetID,
			SecurityGroupIDs:     eniResp.SecurityGroupIDs,
			VpcID:                eniResp.VPCID,
			VpcCIDR:              queryTask.VpcCIDR,
			PrimaryIPAddress:     primaryIPAddress,
			PrivateIPAddresses:   privateIPMap,
			PrivateIPv6Addresses: privateIPv6Map,
		}
		if len(eniResp.SecurityGroupIDs) != 0 {
			allocationEni.SecurityGroupIDs = eniResp.SecurityGroupIDs
		} else {
			allocationEni.SecurityGroupIDs = eniResp.EnterpriseSecurityGroupIds
		}
		bciNode.Spec.EniMultiIP.Pool[queryTask.AccountID][queryTask.EniID] = allocationEni

		return
	}
	// 判断辅助ip个数是否发生变化
	if !reflect.DeepEqual(eniAllocation.PrivateIPAddresses, privateIPMap) {
		klog.V(4).Infof("eniController eni %s on node %s private ip count change , try update", queryTask.EniID, queryTask.NodeName)
		changed = true
		eniAllocation.PrivateIPAddresses = privateIPMap
	}

	// 判断辅助ipv6个数是否发生变化
	if !reflect.DeepEqual(eniAllocation.PrivateIPv6Addresses, privateIPv6Map) {
		klog.V(4).Infof("eniController eni %s on node %s private ipv6 count change , try update", queryTask.EniID, queryTask.NodeName)
		changed = true
		eniAllocation.PrivateIPv6Addresses = privateIPv6Map
	}
	return
}
