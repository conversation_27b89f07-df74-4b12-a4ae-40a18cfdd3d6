package bci

import (
	"time"

	corev1 "k8s.io/api/core/v1"
)

type Volumes struct {
	NFS        []VolumeNFS        `json:"nfs"`
	EmptyDir   []VolumeEmptyDir   `json:"emptyDir"`
	ConfigFile []VolumeConfigFile `json:"configFile"`
}

type VolumeNFS struct {
	Name     string `json:"name"`
	Server   string `json:"server"`
	ReadOnly bool   `json:"readOnly"`
	Path     string `json:"path"`
}

type VolumeEmptyDir struct {
	Name string `json:"name"`
}

type VolumeConfigFile struct {
	Name        string       `json:"name"`
	ConfigFiles []ConfigFile `json:"configFiles"`
}

type ConfigFile struct {
	Path string `json:"path"`
	File string `json:"file"`
}

type VolumeType string

const (
	VolumeTypeNFS        VolumeType = "NFS"
	VolumeTypeEmptyDir   VolumeType = "EmptyDir"
	VolumeTypeConfigFile VolumeType = "ConfigFile"
)

type VolumeMount struct {
	MountPath string     `json:"mountPath"`
	ReadOnly  bool       `json:"readOnly"`
	Name      string     `json:"name"`
	Type      VolumeType `json:"type"`
}

type ContainerPort struct {
	Port     int32                    `json:"port"`
	Protocol ContainerNetworkProtocol `json:"protocol"`
}

type ContainerNetworkProtocol string

const (
	ContainerNetworkProtocolUDP ContainerNetworkProtocol = "UDP"
	ContainerNetworkProtocolTCP ContainerNetworkProtocol = "TCP"
)

type Env struct {
	Key   string `json:"key"`
	Value string `json:"value"`

	ValueFrom *corev1.EnvVarSource `json:"valueFrom,omitempty"`
}

// PullPolicy describes a policy for if/when to pull a container image
type PullPolicy string

const (
	PullAlways       PullPolicy = "Always"
	PullNever        PullPolicy = "Never"
	PullIfNotPresent PullPolicy = "IfNotPresent"
)

type ContainerImageInfo struct {
	ImageAddress string `json:"imageAddress"`
	ImageName    string `json:"imageName"`
	ImageVersion string `json:"imageVersion"`
}

type Container struct {
	Name                string `json:"name" valid:"Required"`
	*ContainerImageInfo `valid:"Required"`
	MemoryInGB          float64          `json:"memory"`
	CPUInCore           float64          `json:"cpu"`
	WorkingDir          string           `json:"workingDir"`
	ImagePullPolicy     PullPolicy       `json:"imagePullPolicy"`
	Commands            []string         `json:"commands"`
	Args                []string         `json:"args"`
	VolumeMounts        []VolumeMount    `json:"volumeMounts"`
	Ports               []ContainerPort  `json:"ports"`
	Envs                []Env            `json:"envs"`
	Status              *ContainerStatus `json:"status,omitempty"`
	UserID              string           `json:"userId,omitempty"`
	ContainerUUID       string           `json:"containerUuid,omitempty"`
	CreatedTime         time.Time        `json:"createdTime,omitempty"`
	UpdatedTime         time.Time        `json:"updatedTime,omitempty"`
	DeletedTime         time.Time        `json:"deletedTime,omitempty"`
	ContainerType       ContainerType    `json:"containerType,omitempty"`
	LivenessProbe       *corev1.Probe    `json:"livenessProbe,omitempty"`
	ReadinessProbe      *corev1.Probe    `json:"readinessProbe,omitempty"`
	StartupProbe        *corev1.Probe    `json:"startupProbe,omitempty"`
}

type ContainerType string

const (
	ContainerTypeInit     ContainerType = "init"
	ContainerTypeWorkload ContainerType = "workload"
)

type ContainerStatus struct {
	PreviousState *ContainerState `json:"previousState"`
	CurrentState  *ContainerState `json:"currentState"`
	RestartCount  int32           `json:"restartCount"`
}

type ContainerState struct {
	State               ContainerStateString `json:"state,omitempty" description:"Pending/Creating/Running/Succeeded/Failed"`
	ContainerStartTime  time.Time            `json:"containerStartTime,omitempty"`
	ExitCode            int32                `json:"exitCode,omitempty"`
	ContainerFinishTime time.Time            `json:"containerFinishTime,omitempty"`
	DetailStatus        string               `json:"detailStatus,omitempty" description:"detailed message"`
}

type ContainerStateString string

const (
	ContainerStateStringPending   ContainerStateString = "Pending"
	ContainerStateStringCreating  ContainerStateString = "Creating"
	ContainerStateStringRunning   ContainerStateString = "Running"
	ContainerStateStringSucceeded ContainerStateString = "Succeeded"
	ContainerStateStringFailed    ContainerStateString = "Failed"
)

type ImageRegistrySecret struct {
	Server   string `json:"server"`
	UserName string `json:"userName"`
	Password string `json:"password"`
}

type ServiceType string

const (
	ServiceTypeBCI ServiceType = "BCI"
	ServiceTypeEIP ServiceType = "EIP"
)

type ProductType string

const (
	ProductTypePostPay ProductType = "PostPay"
)

type EIPProductType string

const (
	EIPProductTypePostPay = "postpay"
)

type SubProductType string

const (
	SubProductTypeNetraffic SubProductType = "netraffic"
)

type PodConfig struct {
	Name                string                `json:"name" valid:"Required"`
	RestartPolicy       PodRestartPolicy      `json:"restartPolicy" valid:"Required"`
	VPCID               string                `json:"vpcId,omitempty"`
	VPCUUID             string                `json:"vpcUuid,omitempty"`
	CCEID               string                `json:"cceId"`
	SubnetID            string                `json:"subnetId" valid:"Required"`
	SubnetUUID          string                `json:"subnetUuid,omitempty"`
	SecurityGroupID     string                `json:"securityGroupId"`
	ServiceType         ServiceType           `json:"serviceType"`
	PurchaseNum         int                   `json:"purchaseNum" valid:"Min(1)"`
	ProductType         ProductType           `json:"productType" valid:"Required"`
	LogicalZone         string                `json:"logicalZone"`
	Volumes             *Volumes              `json:"volumes"`
	Tags                []PodTag              `json:"tags,omitempty"`
	Labels              []PodLabel            `json:"labels,omitempty"`
	Application         string                `json:"application" valid:"Required"`
	Containers          []Container           `json:"containers" valid:"Required;MinSize(1)"`
	ImageRegistrySecret []ImageRegistrySecret `json:"imageRegistrySecret"`
	Annotations         string                `json:"annotations,omitempty"`
	EnableLog           bool                  `json:"enableLog,omitempty"`
}

type EIPConfig struct {
	Name            string         `json:"name" valid:"Required;MaxSize(65)"`
	Region          string         `json:"region" valid:"Required"`
	ServiceType     ServiceType    `json:"serviceType" valid:"Required"`
	BandwidthInMbps int            `json:"bandwidthInMbps" valid:"Min(1)"`
	PurchaseNum     int            `json:"purchaseNum" valid:"Min(1)"`
	PurchaseLength  int            `json:"purchaseLength,omitempty"`
	SubProductType  SubProductType `json:"subProductType" valid:"Required"`
	ProductType     EIPProductType `json:"productType" valid:"Required"`
}

type Pod struct {
	Name          string           `json:"name"`
	PodID         string           `json:"podId"`
	PodUUID       string           `json:"podUuid"`
	Status        PodStatus        `json:"status"`
	VCPU          float64          `json:"vCpu"`
	MemoryInGB    float64          `json:"memory"`
	CCEID         string           `json:"cceUuid"`
	InternalIP    string           `json:"internalIp"`
	RestartPolicy PodRestartPolicy `json:"restartPolicy"`
	OrderID       string           `json:"orderId"`
	CreatedTime   time.Time        `json:"createdTime"`
	UpdatedTime   time.Time        `json:"updatedTime"`
	DeletedTime   time.Time        `json:"deletedTime"`
	Description   string           `json:"description"`
	Region        string           `json:"region"`
	UserID        string           `json:"userId"`
	ResourceUUID  string           `json:"resourceUuid"`
	TaskStatus    string           `json:"taskStatus"`
	Tags          []PodTag         `json:"tags"`
	Labels        []PodLabel       `json:"labels"`
	SubnetUUID    string           `json:"subnetUuid"`
	*Volumes
	// only available when an eip is bind to the pod
	EIPUUID         string `json:"eipUuid"`
	PublicIP        string `json:"publicIp"`
	BandwidthInMbps int64  `json:"bandwidthInMbps"`
	// only set for v2 bci pod
	V2         bool                  `json:"v2,omitempty"`
	Conditions []corev1.PodCondition `json:"conditions,omitempty"`
}

type PodStatus string

const (
	PodStatusRunning   PodStatus = "Running"
	PodStatusPending   PodStatus = "Pending"
	PodStatusSucceeded PodStatus = "Succeeded"
	PodStatusFailed    PodStatus = "Failed"
	PodStatusCrashed   PodStatus = "Crashed"
	PodStatusUnknown   PodStatus = "Unknown"
)

type PodRestartPolicy string

const (
	RestartPolicyAlways    PodRestartPolicy = "Always"
	RestartPolicyOnFailure PodRestartPolicy = "OnFailure"
	RestartPolicyNever     PodRestartPolicy = "Never"
)

type PodTag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

type PodLabel struct {
	LabelKey   string `json:"labelKey"`
	LabelValue string `json:"labelValue"`
}

const DefaultApplication string = "default"

type SecurityGroup struct {
	ID              string `json:"id"`
	SecurityGroupID string `json:"securityGroupId"`
	UUID            string `json:"uuid"`
	Name            string `json:"name"`
	Description     string `json:"description"`
	TenantID        string `json:"tenantId"`
	AssociateNum    int    `json:"associateNum"`
	VPCID           string `json:"vpcId"`
	VPCShortID      string `json:"vpcShortId"`
	Creator         string `json:"creator"`
}

type VPC struct {
	VPCID            string    `json:"vpcId"`
	ShortID          string    `json:"shortId"`
	Name             string    `json:"name"`
	CIDR             string    `json:"cidr"`
	Status           int       `json:"status"`
	SecurityGroupNum int       `json:"securityGroupNum"`
	SubnetNum        int       `json:"subnetNum"`
	CreateTime       time.Time `json:"createTime"`
	Description      string    `json:"description"`
	DefaultVPC       bool      `json:"defaultVpc"`
}

type Subnet struct {
	Name        string    `json:"name"`
	SubnetID    string    `json:"subnetId"`
	AZ          string    `json:"az"`
	CIDR        string    `json:"cidr"`
	VPCID       string    `json:"vpcId"`
	VPCShortID  string    `json:"vpcShortId"`
	SubnetUUID  string    `json:"subnetUuid"`
	AccountID   string    `json:"accountId"`
	SubnetType  int       `json:"subnetType"`
	Type        int       `json:"type"`
	CreatedTime time.Time `json:"createdTime"`
	UpdatedTime time.Time `json:"updatedTime"`
	Description string    `json:"description"`
	ShortID     string    `json:"shortId"`
	UsedIPs     int       `json:"usedIps"`
	TotalIPs    int       `json:"totalIps"`
}
