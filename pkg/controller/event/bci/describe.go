package bci

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/bce"
	"k8s.io/klog/v2"
)

type DescribePodResponse struct {
	*Pod
	Containers    []Container    `json:"containers"`
	SecurityGroup *SecurityGroup `json:"securityGroup"`
	VPC           *VPC           `json:"vpc"`
	Subnet        *Subnet        `json:"subnet"`
}

func (c *client) DescribePod(ctx context.Context, podID string, accountID string, opt *bce.SignOption) (*DescribePodResponse, error) {
	opt = c.NewSignOption(ctx, accountID)
	klog.Infof("in DescribePod state, start podID is: %s\n", podID)
	if podID == "" {
		return nil, fmt.Errorf("bci pod id cannot be empty")
	}

	req, err := bce.NewRequest("GET", c.GetBciURL("api/logical/bci/v1/pod"+"/"+podID, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	describePodResponse := new(DescribePodResponse)
	err = json.Unmarshal(bodyContent, describePodResponse)
	if err != nil {
		return nil, err
	}
	return describePodResponse, nil
}
