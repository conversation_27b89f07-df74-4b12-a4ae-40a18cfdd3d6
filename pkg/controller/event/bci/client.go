package bci

import (
	"context"
	"strings"
	"time"

	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/bce"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/sts"
)

// Endpoint contains all valid endpoints of Baidu Container Instance.
var Endpoint = map[string]string{
	"gz":   "bci.gz.baidubce.com",
	"qa00": "bci.bce-api.baidu-int.com",
	"qa":   "logic-bci.internal-qasandbox.baidu-int.com:8784",
}

var _ Client = &client{}

//go:generate mockgen -destination ./mock.go -package bci -self_package icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/event/bci
//icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/event/bci Client

// Client - 定义 bci 相关方法
type Client interface {
	SetDebug(bool)

	DescribePod(ctx context.Context, podID string, accountID string, opt *bce.SignOption) (*DescribePodResponse, error)
}

type client struct {
	*bce.Client
	// wrappedByV2 indicates whether client is wrapped in a v2 client.
	wrappedByV2   bool
	servicePasswd string
	stsClient     sts.Interface
	credential    *bce.Credentials
	EndPoint      string
}

// Config contains all options for bci.Client.
type Config struct {
	*bce.Config
	wrappedByV2        bool
	iamEndpoint        string
	stsEndpoint        string
	bciServiceName     string
	bciServiceRoleName string
	servicePasswd      string
}

func NewConfig(config *bce.Config, iamEndpoint, stsEndpoint, bciServiceName, bciServiceRoleName, servicePasswd string,
	wrappedByV2Arg ...bool) *Config {
	wrappedByV2 := false
	if len(wrappedByV2Arg) > 0 {
		wrappedByV2 = wrappedByV2Arg[0]
	}
	return &Config{
		Config:             config,
		iamEndpoint:        iamEndpoint,
		stsEndpoint:        stsEndpoint,
		bciServiceName:     bciServiceName,
		bciServiceRoleName: bciServiceRoleName,
		servicePasswd:      servicePasswd,
		wrappedByV2:        wrappedByV2,
	}
}

func NewClient(config *Config) *client {
	bceClient := bce.NewClient(config.Config)

	return &client{
		Client:      bceClient,
		wrappedByV2: config.wrappedByV2,
		stsClient: sts.NewClient(context.TODO(),
			&bce.Config{
				Endpoint: config.stsEndpoint,
			},
			&bce.Config{
				Endpoint: config.iamEndpoint,
			}, config.bciServiceRoleName, config.bciServiceName, config.servicePasswd,
			time.Hour),
		servicePasswd: config.servicePasswd,
		EndPoint:      config.Endpoint,
	}
}

func (c *client) NewSignOption(ctx context.Context, accountID string) *bce.SignOption {
	return c.stsClient.NewSignOption(ctx, accountID)
}

// GetBciURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *client) GetBciURL(objectKey string, params map[string]string) string {
	host := c.Endpoint

	if host == "" {
		host = Endpoint[c.GetRegion()]
	}

	if host == "" {
		host = "bci." + c.GetRegion() + ".baidubce.com"
	}

	uriPath := objectKey

	if c.wrappedByV2 {
		uriPath = strings.ReplaceAll(uriPath, "bci/v1/", "bci/v2/")
	}

	return c.Client.GetURL(host, uriPath, params)
}
