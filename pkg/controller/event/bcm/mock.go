// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/event/bcm (interfaces: Client)

// Package bcm is a generated GoMock package.
package bcm

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/bce"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// PushEvent mocks base method.
func (m *MockClient) PushEvent(arg0 context.Context, arg1 string, arg2 *Event, arg3 *bce.SignOption) (*PushEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushEvent", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*PushEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PushEvent indicates an expected call of PushEvent.
func (mr *MockClientMockRecorder) PushEvent(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushEvent", reflect.TypeOf((*MockClient)(nil).PushEvent), arg0, arg1, arg2, arg3)
}

// SetDebug mocks base method.
func (m *MockClient) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockClientMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockClient)(nil).SetDebug), arg0)
}
