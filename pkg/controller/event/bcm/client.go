package bcm

import (
	"context"

	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/bce"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/iam"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/util"
	klog "k8s.io/klog/v2"
)

var (
	_ Client = (*client)(nil)

	Endpoint = map[string]string{
		"bj":      "bcm.bj.baidubce.com",
		"gz":      "bcm.gz.baidubce.com",
		"su":      "bcm.su.baidubce.com",
		"hkg":     "bcm.hkg.baidubce.com",
		"fwh":     "bcm.fwh.baidubce.com",
		"bd":      "bcm.bd.baidubce.com",
		"sandbox": "10.169.25.203:8869",
	}
)

/*

 */
//go:generate mockgen -destination ./mock.go -package bcm -self_package icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/event/bcm
//icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/event/bcm Client
type Client interface {
	SetDebug(bool)
	PushEvent(ctx context.Context, accountID string, event *Event, option *bce.SignOption) (*PushEventResponse, error)
}

type client struct {
	*bce.Client
	servicePasswd string
	iamAccessKey  string
	iamSecretKey  string
	iamClient     iam.Interface
	credential    *bce.Credentials
}

type Config struct {
	*bce.Config
	iamEndpoint   string
	servicePasswd string
	iamAccessKey  string
	iamSecretKey  string
}

func NewConfig(config *bce.Config, iamEndpoint, servicePasswd, iamAccessKey, iamSecretKey string) *Config {
	return &Config{
		Config:        config,
		iamEndpoint:   iamEndpoint,
		servicePasswd: servicePasswd,
		iamAccessKey:  iamAccessKey,
		iamSecretKey:  iamSecretKey,
	}
}

func NewClient(config *Config) *client {
	bceClient := bce.NewClient(config.Config)
	return &client{
		Client: bceClient,
		iamClient: iam.NewClient(&bce.Config{
			Endpoint: config.iamEndpoint,
		}),
		servicePasswd: config.servicePasswd,
		iamAccessKey:  config.iamAccessKey,
		iamSecretKey:  config.iamSecretKey}
}

func (c *client) GetBcmURL(objectKey string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoint[c.GetRegion()]
	}
	uriPath := objectKey

	return c.Client.GetURL(host, uriPath, params)
}

func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}

func (c *client) GetCredentials(ctx context.Context) error {
	klog.Infof("event bcm client: iam accessKey is %s, iam secretKey is %s", c.iamAccessKey, c.iamSecretKey)
	c.credential = bce.NewCredentials(c.iamAccessKey, c.iamSecretKey)
	return nil
}

func (c *client) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}
