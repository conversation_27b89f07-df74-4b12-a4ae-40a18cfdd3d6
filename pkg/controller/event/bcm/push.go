package bcm

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/bce"
)

func (c *client) GetEvent(ctx context.Context, accountID string, region string, eventID string, option *bce.SignOption) error {
	option = NewSignOption()
	if c.credential == nil {
		if err := c.GetCredentials(ctx); err != nil {
			return err
		}
	}
	option.Credentials = bce.NewCredentials(c.credential.AccessKeyID, c.credential.SecretAccessKey)
	params := map[string]string{}
	url := fmt.Sprintf("event-api/v1/accounts/%s/services/BCE_BCI/regions/%s/events/%s", accountID, region, eventID)
	req, err := bce.NewRequest("GET", c.GetBcmURL(url, params), nil)
	if err != nil {
		return err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	data, err := resp.GetBodyContent()
	if err != nil {
		return err
	}
	fmt.Println(string(data))
	return nil
}

func (c *client) PushEvent(ctx context.Context, accountID string, event *Event, option *bce.SignOption) (*PushEventResponse, error) {
	option = NewSignOption()
	if c.credential == nil {
		if err := c.GetCredentials(ctx); err != nil {
			return nil, err
		}
	}
	option.Credentials = bce.NewCredentials(c.credential.AccessKeyID, c.credential.SecretAccessKey)
	params := map[string]string{}

	postContent, err := json.Marshal(event)
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("event-api/v1/accounts/%s/services/BCE_BCI/events", accountID)
	req, err := bce.NewRequest("POST", c.GetBcmURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	data, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	pushResp := new(PushEventResponse)
	if err := json.Unmarshal(data, pushResp); err != nil {
		return nil, err
	}
	return pushResp, nil
}
