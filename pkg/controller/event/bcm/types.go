package bcm

const (
	ResourceTypeInstance ResourceType = "INSTANCE"

	EventLevelNotice   EventLevel = "NOTICE"
	EventLevelWarning  EventLevel = "WARNING"
	EventLevelMajor    EventLevel = "MAJOR"
	EventLevelCritical EventLevel = "CRITICAL"

	EventTypeFailed EventType = "Failed"
)

type Event struct {
	Region       string       `json:"region"`
	ResourceID   string       `json:"resourceId"`
	ResourceType ResourceType `json:"resourceType"`
	EventID      string       `json:"eventId"`
	EventType    EventType    `json:"eventType"`
	EventLevel   EventLevel   `json:"eventLevel"`
	EventAlias   EventAlias   `json:"eventAlias"`
	EventAliasEn EventAliasEn `json:"eventAliasEn"`
	Content      string       `json:"content"`
	Timestamp    string       `json:"timestamp"`
	AccountID    string       `json:"-"`
}

type Content struct {
	// Info 为必须字段
	Info string `json:"info"`
	// Advice 为必须字段
	Advice string `json:"advice"`

	// copied from v1.Event
	Reason    string `json:"reason"`
	Message   string `json:"message"`
	FieldPath string `json:"fieldPath,omitempty"`
}

type ResourceType string
type EventType string
type EventLevel string
type EventAlias string
type EventAliasEn string

type PushEventResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	EventID string `json:"eventId"`
}
