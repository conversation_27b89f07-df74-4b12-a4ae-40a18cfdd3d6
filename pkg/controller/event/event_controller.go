package event

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/go-cmp/cmp"
	uuid "github.com/satori/go.uuid"
	"golang.org/x/sync/singleflight"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/client-go/informers"
	corev1_listers "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/workqueue"
	"k8s.io/klog/v2"
	controllercache "sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/manager"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/event/bci"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/controller/event/bcm"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/bce"
)

var eventLevels = map[string]bcm.EventLevel{
	corev1.EventTypeWarning: bcm.EventLevelWarning,
	corev1.EventTypeNormal:  bcm.EventLevelNotice,
}

// bci自定义event reason
var bciCustomizeEventReason map[string]struct{}

func init() {
	bciCustomizeEventReason = make(map[string]struct{})
	bciCustomizeEventReason["BciSidecarStartError"] = struct{}{}
	bciCustomizeEventReason["BiddingPodToBeReleased"] = struct{}{}
	bciCustomizeEventReason["TidalPodToBeReleased"] = struct{}{}
}

const (
	BCIInternalPrefix                   = "bci_internal_"
	BCIInternalPrefix2                  = "bci-internal-"
	BCIKubeProxyPrefix                  = "kube-proxy"
	BCIImageDownloadInitContainerPrefix = "bci-internal-image-download-init-container-"
	BCINfsSidecarContainerPrefix        = "bci-internal-nfs-sidecar-container-"
	BCILogSidecarContainerPrefix        = "bci-internal-log-sidecar-container-"
	BCIImageDownloadInitInfix           = "image-init-"
	BCIImageDownloadWorkloadInfix       = "image-workload-"
)

type Controller struct {
	enabled           bool
	informerFactory   informers.SharedInformerFactory
	eventInformer     controllercache.Informer
	configMapInformer controllercache.Informer
	apiServerClient   client.Client
	eventQ            workqueue.Interface
	pushQ             workqueue.Interface
	region            string
	workers           int
	pushers           int
	sync.RWMutex
	bciPods                  map[string]*bci.DescribePodResponse
	requestGroup             singleflight.Group
	requestTokens            chan struct{}
	bciClient                bci.Client
	bcmClient                bcm.Client
	nsOfLastEventTimestamp   string
	nameOfLastEventTimestamp string
	lastEventTimestamp       int64
}

type Event struct {
	ref       *corev1.ObjectReference
	reason    string
	message   string
	timestamp metav1.Time
	level     bcm.EventLevel
}

//func New(option *options.ServerRunOptions, mgr manager.Manager) (*Controller, error) {
func New(option *options.ServerRunOptions, mgr manager.Manager) (interface{}, error) {
	kubeClient := mgr.GetClient()
	bciClient := bci.NewClient(bci.NewConfig(&bce.Config{
		Timeout:     60 * time.Second,
		RetryPolicy: bce.NewDefaultRetryPolicy(0, 0),
		Protocol:    "http",
		Endpoint:    option.EventOptions.BciEndpoint,
	}, option.EventOptions.IamEndpoint, option.EventOptions.StsEndpoint, option.EventOptions.BciServiceName,
		option.EventOptions.BciServiceRoleName, option.EventOptions.BciServicePasswd, true))
	bcmClient := bcm.NewClient(bcm.NewConfig(&bce.Config{
		Timeout:     60 * time.Second,
		RetryPolicy: bce.NewDefaultRetryPolicy(0, 0),
		Protocol:    "http",
		Endpoint:    option.EventOptions.BcmPushEndpoint,
		Region:      option.EventOptions.Region,
	}, option.EventOptions.IamEndpoint, option.EventOptions.BciServicePasswd,
		option.BciIamAccessKey, option.BciIamSecretKey))
	eventQ := workqueue.NewNamed("eventQ")
	pushQ := workqueue.NewNamed("pushQ")
	eventInformer, err := mgr.GetCache().GetInformer(context.TODO(), &corev1.Event{})
	if err != nil {
		return nil, fmt.Errorf("get pod informer of cce clusters failed, error is %w", err)
	}
	configMapInformer, err := mgr.GetCache().GetInformer(context.TODO(), &corev1.ConfigMap{})
	if err != nil {
		return nil, fmt.Errorf("get informer of lastSyncTimestamp failed, error is %w", err)
	}

	klog.Infof("workers is %d, pushers is %d", option.EventOptions.Workers, option.EventOptions.Pushers)
	return &Controller{
		enabled:                  true,
		informerFactory:          nil,
		eventInformer:            eventInformer,
		configMapInformer:        configMapInformer,
		apiServerClient:          kubeClient,
		eventQ:                   eventQ,
		pushQ:                    pushQ,
		region:                   option.EventOptions.Region,
		workers:                  option.EventOptions.Workers,
		pushers:                  option.EventOptions.Pushers,
		RWMutex:                  sync.RWMutex{},
		bciPods:                  make(map[string]*bci.DescribePodResponse),
		requestGroup:             singleflight.Group{},
		requestTokens:            make(chan struct{}, 100),
		bciClient:                bciClient,
		bcmClient:                bcmClient,
		nsOfLastEventTimestamp:   option.EventOptions.NsOfLastEventTimestamp,
		nameOfLastEventTimestamp: option.EventOptions.NameOfLastEventTimestamp,
	}, nil
}

func (c *Controller) Start(stopCh <-chan struct{}) error {
	defer utilruntime.HandleCrash()
	// if controller is not enabled, just skip start
	if !c.enabled {
		klog.V(2).Info("event-sync controller is not enabled, just skip startting.")
		return nil
	}
	klog.V(2).Info("start event-sync controller")
	if ok := cache.WaitForCacheSync(stopCh, c.eventInformer.HasSynced); !ok {
		return fmt.Errorf("failed to wait for caches to sync")
	}
	if ok := cache.WaitForCacheSync(stopCh, c.configMapInformer.HasSynced); !ok {
		return fmt.Errorf("failed to wait for caches to sync")
	}
	err := c.Run(context.Background())
	if err != nil {
		return fmt.Errorf("event controller failed, error is %w", err)
	}
	<-stopCh
	klog.V(2).Info("Shutting down event controller")
	return nil
}

func (c *Controller) filterEvent(event *corev1.Event) bool {
	if !strings.HasPrefix(event.InvolvedObject.Name, "p-") {
		return false
	}
	if event.LastTimestamp.Time.Unix() < c.lastEventTimestamp {
		return false
	}
	if event.Reason == "Unhealthy" && strings.Contains(event.Message, "probe failed") {
		return true
	}
	if (event.Reason == "Pulled" || event.Reason == "Pulling" || event.Reason == "Failed") && (strings.Contains(event.Message, "image")) {
		return true
	}
	if (event.Reason == "Created" || event.Reason == "Started" || event.Reason == "Crashed") && strings.Contains(event.Message, "container") {
		return true
	}
	if event.Reason == "BackOff" && event.Message == "Back-off restarting failed container" {
		return true
	}
	if event.Reason == "FailedPostStartHook" || event.Reason == "FailedPreStopHook" {
		return true
	}
	if event.Reason == "Killing" && strings.Contains(event.Message, "container") {
		return true
	}
	// sidecar、潮汐资源回收等 event 需要推给用户
	if _, ok := bciCustomizeEventReason[event.Reason]; ok {
		return true
	}
	return false
}

func (c *Controller) Run(ctx context.Context) error {
	eventInformer := c.eventInformer
	_, lastEventTimestamp, err := c.getLastEventTimestampFromApiserver()
	if err != nil {
		return fmt.Errorf("failed to initialize lastEventTimestamp, error is %w", err)
	}
	c.lastEventTimestamp = lastEventTimestamp

	klog.Info("Event cache in-sync")

	var eventHandler cache.ResourceEventHandler = cache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			event := obj.(*corev1.Event)
			// TODO: filter event by reason and ref
			if c.filterEvent(event) {
				klog.V(4).Infof("get event to push for pod %s, account %s, filedPath %s reason %s message %s firstTimestamp %s count %d  type %s",
					event.InvolvedObject.DeepCopy().Name, event.InvolvedObject.DeepCopy().Namespace, event.InvolvedObject.DeepCopy().FieldPath,
					event.Reason, event.Message, event.FirstTimestamp, event.Count, event.Type)
				c.eventQ.Add(&Event{
					ref:       event.InvolvedObject.DeepCopy(),
					reason:    event.Reason,
					message:   event.Message,
					level:     eventLevels[event.Type],
					timestamp: event.FirstTimestamp,
				})
			}
		},
		UpdateFunc: func(oldObj, newObj interface{}) {
			oldEvent := oldObj.(*corev1.Event)
			newEvent := newObj.(*corev1.Event)

			// TODO: filter event by reason and ref

			filterNewEvent := true
			// 对其他事件应用原有的过滤逻辑
			if cmp.Equal(oldEvent, newEvent, cmp.FilterPath(func(p cmp.Path) bool {
				switch p.String() {
				// Only following fields are concerned.
				case "LastTimestamp", "Count", "Message", "Reason", "InvolvedObject.UID":
					return false
				}
				return true

			}, cmp.Ignore())) {
				klog.V(4).Infof("ignore update for %s/%s", oldEvent.Namespace, oldEvent.Name)
				filterNewEvent = false
			}

			if filterNewEvent && c.filterEvent(newEvent) {
				c.eventQ.Add(&Event{
					ref:       newEvent.InvolvedObject.DeepCopy(),
					reason:    newEvent.Reason,
					message:   newEvent.Message,
					level:     eventLevels[newEvent.Type],
					timestamp: newEvent.LastTimestamp,
				})
				klog.V(4).Infof("In update state, success filterEvent to push, eventQ size %d "+
					"new eventInfo [lastTimestamp %s count %d reason %s message %s eventName %s eventPodName %s], "+
					"old eventInfo [lastTimestamp %s count %d reason %s message %s eventName %s eventPodName %s]", c.eventQ.Len(),
					newEvent.LastTimestamp.String(), newEvent.Count, newEvent.Reason, newEvent.Message, newEvent.Name, newEvent.InvolvedObject.Name,
					oldEvent.LastTimestamp.String(), oldEvent.Count, oldEvent.Reason, oldEvent.Message, oldEvent.Name, oldEvent.InvolvedObject.Name)
			}
		},
		DeleteFunc: func(obj interface{}) {},
	}

	eventInformer.AddEventHandler(eventHandler)

	var wg sync.WaitGroup
	for i := 0; i < c.workers; i++ {
		wg.Add(1)
		go func(workerIndex int) {
			defer wg.Done()
			c.runWorker(ctx, workerIndex)
		}(i)
	}

	for i := 0; i < c.pushers; i++ {
		wg.Add(1)
		go func(pusherIndex int) {
			defer wg.Done()
			c.runPusher(ctx, pusherIndex)
		}(i)
	}

	<-ctx.Done()
	c.eventQ.ShutDown()
	c.pushQ.ShutDown()
	wg.Wait()
	return nil
}

func (c *Controller) getLastEventTimestampFromApiserver() (*corev1.ConfigMap, int64, error) {
	configmapLister := corev1_listers.NewConfigMapLister(c.configMapInformer.(cache.SharedIndexInformer).GetIndexer())
	configmap, err := configmapLister.ConfigMaps("kube-system").Get("last-event-timestamp")
	if err != nil {
		return nil, 0, fmt.Errorf("failed to initialize lastEventTimestamp, error is %w", err)
	}

	lastSyncTimestampStrInStore, exist := configmap.Data["LastEventTimestamp"]
	if !exist {
		return nil, 0, fmt.Errorf("get LastSyncTimestamp failed, LastSyncTimestamp not exist in configmap")
	}
	lastSyncTimestampInStore, err := strconv.ParseInt(lastSyncTimestampStrInStore, 10, 64)
	if err != nil {
		return nil, 0, fmt.Errorf("parse LastSyncTimestampString from configmap failed, error msg is %w", err)
	}
	klog.V(4).Infof("success get lastSyncTimestampInStore, value is %d", lastSyncTimestampInStore)
	return configmap, lastSyncTimestampInStore, nil
}

func (c *Controller) updateLastEventTimestamp(timestamp int64) error {
	// step1. update lastSyncTimestamp in configmap
	configmap, lastSyncTimestampInStore, err := c.getLastEventTimestampFromApiserver()
	if err != nil {
		return err
	}
	var patchErr error
	if lastSyncTimestampInStore != timestamp {
		patchData, _ := json.Marshal(&map[string]map[string]string{
			"data": {"LastEventTimestamp": fmt.Sprintf("%d", timestamp)},
		})
		for i := 0; i < 3; i++ {
			patchErr = c.apiServerClient.Patch(context.TODO(), configmap, client.RawPatch(types.StrategicMergePatchType, []byte(patchData)))
			if patchErr == nil {
				break
			}
			klog.Errorf("patch lastSyncTimestamp to configmap failed, err is %+v", patchErr)
		}
	}
	if patchErr != nil {
		return patchErr
	}

	// step2. update it in memory
	originLastEventTimestamp := c.lastEventTimestamp
	c.lastEventTimestamp = int64(timestamp)
	klog.Infof("success update LastEventTimestamp from %d to %d", originLastEventTimestamp, c.lastEventTimestamp)
	return nil
}

func (c *Controller) runPusher(ctx context.Context, pusherIndex int) {
	// TODO: implement batch pushing by user
	for {
		obj, shutdown := c.pushQ.Get()
		if shutdown {
			return
		}
		event := obj.(*bcm.Event)
		klog.Infof("start pushing event [id: %s, region: %s, content: %s, timeStamp: %s] to bcm, currentTime %s",
			event.EventID, event.Region, event.Content, event.Timestamp, time.Now().UTC().Format(time.RFC3339))
		parseTime, _ := time.ParseInLocation("2006-01-02T15:04:05Z07:00", event.Timestamp, time.Local)
		if parseTime.Unix() < c.lastEventTimestamp {
			klog.V(4).Infof("eventTimestamp %s parseTime  %d less c.lastEventTimestamp %d", event.Timestamp, parseTime.Unix(), c.lastEventTimestamp)
			continue
		}
		if resp, err := c.bcmClient.PushEvent(ctx, event.AccountID, event, nil); err != nil {
			klog.Errorf("fail to push event %s to bcm: %v", event.EventID, err)
			// TODO: retry policy
		} else {
			klog.Infof("accountID %s eventInfo: [id %s, region %s, content %s timestamp %s] pushed, message %s, code %d",
				event.AccountID, event.EventID, event.Region, event.Content, event.Timestamp, resp.Message, resp.Code)
			// 更新lastEventTime
			if (parseTime.Unix() - c.lastEventTimestamp) > 30 {
				err := c.updateLastEventTimestamp(parseTime.Unix())
				if err != nil {
					klog.Infof("updateLastEventTimestamp %d err %s", parseTime, err.Error())
				}

			}
		}
	}
}

func handleImageInitContainerEvent(event *Event, containerName string) bool {
	if event.reason == "Pulled" || event.reason == "Pulling" || event.reason == "Started" || event.reason == "Created" {
		return false
	}
	if event.reason == "Failed" && strings.Contains(event.message, "Failed to pull image") {
		return false
	}
	if event.reason == "BackOff" && event.message == "Back-off restarting failed container" {
		event.reason = "Failed"
		if strings.Contains(containerName, BCIImageDownloadInitInfix) {
			subList := strings.Split(containerName, BCIImageDownloadInitInfix)
			if len(subList) != 2 {
				event.message = "failed to pull image of containerName"
			}
			event.message = "failed to pull image of " + subList[1]
		} else if strings.Contains(containerName, BCIImageDownloadWorkloadInfix) {
			subList := strings.Split(containerName, BCIImageDownloadWorkloadInfix)
			if len(subList) != 2 {
				event.message = "failed to pull image of containerName"
			}
			event.message = "failed to pull image of " + subList[1]
		} else {
			event.message = "failed to pull image of containerName"
		}
	}
	return true
}

func (c *Controller) runWorker(ctx context.Context, workerIndex int) {
	klog.V(4).Infof("in runWorker start event len:  %d", c.eventQ.Len())
	// 暂时关闭。而是采取关键字模式匹配的方式。
	needDescribePod := false
	for {
		obj, shutdown := c.eventQ.Get()
		if shutdown {
			return
		}
		event := obj.(*Event)

		podID := event.ref.Name // ensure
		containerName := ""
		accountID := event.ref.Namespace
		if _, ok := bciCustomizeEventReason[event.reason]; !ok {
			containerName, err := getContainerName(event.ref)
			if err != nil {
				klog.Errorf("cannot get containerName from ref for pod %s: %+v", podID, err)
				continue
			}
			if strings.HasPrefix(containerName, BCIKubeProxyPrefix) {
				continue
			}
			// 过滤掉nfs和sidecar的event
			if strings.HasPrefix(containerName, BCIInternalPrefix) {
				continue
			}

			if strings.HasPrefix(containerName, BCIInternalPrefix2) && !strings.HasPrefix(containerName, BCIImageDownloadInitContainerPrefix) &&
				(event.message != "nfs mount failed") {
				continue
			}

			if strings.HasPrefix(containerName, BCIImageDownloadInitContainerPrefix) && !handleImageInitContainerEvent(event, containerName) {
				continue
			}
		}
		err := func() error {
			if needDescribePod {
				c.RLock()
				bciPod, ok := c.bciPods[podID]
				c.RUnlock()
				if !ok {
					v, err, shared := c.requestGroup.Do(podID, func() (interface{}, error) {
						c.requestTokens <- struct{}{}
						defer func() {
							<-c.requestTokens
						}()
						return c.bciClient.DescribePod(ctx, podID, accountID, nil) // TODO: auth
					})
					klog.Infof("describe pod %s with shared=%v", podID, shared)

					if err != nil {
						klog.Errorf("fail to describe pod %s: %v", podID, err)
						return err
					}

					bciPod = v.(*bci.DescribePodResponse)
					c.Lock()
					c.bciPods[podID] = bciPod
					c.Unlock()
				}

				if bciPod == nil {
					return fmt.Errorf("bci pod is nil for podID=%s", podID)
				}

				isUserContainer := false
				for _, c := range bciPod.Containers {
					if c.Name == containerName {
						isUserContainer = true
						break
					}
				}
				if !isUserContainer {
					klog.Infof("ignore event of system container %s in pod %s", containerName, podID)
					return nil
				}
			}

			content := &bcm.Content{
				Info:      event.reason,
				Advice:    "string", // TBD
				Reason:    event.reason,
				Message:   event.message,
				FieldPath: event.ref.FieldPath,
			}
			contentBytes, err := json.Marshal(content)
			if err != nil {
				return fmt.Errorf("fail to marshal event content %+v: %w", content, err)
			}

			var eventType bcm.EventType
			var eventAlias bcm.EventAlias

			if string(event.level) == strings.ToUpper(corev1.EventTypeWarning) {
				eventType = "BCIWarning"
				eventAlias = "警告事件"
			} else {
				eventType = "BCINotice"
				eventAlias = "通知事件"
			}
			eventID := podID + "-" + uuid.NewV4().String()
			c.pushQ.Add(&bcm.Event{
				Region:       c.region,
				ResourceID:   podID,
				ResourceType: bcm.ResourceTypeInstance,
				EventID:      eventID,   // TBD
				EventType:    eventType, // TBD
				EventLevel:   event.level,
				EventAlias:   eventAlias, // TBD
				Content:      string(contentBytes),
				Timestamp:    event.timestamp.Time.UTC().Format(time.RFC3339),
				AccountID:    accountID,
			})
			klog.Infof("in runWorker state, add [accountID: %s, podID: %s, containerName: %s], "+
				"eventInfo [eventId %s, eventLevel: %s, eventContent: %s, eventTimestamp %s] to pushQ", accountID, podID,
				containerName, eventID, event.level, string(contentBytes), event.timestamp.Time.UTC().Format(time.RFC3339))

			return nil
		}()

		if err != nil {
			klog.Error(err.Error())
			// TODO: retry policy
		}
	}
}

// getContainerName get container name from fieldPath of pod reference.
// FieldPath examples:
// - spec.containers{container01}
// - spec.initContainers{container01}
func getContainerName(ref *corev1.ObjectReference) (string, error) {
	if i := strings.Index(ref.FieldPath, "{"); i >= 0 && i+1 < len(ref.FieldPath)-1 {
		return ref.FieldPath[i+1 : (len(ref.FieldPath) - 1)], nil
	}
	return "", fmt.Errorf("cannot get containerName from fieldPath %s", ref.FieldPath)
}
