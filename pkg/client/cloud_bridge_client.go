package client

import (
	"bytes"
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/bce-sdk-go/util"
)

// CloudBridgeEndpoints 包含所有地域的 endpoints
var CloudBridgeEndpoints = map[string]string{
	"bj":      "settings.bce-internal.baidu.com/v1",
	"gz":      "settings.bce-internal.baidu.com/v1",
	"su":      "settings.bce-internal.baidu.com/v1",
	"hkg":     "settings.bce-internal.baidu.com/v1",
	"fwh":     "settings.bce-internal.baidu.com/v1",
	"bd":      "settings.bce-internal.baidu.com/v1",
	"sandbox": "10.107.37.48:8690/v1",
}

// QuotaRequest - 查询用户配额请求参数
type QuotaRequest struct {
	UserType   UserType    `json:"userType"`
	UserValue  string      `json:"userValue"`
	QuotaTypes []QuotaType `json:"quotaTypes"`
}

// QuotaResponse - 查询用户配额返回
type QuotaResponse struct {
	QuotaType2Quota map[QuotaType]string `json:"quotaType2quota"`
}

// UserType 用户类型
type UserType string

const (
	// UserTypeAccountID 用户类型 AccountId
	UserTypeAccountID UserType = "AccountId"
	// UserTypeAK 用户类型 Ak
	UserTypeAK UserType = "Ak"
)

// QuotaType 配额类型
type QuotaType string

// CloudBridgeClient is the bos client implemention for Baidu Cloud BOS API.
type CloudBridgeClient struct {
	*bce.Client
}

// NewClient 初始化 Client
func NewCloudBridgeClient(config *bce.Config) *CloudBridgeClient {
	return &CloudBridgeClient{
		Client: bce.NewClient(config),
	}
}

// GetURL 返回完成的 URL 链接
func (c *CloudBridgeClient) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = CloudBridgeEndpoints[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// SetDebug 开启或关闭 Debug 模式
func (c *CloudBridgeClient) SetDebug(debug bool) {
	c.Client.SetDebug(debug)
}

// NewSignOption return BLB specified sign option
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}

// GetUserQuotas - 获取用户的各种资源的配额
func (c *CloudBridgeClient) GetUserQuotas(ctx context.Context, args *QuotaRequest, option *bce.SignOption) (*QuotaResponse, error) {
	params := map[string]string{}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("settings/quota/list", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var response *QuotaResponse
	err = json.Unmarshal(bodyContent, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}
