package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// 竞价实例node event client
type BidEventClient struct {
	BccClient *bcc.Client
}

type GetBidEventsRequest struct {
	InstanceUUIDs []string `json:"instanceUuids"`
}

type GetBidEventsResponse struct {
	BidEvents []BidEvent `json:"events"`
}

type BidEvent struct {
	InstanceID   string         `json:"instanceId"`
	InstanceUUID string         `json:"instanceUuid"`
	EventTime    time.Time      `json:"eventTime"`
	Action       BidEventAction `json:"action"`
}

type BidEventAction string

const (
	// GetBidEvents 接口限制一次查询最多 100 事件
	GetBidEventsLimit = 100

	BidEventActionDelete BidEventAction = "delete"
)

func (c *BidEventClient) GetBidEvents(ctx context.Context, getReq *GetBidEventsRequest, option *bce.SignOption) (*GetBidEventsResponse, error) {
	params := map[string]string{
		"clientToken": c.BccClient.GenerateClientToken(),
	}

	if getReq == nil {
		return nil, fmt.Errorf("args is nil")
	}

	postContent, err := json.Marshal(getReq)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.BccClient.GetURL("api/logical/bcc/v1/instance/bid/events", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.BccClient.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(GetBidEventsResponse)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
