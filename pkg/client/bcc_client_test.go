package client

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/sts"
)

func TestBCCBidEvent(t *testing.T) {

	if true {
		return
	}
	region := "gz"
	iamRoleName := "BceServiceRole_bci"
	iamUserName := "bci"
	iamPassword := "IreriJJDE4XS8AsFBHdkXkBiBxEqWnrK"
	accountID := "eca97e148cb74e9683d7b7240829d1ff"
	// accountID := "b9ca12ddf6064c5eab7961d32496d564"

	eniHexKey := "WG8ugAEcU726q90B"
	stsClient := sts.NewClient(context.Background(), &bce.Config{
		Endpoint: sts.Endpoints[region],
		Checksum: true,
		Timeout:  60 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: iam.Endpoints[region],
		Checksum: true,
		Timeout:  60 * time.Second,
		Region:   region,
	}, iamRoleName, iamUserName, iamPassword)

	buildSignOptionFn := func(accountID string) *bce.SignOption {
		return stsClient.NewSignOptionWithResourceHeader(context.Background(), accountID,
			eniHexKey, accountID, "bci")
	}

	bceConfig := &bce.Config{
		Checksum: true,
		Timeout:  60 * time.Second,
		Endpoint: eni.Endpoint[region],
	}
	client := bcc.NewClient(bceConfig)

	bidClient := &BidEventClient{
		BccClient: client,
	}

	// nodeID := "i-k0O0B8IN"
	req := &GetBidEventsRequest{
		// "i-OwRW7rby"
		InstanceUUIDs: []string{},
	}
	resp, err := bidClient.GetBidEvents(context.Background(), req, buildSignOptionFn(accountID))
	if err != nil {
		fmt.Println(err)
	}
	bytes, err := json.Marshal(resp)
	if err != nil {
		panic(err)
	}
	fmt.Println(resp)
	fmt.Println(string(bytes))
}
