package client

import (
	"fmt"
	"testing"
	"time"
)

func TestApiClient(t *testing.T) {
	client := NewAPIGoCliect("apiGoAk-683d1e06ffff478aba5d8a132c4c9d49-online", "apiGoSk-ee15560388024120bf2333b34315cd90-online")
	// resp,err := client.GetHolidays()
	// if err != nil {
	// 	panic(err)
	// }
	// data, _ := json.Marshal(resp)
	// fmt.Println(string(data))

	fmt.Println(time.Now())
	fmt.Println(time.Local.String())

	var cstSh, _ = time.LoadLocation("Asia/Shanghai")
	fmt.Println(time.Now().In(cstSh))

	holiday, timeoff, err := client.BuildHolidayAndTimeOffDayMap()
	if err != nil {
		panic(err)
	}
	fmt.Println(holiday)
	fmt.Println(timeoff)

	result := timeoff.Contains(2023, 10, 8)
	fmt.Println(result)
}
