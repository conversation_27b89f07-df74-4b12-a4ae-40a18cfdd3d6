package client

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"k8s.io/klog/v2"
)

const (
	HmacAccessKey        = "x-hmac-access-key"
	HmacSignature        = "x-hmac-signature"
	HmacRandom           = "hmac-random"
	SignHeaderTemplate   = HmacRandom + ":%s"
	HmacAuthSignTemplate = "%s\n%s\n%s\n%s\n%s\n%s\n"
	EncodeURIParam       = "encode_uri_param"
	Date                 = "Date"
)

var letters = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")

type HolidayResp struct {
	Code      int           `json:"code"`
	Msg       string        `json:"msg"`
	TracingID string        `json:"tracingId"`
	Result    []HolidayItem `json:"result"`
}

type HolidayItem struct {
	ID              int    `json:"id"`
	HolidayDate     string `json:"holidayDate"`
	HolidayDaysName string `json:"holidayDaysName"`
	Language        string `json:"language"`
}

type HolidayList struct {
	Dates []string
}

func (days *HolidayList) Contains(year, month, day int) bool {
	cstSh, _ := time.LoadLocation("Asia/Shanghai")
	for _, date := range days.Dates {
		t, err := time.ParseInLocation("2006-01-02", date, cstSh)
		if err != nil {
			klog.Errorf("error parsing day %s to time", day)
			continue
		}

		if t.Year() == year && int(t.Month()) == month && t.Day() == day {
			return true
		}
	}
	return false
}

type APIGoClient struct {
	AppKey    string
	SecretKey string
}

func NewAPIGoCliect(appKey, secretKey string) *APIGoClient {
	return &APIGoClient{
		AppKey:    appKey,
		SecretKey: secretKey,
	}
}

// 根据年份获取全部假日信息
func (c *APIGoClient) GetHolidays() (*HolidayResp, error) {
	currentYear := time.Now().Year()
	url := "https://apigo.baidu-int.com/open/api/holiday/getHolidayByYear?year=" + strconv.Itoa(currentYear)

	client := &http.Client{}
	request, err := http.NewRequest("POST", url, nil)

	if err != nil {
		return nil, err
	}
	hMacSign(c.AppKey, c.SecretKey, request)

	resp, err := client.Do(request)
	if err != nil {
		klog.Errorf("GetHolidayByYear error: %s", err)
		return nil, err
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		klog.Errorf("GetHolidayByYear Read resp error: %s", err)
		return nil, err
	}

	result := &HolidayResp{}
	err = json.Unmarshal(body, result)
	return result, err
}

func (c *APIGoClient) BuildHolidayAndTimeOffDayMap() (holiday *HolidayList, timeoffDay *HolidayList, err error) {
	result, err := c.GetHolidays()
	if err != nil {
		return
	}
	if result.Code != 200 {
		err := fmt.Errorf("GetHolidays error: %s,tracingId %s ", result.Msg, result.TracingID)
		klog.Error(err)
		return nil, nil, err
	}
	holiday = &HolidayList{}
	timeoffDay = &HolidayList{}
	for _, item := range result.Result {
		if strings.Contains(item.HolidayDaysName, "工作日") || strings.HasPrefix(item.HolidayDaysName, "1_") {
			// 调休
			timeoffDay.Dates = append(timeoffDay.Dates, item.HolidayDate)
			continue
		}
		holiday.Dates = append(holiday.Dates, item.HolidayDate)
	}
	return
}

// HMacSign 计算Hmac的AK/SK签名
func hMacSign(appKey string, secretKey string, request *http.Request) {

	uri := request.URL.Path
	query := request.URL.Query()
	method := request.Method

	// 请求Url参数排序生成字符串
	queryString := genCanonicalQueryString(query)

	// 获取当前的GMT时间
	date := getGmtTime()

	// 生成随机访问字符串
	random := randString(32)

	// 生成签名
	signedHeaderString := fmt.Sprintf(SignHeaderTemplate, random)
	beforeHashStr := fmt.Sprintf(HmacAuthSignTemplate, method, uri, queryString, appKey, date, signedHeaderString)
	hmacSha256 := hmac.New(sha256.New, []byte(secretKey))
	hmacSha256.Write([]byte(beforeHashStr))
	sign := base64.StdEncoding.EncodeToString(hmacSha256.Sum(nil))

	// 请求的Header里面增加鉴权类参数
	request.Header.Add(EncodeURIParam, "false")
	request.Header.Add(Date, date)
	request.Header.Add(HmacAccessKey, appKey)
	request.Header.Add(HmacSignature, sign)
	request.Header.Add(HmacRandom, random)
}

// GenCanonicalQueryString 拼接传递的参数
func genCanonicalQueryString(queryParams map[string][]string) string {
	var keys []string
	for key := range queryParams {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	paramsBuilder := strings.Builder{}
	for _, key := range keys {
		values := queryParams[key]
		sort.Strings(values)
		for _, value := range values {
			if 0 != paramsBuilder.Len() {
				paramsBuilder.WriteString("&")
			}
			paramsBuilder.WriteString(key + "=" + value)
		}
	}
	return paramsBuilder.String()
}

// 获取GMT时间
func getGmtTime() string {
	gmtTimeLoc := time.FixedZone("GMT", 0)
	return time.Now().In(gmtTimeLoc).Format(http.TimeFormat)
}

// randString 生成随机数
func randString(n int) string {
	rand.Seed(time.Now().UnixNano())
	b := make([]rune, n)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}
