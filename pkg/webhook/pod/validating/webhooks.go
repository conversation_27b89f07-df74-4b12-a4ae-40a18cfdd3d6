package validating

import "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime"

// +kubebuilder:webhook:path=/validate-pod,mutating=false,failurePolicy=fail,sideEffects=None,admissionReviewVersions=v1;v1beta1,groups="",resources=pods,verbs=create;update;delete,versions=v1,name=vpod.kb.io
// +kubebuilder:webhook:path=/validate-pod,mutating=false,failurePolicy=fail,sideEffects=None,admissionReviewVersions=v1;v1beta1,groups="",resources=pods/eviction,verbs=create,versions=v1,name=vpodeviction.kb.io

var (
	// HandlerMap contains admission webhook handlers
	HandlerMap = map[string]runtime.NewWebhookServer{
		"validate-pod": newPodCreateHandler,
	}
)
