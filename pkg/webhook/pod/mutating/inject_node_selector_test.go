package mutating

import (
	"fmt"
	"sort"
	"strings"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
)

func BuildPodWithContainers(namespace, name string, annotations map[string]string, label map[string]string, containers []v1.Container) *v1.Pod {
	return &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			UID:         types.UID(fmt.Sprintf("%v-%v", namespace, name)),
			Name:        name,
			Namespace:   namespace,
			Annotations: annotations,
			Labels: 	 label,
		},
		Spec: v1.PodSpec{
			NodeSelector: map[string]string{},
			Containers:   containers,
		},
	}
}
func BuildResourceList(cpu string, memory string) v1.ResourceList {
	return v1.ResourceList{
		entity.A1024GCGPU: resource.MustParse("1"),
		v1.ResourceCPU:    resource.MustParse(cpu),
		v1.ResourceMemory: resource.MustParse(memory),
	}
}
func TestInjectNodeSelector(t *testing.T) {

	// 测试bci pod
	tests := []struct {
		name                             string
		pod                              *v1.Pod
		getInstanceGroupConfig           func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error)
		getInstanceGroupAccountWhiteList func() ([]string, error)
		isImageCachePodUseUserNode       func() bool
		injectSuccess                    bool
		instancegroup                    []string
	}{
		{
			name: "account 未传",
			pod: BuildPodWithContainers("default", "pod1", map[string]string{}, map[string]string{}, []v1.Container{
				{
					Name: "nginx",
					Resources: v1.ResourceRequirements{
						Requests: BuildResourceList("1", "1G"),
						Limits:   BuildResourceList("1", "1G"),
					},
				},
			}),
			getInstanceGroupAccountWhiteList: func() ([]string, error) {
				return []string{}, nil
			},
			isImageCachePodUseUserNode: func() bool {
				return false
			},
			injectSuccess: false,
		},
		{
			name: "完整匹配",
			pod: BuildPodWithContainers("default", "pod1", map[string]string{entity.BciAccountKey: "account1", bciZoneKey: "zoneA"},
				map[string]string{}, []v1.Container{
				{
					Name: "nginx",
					Resources: v1.ResourceRequirements{
						Requests: BuildResourceList("0.25", "1G"),
						Limits:   BuildResourceList("0.25", "1G"),
					},
				},
			}),
			getInstanceGroupConfig: func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
				result := make([]entity.InstanceGroupCm, 0)
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-cpu",
					InstanceGroupId:   "zoneA-ig0.25c1G-cpu",
					MatchResources: []entity.InstanceGroupResource{
						{
							CPU:    "0.25",
							Memory: "1G",
						},
					},
				})
				return result, nil, nil
			},
			getInstanceGroupAccountWhiteList: func() ([]string, error) {
				return []string{}, nil
			},
			isImageCachePodUseUserNode: func() bool {
				return false
			},
			injectSuccess: true,
			instancegroup: []string{"zoneA-ig0.25c1G-cpu"},
		},
		{
			name: "完整匹配-gpu",
			pod: BuildPodWithContainers("default", "pod1", map[string]string{entity.BciAccountKey: "account1", bciZoneKey: "zoneA"},
				map[string]string{}, []v1.Container{
				{
					Name: "nginx",
					Resources: v1.ResourceRequirements{
						Requests: BuildResourceList("0.25", "1G"),
						Limits:   BuildResourceList("0.25", "1G"),
					},
				},
			}),
			getInstanceGroupConfig: func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
				result := make([]entity.InstanceGroupCm, 0)
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-gpu",
					InstanceGroupId:   "zoneA-ig0.25c1G-gpu",
					MatchResources: []entity.InstanceGroupResource{
						{
							A1024G: "1",
							CPU:    "0.25",
							Memory: "1G",
						},
					},
				})
				return result, nil, nil
			},
			getInstanceGroupAccountWhiteList: func() ([]string, error) {
				return []string{}, nil
			},
			isImageCachePodUseUserNode: func() bool {
				return false
			},
			injectSuccess: true,
			instancegroup: []string{"zoneA-ig0.25c1G-gpu"},
		},
		{
			name: "完整匹配-gpu",
			pod: BuildPodWithContainers("default", "pod1", map[string]string{entity.BciAccountKey: "account1", bciZoneKey: "zoneA"},
				map[string]string{}, []v1.Container{
				{
					Name: "nginx",
					Resources: v1.ResourceRequirements{
						Requests: BuildResourceList("0.25", "1G"),
						Limits:   BuildResourceList("0.25", "1G"),
					},
				},
			}),
			getInstanceGroupConfig: func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
				result := make([]entity.InstanceGroupCm, 0)
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-gpu",
					InstanceGroupId:   "zoneA-ig0.25c1G-gpu",
					MatchResources: []entity.InstanceGroupResource{
						{
							A1024G: "1",
							CPU:    "0.25",
							Memory: "1G",
						},
					},
				})
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-gpu2",
					InstanceGroupId:   "zoneA-ig0.25c1G-gpu2",
					MatchResources: []entity.InstanceGroupResource{
						{
							A1024G: "1",
							CPU:    "0.5",
							Memory: "2G",
						},
					},
				})
				return result, nil, nil
			},
			getInstanceGroupAccountWhiteList: func() ([]string, error) {
				return []string{}, nil
			},
			isImageCachePodUseUserNode: func() bool {
				return false
			},
			injectSuccess: true,
			instancegroup: []string{"zoneA-ig0.25c1G-gpu", "zoneA-ig0.25c1G-gpu2"},
		},
		{
			name: "zone下无节点组",
			pod: BuildPodWithContainers("default", "pod1", map[string]string{entity.BciAccountKey: "account1", bciZoneKey: "zoneD"},
				map[string]string{}, []v1.Container{
				{
					Name: "nginx",
					Resources: v1.ResourceRequirements{
						Requests: BuildResourceList("0.25", "1G"),
						Limits:   BuildResourceList("0.25", "1G"),
					},
				},
			}),
			getInstanceGroupConfig: func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
				result := make([]entity.InstanceGroupCm, 0)
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-cpu",
					InstanceGroupId:   "zoneA-ig0.25c1G-cpu",
					MatchResources: []entity.InstanceGroupResource{
						{
							CPU:    "0.25",
							Memory: "1G",
						},
					},
				})
				return result, nil, nil
			},
			getInstanceGroupAccountWhiteList: func() ([]string, error) {
				return []string{}, nil
			},
			isImageCachePodUseUserNode: func() bool {
				return false
			},
			injectSuccess: false,
		},
		{
			name: "resource向上取整",
			pod: BuildPodWithContainers("default", "pod1", map[string]string{entity.BciAccountKey: "account1", bciZoneKey: "zoneA"},
				map[string]string{}, []v1.Container{
				{
					Name: "nginx",
					Resources: v1.ResourceRequirements{
						Requests: BuildResourceList("0.1", "1.1G"),
						Limits:   BuildResourceList("0.1", "1.1G"),
					},
				},
			}),
			getInstanceGroupConfig: func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
				result := make([]entity.InstanceGroupCm, 0)
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig1c4G-cpu",
					InstanceGroupId:   "zoneA-ig1c4G-cpu",
					MatchResources: []entity.InstanceGroupResource{
						{
							CPU:    "1",
							Memory: "4G",
						},
					},
				})
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-cpu",
					InstanceGroupId:   "zoneA-ig0.25c1G-cpu",
					MatchResources: []entity.InstanceGroupResource{
						{
							CPU:    "0.25",
							Memory: "1G",
						},
					},
				})
				return result, nil, nil
			},
			getInstanceGroupAccountWhiteList: func() ([]string, error) {
				return []string{}, nil
			},
			isImageCachePodUseUserNode: func() bool {
				return false
			},
			injectSuccess: true,
			instancegroup: []string{"zoneA-ig1c4G-cpu"},
		},
		{
			name: "pod 资源大于所有节点组资源",
			pod: BuildPodWithContainers("default", "pod1", map[string]string{entity.BciAccountKey: "account1", bciZoneKey: "zoneA"},
				map[string]string{}, []v1.Container{
				{
					Name: "nginx",
					Resources: v1.ResourceRequirements{
						Requests: BuildResourceList("2", "8G"),
						Limits:   BuildResourceList("2", "8G"),
					},
				},
			}),
			getInstanceGroupConfig: func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
				result := make([]entity.InstanceGroupCm, 0)
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig1c4G-cpu",
					InstanceGroupId:   "zoneA-ig1c4G-cpu",
					MatchResources: []entity.InstanceGroupResource{
						{
							CPU:    "1",
							Memory: "4G",
						},
					},
				})
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-cpu",
					InstanceGroupId:   "zoneA-ig0.25c1G-cpu",
					MatchResources: []entity.InstanceGroupResource{
						{
							CPU:    "0.25",
							Memory: "1G",
						},
					},
				})
				return result, nil, nil
			},
			getInstanceGroupAccountWhiteList: func() ([]string, error) {
				return []string{}, nil
			},
			isImageCachePodUseUserNode: func() bool {
				return false
			},
			injectSuccess: false,
		},
		{
			name: "测试镜像转储Pod",
			pod: BuildPodWithContainers("default", "pod1", map[string]string{entity.BciAccountKey: "account1", bciZoneKey: "zoneA"},
				map[string]string{imageCacheLabel: "true"}, []v1.Container{
				{
					Name: "nginx",
					Resources: v1.ResourceRequirements{
						Requests: BuildResourceList("2", "8G"),
						Limits:   BuildResourceList("2", "8G"),
					},
				},
			}),
			getInstanceGroupConfig: func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
				result := make([]entity.InstanceGroupCm, 0)
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig1c4G-cpu",
					InstanceGroupId:   "zoneA-ig1c4G-cpu",
					MatchResources: []entity.InstanceGroupResource{
						{
							CPU:    "2",
							Memory: "8G",
						},
					},
				})
				result1 := make([]entity.InstanceGroupCm, 0)
				result1 = append(result1, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-cpu",
					InstanceGroupId:   "zoneA-ig0.25c1G-cpu",
					MatchResources: []entity.InstanceGroupResource{},
				})
				return result, result1, nil
			},
			getInstanceGroupAccountWhiteList: func() ([]string, error) {
				return []string{}, nil
			},
			isImageCachePodUseUserNode: func() bool {
				return false
			},
			injectSuccess: true,
			instancegroup: []string{"zoneA-ig0.25c1G-cpu"},
		},
		{
			name: "测试镜像转储Pod在用户节点",
			pod: BuildPodWithContainers("default", "pod1", map[string]string{entity.BciAccountKey: "account1", bciZoneKey: "zoneA"},
				map[string]string{imageCacheLabel: "true"}, []v1.Container{
					{
						Name: "nginx",
						Resources: v1.ResourceRequirements{
							Requests: BuildResourceList("2", "8G"),
							Limits:   BuildResourceList("2", "8G"),
						},
					},
				}),
			getInstanceGroupConfig: func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
				result := make([]entity.InstanceGroupCm, 0)
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig2c8G-cpu",
					InstanceGroupId:   "zoneA-ig2c8G-cpu",
					MatchResources: []entity.InstanceGroupResource{
						{
							CPU:    "2",
							Memory: "8G",
						},
					},
				})
				result1 := make([]entity.InstanceGroupCm, 0)
				result1 = append(result1, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-cpu",
					InstanceGroupId:   "zoneA-ig0.25c1G-cpu",
					MatchResources:    []entity.InstanceGroupResource{},
				})
				return result, result1, nil
			},
			getInstanceGroupAccountWhiteList: func() ([]string, error) {
				return []string{}, nil
			},
			isImageCachePodUseUserNode: func() bool {
				return true
			},
			injectSuccess: true,
			instancegroup: []string{"zoneA-ig2c8G-cpu"},
		},
	}

	for _, test := range tests {

		t.Run(test.name, func(t *testing.T) {
			_, err := injectNodeSelector(test.pod, test.getInstanceGroupConfig, test.getInstanceGroupAccountWhiteList,
				test.isImageCachePodUseUserNode)
			if test.injectSuccess {
				igs := entity.GetInstanceGroupIDFromNodeAffinity(test.pod)
				sort.Strings(igs)
				sort.Strings(test.instancegroup)
				if len(igs) != len(test.instancegroup) {
					t.Errorf("expected instanceGroup : %v, got %v ", test.instancegroup, igs)
				}
				for i := range igs {
					if igs[i] != test.instancegroup[i] {
						t.Errorf("expected instanceGroup : %v, got %v ", test.instancegroup, igs)
					}
				}
			} else {
				if err == nil {
					t.Errorf("expected err not empty  but got empty")
				}
				// fmt.Println(err)
			}
		})
	}

	// 测试 bid pod
	bidTests := []struct {
		name                             string
		pod                              *v1.Pod
		getInstanceGroupConfig           func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error)
		getInstanceGroupAccountWhiteList func() ([]string, error)
		isImageCachePodUseUserNode       func() bool
		injectSuccess                    bool
		instancegroup                    string
	}{
		{
			name: "完整匹配-bid",
			pod: BuildPodWithContainers("default", "pod1",
				map[string]string{entity.BciAccountKey: "account1", bciZoneKey: "zoneA", bciModelKey: "custom", bciPrice: "1"},
				map[string]string{}, []v1.Container{
					{
						Name: "nginx",
						Resources: v1.ResourceRequirements{
							Requests: BuildResourceList("0.25", "1G"),
							Limits:   BuildResourceList("0.25", "1G"),
						},
					},
				}),
			getInstanceGroupConfig: func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
				result := make([]entity.InstanceGroupCm, 0)
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-cpu",
					InstanceGroupId:   "zoneA-ig0.25c1G-cpu",
					MatchResources: []entity.InstanceGroupResource{
						{
							CPU:    "0.25",
							Memory: "1G",
						},
					},
				})
				return result, nil, nil
			},
			getInstanceGroupAccountWhiteList: func() ([]string, error) {
				return []string{}, nil
			},
			isImageCachePodUseUserNode: func() bool {
				return false
			},
			injectSuccess: true,
			instancegroup: "",
		},
		{
			name: "完整匹配-MARKET_PRICE_BID",
			pod: BuildPodWithContainers("default", "pod1",
				map[string]string{entity.BciAccountKey: "account1", bciZoneKey: "zoneA", bciModelKey: "MARKET_PRICE_BID"},
				map[string]string{}, []v1.Container{
					{
						Name: "nginx",
						Resources: v1.ResourceRequirements{
							Requests: BuildResourceList("0.25", "1G"),
							Limits:   BuildResourceList("0.25", "1G"),
						},
					},
				}),
			getInstanceGroupConfig: func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
				result := make([]entity.InstanceGroupCm, 0)
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-gpu",
					InstanceGroupId:   "zoneA-ig0.25c1G-gpu",
					MatchResources: []entity.InstanceGroupResource{
						{
							A1024G: "1",
							CPU:    "0.25",
							Memory: "1G",
						},
					},
				})
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-gpu2",
					InstanceGroupId:   "zoneA-ig0.25c1G-gpu2",
					MatchResources: []entity.InstanceGroupResource{
						{
							A1024G: "2",
							CPU:    "0.25",
							Memory: "1G",
						},
					},
				})
				return result, nil, nil
			},
			getInstanceGroupAccountWhiteList: func() ([]string, error) {
				return []string{}, nil
			},
			isImageCachePodUseUserNode: func() bool {
				return false
			},
			injectSuccess: true,
			instancegroup: "",
		},
		{
			name: "完整匹配-CUSTOM_BID",
			pod: BuildPodWithContainers("default", "pod1",
				map[string]string{entity.BciAccountKey: "account1", bciZoneKey: "zoneA", bciModelKey: "CUSTOM_BID", bciPrice: "1"},
				map[string]string{}, []v1.Container{
					{
						Name: "nginx",
						Resources: v1.ResourceRequirements{
							Requests: BuildResourceList("0.25", "1G"),
							Limits:   BuildResourceList("0.25", "1G"),
						},
					},
				}),
			getInstanceGroupConfig: func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
				result := make([]entity.InstanceGroupCm, 0)
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-gpu",
					InstanceGroupId:   "zoneA-ig0.25c1G-gpu",
					MatchResources: []entity.InstanceGroupResource{
						{
							A1024G: "1",
							CPU:    "0.25",
							Memory: "1G",
						},
					},
				})
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-gpu2",
					InstanceGroupId:   "zoneA-ig0.25c1G-gpu2",
					MatchResources: []entity.InstanceGroupResource{
						{
							A1024G: "2",
							CPU:    "0.25",
							Memory: "1G",
						},
					},
				})
				return result, nil, nil
			},
			getInstanceGroupAccountWhiteList: func() ([]string, error) {
				return []string{}, nil
			},
			isImageCachePodUseUserNode: func() bool {
				return false
			},
			injectSuccess: true,
			instancegroup: "",
		},
		{
			name: "竞价实例",
			pod: BuildPodWithContainers("default", "pod1",
				map[string]string{entity.BciAccountKey: "account1", bciZoneKey: "zoneA", bciModelKey: "CUSTOM_BID", bciPrice: "1"},
				map[string]string{}, []v1.Container{
					{
						Name: "nginx",
						Resources: v1.ResourceRequirements{
							Requests: BuildResourceList("1", "1G"),
							Limits:   BuildResourceList("1", "1G"),
						},
					},
				}),
			getInstanceGroupConfig: func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
				result := make([]entity.InstanceGroupCm, 0)
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig1c4G-cpu",
					InstanceGroupId:   "zoneA-ig1c4G-cpu",
					ChargingType:      "bidding",
					MatchResources: []entity.InstanceGroupResource{
						{
							CPU:    "1",
							Memory: "4G",
						},
					},
				})
				result = append(result, entity.InstanceGroupCm{
					InstanceGroupName: "zoneA-ig0.25c1G-cpu",
					InstanceGroupId:   "zoneA-ig0.25c1G-cpu",
					ChargingType:      "bidding",
					MatchResources: []entity.InstanceGroupResource{
						{
							CPU:    "2",
							Memory: "8G",
						},
					},
				})
				return result, nil, nil
			},
			getInstanceGroupAccountWhiteList: func() ([]string, error) {
				return []string{}, nil
			},
			isImageCachePodUseUserNode: func() bool {
				return false
			},
			injectSuccess: true,
			instancegroup: "",
		},
	}

	for _, test := range bidTests {

		t.Run(test.name, func(t *testing.T) {
			_, err := injectNodeSelector(test.pod, test.getInstanceGroupConfig, test.getInstanceGroupAccountWhiteList,
				test.isImageCachePodUseUserNode)
			if test.injectSuccess {
				ig := test.pod.Spec.NodeSelector[entity.InstanceGroupKey]
				if test.instancegroup != ig {
					t.Errorf("expected instanceGroup : %v, got %v ", test.instancegroup, ig)
				}
			} else {
				if err == nil {
					t.Errorf("expected err not empty  but got empty")
				}
				// fmt.Println(err)
			}
		})
	}
}

func TestStr(t *testing.T) {
	str1 := "zoneD-bcc.g4.c2m8-cpu"
	str2 := "zoned"

	res := strings.Contains(str1, str2)
	fmt.Println(res)
}
