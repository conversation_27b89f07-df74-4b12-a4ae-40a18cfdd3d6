package mutating

import (
	"testing"

	v1 "k8s.io/api/core/v1"
)

func TestInjectGeneralCPUModel(t *testing.T) {

	// 测试 cpu mode pod
	bidTests := []struct {
		name          string
		pod           *v1.Pod
		injectSuccess bool
	}{
		{
			name: "打开cpu型号屏蔽",
			pod: BuildPodWithContainers("default", "pod1",
				map[string]string{admissionWebhookAnnotationGeneralCPUModelKey: "enabled"},
				map[string]string{}, []v1.Container{
					{
						Name: "nginx",
						Resources: v1.ResourceRequirements{
							Requests: BuildResourceList("0.25", "1G"),
							Limits:   BuildResourceList("0.25", "1G"),
						},
					},
				}),
			injectSuccess: true,
		},
		{
			name: "没有cpu型号屏蔽的annotation",
			pod: BuildPodWithContainers("default", "pod1",
				map[string]string{}, map[string]string{}, []v1.Container{
					{
						Name: "nginx",
						Resources: v1.ResourceRequirements{
							Requests: BuildResourceList("0.25", "1G"),
							Limits:   BuildResourceList("0.25", "1G"),
						},
					},
				}),
			injectSuccess: false,
		},
		{
			name: "cpu型号屏蔽的annotation非enabled",
			pod: BuildPodWithContainers("default", "pod1",
				map[string]string{admissionWebhookAnnotationGeneralCPUModelKey: "disabled"},
				map[string]string{}, []v1.Container{
					{
						Name: "nginx",
						Resources: v1.ResourceRequirements{
							Requests: BuildResourceList("0.25", "1G"),
							Limits:   BuildResourceList("0.25", "1G"),
						},
					},
				}),
			injectSuccess: false,
		},
	}

	for _, test := range bidTests {

		t.Run(test.name, func(t *testing.T) {
			ok := injectGeneralCPUModel(test.pod)
			if test.injectSuccess != ok {
				t.Errorf("test %s failed, expected success %t, got %t", test.name, test.injectSuccess, ok)
			}
		})
	}
}
