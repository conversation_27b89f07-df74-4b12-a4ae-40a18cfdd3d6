package mutating

import "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime"

// +kubebuilder:webhook:path=/mutate-pod,mutating=true,failurePolicy=fail,sideEffects=None,admissionReviewVersions=v1;v1beta1,groups="",resources=pods,verbs=create,versions=v1,name=mpod.kb.io

var (
	// HandlerMap contains admission webhook handlers
	HandlerMap = map[string]runtime.NewWebhookServer{
		"mutate-pod": newPodCreateHandler,
	}
)
