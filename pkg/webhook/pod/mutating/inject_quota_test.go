package mutating

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"

	"testing"
)

func TestInjectQuota(t *testing.T) {

	var pod corev1.Pod

	pod.Name = "test"
	pod.Namespace = "testNS"
	pod.Annotations = map[string]string{}
	pod.Annotations[admissionWebhookAnnotationQuotaStatusKey] = "disabled"
	pod.Spec.Containers = []corev1.Container{
		{
			Image: "test_image",
		},
	}
	testInject(t, &pod)
}

func TestInjectQuota2(t *testing.T) {

	var pod corev1.Pod

	pod.Name = "test"
	pod.Namespace = "testNS"
	pod.Spec.Containers = []corev1.Container{
		{
			Image: "test_image",
		},
	}
	pod.Spec.Volumes = []corev1.Volume{
		{
			Name: "test",
			VolumeSource: corev1.VolumeSource{
				HostPath: &corev1.HostPathVolumeSource{
					Path: "/var/test",
				},
			},
		},
	}
	v := corev1.Volume{
		Name: "test2",
		VolumeSource: corev1.VolumeSource{
			EmptyDir: &corev1.EmptyDirVolumeSource{
				Medium: "Memory",
			},
		},
	}
	v2 := corev1.Volume{
		Name: "test3",
		VolumeSource: corev1.VolumeSource{
			EmptyDir: &corev1.EmptyDirVolumeSource{},
		},
	}
	pod.Spec.Volumes = append(pod.Spec.Volumes, v)
	pod.Spec.Volumes = append(pod.Spec.Volumes, v2)
	testInject(t, &pod)
}
func TestInjectQuota3(t *testing.T) {

	var pod corev1.Pod

	pod.Name = "test"
	pod.Namespace = "kube-system"
	pod.Annotations = map[string]string{}
	pod.Annotations[admissionWebhookAnnotationQuotaStatusKey] = "disabled"
	pod.Spec.Containers = []corev1.Container{
		{
			Image: "test_image",
		},
	}
	testInject(t, &pod)
}

func TestInjectQuota4(t *testing.T) {
	var pod corev1.Pod

	pod.Name = "test"
	pod.Namespace = "testNS"
	pod.Annotations = map[string]string{}
	pod.Spec.Containers = []corev1.Container{
		{
			Image: "test_image",
		},
	}
	pod.Spec.Volumes = []corev1.Volume{
		{
			Name: "test",
			VolumeSource: corev1.VolumeSource{
				EmptyDir: &corev1.EmptyDirVolumeSource{
					SizeLimit: &resource.Quantity{},
				},
			},
		},
	}
	testInject(t, &pod)
}

func testInject(t *testing.T, pod *corev1.Pod) {
	injectQuota(pod)
	t.Logf("patch : %+#v", pod)
}
