package mutating

import (
	"strings"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	klog "k8s.io/klog/v2"
)

const (
	admissionWebhookAnnotationGeneralCPUModelKey = "bci.cloud.baidu.com/generalCPUModel"
)

// -v /var/lib/lxcfs-cpu/proc/cpuinfo:/proc/cpuinfo:ro
var generalCPUModelVolumeMountsTemplate = []corev1.VolumeMount{

	{
		Name:      "lxcfs-proc-cpuinfo",
		MountPath: "/proc/cpuinfo",
		ReadOnly:  true,
	},
}
var generalCPUModelVolumesTemplate = []corev1.Volume{
	{
		Name: "lxcfs-proc-cpuinfo",
		VolumeSource: corev1.VolumeSource{
			HostPath: &corev1.HostPathVolumeSource{
				Path: "/var/lib/lxcfs-cpu/proc/cpuinfo",
			},
		},
	},
}

func generalCPUModelRequired(ignoredList []string, metadata *metav1.ObjectMeta) bool {
	// skip special kubernetes system namespaces
	for _, namespace := range ignoredList {
		if metadata.Namespace == namespace {
			klog.Infof("Skip validation for %v for it's in special namespace:%v", metadata.Name, metadata.Namespace)
			return false
		}
	}

	annotations := metadata.GetAnnotations()
	if annotations == nil {
		annotations = map[string]string{}
	}

	var required bool
	switch strings.ToLower(annotations[admissionWebhookAnnotationGeneralCPUModelKey]) {
	default:
		required = false
	case "enabled":
		required = true
	}

	klog.Infof("General CPU model policy for %v/%v: required:%v", metadata.Namespace, metadata.Name, required)
	return required
}

func injectGeneralCPUModel(pod *corev1.Pod) bool {
	resourceName, resourceNamespace, objectMeta := pod.Name, pod.Namespace, &pod.ObjectMeta
	if !generalCPUModelRequired(ignoredNamespaces, objectMeta) {
		klog.Infof("Skipping validation for %s/%s due to policy check", resourceNamespace, resourceName)
		return false
	}

	if pod.Annotations == nil {
		pod.Annotations = map[string]string{}
	}

	containers := pod.Spec.Containers
	// Modify the Pod spec to include the LXCFS volumes, then op the original pod.
	for i := range containers {
		if containers[i].VolumeMounts == nil {
			containers[i].VolumeMounts = generalCPUModelVolumeMountsTemplate
		} else {
			for _, volumeMount := range generalCPUModelVolumeMountsTemplate {
				containers[i].VolumeMounts = append(containers[i].VolumeMounts, volumeMount)
			}
		}
	}

	if pod.Spec.Volumes == nil {
		pod.Spec.Volumes = generalCPUModelVolumesTemplate
	} else {
		for _, volume := range generalCPUModelVolumesTemplate {
			pod.Spec.Volumes = append(pod.Spec.Volumes, volume)
		}
	}

	return true
}
