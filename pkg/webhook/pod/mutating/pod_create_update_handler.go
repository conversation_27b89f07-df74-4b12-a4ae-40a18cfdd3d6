package mutating

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"

	corev1 "k8s.io/api/core/v1"
	klog "k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"
)

// PodCreateHandler handles Pod
type PodCreateHandler struct {
	// To use the client, you need to do the following:
	// - uncomment it
	// - import sigs.k8s.io/controller-runtime/pkg/client
	// - uncomment the InjectClient method at the bottom of this file.
	Client client.Client

	// Decoder decodes objects
	Decoder *admission.Decoder

	mgr manager.Manager
}

func newPodCreateHandler(_ *options.ServerRunOptions, mgr manager.Manager, decoder *admission.Decoder) admission.Handler {
	return &PodCreateHandler{
		mgr:     mgr,
		Decoder: decoder,
		Client:  mgr.GetClient(),
		// InjectClient: mgr.GetClient,
	}
}

var _ admission.Handler = &PodCreateHandler{}

// Handle handles admission requests.
func (h *PodCreateHandler) Handle(ctx context.Context, req admission.Request) admission.Response {
	// 逻辑实现
	obj := &corev1.Pod{}

	err := h.Decoder.Decode(req, obj)
	if err != nil {
		return admission.Errored(http.StatusBadRequest, err)
	}
	// when pod.namespace is empty, using req.namespace
	if obj.Namespace == "" {
		obj.Namespace = req.Namespace
	}
	for i := range obj.Spec.Containers {
		// 清除环境变量，避免环境变量过多造成webhook接口超时
		obj.Spec.Containers[i].Env = obj.Spec.Containers[i].Env[:0]
	}
	oldPod := obj.DeepCopyObject()

	klog.Infof("start handle pod %s, request uid: %s", obj.Name, req.AdmissionRequest.UID)
	var changed bool
	var failedInfo *WebhookFailedInfo
	changed, failedInfo = injectNodeSelector(obj, h.getInstanceGroupConfig, h.getInstanceGroupAccountWhiteList, h.isImageCachePodUseUserNode)
	if failedInfo != nil {
		// 节点组处理失败，返回404，控制面根据404来判断是否找到对应节点组
		// 重要：此处不能修改，bci 控制面根据字符串截取对应reason + message 信息给vk展示；
		bytes, _ := json.Marshal(failedInfo)
		return admission.Errored(http.StatusNotFound, fmt.Errorf("%s", string(bytes)))
	}

	// 添加后续webhook逻辑
	runtimeChanged := injectRuntimeClass(obj)

	klog.Infof("start inject lxcfs, request uid: %s", req.AdmissionRequest.UID)
	lxcfsChanged := injectLxcfs(obj)
	klog.Infof("finish inject lxcfs, request uid: %s", req.AdmissionRequest.UID)

	generalCPUModelChanged := false
	if !runtimeChanged {
		generalCPUModelChanged = injectGeneralCPUModel(obj)
	}

	klog.Infof("start inject eph quota, request uid: %s", req.AdmissionRequest.UID)
	quotaChanged := injectQuota(obj)
	klog.Infof("finish inject eph quota, request uid: %s", req.AdmissionRequest.UID)
	if !(changed || lxcfsChanged || quotaChanged || runtimeChanged || generalCPUModelChanged) {
		klog.Infof("nothing changed in webhook pod name: %s, request uid: %s", obj.Name, req.AdmissionRequest.UID)
		return admission.Allowed("")
	}

	marshalled, err := json.Marshal(obj)
	if err != nil {
		klog.Errorf("marshal mutated pod %s error %v", obj.Name, err)
		return admission.Errored(http.StatusInternalServerError, err)
	}
	oldMarshalled, err := json.Marshal(oldPod)
	if err != nil {
		klog.Errorf("marshal old pod %s error %v", obj.Name, err)
		return admission.Errored(http.StatusInternalServerError, err)
	}

	klog.Infof("handle webhook before PatchResponseFromRaw pod name: %s request uid: %s", obj.Name, req.AdmissionRequest.UID)
	response := admission.PatchResponseFromRaw(oldMarshalled, marshalled)
	klog.Infof("handle webhook after PatchResponseFromRaw pod name: %s request uid: %s", obj.Name, req.AdmissionRequest.UID)
	return response
}

// InjectClient injects the client into the PodCreateHandler
func (h *PodCreateHandler) InjectClient(c client.Client) error {
	h.Client = c
	return nil
}

// InjectDecoder injects the decoder into the PodCreateHandler
func (h *PodCreateHandler) InjectDecoder(d *admission.Decoder) error {
	h.Decoder = d
	return nil
}
