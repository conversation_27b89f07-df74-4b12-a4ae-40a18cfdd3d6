package mutating

import (
	"strconv"
	"strings"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	klog "k8s.io/klog/v2"
)

const (
	admissionWebhookAnnotationRootfsKey      = "bci.cloud.baidu.com/rootfs.size-"
	admissionWebhookAnnotationEmptyDirKey    = "bci.cloud.baidu.com/emptyDir.size-"
	admissionWebhookAnnotationQuotaStatusKey = "bci.cloud.baidu.com/ephemeral-quota"
	defaultEmptyDirSize                      = 10 * 1024 * 1024 // KB
	defaultRootfsSize                        = 20 * 1024 * 1024 // KB
)

func isRequired(ignoredList []string, metadata *metav1.ObjectMeta) bool {
	// skip special kubernetes system namespaces
	for _, namespace := range ignoredList {
		if metadata.Namespace == namespace {
			klog.Infof("Skip validation for %v for it's in special namespace:%v", metadata.Name, metadata.Namespace)
			return false
		}
	}

	annotations := metadata.GetAnnotations()
	if annotations == nil {
		annotations = map[string]string{}
	}

	var required bool
	switch strings.ToLower(annotations[admissionWebhookAnnotationQuotaStatusKey]) {
	default:
		required = true
	case "disabled":
		required = false
	}

	klog.Infof("ephemeral quota policy for %v/%v: required:%v", metadata.Namespace, metadata.Name, required)
	return required
}

func injectQuota(pod *corev1.Pod) bool {
	resourceName, resourceNamespace, objectMeta := pod.Name, pod.Namespace, &pod.ObjectMeta
	if !isRequired(ignoredNamespaces, objectMeta) {
		klog.Infof("Skipping validation for %s/%s due to policy check", resourceNamespace, resourceName)
		return false
	}

	if pod.Annotations == nil {
		pod.Annotations = map[string]string{}
	}

	var size int64
	containers := pod.Spec.Containers
	size = defaultRootfsSize
	//add rootfs annotations
	for i := range containers {
		cn := containers[i].Name
		if containers[i].Resources.Limits != nil {
			es := containers[i].Resources.Limits.StorageEphemeral()
			sizekb, _ := es.AsInt64()
			if sizekb != 0 {
				size = sizekb / 1024
			}
		}
		tmpAnn := admissionWebhookAnnotationRootfsKey + cn
		pod.Annotations[tmpAnn] = strconv.FormatInt(size, 10)
	}

	//add emptydir annotainos
	volumes := pod.Spec.Volumes
	size = defaultEmptyDirSize
	for i := range volumes {
		if volumes[i].EmptyDir == nil {
			continue
		}
		if volumes[i].EmptyDir.Medium == "Memory" {
			continue
		}
		emptyDirName := volumes[i].Name
		if volumes[i].EmptyDir.SizeLimit != nil {
			sizekb := volumes[i].EmptyDir.SizeLimit.AsDec().UnscaledBig().Int64() / 1024
			if sizekb != 0 {
				size = sizekb
			}
		}
		tmpAnn := admissionWebhookAnnotationEmptyDirKey + emptyDirName
		pod.Annotations[tmpAnn] = strconv.FormatInt(size, 10)
		klog.Infof("emptyDir ann: %#+v", pod.Annotations)
	}
	pod.Annotations[admissionWebhookAnnotationQuotaStatusKey] = "enabled"
	return true

}
