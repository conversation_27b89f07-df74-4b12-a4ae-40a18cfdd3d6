package mutating

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	"github.com/baidubce/bce-sdk-go/services/cce/v2/types"
	"github.com/prometheus/client_golang/prometheus"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
)

const (
	// 公有云可用区不等于机房，每个客户看到的可用区顺序可能不同，比如bjdd，在你账号下面是 zoneA，在我下面可能是 zoneC，
	// 前端传进来logicalZone: "zoneA"，后端程序会根据 accountID & zone 获取真实 PhysicalZone
	bciZoneKey = "bci_internal_PhysicalZone"
	// 创建节点组时，根据bci在cce上的account + logicalZone 调用接口获取logicalZone 对应的PhysicalZone 设置到节点组名称上

	// bciModelKey 竞价模式下才有
	bciModelKey = "bci_internal_bidModel"
	// bciPrice 竞价价格
	bciPrice = "bci_internal_bidPrice"

	// bciZoneSubnetsKey zone和subnet列表，支持多可用区才有
	bciZoneSubnetsKey = "bci_internal_ZoneSubnets"

	// 镜像转储pod独有标签
	imageCacheLabel = "image.bci.cloud.baidu.com/image-cache"
	// 镜像转储特定节点组名字
	stargzImageLabel = "stargz-image-node-group"
	// mark securityContaienr
	securityContaienrLabel = "securityContainer"

	cpuTypeAMD = "amd"
)

var (
	// 注入节点组标签失败次数
	injectNodeSelectorFailCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "inject_node_selector_fail_total",
			Help:      "The Webhook Select InstanceGroup Fail Count .",
		},
		[]string{},
	)
	// 转储pod选节点组失败次数
	imageCachePodNodeSelectorFailCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Subsystem: "bci",
			Name:      "image_cache_pod_node_selector_fail_total",
			Help:      "The Image Cache Pod Select InstanceGroup Fail Count .",
		},
		[]string{},
	)
	bciKataRuntimeClass = "kata"
)

type webhookFailedReason string

const (
	ZoneNoResourceSpecificationReason webhookFailedReason = "ZoneNoResourceSpecification"
	InvalidedParamsReason             webhookFailedReason = "InvalidedParams"
	InternalServerErrorReason         webhookFailedReason = "InternalServerError"
)

var (
	webhookFailedReasonAndMessageMap map[string]string = map[string]string{}
)

type WebhookFailedInfo struct {
	Reason  string `json:"reason"`  // 给用户展示的reason
	Message string `json:"message"` // 给用户展示的message
}

func init() {
	webhookFailedReasonAndMessageMap[string(ZoneNoResourceSpecificationReason)] = "this zone have no resource specification."
	webhookFailedReasonAndMessageMap[string(InvalidedParamsReason)] = "invalided params ,please check your params."
	webhookFailedReasonAndMessageMap[string(InternalServerErrorReason)] = "internal server config error."

	metrics.Registry.MustRegister(injectNodeSelectorFailCounter)
	metrics.Registry.MustRegister(imageCachePodNodeSelectorFailCounter)
}

func buildFailedWebhookInfo(failedReason webhookFailedReason) *WebhookFailedInfo {
	result := &WebhookFailedInfo{
		Reason:  string(failedReason),
		Message: webhookFailedReasonAndMessageMap[string(failedReason)],
	}
	return result
}

func (h *PodCreateHandler) getInstanceGroupConfig() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error) {
	// 从informer 中获取，提升性能
	instanceGroups := make([]entity.InstanceGroupCm, 0)
	cm := corev1.ConfigMap{}
	err := h.mgr.GetCache().Get(context.Background(), client.ObjectKey{
		Namespace: entity.InstanceGroupCmNamespace,
		Name:      entity.InstanceGroupCmName,
	}, &cm)

	if err != nil {
		klog.Errorf("injectNodeSelector pod <%s/%s> get getInstanceGroupConfig err %+v ", err)
		return instanceGroups, instanceGroups, err
	}

	config := cm.Data[entity.InstanceGropuCmDataKey]
	if config == "" {
		return instanceGroups, instanceGroups, fmt.Errorf("instance group config empty")
	}

	if err := json.Unmarshal([]byte(config), &instanceGroups); err != nil {
		klog.Errorf("injectNodeSelector pod <%s/%s> Unmarshal instanceGroupConf err %+v ", err)
		return instanceGroups, instanceGroups, fmt.Errorf("instance group config fail")
	}

	// 过滤配置错误的
	userGroups := make([]entity.InstanceGroupCm, 0)
	imageGroups := make([]entity.InstanceGroupCm, 0)
	for _, ig := range instanceGroups {
		if strings.Contains(ig.InstanceGroupName, stargzImageLabel) {
			imageGroups = append(imageGroups, ig)
			continue
		}
		if len(ig.MatchResources) == 0 {
			continue
		}
		parseSuccess := true
		for _, r := range ig.MatchResources {
			_, err := resource.ParseQuantity(r.CPU)
			if err != nil {
				parseSuccess = false
				break
			}
			_, err = resource.ParseQuantity(r.Memory)
			if err != nil {
				parseSuccess = false
				break
			}
		}
		if parseSuccess {
			userGroups = append(userGroups, ig)
		}
	}
	return userGroups, imageGroups, nil
}

// 从cm中获取用户白名单
func (h *PodCreateHandler) getInstanceGroupAccountWhiteList() (whiteAccountIDs []string, err error) {
	whiteAccountIDs = []string{}

	cm := corev1.ConfigMap{}
	err = h.mgr.GetCache().Get(context.Background(), client.ObjectKey{
		Namespace: entity.InstanceGroupCmNamespace,
		Name:      entity.InstanceGroupCmName,
	}, &cm)
	if err != nil {
		klog.Errorf("PodCreateHandler get %s/%s config map err %+v ", entity.InstanceGroupCmNamespace, entity.InstanceGroupCmName, err)
		return
	}

	// 用户白名单，可以略过节点组容器规格检查
	whiteAccountStr := cm.Data[entity.InstanceGroupAccountWhiteListCmDataKey]
	if whiteAccountStr == "" {
		return
	}

	klog.V(3).Infof("PodCreateHandler get %s from %s/%s config map: %s", entity.InstanceGroupAccountWhiteListCmDataKey,
		entity.InstanceGroupCmNamespace, entity.InstanceGroupCmName, whiteAccountStr)
	if err = json.Unmarshal([]byte(whiteAccountStr), &whiteAccountIDs); err != nil {
		klog.Errorf("PodCreateHandler getInstanceGroupAccountWhiteList Unmarshal %s err %+v ", whiteAccountStr, err)
		return
	}
	return
}

// 当 image-cache-pod-config的CM存在时，则转储Pod不使用单独节点组
func (h *PodCreateHandler) isImageCachePodUseUserNode() bool {
	cm := corev1.ConfigMap{}
	err := h.mgr.GetCache().Get(context.Background(), client.ObjectKey{
		Namespace: entity.InstanceGroupCmNamespace,
		Name:      entity.ImageCachePodCmName,
	}, &cm)
	if err != nil {
		// 当前cm只是配置独立转储节点，当cm不存在时认为使用独立大节点组
		return false
	}
	return true
}

// injectNodeSelector 注入pod NodeSelector
func injectNodeSelector(pod *corev1.Pod,
	getInstanceGroupConfig func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error),
	getInstanceGroupAccountWhiteList func() ([]string, error),
	isImageCachePodUseUserNode func() bool) (change bool, failedInfo *WebhookFailedInfo) {

	defer func() {
		if failedInfo != nil {
			// 失败打点
			injectNodeSelectorFailCounter.WithLabelValues().Inc()
		}
	}()

	if getInstanceGroupConfig == nil {
		klog.Errorf("injectNodeSelector pod <%s/%s> getInstanceGroupConfig func is nil ", pod.Namespace, pod.Name)
		return false, buildFailedWebhookInfo(InternalServerErrorReason)
	}
	userGroups, imageGroups, err := getInstanceGroupConfig()
	if err != nil {
		klog.Errorf("injectNodeSelector pod <%s/%s> getInstanceGroupConfig err %+v ", pod.Namespace, pod.Name, err)
		return false, buildFailedWebhookInfo(InternalServerErrorReason)
	}

	if getInstanceGroupAccountWhiteList == nil {
		klog.Errorf("injectNodeSelector pod <%s/%s> getInstanceGroupAccountWhiteList func is nil ", pod.Namespace, pod.Name)
		return false, buildFailedWebhookInfo(InternalServerErrorReason)
	}
	accountWhiteList, err := getInstanceGroupAccountWhiteList()
	if err != nil {
		klog.Errorf("injectNodeSelector pod <%s/%s> getInstanceGroupAccountWhiteList err %+v ", pod.Namespace, pod.Name, err)
		return false, buildFailedWebhookInfo(InternalServerErrorReason)
	}
	accountWhiteMap := make(map[string]bool, 0)
	for _, accountID := range accountWhiteList {
		accountWhiteMap[accountID] = true
	}

	podAnnotation := pod.Annotations
	if len(podAnnotation) == 0 {
		klog.Errorf("injectNodeSelector pod <%s/%s> Annotation empty", pod.Namespace, pod.Name)
		return false, buildFailedWebhookInfo(InvalidedParamsReason)
	}

	bciAccount := podAnnotation[entity.BciAccountKey]
	if bciAccount == "" {
		klog.Errorf("injectNodeSelector pod <%s/%s> bciAccount empty", pod.Namespace, pod.Name)
		return false, buildFailedWebhookInfo(InvalidedParamsReason)
	}

	physicalZones := []string{}
	bciPhysicalZone := podAnnotation[bciZoneKey]
	bciZoneAndSubnetValue := podAnnotation[bciZoneSubnetsKey]
	if bciPhysicalZone != "" && bciZoneAndSubnetValue != "" {
		klog.Errorf("injectNodeSelector pod <%s/%s> %s and %s are not empty", pod.Namespace, pod.Name, bciZoneKey, bciZoneSubnetsKey)
		return false, buildFailedWebhookInfo(InvalidedParamsReason)
	} else if bciPhysicalZone == "" && bciZoneAndSubnetValue == "" {
		klog.Errorf("injectNodeSelector pod <%s/%s> %s and %s are both empty", pod.Namespace, pod.Name, bciZoneKey, bciZoneSubnetsKey)
		return false, buildFailedWebhookInfo(InvalidedParamsReason)
	} else if bciPhysicalZone != "" {
		physicalZones = append(physicalZones, bciPhysicalZone)
	} else if bciZoneAndSubnetValue != "" {
		zoneSubnets := make([]entity.ZoneSubnetInfo, 0)
		err := json.Unmarshal([]byte(bciZoneAndSubnetValue), &zoneSubnets)
		if err != nil {
			klog.Errorf("injectNodeSelector pod <%s/%s> %s json unmarshl err %+v", pod.Namespace, pod.Name, bciZoneAndSubnetValue, err)
			return false, buildFailedWebhookInfo(InvalidedParamsReason)
		}
		filteredZoneSubnets := make([]entity.ZoneSubnetInfo, 0)
		if pod.Spec.Affinity != nil &&
			pod.Spec.Affinity.NodeAffinity != nil &&
			pod.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution != nil {
			for _, term := range pod.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms {
				if term.MatchExpressions != nil {
					for _, expression := range term.MatchExpressions {
						if expression.Key == "topology.kubernetes.io/zone" {
							for _, zone := range expression.Values {
								for _, zoneSubnet := range zoneSubnets {
									if zone == zoneSubnet.ZoneInfo.LogicalZone {
										filteredZoneSubnets = append(filteredZoneSubnets, zoneSubnet)
									}
								}
							}
						}
					}
				}
			}
		}
		if len(filteredZoneSubnets) != 0 {
			zoneSubnets = filteredZoneSubnets
		}
		for i := range zoneSubnets {
			physicalZones = append(physicalZones, zoneSubnets[i].ZoneInfo.PhysicalZone)
		}
	}

	// 将Annotation 打印出来
	bytes, _ := json.Marshal(pod.Annotations)
	klog.V(3).Infof("injectNodeSelector pod <%s/%s> accountID %+v physcicalZones %+v Annotation %s ",
		pod.Namespace, pod.Name, bciAccount, physicalZones, string(bytes))

	var selectInstanceGroups []entity.InstanceGroupCm
	var selectAMDIGs []entity.InstanceGroupCm
	var selectExceptAMDIGs []entity.InstanceGroupCm
	if _, ok := pod.Labels[imageCacheLabel]; ok && !isImageCachePodUseUserNode() {
		for _, ig := range imageGroups {
			for _, zone := range physicalZones {
				if strings.Contains(ig.InstanceGroupName, zone) {
					selectInstanceGroups = append(selectInstanceGroups, ig)
					break
				}
			}
		}
		// 如果镜像转储Pod选节点组失败，肯定是没有创建转储节点组，需要报警出来
		if len(selectInstanceGroups) == 0 {
			imageCachePodNodeSelectorFailCounter.WithLabelValues().Inc()
		}
	} else {
		selectExceptAMDIGs, selectAMDIGs = selectMatchInstanceGroups(pod, physicalZones, userGroups, accountWhiteMap)
		// 需要将amd放在前边, 优先锁amd的ig
		selectInstanceGroups = append(selectInstanceGroups, selectAMDIGs...)
		selectInstanceGroups = append(selectInstanceGroups, selectExceptAMDIGs...)
	}
	if selectInstanceGroups == nil || len(selectInstanceGroups) == 0 {
		klog.Errorf("injectNodeSelector pod <%s/%s> selectMatchInstanceGroups fail ", pod.Namespace, pod.Name)
		return false, buildFailedWebhookInfo(ZoneNoResourceSpecificationReason)
	}

	// set pod node selector
	if pod.Spec.NodeSelector == nil {
		pod.Spec.NodeSelector = make(map[string]string)
	}

	if podAnnotation[bciModelKey] != "" {
		// bid pod annotation
		pod.Spec.NodeSelector[entity.InstanceTemplateKey] = selectInstanceGroups[0].InstanceGroupId
		if podAnnotation[bciModelKey] == string(types.BidModeMarketPrice) {
			pod.Spec.NodeSelector[entity.BidMode] = entity.NodeLabelBidModeMarketValue
		}
		if podAnnotation[bciModelKey] == string(types.BidModeCustomPrice) {
			pod.Spec.NodeSelector[entity.BidMode] = entity.NodeLabelBidModePriceValue
		}
		if podAnnotation[bciPrice] != "" {
			pod.Spec.NodeSelector[entity.BidPrice] = podAnnotation[bciPrice]
		}
	} else {
		// bci pod, 将筛选的节点组ID列表注入nodeAffinity
		selectInstanceGroupIds := make([]string, 0)
		for i := range selectInstanceGroups {
			selectInstanceGroupIds = append(selectInstanceGroupIds, selectInstanceGroups[i].InstanceGroupId)
		}
		selectAMDInstanceGroupIds := make([]string, 0)
		for i := range selectAMDIGs {
			selectAMDInstanceGroupIds = append(selectAMDInstanceGroupIds, selectAMDIGs[i].InstanceGroupId)
		}
		// 构建nodeAffinity
		nodeSelectorRequirement := corev1.NodeSelectorRequirement{
			Key:      entity.InstanceGroupKey,
			Operator: corev1.NodeSelectorOpIn,
			Values:   selectInstanceGroupIds,
		}
		nodeSelectorItem := corev1.NodeSelectorTerm{
			MatchExpressions: []corev1.NodeSelectorRequirement{nodeSelectorRequirement},
		}
		if pod.Spec.Affinity == nil {
			pod.Spec.Affinity = &corev1.Affinity{}
		}
		if pod.Spec.Affinity.NodeAffinity == nil {
			pod.Spec.Affinity.NodeAffinity = &corev1.NodeAffinity{}
		}
		if pod.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution == nil {
			pod.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution = &corev1.NodeSelector{}
		}
		if pod.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms == nil {
			pod.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms = make([]corev1.NodeSelectorTerm, 0)
		}
		// 注入 nodeAffinity
		pod.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms =
			append(pod.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms, nodeSelectorItem)
		// 若果没有指定cpuType(等价于筛选后存在amd的InstanceGroupIds), 则优先调度到amd上
		if len(selectAMDInstanceGroupIds) != 0 {
			preferredSchedulingTerm := corev1.PreferredSchedulingTerm{
				Weight: 1,
				Preference: corev1.NodeSelectorTerm{
					MatchExpressions: []corev1.NodeSelectorRequirement{
						{
							Key:      entity.InstanceGroupKey,
							Operator: corev1.NodeSelectorOpIn,
							Values:   selectAMDInstanceGroupIds,
						},
					},
				},
			}
			if pod.Spec.Affinity.NodeAffinity.PreferredDuringSchedulingIgnoredDuringExecution == nil {
				pod.Spec.Affinity.NodeAffinity.PreferredDuringSchedulingIgnoredDuringExecution = make([]corev1.PreferredSchedulingTerm, 0)
			}
			pod.Spec.Affinity.NodeAffinity.PreferredDuringSchedulingIgnoredDuringExecution =
				append(pod.Spec.Affinity.NodeAffinity.PreferredDuringSchedulingIgnoredDuringExecution, preferredSchedulingTerm)
		}
	}
	// 将租户锁注入node selector
	pod.Spec.NodeSelector[entity.TenantLockKey] = bciAccount
	if s, ok := pod.Annotations[entity.BciMatchTypeKey]; ok {
		if strings.Contains(s, securityContaienrLabel) {
			pod.Spec.NodeSelector[entity.TenantLockKey] = entity.Bci3PodNodeSelectorLock
		}
	}

	// 检查Pod是否需要IPv6支持，如果需要则添加IPv6 nodeSelector
	if checkIPv6Annotation(pod) {
		pod.Spec.NodeSelector[entity.BCIEnableIPv6Key] = "true"
	}
	return true, nil
}

// selectMatchInstanceGroups 选择匹配的节点组
// 1. 检查physicalZone
// 2. 检查 cpu_type
// 3. 支持的容器规格 & 白名单列表
// 4. 支持的账户列表
func selectMatchInstanceGroups(pod *corev1.Pod, physicalZones []string, instanceGroups []entity.InstanceGroupCm,
	accountWhiteMap map[string]bool) ([]entity.InstanceGroupCm, []entity.InstanceGroupCm) {
	// 一期sidecar container 不收费，不考虑sidecar container 资源占用
	// 对instance group排序，从小到大
	sortInstanceGroups(instanceGroups)
	accountID := pod.Annotations[entity.BciAccountKey]
	candidateInstanceGroups := make([]entity.InstanceGroupCm, 0)
	candidateExceptAmdInstanceGroups := make([]entity.InstanceGroupCm, 0)
	candidateAmdInstanceGroups := make([]entity.InstanceGroupCm, 0)

	candidateSupportedAccountInstanceGroup := make([]entity.InstanceGroupCm, 0)
	candidateSupportedAccountExceptAmdInstanceGroups := make([]entity.InstanceGroupCm, 0)
	candidateSupportedAccountAmdInstanceGroups := make([]entity.InstanceGroupCm, 0)
	for _, ig := range instanceGroups {
		// 1. 检查physicalZone
		if !ig.CheckPhysicalZone(physicalZones) {
			continue
		}
		// 2. 检查 cpu_type
		if !ig.CheckCPUType(pod) {
			continue
		}
		// 3. 支持的容器规格 & 白名单列表
		if !ig.ResourceMatchPod(pod, accountWhiteMap) {
			continue
		}
		// 4. 支持的账户列表
		if ig.AuthorizedUsers == nil || len(ig.AuthorizedUsers) == 0 {
			// 未设置账户列表，支持所有用户的pod
			candidateInstanceGroups = append(candidateInstanceGroups, ig)
			if strings.ToLower(ig.CPUType) == cpuTypeAMD {
				candidateAmdInstanceGroups = append(candidateAmdInstanceGroups, ig)
			} else {
				candidateExceptAmdInstanceGroups = append(candidateExceptAmdInstanceGroups, ig)
			}
		} else if ig.IsAuthorizedUser(accountID) {
			// 设置了账户列表，只支持账户列表的pod
			candidateSupportedAccountInstanceGroup = append(candidateSupportedAccountInstanceGroup, ig)
			if strings.ToLower(ig.CPUType) == cpuTypeAMD {
				candidateSupportedAccountAmdInstanceGroups = append(candidateSupportedAccountAmdInstanceGroups, ig)
			} else {
				candidateSupportedAccountExceptAmdInstanceGroups = append(candidateSupportedAccountExceptAmdInstanceGroups, ig)
			}
		}
	}
	// 如果用户在节点组所支持的账户列表中，优选选择该节点组
	if len(candidateSupportedAccountInstanceGroup) > 0 {
		return candidateSupportedAccountExceptAmdInstanceGroups, candidateSupportedAccountAmdInstanceGroups
	}
	return candidateExceptAmdInstanceGroups, candidateAmdInstanceGroups
}

func sortInstanceGroups(instanceGroups []entity.InstanceGroupCm) {
	sort.Slice(instanceGroups, func(i, j int) bool {
		left := instanceGroups[i]
		right := instanceGroups[j]

		// 当一个节点组符合多种规格pod调度时，需要人为确保MatchResources 按照升序规则排序
		leftResource := left.MatchResources[len(left.MatchResources)-1]
		rightResource := right.MatchResources[len(right.MatchResources)-1]

		leftCpu, _ := resource.ParseQuantity(leftResource.CPU)
		leftMemory, _ := resource.ParseQuantity(leftResource.Memory)

		rightCpu, _ := resource.ParseQuantity(rightResource.CPU)
		rightMemory, _ := resource.ParseQuantity(rightResource.Memory)

		//  gpu资源
		leftGpu, _ := left.ResourcePodGPUCount(leftResource)
		rightGpu, _ := right.ResourcePodGPUCount(rightResource)

		// 需要人为确认配置正确
		emptyGPUResource := resource.Quantity{}
		if leftGpu != emptyGPUResource && rightGpu != emptyGPUResource {
			// gpu实例组
			if leftCpu.Cmp(rightCpu) <= 0 && leftMemory.Cmp(rightMemory) <= 0 && leftGpu.Cmp(rightGpu) <= 0 {
				return true
			}
		} else if leftCpu.Cmp(rightCpu) <= 0 && leftMemory.Cmp(rightMemory) <= 0 {
			// cpu实例组
			return true
		}
		return false
	})
}

func injectRuntimeClass(pod *corev1.Pod) bool {
	if _, ok := pod.Labels[entity.BciRuncKey]; ok {
		return false
	}

	if s, ok := pod.Annotations[entity.BciMatchTypeKey]; ok {
		if strings.Contains(s, securityContaienrLabel) {
			pod.Spec.RuntimeClassName = &bciKataRuntimeClass
			return true
		}
	}

	return false
}

// checkIPv6Annotation 检查Pod是否需要IPv6支持
func checkIPv6Annotation(pod *corev1.Pod) bool {
	if pod.Annotations == nil {
		return false
	}
	return pod.Annotations[entity.BciEnableIPv6AnnotationKey] == "true"
}
