package mutating

import (
	corev1 "k8s.io/api/core/v1"

	"testing"
)

func TestCreatePodPatch(t *testing.T) {

	var pod corev1.Pod

	pod.Name = "test"
	pod.Namespace = "testNS"
	pod.Annotations = map[string]string{}
	pod.Annotations[admissionWebhookAnnotationMutateKey] = "disabled"
	pod.Spec.Containers = []corev1.Container{
		{
			Image: "test_image",
		},
	}
	testCreatePodPatch(t, &pod)
}

func TestCreatePodPatch2(t *testing.T) {

	var pod corev1.Pod

	pod.Name = "test"
	pod.Namespace = "testNS"
	pod.Spec.Containers = []corev1.Container{
		{
			Image: "test_image",
		},
	}
	testCreatePodPatch(t, &pod)
}

func TestCreatePodPatch3(t *testing.T) {

	var pod corev1.Pod

	pod.Name = "test"
	pod.Namespace = "testNS"
	pod.Annotations = map[string]string{}
	pod.Spec.Containers = []corev1.Container{
		{
			Image: "test_image",
			VolumeMounts: []corev1.VolumeMount{
				{
					Name:      "test",
					MountPath: "/etc/test",
				},
			},
		},
	}
	pod.Spec.Volumes = []corev1.Volume{
		{
			Name: "test",
			VolumeSource: corev1.VolumeSource{
				HostPath: &corev1.HostPathVolumeSource{
					Path: "/var/test",
				},
			},
		},
	}
	testCreatePodPatch(t, &pod)
}

func testCreatePodPatch(t *testing.T, pod *corev1.Pod) {
	injectLxcfs(pod)
	t.Logf("patch : %+#v", pod)
}
