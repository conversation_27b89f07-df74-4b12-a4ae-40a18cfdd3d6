package mutating

import (
	"strconv"
	"strings"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"
)

type oversellResource struct {
	Resource string
	Rate     float64
}

func getOversellResources(node *corev1.Node) []oversellResource {
	result := make([]oversellResource, 0)
	for labelName, labelValue := range node.Labels {
		// e.g. kubernetes.io/oversell-rate-cpu、kubernetes.io/oversell-rate-memory
		// e.g. kubernetes.io/oversell-amount-cpu、kubernetes.io/oversell-amount-memory
		if !strings.HasPrefix(labelName, oversellRateLabelPrefix) {
			continue
		}

		if strings.HasPrefix(labelName, oversellRateLabelPrefix) {
			splits := strings.Split(labelName, oversellRateLabelPrefix)
			if len(splits) != 2 || strings.TrimSpace(splits[1]) == "" {
				continue
			}
			val, err := strconv.ParseFloat(labelValue, 64)
			if err != nil || val <= 0.0 {
				klog.Errorf("node %s oversell label %s parse value %+v err %+v", node.Name, labelName, labelValue, err)
				continue
			}
			resourceType := strings.ToLower(splits[1])
			_, ok := node.Status.Capacity[corev1.ResourceName(resourceType)]
			if !ok {
				klog.Errorf("node %s oversell label %s not exist in node resource", node.Name, labelName)
				continue
			}
			result = append(result, oversellResource{
				Resource: resourceType,
				Rate:     val,
			})
		}
	}
	return result
}
