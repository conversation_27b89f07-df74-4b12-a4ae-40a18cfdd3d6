package mutating

import (
	"context"
	"encoding/json"
	"net/http"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
)

const (
	oversellRateLabelPrefix = "kubernetes.io/oversell-rate-"
	enableOversellLabel     = "kubernetes.io/oversell-enable"
)

// NodeCreateHandler handles Node
type NodeCreateHandler struct {
	// To use the client, you need to do the following:
	// - uncomment it
	// - import sigs.k8s.io/controller-runtime/pkg/client
	// - uncomment the InjectClient method at the bottom of this file.
	Client client.Client

	// Decoder decodes objects
	Decoder *admission.Decoder

	mgr manager.Manager
}

func newNodeCreateHandler(_ *options.ServerRunOptions, mgr manager.Manager, decoder *admission.Decoder) admission.Handler {
	return &NodeCreateHandler{
		mgr:     mgr,
		Decoder: decoder,
		Client:  mgr.GetClient(),
		//Decoder: decoder,
	}
}

var _ admission.Handler = &NodeCreateHandler{}

// Handle handles admission requests.
func (h *NodeCreateHandler) Handle(ctx context.Context, req admission.Request) admission.Response {
	node := &corev1.Node{}
	err := h.Decoder.Decode(req, node)
	if err != nil {
		return admission.Errored(http.StatusBadRequest, err)
	}

	// todo 上线前取消日志
	klog.V(4).Infof("NodeUpdateHandler node %s receive update request node status %+v ", node.Name, node.Status)
	val, ok := node.Labels[enableOversellLabel]
	// 检查是否开启oversell，如果没有开启，直接返回
	if !ok || val != "true" {
		return admission.Allowed("oversell is disable")
	}

	// 获取oversell的rate和amount

	oversellResources := getOversellResources(node)
	if len(oversellResources) == 0 {
		return admission.Allowed("oversell is disable")
	}

	nodeAllocatableBytes, _ := json.Marshal(node.Status.Allocatable)
	nodeCapacityBytes, _ := json.Marshal(node.Status.Capacity)
	klog.V(3).Infof("NodeUpdateHandler node name %s resource nodeAllocatable %+v nodeCapacity %+v",
		node.Name, string(nodeAllocatableBytes), string(nodeCapacityBytes))

	for _, oversellResource := range oversellResources {
		if oversellResource.Resource == string(corev1.ResourceCPU) {
			// 修改node cpu的allocatable、capacity
			capacityResource := node.Status.Capacity[corev1.ResourceName(oversellResource.Resource)]
			expectCapacity := float64(capacityResource.MilliValue()) * oversellResource.Rate
			node.Status.Capacity[corev1.ResourceName(oversellResource.Resource)] = *resource.NewMilliQuantity(int64(expectCapacity), resource.DecimalSI)
			allocatableResource := node.Status.Allocatable[corev1.ResourceName(oversellResource.Resource)]
			expectAllocatable := float64(allocatableResource.MilliValue()) * oversellResource.Rate
			node.Status.Allocatable[corev1.ResourceName(oversellResource.Resource)] = *resource.NewMilliQuantity(int64(expectAllocatable), resource.DecimalSI)
		} else if oversellResource.Resource == string(corev1.ResourceMemory) {
			// 修改node memory的allocatable、capacity
			capacityResource := node.Status.Capacity[corev1.ResourceName(oversellResource.Resource)]
			expectCapacity := float64(capacityResource.Value()) * oversellResource.Rate
			node.Status.Capacity[corev1.ResourceName(oversellResource.Resource)] = *resource.NewQuantity(int64(expectCapacity), resource.BinarySI)
			allocatableResource := node.Status.Allocatable[corev1.ResourceName(oversellResource.Resource)]
			expectAllocatable := float64(allocatableResource.Value()) * oversellResource.Rate
			node.Status.Allocatable[corev1.ResourceName(oversellResource.Resource)] = *resource.NewQuantity(int64(expectAllocatable), resource.BinarySI)
		}
	}

	// todo 上线前取消日志
	nodeAllocatableBytes, _ = json.Marshal(node.Status.Allocatable)
	nodeCapacityBytes, _ = json.Marshal(node.Status.Capacity)
	klog.V(3).Infof("NodeUpdateHandler node %s resource should change nodeAllocatable %+v nodeCapacity %+v",
		node.Name, string(nodeAllocatableBytes), string(nodeCapacityBytes))

	marshalled, err := json.Marshal(node)
	if err != nil {
		return admission.Errored(http.StatusInternalServerError, err)
	}
	return admission.PatchResponseFromRaw(req.AdmissionRequest.Object.Raw, marshalled)
}

// InjectClient injects the client into the NodeCreateHandler
func (h *NodeCreateHandler) InjectClient(c client.Client) error {
	h.Client = c
	return nil
}

// InjectDecoder injects the decoder into the NodeCreateHandler
func (h *NodeCreateHandler) InjectDecoder(d *admission.Decoder) error {
	h.Decoder = d
	return nil
}
