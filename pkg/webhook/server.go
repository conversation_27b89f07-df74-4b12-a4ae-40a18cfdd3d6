package webhook

import (
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"
	"sync"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/runtime"
	igcmvalidating "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/webhook/configmap/validating"
	nodemutating "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/webhook/node/mutating"
	podmutating "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/webhook/pod/mutating"
	podvalidating "icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/webhook/pod/validating"

	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/webhook"
)

var (
	// HandlerMap contains all admission webhook handlers.
	handlerMap  = make(map[string]runtime.NewWebhookServer)
	handlerLock sync.RWMutex
)

// 注册webhook handler
func init() {
	addAdmissionHandlers(podmutating.HandlerMap)
	addAdmissionHandlers(podvalidating.HandlerMap)
	addAdmissionHandlers(nodemutating.HandlerMap)
	addAdmissionHandlers(igcmvalidating.HandlerMap)
}

func addAdmissionHandlers(m map[string]runtime.NewWebhookServer) {
	handlerLock.Lock()
	defer handlerLock.Unlock()
	for path, handler := range m {
		if len(path) == 0 {
			klog.Warningf("Skip handler with empty path.")
			continue
		}
		if path[0] != '/' {
			path = "/" + path
		}
		_, found := handlerMap[path]
		if found {
			klog.V(3).Infof("conflicting webhook builder path %v in handler map", path)
		}
		handlerMap[path] = handler
	}
}

// register
func SetupWithManager(serverRunOptions *options.ServerRunOptions, mgr manager.Manager) error {
	handlerLock.RLock()
	defer handlerLock.RUnlock()

	hookServer := mgr.GetWebhookServer()

	decoder := admission.NewDecoder(mgr.GetScheme())
	for path, newHandler := range handlerMap {
		handler := newHandler(serverRunOptions, mgr, decoder)
		hookServer.Register(path, &webhook.Admission{Handler: handler})
		klog.V(3).Infof("Registered webhook handler %s", path)
	}
	return nil
}
