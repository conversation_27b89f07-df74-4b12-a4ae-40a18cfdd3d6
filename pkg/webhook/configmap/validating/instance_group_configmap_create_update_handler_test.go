package validating

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	admissionv1 "k8s.io/api/admission/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"
)

var (
	testScheme = scheme.Scheme
)

// init init 函数用于初始化scheme，将其添加到testScheme中。
// 该函数只需要执行一次，通常在程序启动时调用。
func init() {
	_ = scheme.AddToScheme(testScheme)
}

// TestInstanceGroupConfigMapCreateHandler_Handle 测试InstanceGroupConfigMapCreateHandler的Handle方法，包括有效和无效的请求场景。
func TestInstanceGroupConfigMapCreateHandler_Handle(t *testing.T) {
	validCmData := []entity.InstanceGroupCm{
		{
			InstanceGroupName: "test-group",
			InstanceGroupId:   "123",
			Buffer:            10,
		},
	}
	validDataBytes, _ := json.Marshal(validCmData)

	tests := []struct {
		name       string
		req        admission.Request
		wantAllow  bool
		wantStatus int32
	}{
		{
			name: "valid configmap",
			req: admission.Request{
				AdmissionRequest: admissionv1.AdmissionRequest{
					Object: encodeRawExtension(&corev1.ConfigMap{
						ObjectMeta: metav1.ObjectMeta{
							Name:      entity.InstanceGroupCmName,
							Namespace: entity.InstanceGroupCmNamespace,
						},
						Data: map[string]string{
							entity.InstanceGropuCmDataKey: string(validDataBytes),
						},
					}),
				},
			},
			wantAllow:  true,
			wantStatus: http.StatusOK,
		},
		{
			name: "invalid namespace",
			req: admission.Request{
				AdmissionRequest: admissionv1.AdmissionRequest{
					Object: encodeRawExtension(&corev1.ConfigMap{
						ObjectMeta: metav1.ObjectMeta{
							Name:      entity.InstanceGroupCmName,
							Namespace: "invalid-namespace",
						},
						Data: map[string]string{
							entity.InstanceGropuCmDataKey: string(validDataBytes),
						},
					}),
				},
			},
			wantAllow:  true,
			wantStatus: http.StatusOK,
		},
		{
			name: "invalid name",
			req: admission.Request{
				AdmissionRequest: admissionv1.AdmissionRequest{
					Object: encodeRawExtension(&corev1.ConfigMap{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "invalid-name",
							Namespace: entity.InstanceGroupCmNamespace,
						},
						Data: map[string]string{
							entity.InstanceGropuCmDataKey: string(validDataBytes),
						},
					}),
				},
			},
			wantAllow:  true,
			wantStatus: http.StatusOK,
		},
		{
			name: "invalid json data",
			req: admission.Request{
				AdmissionRequest: admissionv1.AdmissionRequest{
					Object: encodeRawExtension(&corev1.ConfigMap{
						ObjectMeta: metav1.ObjectMeta{
							Name:      entity.InstanceGroupCmName,
							Namespace: entity.InstanceGroupCmNamespace,
						},
						Data: map[string]string{
							entity.InstanceGropuCmDataKey: "invalid-json",
						},
					}),
				},
			},
			wantAllow:  false,
			wantStatus: http.StatusBadRequest,
		},
	}
	decoder, err := admission.NewDecoder(testScheme)
	if err != nil {
		t.Errorf("cerate decoder error: %v", err)
		return
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &InstanceGroupConfigMapCreateHandler{
				Decoder: decoder,
			}
			got := h.Handle(context.Background(), tt.req)
			if got.Allowed != tt.wantAllow {
				t.Errorf("Handle() got.Allowed = %v, want %v", got.Allowed, tt.wantAllow)
			}
			if got.Result.Code != tt.wantStatus {
				t.Errorf("Handle() got.Result.Code = %v, want %v", got.Result.Code, tt.wantStatus)
			}
		})
	}
}

// encodeRawExtension 将client.Object类型的对象转换为runtime.RawExtension类型，并返回该对象
func encodeRawExtension(obj client.Object) runtime.RawExtension {
	raw := runtime.RawExtension{}
	raw.Object = obj
	bytes, _ := json.Marshal(obj)
	raw.Raw = bytes
	return raw
}
