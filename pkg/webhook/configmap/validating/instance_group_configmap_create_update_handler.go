/*
Copyright 2021 The Kruise Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package validating

import (
	"context"
	"encoding/json"
	"net/http"

	"icode.baidu.com/baidu/bci2/bci-resource-controller/cmd/options"
	"icode.baidu.com/baidu/bci2/bci-resource-controller/pkg/entity"

	corev1 "k8s.io/api/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"
)

// InstanceGroupConfigMapCreateHandler handles Pod
type InstanceGroupConfigMapCreateHandler struct {
	// To use the client, you need to do the following:
	// - uncomment it
	// - import sigs.k8s.io/controller-runtime/pkg/client
	// - uncomment the InjectClient method at the bottom of this file.
	Client client.Client

	// Decoder decodes objects
	Decoder *admission.Decoder
}

func newInstanceGroupConfigMapCreateHandler(option *options.ServerRunOptions, mgr manager.Manager, decoder *admission.Decoder) admission.Handler {
	return &InstanceGroupConfigMapCreateHandler{
		Decoder: decoder,
		Client:  mgr.GetClient(),
	}
}

var _ admission.Handler = &InstanceGroupConfigMapCreateHandler{}

// Handle handles admission requests.
func (h *InstanceGroupConfigMapCreateHandler) Handle(ctx context.Context, req admission.Request) admission.Response {
	// instancegroup configmap 创建更新校验逻辑实现
	cm := &corev1.ConfigMap{}
	err := h.Decoder.Decode(req, cm)
	if err != nil {
		return admission.Errored(http.StatusBadRequest, err)
	}

	// 校验namespace和name,非目标namespace和name的configmap不做校验
	if cm.Namespace != entity.InstanceGroupCmNamespace || cm.GetName() != entity.InstanceGroupCmName {
		return admission.Allowed("pass")
	}

	// 反序列化configmap的data字段, 看是否能够反序列化成功
	igCmData := make([]entity.InstanceGroupCm, 0)
	if err := json.Unmarshal([]byte(cm.Data[entity.InstanceGropuCmDataKey]), &igCmData); err != nil {
		return admission.Errored(http.StatusBadRequest, err)
	}

	return admission.Allowed("pass")
}

// InjectClient injects the client into the PodCreateHandler
func (h *InstanceGroupConfigMapCreateHandler) InjectClient(c client.Client) error {
	h.Client = c
	return nil
}

// InjectDecoder injects the decoder into the PodCreateHandler
func (h *InstanceGroupConfigMapCreateHandler) InjectDecoder(d *admission.Decoder) error {
	h.Decoder = d
	return nil
}
