# 功能4.1-ENI改造-单元测试清单

## 测试概述
本测试清单涵盖了功能4.1 ENI改造的所有新增功能，包括IPv6支持的ENI创建逻辑。

## 1. 常量和类型定义测试

### 1.1 测试已有常量继续有效
```go
func TestExistingConstants(t *testing.T) {
    // 验证现有常量值不变
    assert.Equal(t, "bci-ipv6-enabled", entity.BCIEnableIPv6Key)
    assert.Equal(t, "bci.baidu.com/bci-enable-ipv6", entity.BciEnableIPv6AnnotationKey)
}
```

## 2. doCreateEni函数测试

### 2.1 测试IPv4单栈ENI创建（向后兼容）
```go
func TestDoCreateEni_IPv4Only(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                "cluster-id": "test-cluster",
                // 没有IPv6标签
            },
        },
    }
    
    podInfo := &entity.WaitEniPodInfo{
        PodName:      "test-pod",
        PodNamespace: "test-account",
        AccountID:    "test-account",
        SubnetID:     "subnet-123",
        SecurityGroupIDStr: "sg-123",
    }
    
    // 创建mock controller
    ctrl := &Controller{
        networkOption: &options.NetworkOptions{
            DefaultENIBufferPrivateIPCount: 2,
            DefaultENIPrivateIPCount:       10,
        },
        eniPrivateIPConfig: map[string]*entity.ENIPrivateIPConfig{
            "test-type": {
                EniMaxPrivateIPCount: 10,
            },
        },
    }
    
    // 模拟eniClient
    mockEniClient := &mockEniClient{
        createEniResult: "eni-12345",
        createEniError:  nil,
    }
    ctrl.eniClient = mockEniClient
    
    // 执行测试
    eniID, err := ctrl.doCreateEni(node, podInfo, "subnet-123", []string{"sg-123"})
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, "eni-12345", eniID)
    
    // 验证调用参数
    createArgs := mockEniClient.lastCreateArgs
    assert.NotNil(t, createArgs)
    assert.Equal(t, "subnet-123", createArgs.SubnetID)
    assert.Len(t, createArgs.PrivateIPSet, 3) // 1主IP + 2辅助IP
    assert.Nil(t, createArgs.IPv6PrivateIPSet) // 未启用IPv6
}
```

### 2.2 测试IPv6双栈ENI创建
```go
func TestDoCreateEni_IPv6DualStack(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                "cluster-id": "test-cluster",
                entity.BCIEnableIPv6Key: "true", // 启用IPv6
            },
        },
    }
    
    podInfo := &entity.WaitEniPodInfo{
        PodName:      "test-pod",
        PodNamespace: "test-account",
        AccountID:    "test-account",
        SubnetID:     "subnet-123",
        SecurityGroupIDStr: "sg-123",
    }
    
    // 创建mock controller
    ctrl := &Controller{
        networkOption: &options.NetworkOptions{
            DefaultENIBufferPrivateIPCount: 2,
            DefaultENIPrivateIPCount:       10,
        },
        eniPrivateIPConfig: map[string]*entity.ENIPrivateIPConfig{
            "test-type": {
                EniMaxPrivateIPCount: 10,
            },
        },
    }
    
    // 模拟eniClient
    mockEniClient := &mockEniClient{
        createEniResult: "eni-12345",
        createEniError:  nil,
    }
    ctrl.eniClient = mockEniClient
    
    // 执行测试
    eniID, err := ctrl.doCreateEni(node, podInfo, "subnet-123", []string{"sg-123"})
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, "eni-12345", eniID)
    
    // 验证调用参数
    createArgs := mockEniClient.lastCreateArgs
    assert.NotNil(t, createArgs)
    assert.Equal(t, "subnet-123", createArgs.SubnetID)
    assert.Len(t, createArgs.PrivateIPSet, 3) // 1主IP + 2辅助IP
    assert.Len(t, createArgs.IPv6PrivateIPSet, 2) // 2个IPv6辅助IP
    
    // 验证IPv6 IP配置
    for _, ip := range createArgs.IPv6PrivateIPSet {
        assert.False(t, ip.Primary)
        assert.Equal(t, "", ip.PrivateIPAddress) // 空地址表示自动分配
    }
}
```

### 2.3 测试IPv6标签值为false的情况
```go
func TestDoCreateEni_IPv6Disabled(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                "cluster-id": "test-cluster",
                entity.BCIEnableIPv6Key: "false", // 明确禁用IPv6
            },
        },
    }
    
    podInfo := &entity.WaitEniPodInfo{
        PodName:      "test-pod",
        PodNamespace: "test-account",
        AccountID:    "test-account",
        SubnetID:     "subnet-123",
        SecurityGroupIDStr: "sg-123",
    }
    
    // 创建mock controller
    ctrl := &Controller{
        networkOption: &options.NetworkOptions{
            DefaultENIBufferPrivateIPCount: 2,
            DefaultENIPrivateIPCount:       10,
        },
        eniPrivateIPConfig: map[string]*entity.ENIPrivateIPConfig{
            "test-type": {
                EniMaxPrivateIPCount: 10,
            },
        },
    }
    
    // 模拟eniClient
    mockEniClient := &mockEniClient{
        createEniResult: "eni-12345",
        createEniError:  nil,
    }
    ctrl.eniClient = mockEniClient
    
    // 执行测试
    eniID, err := ctrl.doCreateEni(node, podInfo, "subnet-123", []string{"sg-123"})
    
    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, "eni-12345", eniID)
    
    // 验证调用参数
    createArgs := mockEniClient.lastCreateArgs
    assert.NotNil(t, createArgs)
    assert.Len(t, createArgs.PrivateIPSet, 3) // 1主IP + 2辅助IP
    assert.Nil(t, createArgs.IPv6PrivateIPSet) // 未启用IPv6
}
```

## 3. computeEniPrivateIPv6InitCount函数测试

### 3.1 测试基本IPv6 IP计算
```go
func TestComputeEniPrivateIPv6InitCount_Basic(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                "kubernetes.io/node-resource-type": "test-type",
                nodeEnableEniPrivateIPGC:           "true",
            },
        },
    }
    
    podInfo := &entity.WaitEniPodInfo{
        PodName:      "test-pod",
        PodNamespace: "test-account",
        AccountID:    "test-account",
        SubnetID:     "subnet-123",
        SecurityGroupIDStr: "sg-123",
    }
    
    // 创建controller
    ctrl := &Controller{
        networkOption: &options.NetworkOptions{
            DefaultENIBufferPrivateIPCount: 2,
            DefaultENIPrivateIPCount:       10,
            EnableAllNodePrivateIPGC:       true,
        },
        eniPrivateIPConfig: map[string]*entity.ENIPrivateIPConfig{
            "test-type": {
                EniMaxPrivateIPCount: 10,
            },
        },
    }
    
    // 执行测试
    count := ctrl.computeEniPrivateIPv6InitCount(podInfo, node)
    
    // 验证结果
    assert.Equal(t, 2, count) // 默认buffer count
}
```

### 3.2 测试IPv6 IP计算-节点资源类型不存在
```go
func TestComputeEniPrivateIPv6InitCount_NodeResourceTypeNotFound(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                // 没有node-resource-type标签
                nodeEnableEniPrivateIPGC: "true",
            },
        },
    }
    
    podInfo := &entity.WaitEniPodInfo{
        PodName:      "test-pod",
        PodNamespace: "test-account",
        AccountID:    "test-account",
        SubnetID:     "subnet-123",
        SecurityGroupIDStr: "sg-123",
    }
    
    // 创建controller
    ctrl := &Controller{
        networkOption: &options.NetworkOptions{
            DefaultENIBufferPrivateIPCount: 2,
            DefaultENIPrivateIPCount:       10,
            EnableAllNodePrivateIPGC:       true,
        },
        eniPrivateIPConfig: map[string]*entity.ENIPrivateIPConfig{
            "test-type": {
                EniMaxPrivateIPCount: 10,
            },
        },
    }
    
    // 执行测试
    count := ctrl.computeEniPrivateIPv6InitCount(podInfo, node)
    
    // 验证结果
    assert.Equal(t, 2, count) // 默认buffer count
}
```

### 3.3 测试IPv6 IP计算-禁用GC模式
```go
func TestComputeEniPrivateIPv6InitCount_GCDisabled(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                "kubernetes.io/node-resource-type": "test-type",
                // 没有nodeEnableEniPrivateIPGC标签
            },
        },
    }
    
    podInfo := &entity.WaitEniPodInfo{
        PodName:      "test-pod",
        PodNamespace: "test-account",
        AccountID:    "test-account",
        SubnetID:     "subnet-123",
        SecurityGroupIDStr: "sg-123",
    }
    
    // 创建controller
    ctrl := &Controller{
        networkOption: &options.NetworkOptions{
            DefaultENIBufferPrivateIPCount: 2,
            DefaultENIPrivateIPCount:       10,
            EnableAllNodePrivateIPGC:       false, // 禁用GC
        },
        eniPrivateIPConfig: map[string]*entity.ENIPrivateIPConfig{
            "test-type": {
                EniMaxPrivateIPCount: 10,
            },
        },
    }
    
    // 执行测试
    count := ctrl.computeEniPrivateIPv6InitCount(podInfo, node)
    
    // 验证结果
    assert.Equal(t, 10, count) // 使用max count
}
```

## 4. computeEniPrivateIPv6CountByNodeResource函数测试

### 4.1 测试Buffer计算
```go
func TestComputeEniPrivateIPv6CountByNodeResource_Buffer(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                "kubernetes.io/node-resource-type": "test-type",
                nodeEniPrivateIPBuffer:             "3",
            },
        },
    }
    
    // 创建controller
    ctrl := &Controller{
        networkOption: &options.NetworkOptions{
            DefaultENIBufferPrivateIPCount: 2,
            DefaultENIPrivateIPCount:       10,
        },
        eniPrivateIPConfig: map[string]*entity.ENIPrivateIPConfig{
            "test-type": {
                EniMaxPrivateIPCount: 10,
            },
        },
    }
    
    // 执行测试
    count := ctrl.computeEniPrivateIPv6CountByNodeResource(node, eniPrivateIPCountBuffer)
    
    // 验证结果
    assert.Equal(t, 3, count)
}
```

### 4.2 测试Max计算
```go
func TestComputeEniPrivateIPv6CountByNodeResource_Max(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                "kubernetes.io/node-resource-type": "test-type",
            },
        },
    }
    
    // 创建controller
    ctrl := &Controller{
        networkOption: &options.NetworkOptions{
            DefaultENIBufferPrivateIPCount: 2,
            DefaultENIPrivateIPCount:       10,
        },
        eniPrivateIPConfig: map[string]*entity.ENIPrivateIPConfig{
            "test-type": {
                EniMaxPrivateIPCount: 15,
            },
        },
    }
    
    // 执行测试
    count := ctrl.computeEniPrivateIPv6CountByNodeResource(node, eniPrivateIPCountMax)
    
    // 验证结果
    assert.Equal(t, 15, count)
}
```

### 4.3 测试无效Buffer值处理
```go
func TestComputeEniPrivateIPv6CountByNodeResource_InvalidBuffer(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                "kubernetes.io/node-resource-type": "test-type",
                nodeEniPrivateIPBuffer:             "-1", // 无效值
            },
        },
    }
    
    // 创建controller
    ctrl := &Controller{
        networkOption: &options.NetworkOptions{
            DefaultENIBufferPrivateIPCount: 2,
            DefaultENIPrivateIPCount:       10,
        },
        eniPrivateIPConfig: map[string]*entity.ENIPrivateIPConfig{
            "test-type": {
                EniMaxPrivateIPCount: 10,
            },
        },
    }
    
    // 执行测试
    count := ctrl.computeEniPrivateIPv6CountByNodeResource(node, eniPrivateIPCountBuffer)
    
    // 验证结果
    assert.Equal(t, 2, count) // 使用默认值
}
```

## 5. 错误处理测试

### 5.1 测试ENI创建失败
```go
func TestDoCreateEni_CreateFailed(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                "cluster-id": "test-cluster",
                entity.BCIEnableIPv6Key: "true",
            },
        },
    }
    
    podInfo := &entity.WaitEniPodInfo{
        PodName:      "test-pod",
        PodNamespace: "test-account",
        AccountID:    "test-account",
        SubnetID:     "subnet-123",
        SecurityGroupIDStr: "sg-123",
    }
    
    // 创建mock controller
    ctrl := &Controller{
        networkOption: &options.NetworkOptions{
            DefaultENIBufferPrivateIPCount: 2,
            DefaultENIPrivateIPCount:       10,
        },
        eniPrivateIPConfig: map[string]*entity.ENIPrivateIPConfig{
            "test-type": {
                EniMaxPrivateIPCount: 10,
            },
        },
    }
    
    // 模拟eniClient返回错误
    mockEniClient := &mockEniClient{
        createEniResult: "",
        createEniError:  fmt.Errorf("create eni failed"),
    }
    ctrl.eniClient = mockEniClient
    
    // 执行测试
    eniID, err := ctrl.doCreateEni(node, podInfo, "subnet-123", []string{"sg-123"})
    
    // 验证结果
    assert.Error(t, err)
    assert.Equal(t, "", eniID)
}
```

## 6. 性能测试

### 6.1 测试大量Pod的IP计算性能
```go
func BenchmarkComputeEniPrivateIPv6InitCount(b *testing.B) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                "kubernetes.io/node-resource-type": "test-type",
                nodeEnableEniPrivateIPGC:           "true",
            },
        },
    }
    
    podInfo := &entity.WaitEniPodInfo{
        PodName:      "test-pod",
        PodNamespace: "test-account",
        AccountID:    "test-account",
        SubnetID:     "subnet-123",
        SecurityGroupIDStr: "sg-123",
    }
    
    // 创建controller
    ctrl := &Controller{
        networkOption: &options.NetworkOptions{
            DefaultENIBufferPrivateIPCount: 2,
            DefaultENIPrivateIPCount:       10,
            EnableAllNodePrivateIPGC:       true,
        },
        eniPrivateIPConfig: map[string]*entity.ENIPrivateIPConfig{
            "test-type": {
                EniMaxPrivateIPCount: 10,
            },
        },
    }
    
    // 执行基准测试
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        ctrl.computeEniPrivateIPv6InitCount(podInfo, node)
    }
}
```

## 7. 日志记录测试

### 7.1 测试日志输出
```go
func TestDoCreateEni_LoggingIPv6(t *testing.T) {
    // 准备测试数据
    node := &corev1.Node{
        ObjectMeta: metav1.ObjectMeta{
            Name: "test-node",
            Labels: map[string]string{
                "cluster-id": "test-cluster",
                entity.BCIEnableIPv6Key: "true",
            },
        },
    }
    
    podInfo := &entity.WaitEniPodInfo{
        PodName:      "test-pod",
        PodNamespace: "test-account",
        AccountID:    "test-account",
        SubnetID:     "subnet-123",
        SecurityGroupIDStr: "sg-123",
    }
    
    // 捕获日志输出
    var logOutput bytes.Buffer
    klog.SetOutput(&logOutput)
    
    // 创建mock controller
    ctrl := &Controller{
        networkOption: &options.NetworkOptions{
            DefaultENIBufferPrivateIPCount: 2,
            DefaultENIPrivateIPCount:       10,
        },
        eniPrivateIPConfig: map[string]*entity.ENIPrivateIPConfig{
            "test-type": {
                EniMaxPrivateIPCount: 10,
            },
        },
    }
    
    // 模拟eniClient
    mockEniClient := &mockEniClient{
        createEniResult: "eni-12345",
        createEniError:  nil,
    }
    ctrl.eniClient = mockEniClient
    
    // 执行测试
    _, err := ctrl.doCreateEni(node, podInfo, "subnet-123", []string{"sg-123"})
    
    // 验证结果
    assert.NoError(t, err)
    
    // 验证日志包含IPv6信息
    logStr := logOutput.String()
    assert.Contains(t, logStr, "enableIPv6")
    assert.Contains(t, logStr, "initPrivateIPv6Count")
}
```

## 8. Mock对象定义

```go
// mockEniClient 用于单元测试的ENI客户端mock
type mockEniClient struct {
    createEniResult string
    createEniError  error
    lastCreateArgs  *eni.CreateENIArgs
}

func (m *mockEniClient) CreateENI(ctx context.Context, args *eni.CreateENIArgs, signOpt *bce.SignOption) (string, error) {
    m.lastCreateArgs = args
    return m.createEniResult, m.createEniError
}

// 其他必要的mock方法...
```

## 测试运行说明

1. 运行全部测试：
```bash
go test -v ./pkg/controller/eni/... -run TestDoCreateEni
```

2. 运行性能测试：
```bash
go test -bench=. ./pkg/controller/eni/... -run=^$
```

3. 运行覆盖率测试：
```bash
go test -coverprofile=coverage.out ./pkg/controller/eni/...
go tool cover -html=coverage.out
```

## 预期测试结果

所有测试应该通过，并且：
- 代码覆盖率应该达到90%以上
- 不应该有内存泄漏
- 性能测试应该在合理范围内（每次调用<1ms）
- 日志输出应该包含必要的调试信息

## 注意事项

1. 测试时需要模拟Kubernetes环境
2. 需要mock外部依赖（如ENI客户端）
3. 测试数据应该覆盖各种边界情况
4. 确保向后兼容性不被破坏 