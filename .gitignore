# Mac OS X files
.DS_Store
 
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
 
# Output of the go coverage tool, specifically when used with LiteIDE
*.out
 
# Project-local glide cache, RE: https://github.com/Masterminds/glide/issues/736
.glide/
 
# Dependency directories (remove the comment below to include it)
vendor/
 
#bcloud cache
.bcloud.cache
 
# output
output
output.tar.gz
result.yml
log.yml
build.log
build_info
.idea
.vscode
eni_admin_test.go