[ req ]
default_bits = 2048
prompt = no
default_md = sha256
req_extensions = req_ext
distinguished_name = dn

[ dn ]
C = CN
ST = Zhejiang
L = Hangzhou
O = opskumu
OU = opskumu
CN = pod-admission-webhook.kube-system

[ req_ext ]
subjectAltName = @alt_names

[ alt_names ]
DNS.1 = pod-admission-webhook
DNS.2 = pod-admission-webhook.kube-system
DNS.3 = pod-admission-webhook.kube-system.svc

[ v3_ext ]
authorityKeyIdentifier=keyid,issuer:always
basicConstraints=CA:FALSE
keyUsage=keyEncipherment,dataEncipherment
extendedKeyUsage=serverAuth,clientAuth
subjectAltName=@alt_names
