apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: pod-admission-webhook
  name: pod-admission-webhook
  namespace: kube-system
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: pod-admission-webhook
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: pod-admission-webhook
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - pod-admission-webhook
              topologyKey: kubernetes.io/hostname
      containers:
        - args:
            - '--kubeconfig=/kube-config/config'
            - '--cert-dir=/certs'
            - '--v=4'
            - '--webhook-server=true'
            - '--enable-leader-election=false'
          command:
            - /bci-resource-controller
          image: >-
            registry.baidubce.com/bci-dev/bci-resource-controller:r80e24ac_20221103
          imagePullPolicy: IfNotPresent
          name: pod-admission-webhook
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 5
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          resources:
            limits:
              cpu: '1'
              memory: 1Gi
            requests:
              cpu: 125m
              memory: 500Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /certs
              name: webhook-certs
              readOnly: true
            - mountPath: /kube-config
              name: kube-config
              readOnly: true
      dnsPolicy: ClusterFirst
      hostNetwork: true
      imagePullSecrets:
        - name: bce-image-secret
      nodeSelector:
        cluster-role: master
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: bci-resource-controller
      serviceAccountName: bci-resource-controller
      terminationGracePeriodSeconds: 30
      tolerations:
        - effect: NoSchedule
          key: node-role.kubernetes.io/master
          operator: Equal
      volumes:
        - name: webhook-certs
          secret:
            defaultMode: 420
            secretName: pod-admission-webhook
        - hostPath:
            path: /root/.kube
            type: ''
          name: kube-config