apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: instance-group-configmap-validator
webhooks:
  - name: instance-group-configmap-validator.yourdomain.com
    clientConfig:
      service:
        name: pod-admission-webhook
        namespace: kube-system # 修改为你的服务所在的命名空间
        path: "/validate-instance-group-configmap"
      caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMzakNDQWNZQ0NRREppUkswcEIrR25UQU5CZ2txaGtpRzl3MEJBUXNGQURBd01TNHdMQVlEVlFRRERDVncKYjJRdFlXUnRhWE56YVc5dUxYZGxZbWh2YjJzdWEzVmlaUzF6ZVhOMFpXMHVjM1pqTUNBWERUSXlNRGt4TlRBNApNVFF4T1ZvWUR6SXdOVEF3TVRNeE1EZ3hOREU1V2pBd01TNHdMQVlEVlFRRERDVndiMlF0WVdSdGFYTnphVzl1CkxYZGxZbWh2YjJzdWEzVmlaUzF6ZVhOMFpXMHVjM1pqTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEEKTUlJQkNnS0NBUUVBcWNiNzRSOHRkRjBFUDhmKzk5OFMvSSt3WUw3NFJVYVN6RnowTWxZbVdVdndFajVNdW55dQpEdnBEdzYrWXZsOHNWaWltSVJHd1ZZNlFwanV5K3ZRcENKSGpJeWFxV1lmQlZXY0pZN3RBRGFFRVdwUC8zQWloCnpuQVRWK2VidXRsb2UyMkJHTG9Rc3NGc3FodUpUL0FqL3pNTSt1d25xSDREV0ZYbTlMUGNuakRneWc1OU9ENGQKekZJNE5oUVVTeU1MWTNSMHFsRFdHL2h6cG1ESjY5MDlraTQ4S2NWU0JIbTRKOTg0Y0lFQUJpMWIvRlZ2ZG5oWAp1bGtZaS8xZWllT1I1aFY5YnVTeEV0REhIZHJmemZsWDRueUl1VDZSRUkxNW16MG03cUtSYWRwSzJwUXM2ZHZMCm5tTm1PMzFLZUVNdWR2eUcxWXpaZWx2SzZKa0pUNHppZ1FJREFRQUJNQTBHQ1NxR1NJYjNEUUVCQ3dVQUE0SUIKQVFBaS92aS9YUjBBdDlwaXBOZmYwck9oWm5qWDJkYld5NjduSjM5b0l5ak9LQUdreEozSGRIc1dtYm1Bcmw1NQpWMTRKWjZMUzk5MFdHdDBhaXNsKy9ubkRWUjF0NjA3WjVhWVVJcU1DUzJqQ24vRDNqTFJGbkhSV3pMeE42aStUClJjR3hlUHl1RlZtTW1iazc5YWM3VW9WWUpEb1ZSWVR1OThMcjRnQUdFOXhheXJmNGRjZjFxWjl6K0c3TmVGeTEKbFc1OEZZWVBxMTh4cys1N1MxZ2JVUUUrYmFDZEU2VFJMVWYwVXJaNXVQLzhKbWJrOHoyNkthTFZBTWVObkI0bwpTM0E0ZGdqSlF0NWlLOEMzejdpakMwRWJLd1o3dkFCODZFRUM4OFJiVGFQVGlJcVBremZMRFhXUlJackh4Qzd1CnY5UVhzSTdvWXlRUVdzMTZseHNYMGVGNQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0t
    rules:
      - operations: ["CREATE", "UPDATE"]
        apiGroups: [""]
        apiVersions: ["v1"]
        resources: ["configmaps"]
        scope: "*"
    admissionReviewVersions: ["v1"]
    sideEffects: None