# 功能3 - Pod NodeSelector增强和Pod调度增强 - 端到端测试清单

## 测试范围
本测试清单针对IPv6双栈网络支持中的Pod NodeSelector增强和Pod调度增强功能进行端到端测试验证。

## 测试环境准备
- 准备完整的K8s集群环境
- 部署完整的BCI系统（bci-resource-controller、bci-cni-driver等）
- 准备IPv6网络环境
- 准备测试用户账号和权限

## 1. 环境搭建验证

### 1.1 测试用例：BCI系统部署验证
**测试目的**: 确保BCI系统正常部署并支持IPv6功能
**测试步骤**:
1. 部署bci-resource-controller
2. 部署bci-cni-driver
3. 检查各组件状态
4. 验证RBAC权限配置

**验证命令**:
```bash
# 检查Pod状态
kubectl get pods -n bci-system

# 检查webhook配置
kubectl get mutatingwebhookconfigurations
kubectl get validatingwebhookconfigurations

# 检查CRD资源
kubectl get crd | grep bci
```

**预期结果**:
- 所有BCI组件Pod处于Running状态
- Webhook配置正确
- CRD资源正常

### 1.2 测试用例：节点标签验证
**测试目的**: 验证支持IPv6的节点标签配置
**测试步骤**:
1. 查看集群中的节点
2. 为测试节点添加IPv6支持标签
3. 验证标签添加成功

**验证命令**:
```bash
# 查看节点
kubectl get nodes

# 为节点添加IPv6支持标签
kubectl label node <node-name> bci-ipv6-enabled=true

# 验证标签
kubectl get nodes -L bci-ipv6-enabled
```

**预期结果**:
- 节点成功添加bci-ipv6-enabled=true标签
- 标签可以正常查询

## 2. IPv6双栈Pod调度测试

### 2.1 测试用例：双栈Pod创建和调度
**测试目的**: 验证带IPv6 annotation的Pod能正确调度到IPv6节点
**测试步骤**:
1. 创建带IPv6 annotation的Pod
2. 观察Pod调度过程
3. 验证Pod最终调度到IPv6节点

**测试Pod配置**:
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-ipv6-pod
  namespace: default
  annotations:
    bci_internal_AccountID: "test-account"
    bci.baidu.com/bci-enable-ipv6: "true"
    bci_internal_PhysicalZone: "zoneA"
    bci_internal_PodCpu: "1"
    bci_internal_PodMem: "1Gi"
spec:
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        cpu: "1"
        memory: "1Gi"
      limits:
        cpu: "1"
        memory: "1Gi"
```

**验证命令**:
```bash
# 创建Pod
kubectl apply -f test-ipv6-pod.yaml

# 观察Pod调度过程
kubectl get pods -w

# 检查Pod详情
kubectl describe pod test-ipv6-pod

# 检查Pod NodeSelector
kubectl get pod test-ipv6-pod -o yaml | grep -A 10 nodeSelector

# 检查Pod调度到的节点
kubectl get pod test-ipv6-pod -o wide
```

**预期结果**:
- Pod.Spec.NodeSelector包含tenant-lock和bci-ipv6-enabled=true
- Pod成功调度到带有bci-ipv6-enabled=true标签的节点
- 节点获得tenant-lock标签

### 2.2 测试用例：IPv4单栈Pod调度验证
**测试目的**: 验证不带IPv6 annotation的Pod仍能正常调度
**测试步骤**:
1. 创建不带IPv6 annotation的Pod
2. 观察Pod调度过程
3. 验证Pod可以调度到任意节点

**测试Pod配置**:
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-ipv4-pod
  namespace: default
  annotations:
    bci_internal_AccountID: "test-account"
    bci_internal_PhysicalZone: "zoneA"
    bci_internal_PodCpu: "1"
    bci_internal_PodMem: "1Gi"
spec:
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        cpu: "1"
        memory: "1Gi"
      limits:
        cpu: "1"
        memory: "1Gi"
```

**验证命令**:
```bash
# 创建Pod
kubectl apply -f test-ipv4-pod.yaml

# 观察Pod调度过程
kubectl get pods -w

# 检查Pod NodeSelector
kubectl get pod test-ipv4-pod -o yaml | grep -A 10 nodeSelector

# 检查Pod调度到的节点
kubectl get pod test-ipv4-pod -o wide
```

**预期结果**:
- Pod.Spec.NodeSelector只包含tenant-lock，不包含bci-ipv6-enabled
- Pod可以调度到任意可用节点
- 节点获得tenant-lock标签，但不获得bci-ipv6-enabled标签

## 3. 节点锁定机制测试

### 3.1 测试用例：IPv6节点锁定验证
**测试目的**: 验证IPv6 Pod调度时节点锁定机制
**测试步骤**:
1. 创建IPv6 Pod
2. 观察节点标签变化
3. 验证节点获得正确的标签

**验证命令**:
```bash
# 创建IPv6 Pod前检查节点标签
kubectl get node <target-node> -o yaml | grep -A 10 labels

# 创建IPv6 Pod
kubectl apply -f test-ipv6-pod.yaml

# 等待Pod调度完成
kubectl wait --for=condition=PodScheduled pod/test-ipv6-pod

# 检查节点标签变化
kubectl get node <target-node> -o yaml | grep -A 10 labels

# 检查tenant-lock标签
kubectl get nodes -L tenant-lock

# 检查bci-ipv6-enabled标签
kubectl get nodes -L bci-ipv6-enabled
```

**预期结果**:
- 节点获得tenant-lock标签
- 节点获得bci-ipv6-enabled=true标签
- 其他节点不受影响

### 3.2 测试用例：节点锁定冲突处理
**测试目的**: 验证多个Pod同时请求同一节点时的处理
**测试步骤**:
1. 并发创建多个IPv6 Pod
2. 观察节点分配情况
3. 验证冲突处理机制

**验证命令**:
```bash
# 并发创建多个Pod
for i in {1..3}; do
  sed "s/test-ipv6-pod/test-ipv6-pod-$i/" test-ipv6-pod.yaml | kubectl apply -f -
done

# 观察Pod调度情况
kubectl get pods -w

# 检查各Pod的节点分配
kubectl get pods -o wide

# 检查节点标签分配
kubectl get nodes -L tenant-lock,bci-ipv6-enabled
```

**预期结果**:
- 每个Pod都能成功调度
- 相同租户的Pod可能共享节点
- 节点标签正确分配

## 4. Webhook功能测试

### 4.1 测试用例：Pod变异Webhook验证
**测试目的**: 验证Pod创建时的变异Webhook功能
**测试步骤**:
1. 创建原始Pod（不包含NodeSelector）
2. 检查Webhook是否正确注入NodeSelector
3. 验证IPv6相关处理

**测试Pod配置**:
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-webhook-pod
  namespace: default
  annotations:
    bci_internal_AccountID: "test-account"
    bci.baidu.com/bci-enable-ipv6: "true"
    bci_internal_PhysicalZone: "zoneA"
    bci_internal_PodCpu: "1"
    bci_internal_PodMem: "1Gi"
spec:
  containers:
  - name: test-container
    image: nginx:latest
    resources:
      requests:
        cpu: "1"
        memory: "1Gi"
```

**验证命令**:
```bash
# 创建Pod
kubectl apply -f test-webhook-pod.yaml

# 检查Pod的NodeSelector
kubectl get pod test-webhook-pod -o yaml | grep -A 10 nodeSelector

# 检查Webhook日志
kubectl logs -n bci-system -l app=bci-resource-controller | grep webhook
```

**预期结果**:
- Pod.Spec.NodeSelector自动包含tenant-lock
- Pod.Spec.NodeSelector包含bci-ipv6-enabled=true
- Webhook日志正常

### 4.2 测试用例：Webhook错误处理验证
**测试目的**: 验证Webhook在异常情况下的处理
**测试步骤**:
1. 创建包含错误annotation的Pod
2. 观察Webhook的错误处理
3. 验证错误信息返回

**测试Pod配置**:
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-error-pod
  namespace: default
  annotations:
    bci_internal_AccountID: ""  # 空账户ID
    bci.baidu.com/bci-enable-ipv6: "true"
    bci_internal_PhysicalZone: "zoneA"
spec:
  containers:
  - name: test-container
    image: nginx:latest
```

**验证命令**:
```bash
# 尝试创建Pod
kubectl apply -f test-error-pod.yaml

# 检查Pod状态
kubectl get pod test-error-pod

# 检查事件
kubectl get events --sort-by=.metadata.creationTimestamp
```

**预期结果**:
- Pod创建失败
- 返回明确的错误信息
- 错误信息指出具体问题

## 5. 租户隔离测试

### 5.1 测试用例：多租户IPv6调度验证
**测试目的**: 验证多租户环境下的IPv6调度
**测试步骤**:
1. 创建不同租户的IPv6 Pod
2. 观察节点分配情况
3. 验证租户隔离机制

**测试步骤**:
```bash
# 创建租户1的Pod
sed 's/test-account/tenant1/' test-ipv6-pod.yaml | sed 's/test-ipv6-pod/tenant1-pod/' | kubectl apply -f -

# 创建租户2的Pod
sed 's/test-account/tenant2/' test-ipv6-pod.yaml | sed 's/test-ipv6-pod/tenant2-pod/' | kubectl apply -f -

# 检查Pod调度情况
kubectl get pods -o wide

# 检查节点标签
kubectl get nodes -L tenant-lock,bci-ipv6-enabled
```

**预期结果**:
- 不同租户的Pod调度到不同节点
- 每个节点只有一个租户的tenant-lock标签
- IPv6标签正确分配

### 5.2 测试用例：租户锁定解除验证
**测试目的**: 验证Pod删除后节点锁定的解除
**测试步骤**:
1. 创建IPv6 Pod并等待调度完成
2. 删除Pod
3. 观察节点标签变化

**验证命令**:
```bash
# 创建Pod
kubectl apply -f test-ipv6-pod.yaml

# 等待Pod调度完成
kubectl wait --for=condition=PodScheduled pod/test-ipv6-pod

# 检查节点标签
kubectl get nodes -L tenant-lock,bci-ipv6-enabled

# 删除Pod
kubectl delete pod test-ipv6-pod

# 等待一段时间，观察节点标签变化
sleep 60
kubectl get nodes -L tenant-lock,bci-ipv6-enabled
```

**预期结果**:
- Pod删除后，节点的tenant-lock标签被清除
- bci-ipv6-enabled标签保持不变（用于后续Pod调度）

## 6. 性能和压力测试

### 6.1 测试用例：大量Pod调度性能测试
**测试目的**: 验证IPv6增强后的调度性能
**测试步骤**:
1. 批量创建大量IPv6 Pod
2. 监控调度性能指标
3. 对比IPv4 Pod的调度性能

**测试脚本**:
```bash
#!/bin/bash

# 批量创建IPv6 Pod
for i in {1..100}; do
  sed "s/test-ipv6-pod/test-ipv6-pod-$i/" test-ipv6-pod.yaml | kubectl apply -f -
done

# 监控调度进度
watch kubectl get pods | grep -c Running

# 记录调度时间
start_time=$(date +%s)
kubectl wait --for=condition=PodScheduled pod -l app=test-ipv6 --timeout=300s
end_time=$(date +%s)
echo "调度时间: $((end_time - start_time))秒"
```

**预期结果**:
- 所有Pod都能成功调度
- 调度时间在可接受范围内
- 系统资源使用正常

### 6.2 测试用例：并发调度压力测试
**测试目的**: 验证系统在高并发情况下的稳定性
**测试步骤**:
1. 并发创建大量不同类型的Pod
2. 监控系统资源使用
3. 验证调度结果的正确性

**测试脚本**:
```bash
#!/bin/bash

# 并发创建IPv6和IPv4 Pod
for i in {1..50}; do
  sed "s/test-ipv6-pod/test-ipv6-pod-$i/" test-ipv6-pod.yaml | kubectl apply -f - &
  sed "s/test-ipv4-pod/test-ipv4-pod-$i/" test-ipv4-pod.yaml | kubectl apply -f - &
done

wait

# 检查调度结果
kubectl get pods -o wide | grep -E "(ipv4|ipv6)"

# 检查节点标签分配
kubectl get nodes -L tenant-lock,bci-ipv6-enabled
```

**预期结果**:
- 所有Pod都能成功调度
- IPv6 Pod只调度到IPv6节点
- IPv4 Pod可以调度到任意节点
- 系统保持稳定

## 7. 故障处理测试

### 7.1 测试用例：Webhook服务故障处理
**测试目的**: 验证Webhook服务故障时的处理机制
**测试步骤**:
1. 停止bci-resource-controller服务
2. 尝试创建Pod
3. 观察故障处理机制

**验证命令**:
```bash
# 停止服务
kubectl scale deployment bci-resource-controller -n bci-system --replicas=0

# 尝试创建Pod
kubectl apply -f test-ipv6-pod.yaml

# 检查Pod状态
kubectl get pod test-ipv6-pod

# 恢复服务
kubectl scale deployment bci-resource-controller -n bci-system --replicas=1
```

**预期结果**:
- 服务故障时Pod创建失败或超时
- 返回明确的错误信息
- 服务恢复后Pod可以正常创建

### 7.2 测试用例：节点故障处理
**测试目的**: 验证IPv6节点故障时的处理
**测试步骤**:
1. 创建IPv6 Pod并调度到指定节点
2. 模拟节点故障
3. 观察Pod重新调度情况

**验证命令**:
```bash
# 创建Pod
kubectl apply -f test-ipv6-pod.yaml

# 等待Pod调度完成
kubectl wait --for=condition=PodScheduled pod/test-ipv6-pod

# 模拟节点故障
kubectl cordon <target-node>
kubectl drain <target-node> --ignore-daemonsets --delete-emptydir-data

# 观察Pod重新调度
kubectl get pods -w
```

**预期结果**:
- Pod能够重新调度到其他IPv6节点
- 新节点获得正确的标签
- 系统保持稳定运行

## 8. 兼容性测试

### 8.1 测试用例：向后兼容性验证
**测试目的**: 验证新功能不影响现有功能
**测试步骤**:
1. 创建旧版本格式的Pod
2. 验证仍能正常调度
3. 检查功能不受影响

**测试Pod配置**:
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: legacy-pod
  namespace: default
  annotations:
    bci_internal_AccountID: "test-account"
    bci_internal_PhysicalZone: "zoneA"
spec:
  containers:
  - name: test-container
    image: nginx:latest
```

**验证命令**:
```bash
# 创建旧版本Pod
kubectl apply -f legacy-pod.yaml

# 检查Pod调度
kubectl get pod legacy-pod -o wide

# 检查NodeSelector
kubectl get pod legacy-pod -o yaml | grep -A 10 nodeSelector
```

**预期结果**:
- 旧版本Pod正常调度
- 只包含tenant-lock标签
- 不包含IPv6相关标签

### 8.2 测试用例：版本升级兼容性
**测试目的**: 验证系统升级后的兼容性
**测试步骤**:
1. 在升级前创建各种类型的Pod
2. 执行系统升级
3. 验证现有Pod不受影响

**验证命令**:
```bash
# 升级前创建Pod
kubectl apply -f test-ipv4-pod.yaml
kubectl apply -f test-ipv6-pod.yaml

# 检查Pod状态
kubectl get pods -o wide

# 模拟升级过程
kubectl rollout restart deployment bci-resource-controller -n bci-system

# 等待升级完成
kubectl rollout status deployment bci-resource-controller -n bci-system

# 检查现有Pod状态
kubectl get pods -o wide

# 创建新Pod验证功能
kubectl apply -f test-new-pod.yaml
```

**预期结果**:
- 现有Pod不受升级影响
- 新Pod能够使用新功能
- 系统功能正常

## 9. 监控和日志验证

### 9.1 测试用例：日志完整性验证
**测试目的**: 验证IPv6相关操作的日志记录
**测试步骤**:
1. 创建IPv6 Pod
2. 检查各组件日志
3. 验证日志的完整性和准确性

**验证命令**:
```bash
# 创建Pod
kubectl apply -f test-ipv6-pod.yaml

# 检查webhook日志
kubectl logs -n bci-system -l app=bci-resource-controller | grep -i ipv6

# 检查controller日志
kubectl logs -n bci-system -l app=bci-resource-controller | grep -i "lock.*node"

# 检查CNI日志
kubectl logs -n kube-system -l app=bci-cni | grep -i ipv6
```

**预期结果**:
- 日志记录完整
- 包含IPv6相关的关键操作
- 日志格式符合规范

### 9.2 测试用例：监控指标验证
**测试目的**: 验证IPv6相关的监控指标
**测试步骤**:
1. 创建IPv6 Pod
2. 检查监控指标
3. 验证指标的准确性

**验证命令**:
```bash
# 检查Prometheus指标
curl -s http://bci-resource-controller:8080/metrics | grep -i ipv6

# 检查调度相关指标
curl -s http://bci-resource-controller:8080/metrics | grep tenant_lock
```

**预期结果**:
- 监控指标正常更新
- 数据准确反映系统状态
- 指标命名符合规范

## 10. 清理和恢复测试

### 10.1 测试用例：资源清理验证
**测试目的**: 验证测试后的资源清理
**测试步骤**:
1. 删除所有测试Pod
2. 检查节点标签清理
3. 验证系统状态恢复

**验证命令**:
```bash
# 删除测试Pod
kubectl delete pods -l test-type=ipv6

# 检查节点标签
kubectl get nodes -L tenant-lock,bci-ipv6-enabled

# 等待系统清理
sleep 120

# 再次检查节点标签
kubectl get nodes -L tenant-lock,bci-ipv6-enabled
```

**预期结果**:
- 所有测试Pod被删除
- tenant-lock标签被清理
- 系统状态恢复正常

## 11. 验收标准

### 11.1 功能验收标准
1. 所有测试用例执行成功
2. IPv6 Pod能够正确调度到IPv6节点
3. IPv4 Pod不受IPv6功能影响
4. 租户隔离机制正常工作
5. 节点锁定和解锁机制正常

### 11.2 性能验收标准
1. IPv6 Pod调度时间不超过IPv4 Pod的120%
2. 系统资源使用增长不超过10%
3. 并发调度性能满足要求
4. 系统稳定性不受影响

### 11.3 兼容性验收标准
1. 向后兼容现有功能
2. 不破坏现有Pod调度
3. 升级过程平滑无影响
4. 支持旧版本Pod格式

## 12. 测试报告

### 12.1 测试环境信息
- Kubernetes版本: 
- BCI版本: 
- 节点数量: 
- 测试时间: 

### 12.2 测试结果汇总
- 测试用例总数: 
- 通过用例数: 
- 失败用例数: 
- 通过率: 

### 12.3 性能测试结果
- IPv6 Pod调度时间: 
- 系统资源使用: 
- 并发处理能力: 

### 12.4 问题记录
- 发现问题: 
- 解决方案: 
- 遗留问题: 

### 12.5 改进建议
- 功能改进: 
- 性能优化: 
- 用户体验: 